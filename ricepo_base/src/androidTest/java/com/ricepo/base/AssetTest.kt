package com.ricepo.base

import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.runner.RunWith

//
// Created by <PERSON><PERSON> on 28/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@RunWith(AndroidJUnit4::class)
class AssetTest {

//    @Test
//    fun testCountry() {
//        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
//
//        val c = AssetUtils.getContent("global.json", appContext)
//
//        assert(c != null)
//
//    }
}
