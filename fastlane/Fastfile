# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane


@versionCode = 615030
@versionName = "6.15.3"

default_platform(:android)

platform :android do

  desc "Clean Build"
  lane :clean_build do
    gradle(task: "clean")
  end

  desc "Assemble Build"
  lane :assemble_build do |options|
    gradle(
      task: "assemble",
      flavor: options[:build_flavor],
      build_type: options[:build_type],
      print_command: true,
      properties: {
          "versionCode" => @versionCode,
          "versionName" => @versionName,
      }
    )
  end

  desc "Runs all the tests"
  lane :test do |options|
    gradle(
      task: "test",
      flavor: options[:build_flavor],
      build_type: options[:build_type]
    )
  end

  desc "Build Apk for release"
  lane :assemble do |options|
    # clean apk outputs
    clean_build()

    # build apk
    assemble_build(build_flavor:options[:build_flavor], build_type:options[:build_type])

    # apk output path
    @apk_path = Actions.lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH]

    puts "app apk path: " + @apk_path
  end

  desc "Run UI Test in Firebase Test Lab and Send to Slack"
    lane :instrumentation_tests_testlab do |options|

      # clean apk outputs
      clean_build()

      # build apk
      assemble_build(build_flavor:options[:build_flavor], build_type:options[:build_type])

      # apk output path
      @apk_path = Actions.lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH]

      puts "android app path: " + @apk_path

      assemble_build(build_flavor:options[:build_flavor] + "Debug", build_type:"AndroidTest")
      @apk_test_path = Actions.lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH]
      puts "android test app path: " + @apk_test_path

      run_tests_firebase_testlab(
        project_id: "balmy-vertex-528",
        app_apk: "" + @apk_path,
        android_test_apk: "" + @apk_test_path,
        # gcloud firebase test android models list
        # spark plan: virtual device 10 tests/day physical device 5 tests/day
        # blaze plan: virtual device 30 hours physical device 15 hours
        devices: [
          {
              model: "Nexus6P",
              version: "27"
          },
          {
              model: "G8142",
              version: "26"
          }
        ],
        delete_firebase_files: false
      )

    file_name = "rohan-" + options[:build_flavor] + "-" + options[:build_type] + "_" + @versionCode.to_s
    sh "curl https://slack.com/api/files.upload -F token=\"****************************************************************************\" \
        -F channels=\"CPWP65HR6\" -F title=\"" + file_name + "\" -F file=@" + @apk_path

    end


  desc "Send APK to Slack"
  lane :send_apk_to_slack do |options|

    # build apk
    assemble_build(build_flavor:options[:build_flavor], build_type:options[:build_type])

    # apk output path
    @apk_path = Actions.lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH]

    file_name = "rohan-" + options[:build_flavor] + "-" + options[:build_type] + "_" + @versionCode.to_s
    sh "curl https://slack.com/api/files.upload -F token=\"****************************************************************************\" \
        -F channels=\"CPWP65HR6\" -F title=\"" + file_name + "\" -F file=@" + @apk_path
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleRelease")
    crashlytics

    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    upload_to_play_store
  end

  desc "Run UI Test in Firebase Test Lab fro Local"
    lane :instrumentation_tests_testlab_local do |options|
      # clean apk outputs
      clean_build()

      assemble_build(build_flavor:options[:build_flavor], build_type:"Debug")
      @app_apk = Actions.lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH]
      puts "app path: " + @app_apk

      assemble_build(build_flavor:options[:build_flavor] + "Debug", build_type:"AndroidTest")
      @android_test_apk = Actions.lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH]
      puts "android test app path: " + @android_test_apk

      run_tests_firebase_testlab(
        project_id: "balmy-vertex-528",
        app_apk: @app_apk,
        android_test_apk: @android_test_apk,
        # gcloud firebase test android models list
        # spark plan: virtual device 10 tests/day physical device 5 tests/day
        # blaze plan: virtual device 30 hours physical device 15 hours
        devices: [
          {
              model: "Nexus6P",
              version: "27"
          },
          {
              model: "G8142",
              version: "26"
          }
        ],
        delete_firebase_files: false
      )
    end

end
