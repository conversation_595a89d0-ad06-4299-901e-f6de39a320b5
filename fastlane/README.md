fastlane documentation
================
# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```
xcode-select --install
```

Install _fastlane_ using
```
[sudo] gem install fastlane -NV
```
or alternatively using `brew install fastlane`

# Available Actions
## Android
### android clean_build
```
fastlane android clean_build
```
Clean Build
### android assemble_build
```
fastlane android assemble_build
```
Assemble Build
### android test
```
fastlane android test
```
Runs all the tests
### android assemble
```
fastlane android assemble
```
Build Apk for release
### android instrumentation_tests_testlab
```
fastlane android instrumentation_tests_testlab
```
Run UI Test in Firebase Test Lab
### android send_apk_to_slack
```
fastlane android send_apk_to_slack
```
Send APK to Slack
### android beta
```
fastlane android beta
```
Submit a new Beta Build to Crashlytics Beta
### android deploy
```
fastlane android deploy
```
Deploy a new version to the Google Play
### android instrumentation_tests_testlab_local
```
fastlane android instrumentation_tests_testlab_local
```
Run UI Test in Firebase Test Lab fro Local

----

This README.md is auto-generated and will be re-generated every time [fastlane](https://fastlane.tools) is run.
More information about fastlane can be found on [fastlane.tools](https://fastlane.tools).
The documentation of fastlane can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
