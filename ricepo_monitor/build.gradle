apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
//apply plugin: 'android-aspectjx'

android {
    namespace 'com.ricepo.monitor'
    def globalConfiguration = rootProject.extensions.getByName("ext")
    compileSdk globalConfiguration["androidcompileSdk"]
    buildToolsVersion globalConfiguration["androidBuildToolsVersion"]

    defaultConfig {
        minSdkVersion globalConfiguration["androidMinSdkVersion"]
        targetSdkVersion globalConfiguration["androidTargetSdkVersion"]

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildFeatures {
        buildConfig true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility javaVersion
        targetCompatibility javaVersion
    }

    sourceSets {
        main {
            // dlopen failed: library "libc++_shared.so" not found
            jniLibs.srcDirs = ['libs']
        }
    }
}

dependencies {

    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation monitorDependencies.kotlin
    implementation monitorDependencies.kotlinCoroutines
    api monitorDependencies.firebaseAnalytics
    api monitorDependencies.firebaseCrashlytics

    implementation platform("com.google.firebase:firebase-bom:${firebaseBomVersion}")
    implementation monitorDependencies.firebasePerf

    implementation monitorDependencies.firebaseConfig

//    implementation monitorDependencies.sentry
    implementation monitorDependencies.rxKotlin

    implementation monitorDependencies.aspectjrt

//    implementation "com.github.akarnokd:rxjava3-extensions:3.0.0"

    implementation monitorDependencies.gson

    testImplementation monitorTestDependencies.junit
    androidTestImplementation monitorTestDependencies.extJunit
    androidTestImplementation monitorTestDependencies.espressoCore

}
