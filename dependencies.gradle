ext {

    // Android Tool Version
    androidBuildToolsVersion = "34.0.0"
    androidMinSdkVersion = 23
    androidTargetSdkVersion = 34
    androidcompileSdk = 34
    kotlinVersion = '1.8.10'
    coroutinesVersion = '1.6.0'
    javaVersion = JavaVersion.VERSION_17

    // Android Libraries
    appCompatVersion = '1.3.1'
    recyclerVersion = '1.1.0'
    materialVersion = '1.3.0'
    constraintLayoutVersion = '2.0.1'
    navigationVersion = '2.2.1'
    lifecycleVersion = '2.2.0'
    coreKtxVersion = '1.0.2'
    legacyVersion = '1.0.0'
    roomVersion = '2.6.0'
    activityVersion = '1.5.1'
    fragmentVersion = '1.5.2'
    swipeRefreshLayoutVersion = '1.0.0'
    viewpager2Version = '1.0.0'
    webkitVersion = '1.2.0'
    workVersion = '2.7.1'
    securityVersion = '1.0.0'
    pagingVersion = '3.0.0-alpha01'
    adsIdentifierVersion = '1.0.0-alpha01'
    guavaVersion = '28.0-android'
    hiltVersion = "2.44"

    // Base Libraries
    rxKotlinVersion = '3.0.0'
    rxAndroidVersion = '3.0.0'
    rxLifecycleVersion = '4.0.2'

    okHttpVersion = '4.9.3'
    retrofitVersion = '2.3.0'
    sandwich = '1.2.4'
    gsonVersion = '2.8.5'
    retrofitRxjava3AdapterVersion = '3.0.0'
    javaxInjectVersion = '1'
    firebaseVersion = '19.2.1'
    leakCanaryVersion = '2.14'
    arouterVersion = '1.5.2'
    arouterCompilerVersion = '1.2.1'

    googlePlacesVersion = '2.6.0'
    firebaseAnalyticsVersion = '17.5.0'
    firebaseCrashlyticsVersion = '17.0.0'
    // 19.0.8 dependencies protobuf-javalite 3.11.0 conflict with security
//    firebasePerfVersion = '19.0.7'
    // firebase perf plugin to firebase bom
    firebaseBomVersion = '27.0.0'
    firebaseConfigVersion = '19.1.4'
    firebaseLinksVersion = '19.1.0'
    firebaseMessagingVersion = '20.2.3'

//    sentryVersion = '4.3.0'
    glideVersion = '4.11.0'

    wechatVersion = '6.6.4'

    phonenumberVersion = '8.12.4'

    stripeVersion = '16.2.1'

    paypal = "1.3.0"

    googleMapUtilsVersion = '2.3.0'
    playServicesBaseVersion = '17.4.0'
//    playCoreVersion = '1.9.1'
//    playCoreKtxVersion = '1.8.1'
//    appUpdateVersion = '2.0.0' // 2.1.0
    reviewVersion = '2.0.1'

    googleWalletVersion = '18.1.2'

    startupVersion = '1.0.0'

    onesingalVersion = '[4.0.0, 4.99.99]'

//    marsVersion = '1.2.3'
    xlogVersion = "1.11.0"

    // mapbox
    mapboxMapPluginPrefix    = 'v9'
    mapboxMapSdk             = '9.6.1'
    mapboxJavaSdk            = '5.5.0'
    mapboxPluginBuilding     = '0.7.0'
    mapboxPluginPlaces       = '0.12.0'
    mapboxPluginLocalization = '0.12.0'
    mapboxPluginTraffic      = '0.10.0'
    mapboxChinaPlugin        = '2.4.0'
    mapboxPluginMarkerView   = '0.4.0'
    mapboxPluginAnnotation   = '0.9.0'
    mapboxPluginScalebar     = '0.5.0'
    mapboxPluginUtils        = 'v0.3'

    // branch.io
    branchIOVersion = '5.0.10'

    // Third Libraries
    autoSizeVersion = '1.2.1'
    immersionbarVersion = '3.2.2'
    threeTenAbpVersion = '1.2.3'
    aspectjrtVersion = '1.9.5'
    circleImageViewVersion = '3.1.0'
//    coroutinesPermissionVersion = '2.0.3'
    smartRefreshVersion = '2.1.0'
    bannerVersion = '2.2.3'
    gravitySnapVersion = '2.2.1'
//    indicatorSeekbarVersion = '2.1.2'
    gifDrawableVersion = '1.2.22'
    smartRefreshHorizontalVersion = '2.1.0' // there is more blank space in the bottom
    materialratingbar = '1.4.0'

    // lint
    ktLint ='0.41.0'

    // chucker
    chucker = '3.5.2'

    //whatif
    whatif =  '1.1.1'

    // Test Libraries
    junitVersion = '4.12'
    extJunitVersion = '1.1.0'
    espressoVersion = '3.1.1'
    mockitoKotlinVersion = '2.2.0'
    turbineVersion = "0.6.0"

    //compose
    compose_version = '1.4.3'
    composs_activity = '1.4.0'
    compose_viewmodel = '2.4.1'

    firebase_config_ktx = "21.0.1"

    withoutSecurityStuff = {
        exclude group: "com.google.protobuf", module: "protobuf-javalite"
    }

    common = [
            compose_runtime: "androidx.compose.runtime:runtime:${compose_version}",
            // Integration with activities
            compose_activity: "androidx.activity:activity-compose:${composs_activity}",
            // Compose Material Design
            compose_material: "androidx.compose.material:material:${compose_version}",
            // Animations
            compose_animation: "androidx.compose.animation:animation:${compose_version}",
            //fundation
            compose_fundation: "androidx.compose.foundation:foundation:${compose_version}",
            // Tooling support (Previews, etc.)
            compose_ui_tooling: "androidx.compose.ui:ui-tooling:${compose_version}",
            // Integration with ViewModels
            compose_viewmodel: "androidx.lifecycle:lifecycle-viewmodel-compose:${compose_viewmodel}",
            //preview
            compose_preview: "androidx.compose.ui:ui-tooling-preview:${compose_version}",
            // UI Tests
            compose_ui_test: "androidx.compose.ui:ui-test-junit4:${compose_version}",
    ]

    appDependencies = [
            kotlin: "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlinVersion}",
            kotlinCoroutines: "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutinesVersion",
            kotlinCoroutine_tst: "org.jetbrains.kotlinx:kotlinx-coroutines-test:$coroutinesVersion",
            turbine: "app.cash.turbine:turbine:$turbineVersion",
            appCompat: "androidx.appcompat:appcompat:${appCompatVersion}",
            constraintLayout: "androidx.constraintlayout:constraintlayout:${constraintLayoutVersion}",

            coreKtx: "androidx.core:core-ktx:${coreKtxVersion}",
            legacySupport: "androidx.legacy:legacy-support-v4:${legacyVersion}",

            navigationFragmentKtx: "androidx.navigation:navigation-fragment-ktx:${navigationVersion}",
            navigationUiKtx: "androidx.navigation:navigation-ui-ktx:${navigationVersion}",

            lifecycleViewModelKtx: "androidx.lifecycle:lifecycle-viewmodel-ktx:${lifecycleVersion}",
            lifecycleLiveDataKtx: "androidx.lifecycle:lifecycle-livedata-ktx:${lifecycleVersion}",
            lifecycleRuntimeKtx: "androidx.lifecycle:lifecycle-runtime-ktx:${lifecycleVersion}",

            lifecycleExtensions: "androidx.lifecycle:lifecycle-extensions:${lifecycleVersion}",

            rxAndroid: "io.reactivex.rxjava3:rxandroid:${rxAndroidVersion}",
            rxKotlin: "io.reactivex.rxjava3:rxkotlin:${rxKotlinVersion}",

            retrofit: "com.squareup.retrofit2:retrofit:${retrofitVersion}",

            javaxInject: "javax.inject:javax.inject:${javaxInjectVersion}",

            leakCanary: "com.squareup.leakcanary:leakcanary-android:${leakCanaryVersion}",
            leakCanaryPlumber: "com.squareup.leakcanary:plumber-android:${leakCanaryVersion}",
            leakCanaryProd: "com.squareup.leakcanary:leakcanary-object-watcher-android:${leakCanaryVersion}",

            arouter: "com.alibaba:arouter-api:${arouterVersion}",
            arouterCompiler: "com.alibaba:arouter-compiler:${arouterCompilerVersion}",

            autoSize: "me.jessyan:autosize:${autoSizeVersion}",
            material: "com.google.android.material:material:${materialVersion}",
            immersionbar: "com.geyifeng.immersionbar:immersionbar:${immersionbarVersion}",
            roomRuntime: "androidx.room:room-runtime:${roomVersion}",
            roomCompiler: "androidx.room:room-compiler:${roomVersion}",
            roomKtx: "androidx.room:room-ktx:${roomVersion}",

            googlePlaces: "com.google.android.libraries.places:places:${googlePlacesVersion}",

            activityKtx: "androidx.activity:activity-ktx:${activityVersion}",
            fragmentKtx: "androidx.fragment:fragment-ktx:${fragmentVersion}",
            swipeRefreshLayout: "androidx.swiperefreshlayout:swiperefreshlayout:${swipeRefreshLayoutVersion}",
            viewpager2: "androidx.viewpager2:viewpager2:${viewpager2Version}",

            threeTenAbp: "com.jakewharton.threetenabp:threetenabp:${threeTenAbpVersion}",
            webkit: "androidx.webkit:webkit:${webkitVersion}",

            security: "androidx.security:security-crypto:${securityVersion}",
            phonenumber: "com.googlecode.libphonenumber:libphonenumber:${phonenumberVersion}",

            rxLifecycle: "com.trello.rxlifecycle4:rxlifecycle:${rxLifecycleVersion}",
            rxLifecycleComponent: "com.trello.rxlifecycle4:rxlifecycle-components:${rxLifecycleVersion}",

            circleImageView: "de.hdodenhof:circleimageview:${circleImageViewVersion}",
//            coroutinesPermission: "com.sagar:coroutinespermission:${coroutinesPermissionVersion}",

            wechat: "com.tencent.mm.opensdk:wechat-sdk-android-without-mta:${wechatVersion}",
            stripe: "com.stripe:stripe-android:${stripeVersion}",

            paging: "androidx.paging:paging-runtime:${pagingVersion}",

            workRuntimeKtx: "androidx.work:work-runtime-ktx:${workVersion}",
            workRxJava: "androidx.work:work-rxjava3:${workVersion}",

            smartRefreshLayout: "io.github.scwang90:refresh-layout-kernel:${smartRefreshVersion}",
            smartRefreshClassicsHeader: "io.github.scwang90:refresh-header-classics:${smartRefreshVersion}",
            smartRefreshClassicsFooter: "io.github.scwang90:refresh-footer-classics:${smartRefreshVersion}",
            smartRefreshHorizontal: "io.github.scwang90:refresh-layout-horizontal:${smartRefreshHorizontalVersion}",

            firebaseLinksKtx: "com.google.firebase:firebase-dynamic-links-ktx:${firebaseLinksVersion}",
            firebaseMessaging: "com.google.firebase:firebase-messaging:${firebaseMessagingVersion}",

            banner: "io.github.youth5201314:banner:${bannerVersion}",

            firebaseAnalytics: "com.google.firebase:firebase-analytics-ktx:${firebaseAnalyticsVersion}",

            playServicesBase: "com.google.android.gms:play-services-base:${playServicesBaseVersion}",

            gravitySnapHelper: "com.github.rubensousa:gravitysnaphelper:${gravitySnapVersion}",

//            playCore: "com.google.android.play:core:${playCoreVersion}",
//            playCoreKtx: "com.google.android.play:core-ktx:${playCoreKtxVersion}",
//            appUpdate: "com.google.android.play:app-update:${appUpdateVersion}",
//            appUpdateKtx: "com.google.android.play:app-update-ktx:${appUpdateVersion}",

            review: "com.google.android.play:review:${reviewVersion}",
            reviewKtx: "com.google.android.play:review-ktx:${reviewVersion}",

            googleWallet: "com.google.android.gms:play-services-wallet:${googleWalletVersion}",

            startup: "androidx.startup:startup-runtime:${startupVersion}",

//            indicatorSeekbar: "com.github.warkiz.widget:indicatorseekbar:${indicatorSeekbarVersion}",


            gifDrawable: "pl.droidsonroids.gif:android-gif-drawable:${gifDrawableVersion}",

            branchIO: "io.branch.sdk.android:library:${branchIOVersion}",

            //hilt
            hiltAndroid: "com.google.dagger:hilt-android:${hiltVersion}",
            hiltCompiler: "com.google.dagger:hilt-compiler:${hiltVersion}",

            //paypal
            paypal: "com.paypal.checkout:android-sdk:${paypal}",

            materialratingbar: "me.zhanghai.android.materialratingbar:library:${materialratingbar}",
    ]

    appTestDependencies = [
            junit: "junit:junit:${junitVersion}",
            extJunit: "androidx.test.ext:junit:${extJunitVersion}",
            espressoCore: "androidx.test.espresso:espresso-core:${espressoVersion}",
            mockitoKotlin:  "com.nhaarman.mockitokotlin2:mockito-kotlin:${mockitoKotlinVersion}",
    ]

    baseDependencies = [
            kotlin: "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlinVersion}",
            kotlinCoroutines: "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutinesVersion",
            kotlinReflect: "org.jetbrains.kotlin:kotlin-reflect:${kotlinVersion}",
            appCompat: "androidx.appcompat:appcompat:${appCompatVersion}",
            rxAndroid: "io.reactivex.rxjava3:rxandroid:${rxAndroidVersion}",
            rxKotlin: "io.reactivex.rxjava3:rxkotlin:${rxKotlinVersion}",
            retrofitAdapter: "com.squareup.retrofit2:adapter-rxjava2:${retrofitVersion}",

            recyclerview: "androidx.recyclerview:recyclerview:${recyclerVersion}",

            javaxInject: "javax.inject:javax.inject:${javaxInjectVersion}",

            gson: "com.google.code.gson:gson:${gsonVersion}",
            autoSize: "me.jessyan:autosize:${autoSizeVersion}",
            // immersive status bar
            immersionbar: "com.geyifeng.immersionbar:immersionbar:${immersionbarVersion}",

            roomRuntime: "androidx.room:room-runtime:${roomVersion}",
            roomCompiler: "androidx.room:room-compiler:${roomVersion}",
            roomKtx: "androidx.room:room-ktx:${roomVersion}",
            viewpager2: "androidx.viewpager2:viewpager2:${viewpager2Version}",

            glide: "com.github.bumptech.glide:glide:${glideVersion}",
            glideCompiler: "com.github.bumptech.glide:glide:${glideVersion}",

            constraintLayout: "androidx.constraintlayout:constraintlayout:${constraintLayoutVersion}",
//            coroutinesPermission: "com.sagar:coroutinespermission:${coroutinesPermissionVersion}",

            lifecycleViewModelKtx: "androidx.lifecycle:lifecycle-viewmodel-ktx:${lifecycleVersion}",
            lifecycleRuntimeKtx: "androidx.lifecycle:lifecycle-runtime-ktx:${lifecycleVersion}",

            rxLifecycle: "com.trello.rxlifecycle4:rxlifecycle:${rxLifecycleVersion}",
            rxLifecycleComponent: "com.trello.rxlifecycle4:rxlifecycle-components:${rxLifecycleVersion}",

            smartRefreshLayout: "io.github.scwang90:refresh-layout-kernel:${smartRefreshVersion}",

            threeTenAbp: "com.jakewharton.threetenabp:threetenabp:${threeTenAbpVersion}",

            firebaseAnalytics: "com.google.firebase:firebase-analytics-ktx:${firebaseAnalyticsVersion}",

            paging: "androidx.paging:paging-runtime:${pagingVersion}",

            coreKtx: "androidx.core:core-ktx:${coreKtxVersion}",

            activityKtx: "androidx.activity:activity-ktx:${activityVersion}",

            whatif: "com.github.skydoves:whatif:${whatif}",

            firebase_config_ktx: "com.google.firebase:firebase-config-ktx:${firebase_config_ktx}",

    ]

    baseTestDependencies = [
            junit: "junit:junit:${junitVersion}",
            extJunit: "androidx.test.ext:junit:${extJunitVersion}",
            espressoCore: "androidx.test.espresso:espresso-core:${espressoVersion}"
    ]

    networkDependencies = [
            kotlin: "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlinVersion}",
            kotlinCoroutines: "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutinesVersion",
            okHttp: "com.squareup.okhttp3:okhttp:${okHttpVersion}",
            okHttpLogger: "com.squareup.okhttp3:logging-interceptor:${okHttpVersion}",
            retrofit: "com.squareup.retrofit2:retrofit:${retrofitVersion}",
            retrofitConverter: "com.squareup.retrofit2:converter-gson:${retrofitVersion}",
            retrofitAdapter: "com.squareup.retrofit2:adapter-rxjava2:${retrofitVersion}",
            retrofitRxjava3Adapter: "com.github.akarnokd:rxjava3-retrofit-adapter:${retrofitRxjava3AdapterVersion}",
            javaxInject: "javax.inject:javax.inject:${javaxInjectVersion}",

            rxKotlin: "io.reactivex.rxjava3:rxkotlin:${rxKotlinVersion}",
            rxAndroid: "io.reactivex.rxjava3:rxandroid:${rxAndroidVersion}",

            chuckerDebug: "com.github.chuckerteam.chucker:library:${chucker}",
            chuckerRelease: "com.github.chuckerteam.chucker:library-no-op:${chucker}",

            sandwich:"com.github.skydoves:sandwich:${sandwich}"
    ]

    networkTestDependencies = [
            junit: "junit:junit:${junitVersion}",
            extJunit: "androidx.test.ext:junit:${extJunitVersion}",
            espressoCore: "androidx.test.espresso:espresso-core:${espressoVersion}",
            mockitoKotlin:  "com.nhaarman.mockitokotlin2:mockito-kotlin:${mockitoKotlinVersion}",
    ]

    monitorDependencies = [
            kotlin: "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlinVersion}",
            kotlinCoroutines: "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutinesVersion",
            //firebase: "com.google.firebase:firebase-database-ktx:${firebaseVersion}",

            firebaseAnalytics: "com.google.firebase:firebase-analytics-ktx:${firebaseAnalyticsVersion}",
            firebaseCrashlytics: "com.google.firebase:firebase-crashlytics:${firebaseCrashlyticsVersion}",
//            firebasePerf: "com.google.firebase:firebase-perf:${firebasePerfVersion}",
            firebasePerf: "com.google.firebase:firebase-perf-ktx",
            firebaseConfig: "com.google.firebase:firebase-config:${firebaseConfigVersion}",

//            sentry: "io.sentry:sentry-android:${sentryVersion}",

            rxKotlin: "io.reactivex.rxjava3:rxkotlin:${rxKotlinVersion}",
            aspectjrt: "org.aspectj:aspectjrt:${aspectjrtVersion}",

            gson: "com.google.code.gson:gson:${gsonVersion}",

//            marsLog: "com.tencent.mars:mars-xlog:${marsVersion}",
            xlog: "com.elvishew:xlog:${xlogVersion}",

    ]

    monitorTestDependencies = [
            junit: "junit:junit:${junitVersion}",
            extJunit: "androidx.test.ext:junit:${extJunitVersion}",
            espressoCore: "androidx.test.espresso:espresso-core:${espressoVersion}"
    ]

    styleDependencies = [
            appCompat: "androidx.appcompat:appcompat:${appCompatVersion}",
            material: "com.google.android.material:material:${materialVersion}",
            constraintLayout: "androidx.constraintlayout:constraintlayout:${constraintLayoutVersion}",
            coreKtx: "androidx.core:core-ktx:${coreKtxVersion}",

            kotlin: "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlinVersion}",

            gson: "com.google.code.gson:gson:${gsonVersion}",

    ]

    mapDependencies = [
            googlePlaces: "com.google.android.libraries.places:places:${googlePlacesVersion}",
            rxKotlin: "io.reactivex.rxjava3:rxkotlin:${rxKotlinVersion}",

            roomRuntime: "androidx.room:room-runtime:${roomVersion}",
            roomCompiler: "androidx.room:room-compiler:${roomVersion}",
            roomKtx: "androidx.room:room-ktx:${roomVersion}",

            googleMapUtils: "com.google.maps.android:android-maps-utils:${googleMapUtilsVersion}",
            // does not require google play service
//            googleMapUtils: "com.google.maps.android:android-maps-utils-v3:${googleMapUtilsVersion}",

            constraintLayout: "androidx.constraintlayout:constraintlayout:${constraintLayoutVersion}",

            // Mapbox
            mapboxMapSdk             : "com.mapbox.mapboxsdk:mapbox-android-sdk:${mapboxMapSdk}",
            mapboxTurf               : "com.mapbox.mapboxsdk:mapbox-sdk-turf:${mapboxJavaSdk}",
            mapboxServices           : "com.mapbox.mapboxsdk:mapbox-sdk-services:${mapboxJavaSdk}",

            // Mapbox plugins
            mapboxPluginBuilding     : "com.mapbox.mapboxsdk:mapbox-android-plugin-building-${mapboxMapPluginPrefix}:${mapboxPluginBuilding}",
            mapboxPluginPlaces       : "com.mapbox.mapboxsdk:mapbox-android-plugin-places-${mapboxMapPluginPrefix}:${mapboxPluginPlaces}",
            mapboxPluginLocalization : "com.mapbox.mapboxsdk:mapbox-android-plugin-localization-${mapboxMapPluginPrefix}:${mapboxPluginLocalization}",
            mapboxPluginTraffic      : "com.mapbox.mapboxsdk:mapbox-android-plugin-traffic-${mapboxMapPluginPrefix}:${mapboxPluginTraffic}",
            mapboxChinaPlugin        : "com.mapbox.mapboxsdk:mapbox-android-plugin-china:${mapboxChinaPlugin}",
            mapboxPluginMarkerView   : "com.mapbox.mapboxsdk:mapbox-android-plugin-markerview-${mapboxMapPluginPrefix}:${mapboxPluginMarkerView}",
            mapboxPluginAnnotation   : "com.mapbox.mapboxsdk:mapbox-android-plugin-annotation-${mapboxMapPluginPrefix}:${mapboxPluginAnnotation}",
            mapboxPluginScalebar     : "com.mapbox.mapboxsdk:mapbox-android-plugin-scalebar-${mapboxMapPluginPrefix}:${mapboxPluginScalebar}",

            mapboxPluginUtils        : "com.github.mastrgamr:mapbox-android-utils:$mapboxPluginUtils"

    ]

    tripartiteDependencies = [
            wechat: "com.tencent.mm.opensdk:wechat-sdk-android-without-mta:${wechatVersion}",
            stripe: "com.stripe:stripe-android:${stripeVersion}",
            constraintLayout: "androidx.constraintlayout:constraintlayout:${constraintLayoutVersion}",
            firebaseLinksKtx: "com.google.firebase:firebase-dynamic-links-ktx:${firebaseLinksVersion}",
            firebaseMessaging: "com.google.firebase:firebase-messaging:${firebaseMessagingVersion}",

            adsIdentifier: "androidx.ads:ads-identifier:${adsIdentifierVersion}",
            guava: "com.google.guava:guava:${guavaVersion}",

            junit: "junit:junit:${junitVersion}",
            extJunit: "androidx.test.ext:junit:${extJunitVersion}",
    ]

}

