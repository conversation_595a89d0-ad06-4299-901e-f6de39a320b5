package com.ricepo.tripartite.google

import android.net.Uri
import com.google.firebase.dynamiclinks.ktx.androidParameters
import com.google.firebase.dynamiclinks.ktx.dynamicLinks
import com.google.firebase.dynamiclinks.ktx.iosParameters
import com.google.firebase.dynamiclinks.ktx.navigationInfoParameters
import com.google.firebase.dynamiclinks.ktx.shortLinkAsync
import com.google.firebase.ktx.Firebase
import java.lang.StringBuilder
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

//
// Created by <PERSON><PERSON> on 23/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object DynamicLinks {

  private const val BASE_URL = "https://rice.rocks"

  private const val DEEP_LINK_PREFIX = "https://ricerocks.page.link"

  private const val PACKAGE_NAME = "rocks.rice.app"

  private const val APPSTORE_ID = "1577237164"

  private const val IOS_MIN_VERSION = "6.2.30"

  private const val ANDROID_MIN_VERSION = 605010

  /**
   * get the dynamic short link
   * the url contain page restId and groupId
   */
  suspend fun getGroupOrderLink(restaurantId: String, groupId: String): Uri? {

    val url = StringBuilder(BASE_URL)
      .append("/?page=groupOrder")
      .append("&restId=")
      .append(restaurantId)
      .append("&groupId=")
      .append(groupId)
      .toString()

    val dynamicLink = Firebase.dynamicLinks.shortLinkAsync {
      link = Uri.parse(url)
      domainUriPrefix = DEEP_LINK_PREFIX
      // open links with on iOS
      iosParameters(
        bundleId = PACKAGE_NAME
      ) {
        appStoreId = APPSTORE_ID
        minimumVersion = IOS_MIN_VERSION
        navigationInfoParameters {
          this.forcedRedirectEnabled = true
        }
      }
      // open links with this app on Android (version 6.5.1)
      androidParameters(PACKAGE_NAME) {
        minimumVersion = ANDROID_MIN_VERSION
      }
    }

    return suspendCoroutine { cont ->
      dynamicLink.addOnSuccessListener { result ->
        cont.resume(result.shortLink)
      }
      dynamicLink.addOnFailureListener { e ->
        e.printStackTrace()
        // ApiException 8
        cont.resume(null)
      }
    }
  }
}
