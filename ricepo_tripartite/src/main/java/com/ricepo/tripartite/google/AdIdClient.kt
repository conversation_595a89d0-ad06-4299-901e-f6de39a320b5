package com.ricepo.tripartite.google

import android.content.Context
import android.util.Log
import androidx.ads.identifier.AdvertisingIdClient
import androidx.ads.identifier.AdvertisingIdInfo
import com.google.common.util.concurrent.FutureCallback
import com.google.common.util.concurrent.Futures.addCallback
import java.util.concurrent.Executors
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

//
// Created by <PERSON><PERSON> on 29/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object AdIdClient {

  private const val INVALID_ID = "00000000-0000-0000-0000-000000000000"

  suspend fun determineAdvertisingInfo(applicationContext: Context): String {
    if (AdvertisingIdClient.isAdvertisingIdProviderAvailable(applicationContext)) {
      val advertisingIdInfoListenableFuture =
        AdvertisingIdClient.getAdvertisingIdInfo(applicationContext)

      return suspendCoroutine<String> { cont ->
        addCallback(
          advertisingIdInfoListenableFuture,
          object : FutureCallback<AdvertisingIdInfo> {
            override fun onSuccess(adInfo: AdvertisingIdInfo?) {
              val id: String? = adInfo?.id
              val providerPackageName: String? = adInfo?.providerPackageName
              val isLimitTrackingEnabled: Boolean? = adInfo?.isLimitAdTrackingEnabled

              Log.i("thom", "$id $providerPackageName $isLimitTrackingEnabled")

              if (checkUUIDPref(applicationContext).isNullOrEmpty() && id != null &&
                id != INVALID_ID
              ) {
                val content = createUUIDPref(applicationContext, id)
                cont.resume(content)
              } else {
                cont.resume(createUUID(applicationContext))
              }
            }

            override fun onFailure(t: Throwable) {
              Log.e("thom", "Failed to connect to Advertising ID provider.")
              // Try to connect to the Advertising ID provider again, or fall
              // back to an ads solution that doesn't require using the
              // Advertising ID library.
              cont.resume(createUUID(applicationContext))
            }
          },
          Executors.newSingleThreadExecutor()
        )
      }
    } else {
      // The Advertising ID client library is unavailable. Use a different
      // library to perform any required ads use cases.
      Log.i("thom", "adid not available")
      return createUUID(applicationContext)
    }
  }

  /**
   * get the uuid by random if adid failed
   */
  private fun createUUID(context: Context): String {
    var uuid = DeviceIdUtils.getDeviceId(context)
    val content = checkUUIDPref(context)
    return if (content.isNullOrEmpty()) {
      createUUIDPref(context, uuid)
    } else {
      content
    }
  }

  /**
   * save uuid to share preference
   * clear storage also clear uuid
   */
  fun createUUIDPref(context: Context, content: String): String {
    val id = "ricepo_$content"
    val sharedPref = context.getSharedPreferences(
      ADID_PREF,
      Context.MODE_PRIVATE
    )
    with(sharedPref.edit()) {
      putString(ADID_KEY, id)
      commit()
    }

    return id
  }

  /**
   * check uuid from share preference
   */
  fun checkUUIDPref(context: Context): String? {
    val sharedPref = context.getSharedPreferences(
      ADID_PREF,
      Context.MODE_PRIVATE
    )

    val uuid = sharedPref.getString(ADID_KEY, null)
    return if (uuid == "ricepo_$INVALID_ID") {
      null
    } else {
      uuid
    }
  }

  private const val ADID_PREF = "pref_adid"

  private const val ADID_KEY = "adid"
}
