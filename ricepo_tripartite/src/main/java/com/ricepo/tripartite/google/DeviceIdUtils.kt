package com.ricepo.tripartite.google

import android.content.Context
import android.os.Build
import android.provider.Settings
import android.telephony.TelephonyManager
import android.util.Log
import java.security.MessageDigest
import java.util.UUID

//
// Created by <PERSON><PERSON> on 12/28/20.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
object DeviceIdUtils {

  private val INVALID_IMEI = "000000000000000"

  private val INVALID_ANDROID_ID = "9774d56d682e549c"

  private val INVALID_SERIAL = "unknown"

  fun getDeviceId(context: Context): String {
    val sbDeviceId = StringBuilder()

    val imei = getIMEI(context)

    val androidId = getAndroidId(context)

    val serial = getSerial()

    val uuid: String = getDeviceUUID().replace("-", "")

    if (imei != null && imei.isNotEmpty() && imei != INVALID_IMEI) {
      sbDeviceId.append(imei)
      sbDeviceId.append("|")
    }

    if (androidId != null && androidId.isNotEmpty() && androidId != INVALID_ANDROID_ID) {
      sbDeviceId.append(androidId)
      sbDeviceId.append("|")
    }

    if (serial != null && serial.isNotEmpty() && serial != INVALID_SERIAL) {
      sbDeviceId.append(serial)
      sbDeviceId.append("|")
    }

    if (uuid != null && uuid.isNotEmpty()) {
      sbDeviceId.append(uuid)
    }

    if (sbDeviceId.isNotEmpty()) {
      try {
        Log.i("thom", "combine device id: $sbDeviceId")
        val hash: ByteArray? = getHashByString(sbDeviceId.toString())
        val sha1: String? = bytesToHex(hash)
        if (sha1 != null && sha1.isNotEmpty()) {
          return sha1
        }
      } catch (ex: Exception) {
        ex.printStackTrace()
      }
    }

    return UUID.randomUUID().toString()
  }

  /**
   */
  fun getIMEI(context: Context): String? {
    try {
      val tm = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
      return tm.deviceId
    } catch (ex: Exception) {
      ex.printStackTrace()
    }
    return ""
  }

  /**
   */
  fun getAndroidId(context: Context): String? {
    try {
      return Settings.Secure.getString(
        context.getContentResolver(),
        Settings.Secure.ANDROID_ID
      )
    } catch (ex: Exception) {
      ex.printStackTrace()
    }
    return ""
  }

  /**
   */
  fun getSerial(): String? {
    try {
      return Build.SERIAL
    } catch (ex: Exception) {
      ex.printStackTrace()
    }
    return ""
  }

  /**
   */
  private fun getDeviceUUID(): String {
    return try {
      val dev =
        "12280423" + Build.BOARD.length % 10 + Build.BRAND.length % 10
      + Build.DEVICE.length % 10 + Build.HARDWARE.length % 10
      + Build.ID.length % 10 + Build.MODEL.length % 10
      + Build.PRODUCT.length % 10 + Build.SERIAL.length % 10
      UUID(
        dev.hashCode().toLong(),
        Build.SERIAL.hashCode().toLong()
      ).toString()
    } catch (ex: java.lang.Exception) {
      ex.printStackTrace()
      ""
    }
  }

  private fun getHashByString(data: String): ByteArray? {
    return try {
      val messageDigest: MessageDigest = MessageDigest.getInstance("SHA1")
      messageDigest.reset()
      messageDigest.update(data.toByteArray(charset("UTF-8")))
      messageDigest.digest()
    } catch (e: java.lang.Exception) {
      "".toByteArray()
    }
  }

  /**
   */
  private fun bytesToHex(data: ByteArray?): String? {
    val bytes = data ?: return null
    return bytes.joinToString("") { String.format("%02X", (it.toInt() and 0xFF)) }
  }
}
