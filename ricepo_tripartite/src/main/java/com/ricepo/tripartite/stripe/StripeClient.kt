package com.ricepo.tripartite.stripe

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.fragment.app.Fragment
import com.stripe.android.ApiResultCallback
import com.stripe.android.PaymentConfiguration
import com.stripe.android.PaymentIntentResult
import com.stripe.android.SetupIntentResult
import com.stripe.android.Stripe
import com.stripe.android.model.ConfirmPaymentIntentParams
import com.stripe.android.model.ConfirmSetupIntentParams
import com.stripe.android.model.PaymentMethod
import com.stripe.android.model.PaymentMethodCreateParams
import com.stripe.android.model.Source
import com.stripe.android.model.StripeIntent

//
// Created by <PERSON><PERSON> on 8/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class StripeClient {

  private var stripe: Stripe

  fun createPaymentMethod(
    params: PaymentMethodCreateParams,
    callback: (Result<PaymentMethod>) -> Unit
  ) {
    stripe.createPaymentMethod(
      paymentMethodCreateParams = params,
      callback = object : ApiResultCallback<PaymentMethod> {
        override fun onError(e: Exception) {
          callback(Result.failure(e))
        }

        override fun onSuccess(result: PaymentMethod) {
          callback(Result.success(result))
        }
      }
    )
  }

  fun handleNextAction(context: Context, signed: String) {
    if (context is Activity) {
      val activity = context
      stripe.handleNextActionForPayment(activity, signed)
    } else if (context is Fragment) {
    }
  }
  fun handleConfirmSetupIntent(context: Context, signed: String) {
    if (context is Activity) {
      val activity = context
      val params = ConfirmSetupIntentParams.createWithoutPaymentMethod(signed)
      stripe.confirmSetupIntent(context, params)
    } else if (context is Fragment) {
    }
  }

  fun onPaymentResult(
    requestCode: Int,
    data: Intent?,
    callback: (Result<PaymentIntentResult>) -> Unit
  ): Boolean {
    return stripe.onPaymentResult(
      requestCode,
      data,
      object : ApiResultCallback<PaymentIntentResult> {
        override fun onError(e: Exception) {
          callback(Result.failure(e))
        }

        override fun onSuccess(result: PaymentIntentResult) {
          callback(Result.success(result))
        }
      }
    )
  }

  fun onSetupResult(
    requestCode: Int,
    data: Intent?,
    callback: (Result<SetupIntentResult>) -> Unit
  ): Boolean {
    return stripe.onSetupResult(
      requestCode,
      data,
      object : ApiResultCallback<SetupIntentResult> {
        override fun onError(e: Exception) {
          callback(Result.failure(e))
        }

        override fun onSuccess(result: SetupIntentResult) {
          callback(Result.success(result))
        }
      }
    )
  }

  fun authenticateSource(context: Context, source: Source) {
    if (context is Activity) {
      stripe.authenticateSource(context, source)
    }
  }

  fun authenticatePayment(context: Context, clientSecret: String?) {
    if (context is Activity && clientSecret != null) {
//            stripe.authenticatePayment(context, clientSecret)
      stripe.handleNextActionForPayment(context, clientSecret)
    }
  }

  private val RETURN_URL = "https://rice"

  /**
   * confirm the payment
   */
  fun confirmPayment(activity: Activity, stripeIntent: StripeIntent) {
    val paymentMethodId = stripeIntent.paymentMethodId
    val clientSecret = stripeIntent.clientSecret
    if (paymentMethodId != null && clientSecret != null) {
      stripe.confirmPayment(
        activity,
        ConfirmPaymentIntentParams.createWithPaymentMethodId(
          paymentMethodId, clientSecret, RETURN_URL
        )
      )
    }
  }

  companion object {
    lateinit var instance: StripeClient

    fun init(
      appContext: Context,
      enableLogging: Boolean
    ) {
      instance =
        ClientHolder.holder(
          appContext,
          enableLogging
        )
    }
  }

  private constructor(
    appContext: Context,
    enableLogging: Boolean
  ) {
    stripe = StripeFactory(
      appContext, enableLogging
    ).create()
  }

  internal class StripeFactory(
    private val context: Context,
    private val enableLogging: Boolean = true
  ) {
    fun create(): Stripe {
      return Stripe(
        context,
        PaymentConfiguration.getInstance(context).publishableKey,
        null,
        enableLogging
      )
    }
  }

  private object ClientHolder {
    fun holder(
      appContext: Context,
      enableLogging: Boolean
    ): StripeClient {
      return StripeClient(
        appContext,
        enableLogging
      )
    }
  }
}
