package com.ricepo.tripartite.stripe

import android.content.Context

//
// Created by <PERSON><PERSON> on 2/8/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class StripeRangeStore(private val context: Context) {

  private val prefs by lazy {
    context.getSharedPreferences(PREF_FILE, Context.MODE_PRIVATE)
  }

  fun clearStore(versionCode: Long, isForceClean: <PERSON>olean) {
    val code = prefs.getLong(PREF_KEY_VERSION_CODE, 0)
    if (code == versionCode && !isForceClean) return
    prefs.edit().clear().apply()
    prefs.edit().putLong(PREF_KEY_VERSION_CODE, versionCode).apply()
  }

  companion object {
    private const val PREF_FILE = "InMemoryCardAccountRangeSource.Store"
    private const val PREF_KEY_VERSION_CODE = "versionCode"
  }
}
