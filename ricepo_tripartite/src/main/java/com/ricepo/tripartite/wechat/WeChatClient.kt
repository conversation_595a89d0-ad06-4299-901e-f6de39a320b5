package com.ricepo.tripartite.wechat

import android.content.Context
import com.ricepo.tripartite.TripartiteConst
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class WeChatClient(context: Context, appId: String?) {

  // the third param is whether validate signature
  val weChatApi: IWXAPI = WXAPIFactory.createWXAPI(context, appId, true)

  fun isWeiXinAppInstall(): Boolean {
    return weChatApi.isWXAppInstalled
  }

  companion object {
    fun isAppInstall(context: Context): <PERSON><PERSON>an {
      return WXAPIFactory.createWXAPI(
        context,
        TripartiteConst.WECHAT_APP_ID
      ).isWXAppInstalled
    }
  }
}
