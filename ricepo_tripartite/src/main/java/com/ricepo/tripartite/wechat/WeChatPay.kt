package com.ricepo.tripartite.wechat

import android.content.Context
import com.ricepo.style.ResourcesUtil
import com.tencent.mm.opensdk.modelpay.PayReq
import com.ricepo.tripartite.R

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class WeChatPay(context: Context, appId: String?) : WeChatClient(context, appId) {

  fun isRegister(appSignature: String?): String? {
    val success = weChatApi.registerApp(appSignature)
    if (success) {
      return WeChatPay.PENDING
    } else {
      return (ResourcesUtil.getString(com.ricepo.style.R.string.wechat_payment_failed))
    }
  }

  fun launchPay(payReq: PayReq) {
    weChatApi.sendReq(payReq)
  }

  companion object {
    const val RESULT_SUCCESS = 0
    const val RESULT_ERROR = -1
    const val RESULT_CANCELED = -2

    const val PENDING = "1"
  }
}
