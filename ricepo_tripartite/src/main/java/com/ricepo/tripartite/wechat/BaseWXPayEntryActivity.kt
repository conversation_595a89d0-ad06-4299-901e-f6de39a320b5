package com.ricepo.tripartite.wechat

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.ricepo.tripartite.TripartiteConst
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelpay.PayResp
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

abstract class BaseWXPayEntryActivity : AppCompatActivity(), IWXAPIEventHandler {

  private val weChatApi: IWXAPI by lazy {
    WXAPIFactory.createWXAPI(this, TripartiteConst.WECHAT_APP_ID, true)
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    weChatApi.handleIntent(intent, this)
  }

  override fun onNewIntent(intent: Intent?) {
    super.onNewIntent(intent)
    weChatApi.handleIntent(intent, this)
  }

  override fun onResp(resp: BaseResp?) {
    if (resp is PayResp) {
      val message = when (resp.errCode) {
        WeChatPay.RESULT_SUCCESS -> "Payment successful!"
        WeChatPay.RESULT_ERROR -> "Error during payment"
        WeChatPay.RESULT_CANCELED -> "User canceled"
        else -> "Unknown result"
      }
      Log.i("WXPay", message)
    }
  }

  override fun onReq(req: BaseReq?) {
    Log.i("WXPay", "request type ${req?.type}")
  }
}
