package com.ricepo.tripartite.wechat

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.ricepo.tripartite.TripartiteConst
import com.tencent.mm.opensdk.openapi.WXAPIFactory

//
// Created by <PERSON><PERSON> on 11/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class WeChatRegister : BroadcastReceiver() {

  override fun onReceive(context: Context, intent: Intent) {
    val api = WXAPIFactory.createWXAPI(context, TripartiteConst.WECHAT_APP_ID, true)
    api.registerApp(TripartiteConst.WECHAT_APP_SIGNATURE)
  }
}
