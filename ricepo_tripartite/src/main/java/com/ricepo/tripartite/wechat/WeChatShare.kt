package com.ricepo.tripartite.wechat

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Build
import android.view.Gravity
import android.widget.Toast
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.R
import com.ricepo.tripartite.TripartiteConst
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX
import com.tencent.mm.opensdk.modelmsg.WXImageObject
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage
import com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import java.io.ByteArrayOutputStream

//
// Created by <PERSON><PERSON> on 18/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object WeChatShare {

  const val MINI_PROGRAM_ID = ""

  private var wxApiClient: IWXAPI? = null

  /**
   * shared the content to wechat
   */
  fun shareStringToWx(
    context: Context,
    webUrl: String?,
    title: String?,
    content: String?,
    thumbImage: Bitmap? = null,
    target: WeChatTarget = WeChatTarget.SESSION
  ) {
    if (!isWeiXinAppInstall(context)) {
      showToast(context, com.ricepo.style.R.string.error_wechat_notInstalled)
      return
    }
    registerToWeiXin(context)
    val webpageObject = WXWebpageObject()
    webpageObject.webpageUrl = webUrl
    val mediaMessage = WXMediaMessage(webpageObject)
    mediaMessage.title = title
    mediaMessage.description = content
    if (thumbImage != null) {
      mediaMessage.setThumbImage(thumbImage)
    }
    val req = SendMessageToWX.Req()
    req.transaction = ""
    when (target) {
      WeChatTarget.SESSION -> {
        req.scene = SendMessageToWX.Req.WXSceneSession
      }
      WeChatTarget.TIMELINE -> {
        req.scene = SendMessageToWX.Req.WXSceneTimeline
      }
      WeChatTarget.FAVORITE -> {
        req.scene = SendMessageToWX.Req.WXSceneFavorite
      }
      WeChatTarget.SPECIFIED_CONTACT -> {
        req.scene = SendMessageToWX.Req.WXSceneSpecifiedContact
      }
    }
    req.message = mediaMessage
    wxApiClient?.sendReq(req)
  }

  /**
   * share the content to micro program
   * [path] the url of micro program
   * bitmap should not be null or exceed 128kb
   */
  fun shareMicroProgram(
    context: Context,
    title: String? = null,
    userName: String?,
    path: String?,
    bitmap: Bitmap? = null
  ) {
    if (!isWeiXinAppInstall(context)) {
      showToast(context, com.ricepo.style.R.string.error_wechat_notInstalled)
      return
    }
    registerToWeiXin(context)
    val miniProgram = WXMiniProgramObject()
    // 0: release 1: test  2: pre
    miniProgram.miniprogramType = 0
    // custom url
    miniProgram.webpageUrl = "http://www.rice.rocks"
    // mini program id
    miniProgram.userName = userName ?: MINI_PROGRAM_ID
    // default mini home url
    miniProgram.path = path
    val mediaMessage = WXMediaMessage(miniProgram)
    // custom title
    mediaMessage.title = title ?: ""
    // custom description
    mediaMessage.description = ""
    if (bitmap != null) {
      mediaMessage.thumbData = bmpToByteArray(bitmap)
    }
    val req = SendMessageToWX.Req()
    req.transaction = ""
    req.scene = SendMessageToWX.Req.WXSceneSession
    req.message = mediaMessage
    wxApiClient?.sendReq(req)
    bitmap?.recycle()
  }

  /**
   * enter micro program
   */
  fun enterMicroProgram(
    context: Context,
    userName: String?,
    path: String?,
  ) {
    if (!isWeiXinAppInstall(context)) {
      showToast(context, com.ricepo.style.R.string.error_wechat_notInstalled)
      return
    }
    registerToWeiXin(context)
    val miniProgram = WXLaunchMiniProgram.Req()
    // 0: release 1: test  2: pre
    miniProgram.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE
    // custom url
    // mini program id
    miniProgram.userName = userName ?: MINI_PROGRAM_ID
    // default mini home url
    miniProgram.path = path ?: ""
    wxApiClient?.sendReq(miniProgram)
  }

  /**
   * shared the picture to wechat
   */
  fun sharePictureToWx(context: Context, drawableId: Int) {
    if (!isWeiXinAppInstall(context)) {
      showToast(context, com.ricepo.style.R.string.error_wechat_notInstalled)
      return
    }
    registerToWeiXin(context)
    val bmp = BitmapFactory.decodeResource(context.resources, drawableId)

    // init the image and media object
    val imgObj = WXImageObject(bmp)
    val msg = WXMediaMessage()
    msg.mediaObject = imgObj

    // sht the scale
    val thumbBmp = Bitmap.createScaledBitmap(bmp, 100, 100, true)
    bmp.recycle()
    msg.thumbData = bmpToByteArray(thumbBmp)

    val req = SendMessageToWX.Req()
    req.transaction = ""
    req.message = msg
    // WXSceneTimeline: friend circle
    // WXSceneSession: friend
    // WXSceneSession: collection
    req.scene = SendMessageToWX.Req.WXSceneSession
    wxApiClient?.sendReq(req)
  }

  /**
   * registered wechat
   */
  private fun registerToWeiXin(context: Context) {
    if (wxApiClient == null) {
      // false : not check signature
      wxApiClient = WXAPIFactory.createWXAPI(context, TripartiteConst.WECHAT_APP_ID, false)
      wxApiClient?.registerApp(TripartiteConst.WECHAT_APP_ID)
    }
  }

  /**
   * check the wechat whether installed
   */
  private fun isWeiXinAppInstall(context: Context): Boolean {
    if (wxApiClient == null) {
      wxApiClient = WXAPIFactory.createWXAPI(context, TripartiteConst.WECHAT_APP_ID)
    }
    return wxApiClient?.isWXAppInstalled ?: false
  }

  /**
   * bitmap to byte array
   */
  private fun bmpToByteArray(bmp: Bitmap): ByteArray? {
    val output = ByteArrayOutputStream()
    bmp.compress(Bitmap.CompressFormat.PNG, 100, output)
    bmp.recycle()
    val result = output.toByteArray()
    try {
      output.close()
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return result
  }

  private var toast: Toast? = null

  /**
   * show toast message in center
   */
  private fun showToast(context: Context, messageId: Int?) {
    val id = messageId ?: return
    val message = ResourcesUtil.getString(id)

    if (Build.VERSION.SDK_INT == Build.VERSION_CODES.P || toast == null) {
      toast = Toast.makeText(context, message, Toast.LENGTH_SHORT)
    } else {
      toast?.setText(message)
    }
    toast?.setGravity(Gravity.CENTER, 0, 0)
    toast?.show()
  }
}

enum class WeChatTarget {
  SESSION,
  TIMELINE,
  FAVORITE,
  SPECIFIED_CONTACT
}
