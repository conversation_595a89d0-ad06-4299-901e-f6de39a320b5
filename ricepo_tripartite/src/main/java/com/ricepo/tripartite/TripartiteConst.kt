package com.ricepo.tripartite

import com.ricepo.tripartite.BuildConfig

//
// Created by <PERSON><PERSON> on 8/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * See [Configure the app](https://github.com/stripe/stripe-android/tree/master/example#configure-the-app)
 * for instructions on how to configure the example app before running it.
 */
object TripartiteConst {

  val publishableKey: String
    get() {
      return if (BuildConfig.DEBUG) DEBUGTEST_KEY else PUBLISHABLE_KEY
    }

  const val WECHAT_APP_ID: String = "wxdfca4b999ccabc13"

  const val WECHAT_APP_SIGNATURE = "71f240d38579ddf245a01b894f3535c5"

  private const val DEBUGTEST_KEY =
    "pk_test_51Oxr1BHfnuUM2c7i4oWqz2nMQ51tpeZJNcQQuW2zWApOOEj783i6hDgbPrOMvRUaDVlRH0tNDqhnZnQcFF319THs00NPdaMpPD"

  private const val PUBLISHABLE_KEY =
    "pk_live_51Oxr1BHfnuUM2c7iCp639MMByWN8rXEszQPABlKS9Nfu11RqpDw5jLgJqPUPPO0ihBVFdc5TOGjSW3N91gkUma3500wG30JvPj"
}
