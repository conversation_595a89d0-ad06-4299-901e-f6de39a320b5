package com.ricepo.tripartite.alipay

import android.app.Activity
import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.alipay.sdk.app.PayTask
import com.ricepo.style.ResourcesUtil
import com.ricepo.tripartite.R

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object AlipayClient {

  /**
   * return the payment error live data
   * if error is null that success
   */
  fun pay(context: Context, orderInfo: String): LiveData<String?> {
    val liveData = MutableLiveData<String?>()
    if (context is Activity) {
      val runnable = Runnable {
        val alipay = PayTask(context)
        // orderInfo the merchant order info (connect with &)
        // isShowPayLoading intent app loading
        val result = alipay.payV2(orderInfo, false)

        // handle result
        if (result != null) {
          when (result["resultStatus"]) {
            AlipayResultStatus.RESULT_SUCCESS -> {
              // success or cancel
              liveData.postValue(null)
            }
            AlipayResultStatus.RESULT_CANCELED -> {
              // cancel with the WeChatPay.RESULT_SUCCESS
              liveData.postValue(CANCELED)
            }
            else -> {
              liveData.postValue(ResourcesUtil.getString(com.ricepo.style.R.string.alipay_payment_failed))
            }
          }
        }
      }
      val payThread = Thread(runnable)
      payThread.start()
    }

    return liveData
  }

  const val CANCELED = "11"
}
