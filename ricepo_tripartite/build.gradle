apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'com.ricepo.tripartite'
    compileSdk androidcompileSdk

    defaultConfig {
        minSdkVersion androidMinSdkVersion
        targetSdkVersion androidTargetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildFeatures {
        buildConfig true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility javaVersion
        targetCompatibility javaVersion
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

}

dependencies {

    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    // use ext aar need migrate to app/libs
//    implementation(name: 'alipaySdk-15.7.6-20200521195109', ext: 'aar')
    api 'com.alipay.sdk:alipaysdk-android:+@aar'
//    implementation project(':ricepo_base')
    implementation project(':ricepo_style')

    implementation tripartiteDependencies.wechat
    implementation tripartiteDependencies.stripe

    implementation tripartiteDependencies.adsIdentifier
    implementation tripartiteDependencies.guava

    implementation tripartiteDependencies.constraintLayout

    implementation tripartiteDependencies.firebaseLinksKtx

    testImplementation tripartiteDependencies.junit

    androidTestImplementation tripartiteDependencies.extJunit

}
