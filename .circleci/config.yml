version: 2

references:

  workspace: &workspace
    ~/src

  android_config: &android_config
    working_directory: *workspace
    docker:
      - image: circleci/android:api-28
    environment:
      TERM: dumb
      _JAVA_OPTIONS: "-Xmx1280m -XX:+UnlockExperimentalVMOptions"
      GRADLE_OPTS: '-Dorg.gradle.jvmargs="-Xmx1536m -XX:+HeapDumpOnOutOfMemoryError
      -Dorg.gradle.caching=true -Dorg.gradle.configureondemand=true
      -Dkotlin.compiler.execution.strategy=in-process -Dkotlin.incremental=false"'

  gradle_key: &gradle_key
    jars-{{ checksum "build.gradle" }}-{{ checksum  "rohan_app/build.gradle" }}-{{ checksum "gradle/wrapper/gradle-wrapper.properties" }}

  gems_key: &gems_key
    gems-{{ checksum "Gemfile.lock" }}

  restore_gradle_cache: &restore_gradle_cache
    restore_cache:
      key: *gradle_key

  restore_gems_cache: &restore_gems_cache
    restore_cache:
      key: *gems_key

  save_gradle_cache: &save_gradle_cache
    save_cache:
      key: *gradle_key
      paths:
        - ~/.gradle
        - ~/.m2

  save_gems_cache: &save_gems_cache
    save_cache:
      key: *gems_key
      paths:
        - vendor/bundle

  ruby_dependencies: &ruby_dependencies
    run:
      name: Download Ruby Dependencies
      command: |
        gem install bundler
        bundle check || bundle install --path vendor/bundle

  android_dependencies: &android_dependencies
    run:
      name: Download Android Dependencies
      command: ./gradlew androidDependencies


jobs:

  # Run Unit Test
  test_unit:
    <<: *android_config
    steps:
      - checkout
      - *restore_gradle_cache
      - *restore_gems_cache
      - *ruby_dependencies
      - *android_dependencies
      - *save_gradle_cache
      - *save_gems_cache

      - run:
          name: Run Unit Tests
          command: bundle exec fastlane assemble_build build_flavor:$BUILD_FLAVOR build_type:$BUILD_TYPE

      - store_artifacts:
          path: rohan_app/build/reports
          destination: reports

      - store_test_results:
          path: rohan_app/build/test-results
          destination: /test-results

  # Run UI Test
  test_instrumentation:
    <<: *android_config
    steps:
      - checkout
      - *restore_gradle_cache
      - *restore_gems_cache
      - *ruby_dependencies
      - *android_dependencies
      - *save_gradle_cache
      - *save_gems_cache

     # - run:
     #     name: Build output apk
     #     no_output_timeout: 60m
     #     command: bundle exec fastlane assemble build_flavor:"Prod" build_type:"Release"

      - run:
          name: Run instrumentation tests in Firebase Testlab and Send to Slack
          no_output_timeout: 60m
          command: bundle exec fastlane instrumentation_tests_testlab build_flavor:"Prod" build_type:"Release"

     # - run:
     #     name: Send Debug and Release Apk
     #     no_output_timeout: 30m
     #     command:
     #         bundle exec fastlane send_apk_to_slack build_flavor:"Prod" build_type:"Release"

      - store_artifacts:
          path: rohan_app/build/outputs/apk
          destination: /apk/

      - store_artifacts:
          path: firebase/
          destination: /firebase/


  test:
    docker:
      - image: circleci/android:api-28  # gcloud is baked into this image
    steps:
      - run:
          name: Build debug APK and release APK
          command: |
            ./gradlew :app:assembleDebug
            ./gradlew :app:assembleDebugAndroidTest
      - run:
          name: Store Google Service Account
          command: echo $GCLOUD_SERVICE_KEY > ${HOME}/gcloud-service-key.json
      - run:
          name: Authorize gcloud and set config defaults
          command: |
            sudo gcloud auth activate-service-account --key-file=${HOME}/gcloud-service-key.json
            sudo gcloud --quiet config set project ${GOOGLE_PROJECT_ID}
      - run:
          name: Test with Firebase Test Lab
          command: >
            sudo gcloud firebase test android run \
              --app <local_server_path>/<app_apk>.apk \
              --test <local_server_path>/<app_test_apk>.apk \
              --results-bucket cloud-test-${GOOGLE_PROJECT_ID}
      - run:
          name: Install gsutil dependency and copy test results data
          command: |
            sudo pip install -U crcmod
            sudo gsutil -m cp -r -U `sudo gsutil ls gs://[BUCKET_NAME]/[OBJECT_NAME] | tail -1` ${CIRCLE_ARTIFACTS}/ | true

workflows:
  version: 2
  workflow:
    jobs:

      - test_unit:
          filters:
            branches:
              only:
                - develop

      - test_instrumentation:
          filters:
            branches:
              only:
                - master
#          requires:
#            - test_unit
