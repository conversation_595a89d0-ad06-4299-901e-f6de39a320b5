// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    ext {
        androidGradlePluginVersion = '8.1.1'
        kotlinVersion = '1.8.10'
        googleServicesGradlePluginVersion = '4.4.2'
        firebaseCrashlyticsVersion = '2.7.1'
        firebasePerfVersion = '1.4.1'
//        sentryVersion = '1.7.31'
        aspectjxVersion = '2.0.8'
        spotlessGradle   = '5.12.3'
        hiltPluginVersion = "2.44"
    }

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }

    dependencies {
        classpath "com.android.tools.build:gradle:$androidGradlePluginVersion"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        // Check that you have the Google Services Gradle plugin v4.3.2 or later
        // (if not, add it).
        classpath "com.google.gms:google-services:$googleServicesGradlePluginVersion"
        // Add the Crashlytics Gradle plugin
        classpath "com.google.firebase:firebase-crashlytics-gradle:$firebaseCrashlyticsVersion"
        // Add the dependency for the Performance Monitoring plugin
        classpath "com.google.firebase:perf-plugin:$firebasePerfVersion"
        // Sentry Config plugin
//        classpath "io.sentry:sentry-android-gradle-plugin:$sentryVersion"

//        classpath "com.hujiang.aspectjx:gradle-android-plugin-aspectjx:$aspectjxVersion


        classpath "com.diffplug.spotless:spotless-plugin-gradle:$spotlessGradle"

        classpath "com.google.dagger:hilt-android-gradle-plugin:$hiltPluginVersion"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

plugins {
    id 'org.jetbrains.dokka' version '0.10.1'
    id 'org.sonarqube' version '2.7'
    id 'org.jetbrains.kotlin.android' version "$kotlinVersion" apply false
}

apply from: 'dependencies.gradle'

allprojects {
    repositories {
        google()
        // for ShadowLayout
        maven { url 'https://jitpack.io' }
        // for onesignal
        mavenCentral()
        // alipay sdk aar
//        flatDir { dirs 'libs' }
        // banner
        maven { url "https://s01.oss.sonatype.org/content/groups/public" }
        // mapbox maven
        maven {
            url 'https://api.mapbox.com/downloads/v2/releases/maven'
            authentication {
                basic(BasicAuthentication)
            }
            credentials {
                username = "mapbox"
                password = "****************************************************************************************"
            }
        }
        maven {
            url  "https://cardinalcommerceprod.jfrog.io/artifactory/android"
            credentials {
                // Be sure to add these non-sensitive credentials in order to retrieve dependencies from
                // the private repository.
                username 'paypal_sgerritz'
                password '*************************************************************************'
            }
        }
        apply from: "$rootDir/spotless.gradle"
    }
}


//task clean(type: Delete) {
//    delete rootProject.buildDir
//}


dokka {
    outputFormat = 'html'
    outputDirectory = "$buildDir/dokka"

    // In case of a Gradle multiproject build, you can include subprojects here to get merged documentation
    // Note however, that you have to have the Kotlin plugin available in the root project and in the subprojects
    subProjects = ["ricepo_network"]

    // Used for disabling auto extraction of sources and platforms in both multi-platform and single-platform modes
    // When set to true, subProject and kotlinTasks are also omitted
    disableAutoconfiguration = false

    configuration {
        includeNonPublic = false
    }
}

// sonar key ****************************************

// ./gradlew sonarqube -Dsonar.projectKey=rohan-android -Dsonar.host.url=http://***********:9000 -Dsonar.login=****************************************