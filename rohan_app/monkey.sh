# -v	命令行上的每个 -v 都会增加详细程度级别。 级别 0（默认值）只提供启动通知、测试完成和最终结果。 级别 1 提供有关测试在运行时（例如发送到您的 Activity 的各个事件）的更多详细信息。 级别 2 提供更详细的设置信息，例如已选择或未选择用于测试的 Activity。

# -s <seed>	伪随机数生成器的种子值。如果您使用相同的种子值重新运行 Monkey，它将会生成相同的事件序列。
# --throttle <milliseconds>	在事件之间插入固定的延迟时间。您可以使用此选项减慢 Monkey 速度。 如果未指定，则不延迟，系统会尽快地生成事件。
# --pct-touch <percent>	调整轻触事件所占百分比。 （轻触事件是指屏幕上的单个位置上的按下/释放事件。）
# --pct-motion <percent>	调整动作事件所占百分比。 （动作事件包括屏幕上某个位置的按下事件，一系列伪随机动作和一个释放事件。）
# --pct-trackball <percent>	调整轨迹球事件所占百分比。 （轨迹球事件包括一个或多个随机动作，有时后跟点击。）
# --pct-nav <percent>	调整“基本”导航事件所占百分比。 （导航事件包括向上/向下/向左/向右，作为方向输入设备的输入。）
# --pct-majornav <percent>	调整“主要”导航事件所占百分比。 （这些导航事件通常会导致界面中的操作，例如 5 方向键的中间按钮、返回键或菜单键。）
# --pct-syskeys <percent>	调整“系统”按键事件所占百分比。 （这些按键通常预留供系统使用，例如“主屏幕”、“返回”、“发起通话”、“结束通话”或“音量控件”。）
# --pct-appswitch <percent>	调整 Activity 启动次数所占百分比。Monkey 会以随机间隔发起 startActivity() 调用，以最大限度地覆盖软件包中的所有 Activity。
# --pct-anyevent <percent>	调整其他类型事件所占百分比。这包括所有其他类型的事件，例如按键、设备上的其他不太常用的按钮等等。

# -p <allowed-package-name>	如果您通过这种方式指定一个或多个软件包，Monkey 将仅允许系统访问这些软件包内的 Activity。如果应用需要访问其他软件包中的 Activity（例如选择联系人），您还需要指定这些软件包。 如果您未指定任何软件包，Monkey 将允许系统启动所有软件包中的 Activity。要指定多个软件包，请多次使用 -p 选项 - 每个软件包对应一个 -p 选项。
# -c <main-category>	如果您通过这种方式指定一个或多个类别，Monkey 将仅允许系统访问其中一个指定类别中所列的 Activity。 如果您没有指定任何类别，Monkey 会选择 Intent.CATEGORY_LAUNCHER 或 Intent.CATEGORY_MONKEY 类别所列的 Activity。要指定多个类别，请多次使用 -c 选项 - 每个类别对应一个 -c 选项。
# 调试	--dbg-no-events	指定后，Monkey 将初始启动到测试 Activity，但不会生成任何其他事件。 为了获得最佳结果，请结合使用 -v、一个或多个软件包约束条件以及非零限制，以使 Monkey 运行 30 秒或更长时间。这提供了一个环境，您可以在其中监控应用调用的软件包转换操作。
# --hprof	如果设置此选项，则会在 Monkey 事件序列之前和之后立即生成分析报告。这将在 data/misc 下生成大型（约为 5Mb）文件，因此请谨慎使用。要了解如何分析性能分析报告，请参阅分析应用性能。
# --ignore-crashes	通常，当应用崩溃或遇到任何类型的未处理异常时，Monkey 将会停止。如果您指定此选项，Monkey 会继续向系统发送事件，直到计数完成为止。
# --ignore-timeouts	通常情况下，如果应用遇到任何类型的超时错误（例如“应用无响应”对话框），Monkey 将会停止。如果您指定此选项，Monkey 会继续向系统发送事件，直到计数完成为止。
# --ignore-security-exceptions	通常情况下，如果应用遇到任何类型的权限错误（例如，如果它尝试启动需要特定权限的 Activity），Monkey 将会停止。如果您指定此选项，Monkey 会继续向系统发送事件，直到计数完成为止。
# --kill-process-after-error	通常情况下，当 Monkey 因出错而停止运行时，出现故障的应用将保持运行状态。设置此选项后，它将会指示系统停止发生错误的进程。 注意，在正常（成功）完成情况下，已启动的进程不会停止，并且设备仅会处于最终事件之后的最后状态。
# --monitor-native-crashes	监视并报告 Android 系统原生代码中发生的崩溃。如果设置了 --kill-process-after-error，系统将会停止。
# --wait-dbg	阻止 Monkey 执行，直到为其连接了调试程序。


adb shell monkey -p com.ricepo.app -v 500 --throttle 100 --pct-touch 0 --pct-motion 10 --pct-trackball 5 --pct-nav 70 \
    --pct-majornav 5 --pct-syskeys 0 --pct-appswitch 10 --pct-anyevent 0 -c android.intent.category.MONKEY