package com.ricepo.app

import app.cash.turbine.test
import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.whenever
import com.ricepo.app.model.PopUp
import com.ricepo.app.restapi.RiceApi
import com.ricepo.app.restaurant.usecase.PopupUseCase
import com.ricepo.base.data.pref.PrefDataSource
import com.skydoves.sandwich.ApiResponse
import junit.framework.TestCase.assertEquals
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import retrofit2.Response
import kotlin.time.ExperimentalTime

class PopUpUseCaseTest {

  private val riceApi: RiceApi = mock()
  private val prefDataSource: PrefDataSource = mock()
  lateinit var popupUseCase: PopupUseCase

  @Before
  fun setUp() {
    popupUseCase = PopupUseCase(prefDataSource, riceApi)
  }

  @OptIn(ExperimentalTime::class, ExperimentalCoroutinesApi::class)
  @Test
  fun sample() = runTest {
    flowOf("one", "two").test {
      assertEquals("one", awaitItem())
      assertEquals("two", awaitItem())
      awaitComplete()
    }
  }

  @OptIn(
    ExperimentalCoroutinesApi::class,
    ExperimentalTime::class
  )
  @Test
  fun testPopupLogic() = runTest {

    whenever(riceApi.fetchPopup("")).thenReturn(
      ApiResponse.of {
        Response.success(
          listOf(
            PopUp.mock().copy(_id = "1"),
            PopUp.mock().copy(_id = "2"),
            PopUp.mock().copy(_id = "3"),
            PopUp.mock().copy(_id = "4"),
            PopUp.mock().copy(_id = "5"),
          )
        )
      }
    )

    whenever(prefDataSource.getPopups()).thenReturn(setOf("1", "2", "3"))

//    popupUseCase.fetchPopup("", false)
//    popupUseCase.popupData.test {
//      val expectItem = awaitItem()
//      assert(expectItem.size == 2)
//      assert(expectItem.first()._id == "4")
//      assert(expectItem[1]._id == "5")
//      cancelAndIgnoreRemainingEvents()
//    }
  }
}
