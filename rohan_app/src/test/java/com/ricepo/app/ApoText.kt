package com.ricepo.app

import okhttp3.OkHttpClient
import okhttp3.Request

fun main() {

  val order = "6SF47144BG9069454"

  val client = OkHttpClient.Builder().apply {}.build()

  val request = Request.Builder().apply {
    url("https://api-m.sandbox.paypal.com/v2/checkout/orders/$order")
    addHeader(
      "Authorization",
      "Bearer A21AAKlZgwyrRRxRJ-jdFi5II7PXqjox_MogT3ihfKnGC4b1_9JeR_KCgEZusgVp0Q2Ubup20xRhHMXKg5jLpKo2fJgnTrcYA"
    )
  }.build()
  val execute = client.newCall(request = request).execute()
  println(execute.body?.string())
}
// 22234609B8109454R
// 5DR438143B4159503
