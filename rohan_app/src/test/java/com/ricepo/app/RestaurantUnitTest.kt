package com.ricepo.app

import com.nhaarman.mockitokotlin2.mock
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import com.ricepo.app.restapi.RestaurantRestApi
import com.ricepo.app.restaurant.RestaurantMapper
import com.ricepo.app.restaurant.RestaurantRemoteImpl
import com.ricepo.base.model.Restaurant
import io.reactivex.rxjava3.core.Single
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class RestaurantUnitTest {

  private val mapper = mock<RestaurantMapper>()
  private val restApi = mock<RestaurantRestApi>()
  private val remote = RestaurantRemoteImpl(restApi)

  @Test
  fun getNearByRestaurants() {
    val loc = "-149.8071656%2C61.18950770000001"
//        stubClientRestApi(Single.just(RestaurantDataFactory.makeRestaurantResponse()), loc, null)

    remote.getRestaurants(loc)?.test()

    verify(restApi).getRestaurants(loc, null)
  }

  private fun stubClientRestApi(singleObserver: Single<List<Restaurant>>?, param: String?, search: String?) {
    whenever(restApi.getRestaurants(param, search)).thenReturn(singleObserver)
  }
}
