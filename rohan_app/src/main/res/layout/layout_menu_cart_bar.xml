<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="bottom"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:animateLayoutChanges="true"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_menu_cart"
        android:layout_gravity="bottom"
        android:visibility="gone"
        tools:visibility="visible"
        android:focusable="true"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_delivery"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/green"
            android:textSize="@dimen/sw_15sp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/rcv_menu_cart"
            app:layout_constraintRight_toRightOf="parent"
            android:paddingBottom="8dp"
            android:layout_marginRight="8dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcv_menu_cart"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ml"
            android:background="@color/banner_background"
            android:layout_width="0dp"
            android:layout_height="@dimen/sw_bottom_bar_height" />

        <FrameLayout
            android:id="@+id/ml"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            app:layout_constraintStart_toEndOf="@+id/rcv_menu_cart"
            app:layout_goneMarginRight="@dimen/dip_15"
            app:layout_constraintTop_toTopOf="@id/rcv_menu_cart"
            app:layout_constraintBottom_toBottomOf="@id/rcv_menu_cart"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/banner_background"
            >
            <com.google.android.material.button.MaterialButton
                android:id="@+id/tv_menu_cart"
                android:text="@string/menu_car_checkout"
                android:minHeight="@dimen/sw_43dp"
                android:layout_marginLeft="15dp"
                android:layout_marginBottom="@dimen/sw_7dp"
                android:gravity="center"
                android:layout_marginEnd="20dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/mr"
                app:cornerRadius="@dimen/button_radius"
                android:textColor="@color/w"
                android:layout_gravity="center"
                android:clickable="false"
                />

        </FrameLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- focusable and clickable otherwise clickable backend view -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_menu_recommend"
        tools:visibility="visible"
        android:visibility="gone"
        android:focusable="true"
        android:clickable="true"
        android:background="@color/banner_background"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_120">

        <ImageView
            android:id="@+id/iv_recommend_close"
            app:layout_constraintTop_toTopOf="@+id/tv_menu_recommend"
            app:layout_constraintBottom_toBottomOf="@+id/tv_menu_recommend"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginLeft="@dimen/card_side_margin"
            android:layout_width="19dp"
            android:layout_height="19dp" />

        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/iv_recommend_cross"
            android:src="@drawable/ic_recommend_cross"
            android:layout_width="11dp"
            android:layout_height="11dp"
            app:layout_constraintTop_toTopOf="@+id/iv_recommend_close"
            app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_close"
            app:layout_constraintLeft_toLeftOf="@+id/iv_recommend_close"
            app:layout_constraintRight_toRightOf="@+id/iv_recommend_close" />

        <TextView
            android:id="@+id/tv_menu_recommend"
            app:layout_constraintLeft_toRightOf="@+id/iv_recommend_close"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/rcv_menu_recommend"
            android:text="@string/menu_recommend_like"
            android:layout_marginTop="@dimen/dip_3"
            android:layout_marginLeft="@dimen/dip_3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcv_menu_recommend"
            app:layout_constraintTop_toBottomOf="@+id/tv_menu_recommend"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="@dimen/dip_10"
            android:background="@color/banner_background"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>