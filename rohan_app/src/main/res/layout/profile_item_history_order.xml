<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:id="@+id/order_history_root"
    android:layout_height="wrap_content">

    <TableLayout
        android:id="@+id/ll_order_restaurant"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_order_price"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/barrier_order_price"
        android:layout_marginTop="15dp"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal"
        android:shrinkColumns="0"
        android:stretchColumns="1"
        android:layout_width="0dp"
        android:layout_height="wrap_content" >

        <TableRow>
            <TextView
                android:id="@+id/tv_order_restaurant_name"
                style="@style/ItemStyle"
                android:ellipsize="end"
                android:maxLines="1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_order_group_order"
                android:text="@string/label_group_order"
                style="@style/SubItemStyle"
                android:layout_marginLeft="@dimen/dip_3"
                android:layout_marginRight="@dimen/dip_3"
                android:maxLines="1"
                android:visibility="gone"
                android:textSize="@dimen/font_size_h9"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </TableRow>

    </TableLayout>

    <TextView
        android:id="@+id/tv_order_detail"
        app:layout_constraintTop_toBottomOf="@+id/btn_order_rating"
        app:layout_constraintEnd_toEndOf="@id/btn_order_rating"
        app:layout_constraintStart_toStartOf="@id/btn_order_rating"
        android:layout_marginTop="6dp"
        style="@style/SubTitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_order_price"
        app:barrierDirection="start"
        app:constraint_referenced_ids="tv_order_detail, btn_order_rating"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_order_price"
        app:layout_constraintTop_toBottomOf="@+id/ll_order_restaurant"
        app:layout_constraintLeft_toLeftOf="@+id/ll_order_restaurant"
        app:layout_constraintBottom_toTopOf="@+id/tv_order_number"
        style="@style/SubTitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_order_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@id/tv_order_price"
        app:layout_constraintTop_toBottomOf="@id/tv_order_price"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="15dp"
        style="@style/SubTitleStyle"
        />
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_order_rating"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="10dp"
        android:layout_marginRight="@dimen/card_side_margin"
        android:text="@string/rate_driver"
        android:textSize="@dimen/font_size_h6"
        android:minHeight="37dp"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        app:cornerRadius="12dp"
        app:backgroundTint="@color/mr"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>