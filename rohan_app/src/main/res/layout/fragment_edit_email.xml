<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_title_icon"
            android:tint="@color/fun_n6"
            app:srcCompat="@drawable/ic_back" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/TitleStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="Email" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:layout_marginHorizontal="36dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_email_display"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:enabled="false"
            android:gravity="center"
            android:textColor="@color/fun_n6"
            android:visibility="gone"
            tools:text="<EMAIL>" />

        <LinearLayout
            android:id="@+id/edit_email_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_email"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/email"
                android:paddingLeft="0dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_code"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:hint="@string/email_verify_codePlaceholder"
                        android:paddingLeft="0dp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                </LinearLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="59dp"
                    android:text="@string/email_verify_receive"
                    android:textColor="@color/mr" />
            </LinearLayout>

        </LinearLayout>


    </FrameLayout>


    <com.google.android.material.button.MaterialButton
        android:id="@+id/submit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="35dp"
        android:backgroundTint="@color/mr"
        android:minHeight="40dp"
        android:textColor="@color/white"
        app:cornerRadius="@dimen/button_radius" />

    <View
        android:layout_width="0dp"
        android:layout_height="match_parent" />

</LinearLayout>