<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_restaurant_card"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_rating_one"
        android:text="@string/slow_inefficient"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingLeft="@dimen/card_side_margin"
        android:paddingRight="@dimen/card_side_margin"
        android:paddingTop="@dimen/dip_20"
        android:paddingBottom="@dimen/dip_20"
        style="@style/ItemStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />
    
    <ImageView
        android:id="@+id/iv_rating_one"
        android:layout_width="@dimen/rating_check_size"
        android:layout_height="@dimen/rating_check_size"
        android:layout_marginRight="@dimen/card_side_margin"
        android:src="@drawable/ic_path_checkmark"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_rating_one"
        app:layout_constraintBottom_toBottomOf="@+id/tv_rating_one" />

    <TextView
        android:id="@+id/tv_rating_one_divider"
        android:layout_width="0dp"
        style="@style/RestaurantDividerHorizontalStyle"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_rating_one" />

    <TextView
        android:id="@+id/tv_rating_two"
        android:text="@string/bad_communication"
        app:layout_constraintTop_toBottomOf="@+id/tv_rating_one"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingLeft="@dimen/card_side_margin"
        android:paddingRight="@dimen/card_side_margin"
        android:paddingTop="@dimen/dip_20"
        android:paddingBottom="@dimen/dip_20"
        style="@style/ItemStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_rating_two"
        android:layout_width="@dimen/rating_check_size"
        android:layout_height="@dimen/rating_check_size"
        android:layout_marginRight="@dimen/card_side_margin"
        android:src="@drawable/ic_path_checkmark"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_rating_two"
        app:layout_constraintBottom_toBottomOf="@+id/tv_rating_two"/>

    <TextView
        style="@style/RestaurantDividerHorizontalStyle"
        android:layout_width="0dp"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_rating_two" />

    <TextView
        android:id="@+id/tv_rating_three"
        android:text="@string/delivered_with_careless"
        app:layout_constraintTop_toBottomOf="@+id/tv_rating_two"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingLeft="@dimen/card_side_margin"
        android:paddingRight="@dimen/card_side_margin"
        android:paddingTop="@dimen/dip_20"
        android:paddingBottom="@dimen/dip_20"
        style="@style/ItemStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_rating_three"
        android:layout_width="@dimen/rating_check_size"
        android:layout_height="@dimen/rating_check_size"
        android:layout_marginRight="@dimen/card_side_margin"
        android:src="@drawable/ic_path_checkmark"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_rating_three"
        app:layout_constraintBottom_toBottomOf="@+id/tv_rating_three" />

    <TextView
        style="@style/RestaurantDividerHorizontalStyle"
        android:layout_width="0dp"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_rating_three" />

    <TextView
        android:id="@+id/tv_rating_four"
        android:text="@string/unfriendly_service"
        app:layout_constraintTop_toBottomOf="@+id/tv_rating_three"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingLeft="@dimen/card_side_margin"
        android:paddingRight="@dimen/card_side_margin"
        android:paddingTop="@dimen/dip_20"
        android:paddingBottom="@dimen/dip_20"
        style="@style/ItemStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_rating_four"
        android:layout_width="@dimen/rating_check_size"
        android:layout_height="@dimen/rating_check_size"
        android:layout_marginRight="@dimen/card_side_margin"
        android:src="@drawable/ic_path_checkmark"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_rating_four"
        app:layout_constraintBottom_toBottomOf="@+id/tv_rating_four" />


</androidx.constraintlayout.widget.ConstraintLayout>