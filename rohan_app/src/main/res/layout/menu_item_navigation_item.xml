<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_menu_navigation"
        style="@style/SubItemStyle"
        android:gravity="center"
        android:layout_marginRight="@dimen/dip_10"
        android:layout_marginLeft="@dimen/dip_10"
        android:layout_marginTop="@dimen/dip_15"
        android:layout_marginBottom="@dimen/dip_15"
        tools:background="@drawable/button_secondary"
        android:layout_width="wrap_content"
        android:layout_height="match_parent" />


</FrameLayout>