<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:shadowColor="@color/second_satisfied_button_shadow"
    app:shadowRadius="8dp"
    app:yOffset="0dp"
    app:xOffset="0dp"
    app:blurRadius="0dp"
    android:background="@drawable/bg_restaurant_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.ricepo.style.round.RoundImageView
        android:src="@drawable/ic_refer_share"
        android:scaleType="centerCrop"
        android:adjustViewBounds="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:round_corner="8dp"
        android:paddingTop="0.8dp"
        android:paddingLeft="0.5dp"
        android:paddingRight="0.5dp"
        android:paddingBottom="0.8dp"
        android:layout_width="0dp"
        android:layout_height="@dimen/sw_90dp" />

    <TextView
        android:id="@+id/tv_share_top_title"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_share_subtitle"
        app:layout_constraintVertical_chainStyle="packed"
        style="@style/LargeTitleStyle"
        android:textSize="@dimen/sw_24dp"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:text="@string/refer_friend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_share_subtitle"
        app:layout_constraintLeft_toLeftOf="@+id/tv_share_top_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_share_top_title"
        app:layout_constraintBottom_toBottomOf="parent"
        style="@style/SubTitleStyle"
        android:text="@string/refer_recommend_now"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_share_arrow"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintTop_toTopOf="@+id/tv_share_subtitle"
        app:layout_constraintBottom_toBottomOf="@+id/tv_share_subtitle"
        app:layout_constraintLeft_toRightOf="@+id/tv_share_subtitle"
        android:layout_marginLeft="0dp"
        android:layout_marginBottom="@dimen/sw_1dp"
        android:layout_width="@dimen/sw_14dp"
        android:layout_height="@dimen/sw_14dp" />

</androidx.constraintlayout.widget.ConstraintLayout>

