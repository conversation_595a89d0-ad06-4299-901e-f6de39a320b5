<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_restaurant_card"
    android:orientation="vertical"
    android:visibility="gone"
    tools:visibility="visible"
    android:layout_marginLeft="@dimen/card_side_margin"
    android:layout_marginRight="@dimen/card_side_margin"
    android:paddingLeft="@dimen/dip_20"
    android:paddingRight="@dimen/dip_20"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_order_call_driver"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_65" >

        <TextView
            android:id="@+id/tv_order_call_driver"
            android:text="@string/call_driver"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            style="@style/LabelItemStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_order_call_driver"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:src="@drawable/ic_right_arrow"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_15" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/divider_call_driver"
        android:visibility="gone"
        tools:visibility="visible"
        style="@style/RestaurantDividerHorizontalStyle" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_order_reorder"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_65" >

        <TextView
            android:id="@+id/tv_order_reorder"
            android:text="@string/re_order"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            style="@style/LabelItemStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_order_reorder"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:src="@drawable/ic_right_arrow"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_15" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/divider_order_reorder"
        android:visibility="gone"
        tools:visibility="visible"
        style="@style/RestaurantDividerHorizontalStyle" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_order_receipt"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_65" >

        <TextView
            android:id="@+id/tv_order_receipt"
            android:text="@string/view_receipt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            style="@style/LabelItemStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_order_receipt"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:src="@drawable/ic_right_arrow"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_15" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/divider_receipt"
        android:visibility="gone"
        tools:visibility="visible"
        style="@style/RestaurantDividerHorizontalStyle" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_order_chat"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_65" >

        <TextView
            android:id="@+id/tv_order_chat"
            android:text="@string/chat"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            style="@style/LabelItemStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_order_chat"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:src="@drawable/ic_right_arrow"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_15" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>