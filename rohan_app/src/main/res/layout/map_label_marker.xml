<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content" >

    <LinearLayout
        android:id="@+id/ll_map_label"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:orientation="vertical"
        android:minHeight="38dp"
        android:paddingBottom="@dimen/sw_15dp"
        android:paddingTop="@dimen/sw_15dp"
        android:gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/ll_map_finish_at"
            android:layout_marginBottom="@dimen/sw_2dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_map_finish_at"
                android:src="@drawable/map_delivery_finish_at"
                android:layout_gravity="center_vertical"
                android:scaleType="center"
                android:layout_marginRight="2dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_map_finish_at"
                android:textSize="@dimen/sw_15sp"
                android:maxLines="1"
                android:ellipsize="end"
                android:shadowRadius="1"
                android:shadowDx="0"
                android:shadowDy="1"
                android:layout_gravity="center_vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_map_label_marker"
            android:textSize="@dimen/sw_13sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:shadowRadius="1"
            android:shadowDx="0"
            android:shadowDy="1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />


    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>