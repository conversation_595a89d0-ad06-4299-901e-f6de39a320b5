<?xml version="1.0" encoding="utf-8"?>
<!-- todo seems just used for as view type can remove in future-->
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_restaurant_menu"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="-10dp"
        android:scrollbars="none"
        android:layout_width="0dp"
        android:layout_height="180dp" />

    <TextView
        android:id="@+id/tv_restaurant_name"
        app:layout_constraintTop_toBottomOf="@+id/rv_restaurant_menu"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/fl_bundle_tick"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="left"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        style="@style/LargeTitleStyle" />

    <TextView
        android:id="@+id/tv_restaurant_info"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_name"
        app:layout_constraintBottom_toTopOf="@+id/tv_restaurant_vip_promotion"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@+id/tv_restaurant_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:maxLines="2"
        android:ellipsize="end"
        style="@style/SubTitleMenuStyle"
        android:layout_marginTop="10dp" />

    <TextView
        android:id="@+id/tv_restaurant_vip_promotion"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@+id/tv_restaurant_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_info"
        app:layout_constraintBottom_toTopOf="@+id/tv_restaurant_promotion"
        android:maxLines="1"
        android:ellipsize="end"
        style="@style/SubTitleMenuStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginTop="5dp"
        android:visibility="invisible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_restaurant_promotion"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@+id/tv_restaurant_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_vip_promotion"
        android:maxLines="1"
        android:ellipsize="end"
        style="@style/SubTitleMenuStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginTop="5dp"
        android:visibility="invisible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_restaurant_closed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@+id/tv_restaurant_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_promotion"
        android:maxLines="1"
        android:ellipsize="end"
        style="@style/SubTitleMenuStyle"
        android:textColor="@color/alert"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginTop="5dp"
        android:visibility="gone"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/fl_bundle_tick"
        app:layout_constraintRight_toRightOf="@+id/tv_restaurant_info_divider"
        app:layout_constraintTop_toTopOf="@+id/tv_restaurant_name"
        android:layout_width="40dp"
        android:layout_height="40dp">

        <com.ricepo.style.round.RoundLayout
            android:id="@+id/round_bundle_tick"
            app:stroke_width="1dp"
            app:stroke_color="@color/restaurant_see_all_shadow"
            app:round_corner="20dp"
            android:layout_width="40dp"
            android:layout_height="40dp" />

        <com.ricepo.style.round.RoundLayout
            android:id="@+id/round_bundle_tick_placeholder"
            app:stroke_width="1dp"
            app:round_corner="19dp"
            app:stroke_color="@color/colorPrimary"
            android:layout_marginBottom="0.5dp"
            android:layout_marginTop="0dp"
            android:layout_width="40dp"
            android:layout_height="38dp" />

        <ImageView
            android:id="@+id/iv_bundle_tick"
            android:src="@drawable/ic_bundle_plus"
            android:scaleType="fitCenter"
            android:layout_gravity="center"
            android:layout_width="28dp"
            android:layout_height="28dp" />

    </FrameLayout>


    <TextView
        android:id="@+id/tv_restaurant_info_divider"
        style="@style/DividerHorizontalStyle"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginRight="@dimen/restaurant_text_margin"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_closed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <LinearLayout
        android:id="@+id/ll_restaurant_match_food"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_info_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="-10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical" />

    <TextView
        android:id="@+id/tv_restaurant_divider"
        style="@style/DividerHorizontalStyle"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/ll_restaurant_match_food"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>