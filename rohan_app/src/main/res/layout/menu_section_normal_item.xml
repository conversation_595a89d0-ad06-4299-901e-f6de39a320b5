<?xml version="1.0" encoding="utf-8"?>

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_section_item"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/divider_food"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/RestaurantDividerHorizontalStyle" />

        <ImageView
            android:id="@+id/iv_food_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/sw_15dp"
            android:layout_width="@dimen/sw_food_big_width"
            android:layout_height="@dimen/sw_food_big_height"
            tools:src="@drawable/bg_plate" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_food"
            app:layout_constraintLeft_toLeftOf="@+id/iv_food_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_food_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_food_bg"
            android:layout_width="@dimen/sw_food_big_width"
            android:layout_height="@dimen/sw_food_big_width"  />

        <TextView
            android:id="@+id/tv_food_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/sw_18dp"
            android:layout_marginRight="4dp"
            android:layout_marginLeft="@dimen/sw_8dp"
            style="@style/MenuFoodNameStyle"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintLeft_toRightOf="@+id/iv_food_bg"
            app:layout_constraintRight_toLeftOf="@+id/barrier_food_option"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_food_info"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_food_name"
            app:layout_constraintRight_toLeftOf="@+id/barrier_food_option"
            style="@style/SubTitleStyle"
            android:textSize="@dimen/font_size_h6"
            android:textFontWeight="500"
            android:maxLines="3"
            android:ellipsize="end"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginTop="4dp"
            android:layout_marginRight="4dp"
            android:lineSpacingExtra="0dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_food_price"
            app:layout_constraintTop_toBottomOf="@+id/tv_food_info"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
            tools:text="20.00"
            android:layout_marginTop="4dp"
            style="@style/SubItemStyle"
            android:textColor="@color/inputLineText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_food_bottom"
            app:layout_constraintTop_toBottomOf="@+id/tv_food_price"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_10" />

        <ImageView
            android:id="@+id/iv_reward_icon"
            app:layout_constraintLeft_toRightOf="@+id/tv_food_price"
            app:layout_constraintBottom_toBottomOf="@+id/tv_food_price"
            app:layout_constraintTop_toTopOf="@+id/tv_food_price"
            android:src="@drawable/ic_coin"
            android:scaleType="centerInside"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_width="18dp"
            android:layout_height="18dp" />

        <TextView
            android:id="@+id/tv_food_original_price"
            app:layout_constraintBottom_toBottomOf="@+id/tv_food_price"
            app:layout_constraintLeft_toRightOf="@+id/iv_reward_icon"
            tools:text="20.00"
            android:layout_marginLeft="4dp"
            style="@style/SubItemStyle"
            android:textColor="@color/inputLineText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_food_option"
            app:constraint_referenced_ids="btn_food_option"
            app:barrierDirection="start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />


        <!-- minWidth resulting in the right side not being aligned after adding the quantity -->
        <com.ricepo.style.button.ChocolateButton
            android:id="@+id/btn_food_option"
            android:layout_marginTop="@dimen/sw_20dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:clickable="false"
            android:minHeight="26dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>