<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_support_group_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="@dimen/dip_40"
        style="@style/TitleStyle"
        android:textFontWeight="650"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:paddingLeft="@dimen/margin_left"
        android:paddingRight="@dimen/margin_right"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        android:visibility="gone"
        style="@style/DividerHorizontalStyle" />


</androidx.constraintlayout.widget.ConstraintLayout>