<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dip_12"
    android:layout_marginBottom="@dimen/dip_12">

    <com.ricepo.style.shadow.LuckyShadowLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_84"
        android:layout_marginLeft="37dp"
        android:layout_marginRight="37dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:luckyShadowColor="#4c000000"
        app:luckyShadowDirection="bottom|left|right"
        app:luckyShadowLength="@dimen/dip_5"
        app:luckyShadowRadius="@dimen/button_radius">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_coupon_code"
            app:layout_constraintTop_toTopOf="parent">

            <RelativeLayout
                android:layout_width="@dimen/dip_72"
                android:layout_height="match_parent"
                android:background="@drawable/coupon_bg">

                <ImageView
                    android:id="@+id/iv_coupon"
                    android:layout_width="@dimen/dip_30"
                    android:layout_height="@dimen/dip_30"
                    android:layout_centerInParent="true"
                    android:src="@drawable/coupon" />

            </RelativeLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/coupon_layout_bg">

                <TextView
                    android:id="@+id/tv_coupon_code"
                    style="@style/ItemStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dip_15"
                    android:layout_marginTop="@dimen/dip_10"
                    android:layout_marginRight="@dimen/dip_15"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textSize="@dimen/font_size_h5"
                    app:layout_constraintBottom_toTopOf="@+id/tv_coupon_detail"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/tv_coupon_price"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_coupon_price"
                    style="@style/ItemStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/dip_20"
                    android:textSize="@dimen/font_size_h5"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_coupon_code" />

                <TextView
                    android:id="@+id/tv_coupon_detail"
                    style="@style/SubTitleStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_goneMarginBottom="@dimen/dip_10"
                    app:layout_constraintBottom_toTopOf="@+id/tv_coupon_condition"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_coupon_code"
                    app:layout_constraintTop_toBottomOf="@+id/tv_coupon_code" />

                <TextView
                    android:id="@+id/tv_coupon_condition"
                    style="@style/SubTitleStyle"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_coupon_code"
                    app:layout_constraintTop_toBottomOf="@+id/tv_coupon_detail"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginBottom="@dimen/dip_10"
                    android:layout_marginRight="@dimen/dip_15"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_coupon_item"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:constraint_referenced_ids="tv_coupon_code, tv_coupon_detail, tv_coupon_price" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>


    </com.ricepo.style.shadow.LuckyShadowLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
