<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <com.ricepo.base.view.RicePoolView
        android:id="@+id/rtv_pool"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsl_checkout_content"
        app:layout_constraintTop_toBottomOf="@+id/iv_close"
        app:layout_constraintBottom_toTopOf="@id/bottom_holder"
        android:layout_width="match_parent"
        android:layout_height="0dp">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_checkout_normal_divider_top"
                style="@style/DividerHorizontalStyle"
                android:visibility="gone"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:id="@+id/ll_checkout_normal"
                android:orientation="vertical"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:visibility="gone"
                tools:visibility="visible"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:paddingLeft="@dimen/card_side_margin"
                android:paddingRight="@dimen/card_side_margin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_checkout_normal_divider_bottom"
                style="@style/DividerHorizontalStyle"
                android:visibility="gone"
                android:layout_marginTop="8dp" />

            <LinearLayout
                android:id="@+id/lay_checkout_delivery"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginTop="@dimen/sw_section_space"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:orientation="vertical"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:visibility="gone">

                <include
                    android:id="@+id/in_checkout_delivery"
                    layout="@layout/item_checkout_delivery" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lay_checkout_pickup"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginTop="@dimen/sw_section_space"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/sw_26dp"
                android:paddingLeft="@dimen/card_side_margin"
                android:paddingRight="@dimen/sw_19dp"
                android:orientation="vertical"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:visibility="gone">

                <include
                    android:id="@+id/in_checkout_pickup"
                    layout="@layout/item_checkout_pickup" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lay_checkout_fast_delivery"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginTop="@dimen/sw_section_space"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:paddingLeft="@dimen/card_side_margin"
                android:paddingRight="@dimen/sw_19dp"
                android:orientation="vertical"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:visibility="gone">

                <include
                    android:id="@+id/in_checkout_fast_delivery"
                    layout="@layout/item_checkout_fast_delivery" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lay_checkout_payment"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginTop="@dimen/sw_section_space"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:paddingLeft="@dimen/card_side_margin"
                android:paddingRight="@dimen/sw_19dp"
                android:orientation="vertical"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:visibility="gone">

                <include
                    android:id="@+id/in_checkout_payment"
                    layout="@layout/item_checkout_payment" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lay_checkout_membership"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginTop="@dimen/sw_section_space"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:orientation="vertical"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:visibility="gone">

                <include
                    android:id="@+id/in_checkout_membership"
                    layout="@layout/restaurant_item_plan" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lay_checkout_fees"
                android:orientation="vertical"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:layout_marginTop="@dimen/sw_section_space"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_fees_diff"
                    android:layout_marginTop="15dp"
                    style="@style/AssertiveStyle"
                    android:layout_marginLeft="@dimen/card_side_margin"
                    android:layout_marginRight="@dimen/card_side_margin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <!-- order info margin top 8dp -->
                <LinearLayout
                    android:id="@+id/ll_checkout_fees"
                    android:orientation="vertical"
                    android:paddingTop="7dp"
                    android:paddingBottom="15dp"
                    tools:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lay_checkout_fees_discount"
                android:orientation="vertical"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:layout_marginTop="@dimen/sw_section_space"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <!-- order info margin top 8dp -->
                <LinearLayout
                    android:id="@+id/ll_checkout_fees_discount"
                    android:orientation="vertical"
                    android:paddingTop="15dp"
                    android:paddingBottom="15dp"
                    tools:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lay_checkout_tips"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginTop="@dimen/sw_section_space"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:paddingLeft="@dimen/card_side_margin"
                android:paddingRight="@dimen/sw_19dp"
                android:orientation="vertical"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:visibility="gone">

                <include
                    android:id="@+id/in_checkout_tips"
                    layout="@layout/item_checkout_tips" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lay_checkout_policy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone" >

                <include
                    android:id="@+id/in_checkout_policy"
                    layout="@layout/item_checkout_policy" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/card_background"
        android:paddingTop="20dp"
        android:id="@+id/bottom_holder"
        >


        <com.google.android.material.card.MaterialCardView
            android:id="@+id/in_place_order"
            xmlns:app="http://schemas.android.com/apk/res-auto"
            xmlns:android="http://schemas.android.com/apk/res/android"
            xmlns:tools="http://schemas.android.com/tools"
            android:visibility="gone"
            tools:visibility="visible"
            app:cardBackgroundColor="@color/mr"
            app:cardCornerRadius="@dimen/button_radius"
            android:layout_width="match_parent"
            android:layout_marginHorizontal="30dp"
            android:minHeight="@dimen/_sw_43dp"
            android:layout_marginBottom="@dimen/sw_43dp"
            android:layout_height="@dimen/sw_43dp">

            <LinearLayout
                android:layout_gravity="center_vertical"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/tv_checkout_order_place"
                    android:textSize="@dimen/font_size_h3"
                    android:textColor="@color/w"
                    android:layout_marginLeft="@dimen/dip_30"
                    android:layout_marginBottom="@dimen/dip_5"
                    android:layout_width="wrap_content"
                    android:layout_weight="1"
                    tools:text="confirm"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_checkout_order_price"
                    style="@style/PlaceOrderStyle"
                    tools:text="$0"
                    android:textSize="@dimen/font_size_h3"
                    android:textColor="@color/w"
                    android:layout_marginRight="@dimen/dip_30"
                    android:layout_marginBottom="@dimen/dip_5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>


    </FrameLayout>



    <FrameLayout
        android:id="@+id/fl_checkout_page"
        app:layout_constraintTop_toBottomOf="@+id/iv_close"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_width="0dp"
        android:layout_height="0dp" />


</androidx.constraintlayout.widget.ConstraintLayout>