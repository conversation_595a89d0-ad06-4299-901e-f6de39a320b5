<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_support_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/RestaurantDividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_support_item"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        style="@style/ItemStyle"
        android:textFontWeight="650"
        android:paddingTop="15dp"
        android:paddingBottom="15dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_support_item"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:src="@drawable/ic_check_black_24dp"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_support_item"
        app:layout_constraintBottom_toBottomOf="@+id/tv_support_item" />



</androidx.constraintlayout.widget.ConstraintLayout>