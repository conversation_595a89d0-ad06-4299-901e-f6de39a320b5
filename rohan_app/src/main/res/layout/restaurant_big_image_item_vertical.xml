<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:orientation="vertical"
        android:background="@drawable/bg_restaurant_card"
        android:layout_width="match_parent"
        android:paddingBottom="12dp"
        android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.ricepo.style.round.RoundImageView
            android:id="@+id/big_image"
            android:layout_width="match_parent"
            android:layout_height="@dimen/sw_156dp"
            android:scaleType="centerCrop"
            app:round_corner_top_left="12dp"
            app:round_corner_top_right="12dp" />

        <FrameLayout
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <include
                android:id="@+id/in_restaurant_closed"
                layout="@layout/layout_restaurant_closed"/>

        </FrameLayout>

        <com.ricepo.base.view.RicePoolHomeView
            android:id="@+id/rtv_pool"
            tools:visibility="visible"
            android:visibility="gone"
            android:layout_gravity="end"
            android:layout_marginTop="12dp"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/restaurant_pool_height"/>

        <com.ricepo.style.button.ChocolateButton
            android:id="@+id/btn_restaurant_bundle"
            android:layout_gravity="top|end"
            android:layout_margin="12dp"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/tv_feature_motd"
            style="@style/MotdLabelStyle"
            android:minHeight="@dimen/sw_21dp"
            android:textSize="@dimen/font_size_h6"
            android:backgroundTint="@color/shape_color"
            android:singleLine="true"
            />

    </FrameLayout>

    <include
        android:id="@+id/in_restaurant_info"
        layout="@layout/layout_restaurant_big_image_vertical_info" />

</LinearLayout>