<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipChildren="false"
    android:layout_width="@dimen/sw_378dp"
    android:layout_height="@dimen/sw_151dp">

        <ImageView
            android:id="@+id/iv_banner_icon"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="1dp"
            android:layout_marginLeft="0dp"
            android:layout_marginRight="0dp"
            android:scaleType="fitXY"
            android:adjustViewBounds="true"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/sw_151dp" />

<!--    </com.ricepo.style.round.RoundLayout>-->

    <FrameLayout
        android:id="@+id/ll_banner_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/sw_26dp"
        android:layout_marginTop="@dimen/sw_21dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" >

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_banner_title"
            android:layout_width="@dimen/sw_160dp"
            android:layout_height="wrap_content"
            tools:text="Amex美食津贴\n每月"
            style="@style/LargeTitleStyle"
            android:textColor="@color/mainText"
            android:maxLines="2"
            android:autoSizeTextType="uniform"
            android:autoSizeStepGranularity="1sp"
            android:autoSizeMaxTextSize="@dimen/font_size_h2"
            android:ellipsize="end" />

    </FrameLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="31dp"
        app:layout_constraintLeft_toLeftOf="@+id/ll_banner_title"
        app:layout_constraintBottom_toBottomOf="parent"
        android:orientation="horizontal"
        android:layout_marginBottom="@dimen/sw_24dp"
        android:background="@drawable/banner_button_outline"
        android:paddingHorizontal="12dp"
        android:gravity="center">
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_banner_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="31dp"
            android:gravity="center"
            android:textSize="@dimen/font_size_h6"
            android:textColor="@color/subText"
            tools:text="@string/help"
            app:icon="@drawable/ic_right_arrow_small"
            app:iconGravity="end"
            app:iconTint="@color/subText"
            android:clickable="false"
            app:strokeColor="@color/disableText"
            app:cornerRadius="8dp"
            android:paddingRight="4dp"
            />
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_right_arrow_small"
            app:tint="@color/subText" />
    </LinearLayout>


    <View
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginRight="0dp"
        android:layerType="software"
        android:visibility="gone"
        style="@style/DividerVerticalStyle" />

</androidx.constraintlayout.widget.ConstraintLayout>