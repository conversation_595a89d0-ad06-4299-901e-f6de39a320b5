<?xml version="1.0" encoding="utf-8"?>

<HorizontalScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipChildren="false"
    android:scrollbars="none"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/lay_section_gallery"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" >

        <com.ricepo.style.round.RoundLayout
            android:id="@+id/lay_section_gallery_bg"
            android:background="@drawable/bg_restaurant_card"
            app:round_corner="@dimen/sw_11dp"
            android:layout_marginLeft="@dimen/sw_22dp"
            android:layout_marginRight="@dimen/sw_22dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_menu_featured_bg"
                android:scaleType="fitXY"
                android:adjustViewBounds="true"
                android:maxWidth="1500dp"
                android:layout_marginTop="1dp"
                android:layout_marginBottom="0dp"
                tools:src="@drawable/bg_default"
                app:layout_constraintLeft_toLeftOf="@+id/rv_menu_container"
                app:layout_constraintRight_toRightOf="@+id/rv_menu_container"
                app:layout_constraintTop_toTopOf="@+id/rv_menu_container"
                app:layout_constraintBottom_toBottomOf="@+id/rv_menu_container"
                android:layout_width="0dp"
                android:layout_height="0dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_menu_container"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.ricepo.style.round.RoundLayout>

    </RelativeLayout>

</HorizontalScrollView>