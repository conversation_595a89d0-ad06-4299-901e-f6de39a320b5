<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="@dimen/sw_295dp"
    android:layout_height="@dimen/sw_317dp">

    <include
        android:id="@+id/in_restaurant_closed"
        layout="@layout/layout_restaurant_closed" />


    <include
        android:id="@+id/in_restaurant_info"
        layout="@layout/layout_restaurant_info_small" />

    <LinearLayout
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/sw_3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_feature_motd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:minHeight="@dimen/sw_21dp"
            android:textSize="@dimen/font_size_h6"
            android:singleLine="true"
            android:layout_marginTop="3dp"
            android:layout_marginHorizontal="16dp" />

        <com.ricepo.base.view.RicePoolHomeView
            android:id="@+id/rtv_pool"
            tools:visibility="visible"
            android:visibility="gone"
            android:layout_gravity="center_vertical"
            android:layout_width="wrap_content"
            android:layout_marginTop="@dimen/sw_3dp"
            android:layout_height="17dp" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_restaurant_small"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_165dp">

        <!-- left -->
        <ImageView
            android:id="@+id/iv_left_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/iv_middle_bg"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginLeft="@dimen/sw_26dp"
            android:layout_width="@dimen/sw_food_small_width"
            android:layout_height="@dimen/sw_food_small_height"
            android:scaleType="fitXY"
            android:adjustViewBounds="true"
            tools:src="@drawable/bg_plate" />

        <ImageView
            android:id="@+id/iv_left"
            app:layout_constraintLeft_toLeftOf="@+id/iv_left_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_left_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_left_bg"
            android:layout_width="@dimen/sw_food_small_width"
            android:layout_height="@dimen/sw_food_small_width"  />

        <TextView
            android:id="@+id/tv_food_name_left"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:ellipsize="end"
            android:gravity="center"
            android:layout_marginTop="@dimen/sw_16dp"
            app:layout_constraintLeft_toLeftOf="@+id/iv_left_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_left_bg"
            app:layout_constraintTop_toBottomOf="@+id/iv_left"
            android:layout_gravity="center_horizontal"
            tools:text="@string/test_text_length"
            style="@style/RestaurantInfoStyle" />

        <!-- middle -->
        <ImageView
            android:id="@+id/iv_middle_bg"
            app:layout_constraintLeft_toRightOf="@+id/iv_left_bg"
            app:layout_constraintBottom_toBottomOf="@+id/iv_left_bg"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginLeft="@dimen/sw_26dp"
            android:layout_marginRight="@dimen/sw_36dp"
            android:layout_width="@dimen/sw_food_small_width"
            android:layout_height="@dimen/sw_food_small_height"
            tools:src="@drawable/bg_plate" />

        <ImageView
            android:id="@+id/iv_middle"
            app:layout_constraintLeft_toLeftOf="@+id/iv_middle_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_middle_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_middle_bg"
            android:layout_width="@dimen/sw_food_small_width"
            android:layout_height="@dimen/sw_food_small_width" />

        <TextView
            android:id="@+id/tv_food_name_middle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/sw_16dp"
            android:maxLines="2"
            android:ellipsize="end"
            android:gravity="center"
            app:layout_constraintLeft_toLeftOf="@+id/iv_middle_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_middle_bg"
            app:layout_constraintTop_toBottomOf="@+id/iv_middle"
            android:layout_gravity="center_horizontal"
            tools:text="@string/test_text_length"
            style="@style/RestaurantInfoStyle" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>