<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_subscription_profile"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/ic_membership_profile"
        android:scaleType="centerInside"
        app:layout_constraintDimensionRatio="h, 10:3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_subscription_vip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/subscription_vip"
        android:textColor="@color/mainText"
        style="@style/LargeTitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_subscription_profile" />

    <TextView
        android:id="@+id/tv_subscription_check_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/subscription_check_detail"
        android:textColor="@color/goldSubText"
        style="@style/SubTitleStyle"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/iv_subscription_arrow"
        app:layout_constraintTop_toBottomOf="@+id/tv_subscription_vip" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_subscription_arrow"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:src="@drawable/ic_membership_arrow"
        android:scaleType="centerInside"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_subscription_check_detail"
        app:layout_constraintBottom_toBottomOf="@+id/tv_subscription_check_detail"
        app:layout_constraintTop_toTopOf="@id/tv_subscription_check_detail" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_subscription_vip"
        app:constraint_referenced_ids="iv_subscription_profile, tv_subscription_vip, tv_subscription_check_detail, iv_subscription_arrow"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <include
        android:id="@+id/in_membership"
        layout="@layout/item_checkout_membership"
        app:layout_constraintTop_toTopOf="parent"
        tools:visible="true" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_vip"
        app:constraint_referenced_ids="tv_subscription_check_detail, in_membership"
        app:barrierDirection="bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_days_on"
        android:text="@string/days_on_ricepo"
        android:textSize="@dimen/sw_24sp"
        android:textColor="@color/mainText"
        app:layout_constraintTop_toBottomOf="@+id/barrier_vip"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_order_count"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_order_count"
        android:text="@string/order_count"
        android:textSize="@dimen/sw_24sp"
        android:textColor="@color/mainText"
        app:layout_constraintTop_toTopOf="@+id/tv_days_on"
        app:layout_constraintLeft_toRightOf="@+id/tv_days_on"
        app:layout_constraintRight_toLeftOf="@+id/tv_saving"
        app:layout_goneMarginRight="40dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_saving"
        android:text="@string/saving"
        android:textSize="@dimen/sw_24sp"
        android:textColor="@color/mainText"
        app:layout_constraintTop_toTopOf="@+id/tv_days_on"
        app:layout_constraintLeft_toRightOf="@+id/tv_order_count"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="@dimen/margin_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_days_on_label"
        android:text="@string/days_on_ricepo"
        android:textSize="@dimen/sw_13sp"
        android:textColor="@color/subText"
        app:layout_constraintTop_toBottomOf="@+id/tv_days_on"
        app:layout_constraintLeft_toLeftOf="@+id/tv_days_on"
        app:layout_constraintRight_toRightOf="@+id/tv_days_on"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="20dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_order_count_label"
        android:text="@string/order_count"
        android:textSize="@dimen/sw_13sp"
        android:textColor="@color/subText"
        app:layout_constraintTop_toBottomOf="@+id/tv_order_count"
        app:layout_constraintLeft_toLeftOf="@+id/tv_order_count"
        app:layout_constraintRight_toRightOf="@+id/tv_order_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_saving_label"
        android:text="@string/saving"
        android:textSize="@dimen/sw_13sp"
        android:textColor="@color/subText"
        app:layout_constraintTop_toBottomOf="@+id/tv_saving"
        app:layout_constraintLeft_toLeftOf="@+id/tv_saving"
        app:layout_constraintRight_toRightOf="@+id/tv_saving"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_saving"
        app:constraint_referenced_ids="tv_saving, tv_saving_label"
        android:visibility="visible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>