<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginBottom="20dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_restaurant_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginTop="15dp"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginRight="@dimen/restaurant_text_margin"
        style="@style/TitleStyle"
        android:textSize="@dimen/sw_24sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_restaurant_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/SubTitleStyle"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_name" />

    <TextView
        android:id="@+id/tv_restaurant_vip_promotion"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/SubTitleStyle"
        android:visibility="gone"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_info" />

    <TextView
        android:id="@+id/tv_restaurant_promotion"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/SubTitleStyle"
        android:visibility="gone"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_vip_promotion" />

<!--    <TextView-->
<!--        android:id="@+id/tv_restaurant_motd_star"-->
<!--        app:layout_constraintTop_toTopOf="@+id/tv_restaurant_motd"-->
<!--        app:layout_constraintBottom_toBottomOf="@+id/tv_restaurant_motd"-->
<!--        app:layout_constraintRight_toLeftOf="@+id/tv_restaurant_motd"-->
<!--        android:text="*"-->
<!--        android:paddingTop="3dp"-->
<!--        android:gravity="center_vertical"-->
<!--        style="@style/SubTitleStyle"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="0dp" />-->

    <TextView
        android:id="@+id/tv_restaurant_motd"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/SubTitleStyle"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_promotion" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_restaurant_motd"
        app:constraint_referenced_ids="tv_restaurant_motd"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_restaurant_closed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_motd"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center"
        style="@style/SubTitleMenuStyle"
        android:textColor="@color/alert"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        android:layout_marginTop="5dp"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>