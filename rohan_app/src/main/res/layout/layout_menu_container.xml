<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <TextView
        android:id="@+id/tv_divider"
        style="@style/DividerHorizontalStyle"
        android:layout_marginTop="20dp"
        app:layout_goneMarginBottom="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_category_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_category_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_divider"
        app:layout_constraintBottom_toTopOf="@+id/tv_category_description"
        app:layout_goneMarginBottom="30dp"
        style="@style/TitleStyle"
        android:visibility="gone"
        android:layout_marginTop="@dimen/menu_category_margin_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_category_description"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_category_name"
        app:layout_constraintBottom_toTopOf="@+id/ll_menu_container"
        style="@style/SubTitleStyle"
        android:visibility="gone"
        android:layout_marginBottom="30dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_menu_featured_bg"
        app:layout_constraintLeft_toLeftOf="@+id/ll_menu_container"
        app:layout_constraintRight_toRightOf="@+id/ll_menu_container"
        app:layout_constraintTop_toTopOf="@+id/ll_menu_container"
        app:layout_constraintBottom_toBottomOf="@+id/ll_menu_container"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        android:maxHeight="5040dp"
        android:layout_marginTop="30dp"
        android:layout_width="0dp"
        android:layout_height="0dp" />

<!--    <LinearLayout-->
<!--        android:id="@+id/ll_menu_featured"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/tv_category_description"-->
<!--        app:layout_constraintBottom_toTopOf="@id/tv_menu_margin_bottom"-->
<!--        android:orientation="vertical"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content" />-->

    <LinearLayout
        android:id="@+id/ll_menu_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_category_description"
        app:layout_constraintBottom_toTopOf="@+id/tv_menu_margin_bottom"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_menu_margin_bottom"
        android:layout_width="match_parent"
        android:layout_height="112dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/ll_menu_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>