<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_food_image"
        android:layout_gravity="center_horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_menu_bg"
            android:layout_width="@dimen/sw_food_small_width"
            android:layout_height="@dimen/sw_food_small_height"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/bg_plate" />

        <ImageView
            android:id="@+id/iv_menu"
            android:layout_width="@dimen/sw_food_small_width"
            android:layout_height="@dimen/sw_food_small_width"
            app:layout_constraintLeft_toLeftOf="@+id/iv_menu_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_menu_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_menu_bg"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_food_name"
        android:layout_width="@dimen/sw_77dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_30"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        android:layout_gravity="center_horizontal"
        tools:text="@string/test_text_length"
        style="@style/RestaurantInfoStyle" />

</LinearLayout>
