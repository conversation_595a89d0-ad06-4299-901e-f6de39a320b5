<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        app:layout_constraintEnd_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/home_bar"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:gravity="center_vertical"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/padding_title_icon"
            android:paddingTop="@dimen/padding_title_icon"
            android:paddingRight="@dimen/dip_5"
            android:paddingBottom="@dimen/padding_title_icon"
            android:visibility="invisible"
            tools:visibility="visible"
            android:tint="@color/fun_n6"
            app:srcCompat="@drawable/ic_search" />

        <LinearLayout
            android:id="@+id/ll_address"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_10"
            android:layout_marginRight="@dimen/dip_10"
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_weight="1"
            >

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_address"
                style="@style/FontH5"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:drawableRight="@drawable/ic_arrow_down"
                app:drawableTint="@color/fun_n6"
                android:ellipsize="end"
                android:gravity="center"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:text="@string/enter_address"
                android:textColor="@color/title"
                />

        </LinearLayout>


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_profile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/dip_5"
            android:paddingTop="@dimen/padding_title_icon"
            android:paddingRight="@dimen/padding_title_icon"
            android:paddingBottom="@dimen/padding_title_icon"
            app:srcCompat="@drawable/ic_profile"
            android:tint="@color/fun_n6"
            />

    </LinearLayout>



    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_restaurant_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/home_bar" />

    <include
        android:id="@+id/in_restaurant_tab"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        layout="@layout/layout_restaurant_tab" />

    <include
        android:id="@+id/in_menu_cart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        layout="@layout/layout_menu_cart_bar" />


    <FrameLayout
        android:id="@+id/fl_motion_launch"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <androidx.constraintlayout.motion.widget.MotionLayout
            android:id="@+id/motion_launch"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:transitionName="motion_launch"
            app:layoutDescription="@xml/motion_launch">

            <ImageView
                android:id="@+id/iv_splash_screen"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:contentDescription="@string/help"
                android:src="@drawable/layer_launcher"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.motion.widget.MotionLayout>

    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>