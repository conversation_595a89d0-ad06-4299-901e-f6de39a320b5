<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_item_left"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="@dimen/card_side_margin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_item_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_begin="10dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_left_icon"
        app:layout_constraintLeft_toRightOf="@+id/guideline_item_left"
        app:layout_constraintTop_toTopOf="@+id/lay_left_text"
        app:layout_constraintBottom_toBottomOf="@+id/lay_left_text"
        android:layout_width="16dp"
        android:layout_height="16dp" />

    <LinearLayout
        android:id="@+id/lay_left_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@+id/iv_left_icon"
        app:layout_constraintTop_toBottomOf="@+id/guideline_item_top"
        app:layout_constraintBottom_toTopOf="@+id/tv_left_info"
        app:layout_constraintRight_toLeftOf="@+id/barrier_right_text"
        android:orientation="horizontal" >

        <TextView
            android:id="@+id/tv_left_text"
            android:singleLine="true"
            android:ellipsize="end"
            tools:text="优惠券：aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
            style="@style/SubTitleMenuStyle"
            android:focusableInTouchMode="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </LinearLayout>


    <TextView
        android:id="@+id/tv_left_info"
        app:layout_constraintLeft_toLeftOf="@+id/lay_left_text"
        app:layout_constraintTop_toBottomOf="@+id/lay_left_text"
        app:layout_constraintRight_toLeftOf="@+id/barrier_right_text"
        app:layout_constraintBottom_toTopOf="@+id/guideline_item_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="@dimen/sw_6dp"
        android:textSize="@dimen/font_size_h6"
        android:textColor="@color/subText"
        android:visibility="gone"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />


    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_right_text"
        app:constraint_referenced_ids="tv_right_text, guideline_item_right, iv_right_icon"
        app:barrierDirection="start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_right_text"
        app:layout_constraintRight_toLeftOf="@+id/guideline_item_right"
        app:layout_constraintTop_toTopOf="@+id/lay_left_text"
        style="@style/SubTitleMenuStyle"
        android:layout_marginRight="0dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_right_icon"
        app:layout_constraintLeft_toLeftOf="@+id/guideline_item_right"
        app:layout_constraintTop_toTopOf="@+id/tv_right_text"
        app:layout_constraintBottom_toBottomOf="@+id/tv_right_text"
        tools:src="@drawable/ic_right_arrow"
        android:layout_width="@dimen/sw_12dp"
        android:layout_height="@dimen/sw_12dp" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_item_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_end="15dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_item_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintGuide_end="@dimen/sw_32dp" />

</androidx.constraintlayout.widget.ConstraintLayout>