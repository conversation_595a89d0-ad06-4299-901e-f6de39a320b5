<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/bg_restaurant_card"
    android:layout_width="match_parent"
    android:paddingBottom="12dp"
    android:layout_height="match_parent">

    <include
        android:id="@+id/in_restaurant_closed"
        layout="@layout/layout_restaurant_closed" />

    <com.ricepo.base.view.RicePoolHomeView
        android:id="@+id/rtv_pool"
        tools:visibility="visible"
        android:visibility="gone"
        android:layout_gravity="center_vertical"
        android:layout_width="match_parent"
        android:layout_height="@dimen/restaurant_pool_height" />

    <View
        android:id="@+id/divider_pool"
        android:layout_height="@dimen/dip_3"
        android:visibility="gone"
        style="@style/RestaurantDividerHorizontalStyle" />

    <include
        android:id="@+id/in_restaurant_info"
        layout="@layout/layout_restaurant_info" />

    <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_feature_motd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:minHeight="@dimen/sw_21dp"
            android:textSize="@dimen/font_size_h6"
            android:singleLine="true"
            android:layout_marginTop="3dp"
            android:layout_marginHorizontal="@dimen/card_side_margin"/>

    <androidx.constraintlayout.widget.ConstraintLayout
       android:id="@+id/lay_restaurant_menu"
       android:layout_width="match_parent"
       android:layout_height="wrap_content">

       <androidx.recyclerview.widget.RecyclerView
           android:id="@+id/rv_restaurant_menu"
           app:layout_constraintLeft_toLeftOf="parent"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintRight_toRightOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           android:paddingRight="@dimen/sw_40dp"
           android:paddingLeft="@dimen/sw_40dp"
           android:clipToPadding="false"
           android:scrollbars="none"
           android:layout_width="match_parent"
           android:layout_height="@dimen/dip_165" />

       <ImageView
           android:id="@+id/iv_restaurant_left"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           app:layout_constraintLeft_toLeftOf="parent"
           android:src="@drawable/ic_left_arrow_wide"
           android:clickable="true"
           android:layout_marginBottom="@dimen/sw_30dp"
           android:paddingLeft="@dimen/sw_20dp"
           android:paddingRight="@dimen/sw_16dp"
           android:layout_width="@dimen/sw_41dp"
           android:layout_height="@dimen/sw_41dp" />

       <ImageView
           android:id="@+id/iv_restaurant_right"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           app:layout_constraintRight_toRightOf="parent"
           android:src="@drawable/ic_right_arrow_wide"
           android:clickable="true"
           android:layout_marginBottom="@dimen/sw_30dp"
           android:paddingLeft="@dimen/sw_16dp"
           android:paddingRight="@dimen/sw_20dp"
           android:layout_width="@dimen/sw_41dp"
           android:layout_height="@dimen/sw_41dp" />

   </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>