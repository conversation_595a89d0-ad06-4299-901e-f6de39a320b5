<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <TextView
        android:id="@+id/tv_divider"
        style="@style/DividerHorizontalStyle"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <LinearLayout
        android:id="@+id/ll_category_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_category_show_more"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_category_description"
        android:layout_marginTop="@dimen/sw_26dp"
        android:layout_marginLeft="@dimen/dip_40"
        android:orientation="horizontal"
        android:gravity="left"
        android:layout_width="0dp"
        android:layout_height="wrap_content" >

        <TextView
            android:id="@+id/tv_category_name"
            android:textSize="@dimen/sw_24sp"
            android:textColor="@color/mainText"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:layout_gravity="center_vertical"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/sw_34dp" />

        <ImageView
            android:id="@+id/iv_category_jump"
            android:src="@drawable/ic_category_arrow_down"
            android:layout_gravity="center_vertical"
            android:paddingTop="4dp"
            android:paddingBottom="6dp"
            android:paddingLeft="2dp"
            android:scaleType="fitStart"
            android:layout_height="@dimen/sw_34dp"
            android:layout_width="@dimen/sw_34dp" />

    </LinearLayout>


    <TextView
        android:id="@+id/tv_category_description"
        app:layout_constraintLeft_toLeftOf="@+id/ll_category_name"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_category_name"
        style="@style/SubTitleStyle"
        android:layout_marginRight="@dimen/dip_40"
        android:gravity="left"
        android:layout_width="0dp"
        android:layout_height="@dimen/sw_22dp" />

    <TextView
        android:id="@+id/tv_category_show_more"
        app:layout_constraintRight_toLeftOf="@+id/iv_category_show_more"
        app:layout_constraintTop_toTopOf="@+id/ll_category_name"
        app:layout_constraintBottom_toBottomOf="@+id/ll_category_name"
        android:text="@string/menu_see_all"
        style="@style/SubTitleStyle"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content" />

    <ImageView
        android:id="@+id/iv_category_show_more"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_category_show_more"
        app:layout_constraintBottom_toBottomOf="@+id/tv_category_show_more"
        android:src="@drawable/ic_right_arrow"
        android:layout_marginRight="@dimen/dip_40"
        android:scaleType="fitXY"
        android:adjustViewBounds="true"
        android:layout_marginBottom="1dp"
        android:layout_height="@dimen/sw_14dp"
        android:layout_width="@dimen/sw_12dp" />

    <TextView
        android:id="@+id/tv_category_bottom_space"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_category_description"
        android:layout_width="0dp"
        android:layout_height="@dimen/sw_13dp" />

    <TextView
        android:id="@+id/tv_category_show_more_click"
        app:layout_constraintLeft_toLeftOf="@+id/tv_category_show_more"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/ll_category_name"
        android:layout_height="0dp"
        android:layout_width="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>