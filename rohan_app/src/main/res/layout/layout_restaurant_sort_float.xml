<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_restaurant_sort"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:layout_marginRight="@dimen/sw_14dp"
        android:layout_marginTop="@dimen/_sw_10dp"
        android:visibility="visible"
        android:alpha="0.0"
        android:clickable="true"
        android:orientation="horizontal"
        android:background="@drawable/button_plate_floating_3d"
        android:minHeight="@dimen/sw_50dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_restaurant_sort"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/iv_restaurant_sort"
            app:layout_constraintHorizontal_chainStyle="packed"
            tools:text="@string/add"
            android:gravity="center"
            android:layout_marginLeft="@dimen/sw_26dp"
            android:layout_marginBottom="@dimen/sw_18dp"
            android:layout_marginTop="@dimen/sw_20dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_restaurant_sort"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toRightOf="@+id/tv_restaurant_sort"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/tv_restaurant_sort"
            app:layout_constraintTop_toTopOf="@+id/tv_restaurant_sort"
            android:layout_marginRight="@dimen/sw_22dp"
            android:src="@drawable/ic_restaurant_sort" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</RelativeLayout>