<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/sw_20dp"
        android:layout_marginTop="@dimen/sw_20dp"
        android:src="@drawable/ic_close"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:context=".features.menu.combo.MenuComboActivity">

            <TextView
                android:id="@+id/tv_food_name"
                style="@style/MenuFoodNameStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/sw_30dp"
                android:layout_marginEnd="@dimen/sw_30dp"
                android:layout_marginTop="20dp"
                android:textSize="@dimen/sw_20sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="name" />

            <TextView
                android:id="@+id/tv_food_info"
                style="@style/SubTitleStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginRight="@dimen/sw_30dp"
                android:lineSpacingExtra="0dp"
                android:textFontWeight="500"
                android:textSize="@dimen/font_size_h6"
                app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_food_name"
                tools:text="name des"
                tools:visibility="visible" />


            <TextView
                android:id="@+id/tv_food_price"
                style="@style/SubItemStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/inputLineText"
                app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_food_info"
                tools:text="20.00" />

            <TextView
                android:id="@+id/tv_food_original_price"
                style="@style/SubItemStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:textColor="@color/inputLineText"
                app:layout_constraintBottom_toBottomOf="@+id/tv_food_price"
                app:layout_constraintLeft_toRightOf="@+id/tv_food_price"
                tools:text="20.00" />

            <TextView
                android:id="@+id/tv_container"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/sw_30dp"
                app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_food_price" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/sw_20dp"
                android:layout_marginRight="@dimen/sw_20dp"
                android:layout_marginBottom="@dimen/sw_50dp"
                android:background="@drawable/bg_restaurant_card"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_container" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>


</LinearLayout>
