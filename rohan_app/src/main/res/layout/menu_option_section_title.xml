<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:paddingRight="@dimen/card_side_margin"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_food_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/dip_10"
            android:layout_marginLeft="@dimen/sw_38dp"
            android:layout_width="@dimen/sw_food_big_width"
            android:layout_height="@dimen/sw_food_big_height"
            tools:src="@drawable/bg_plate" />

        <ImageView
            android:id="@+id/iv_food"
            app:layout_constraintLeft_toLeftOf="@+id/iv_food_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_food_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_food_bg"
            android:layout_width="@dimen/sw_food_big_width"
            android:layout_height="@dimen/sw_food_big_width"  />

        <TextView
            android:id="@+id/tv_food_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginRight="4dp"
            android:layout_marginLeft="@dimen/sw_6dp"
            android:textSize="@dimen/sw_22sp"
            android:textColor="@color/mainText"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_goneMarginLeft="@dimen/sw_48dp"
            app:layout_constraintLeft_toRightOf="@+id/iv_food_bg"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_food_info"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_food_name"
            app:layout_constraintRight_toRightOf="parent"
            style="@style/SubTitleStyle"
            android:textSize="@dimen/font_size_h6"
            android:textFontWeight="500"
            android:maxLines="3"
            android:ellipsize="end"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginTop="4dp"
            android:layout_marginRight="4dp"
            android:lineSpacingExtra="0dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_food_subinfo"
            app:layout_constraintTop_toBottomOf="@+id/tv_food_info"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
            tools:text="20.00"
            android:layout_marginTop="4dp"
            style="@style/AssertiveStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dip_30"
            app:layout_constraintTop_toBottomOf="@+id/tv_food_subinfo"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        style="@style/DividerHorizontalStyle" />

</LinearLayout>
