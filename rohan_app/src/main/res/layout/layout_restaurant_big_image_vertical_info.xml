<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_restaurant_one"
        android:layout_marginHorizontal="@dimen/card_side_margin"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_restaurant_name"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/guideline_tags"
            android:minHeight="26dp"
            android:gravity="center_vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="@string/test_text_length"
            style="@style/LargeTitleStyle" />


        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_tags"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/lay_restaurant_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/guideline_tags" >

            <TextView
                android:id="@+id/tv_restaurant_tags"
                style="@style/RestaurantInfoStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical|right"
                android:maxLines="1"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/btn_restaurant_bundle"
                app:layout_constraintLeft_toLeftOf="parent"
                tools:text="222" />

            <com.ricepo.style.button.ChocolateButton
                android:id="@+id/btn_restaurant_bundle"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_restaurant_tags"
                android:layout_marginLeft="@dimen/sw_4dp"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_restaurant_info"
        style="@style/RestaurantInfoStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:layout_marginHorizontal="@dimen/card_side_margin"
        android:maxLines="1"
        tools:text="111" />

    <TextView
        android:id="@+id/tv_restaurant_sub_info"
        style="@style/RestaurantInfoStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/card_side_margin"
        android:layout_marginTop="6dp"
        android:ellipsize="marquee"
        android:gravity="center_vertical"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:visibility="gone"
        tools:text="@string/test_text_length" />

</LinearLayout>