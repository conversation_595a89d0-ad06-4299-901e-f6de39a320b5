<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tv_restaurant_closed"
    tools:visibility="visible"
    tools:text="@string/test_text_length"
    android:maxLines="1"
    android:ellipsize="end"
    style="@style/FontH7"
    android:background="@drawable/bg_restaurant_closed"
    android:layout_gravity="center"
    android:paddingLeft="@dimen/dip_10"
    android:paddingRight="@dimen/dip_10"
    android:paddingTop="@dimen/dip_3"
    android:paddingBottom="@dimen/dip_3"
    android:layout_marginLeft="@dimen/card_side_margin"
    android:layout_marginRight="@dimen/card_side_margin"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:showIn="@layout/restaurant_item_vertical" />