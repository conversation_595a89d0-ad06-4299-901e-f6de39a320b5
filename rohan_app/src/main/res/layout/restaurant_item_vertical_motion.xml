<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ricepo.style.behavior.CustomMotionLayout
        android:id="@+id/motionLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="true"
        app:motionDebug="NO_DEBUG"
        app:layoutDescription="@xml/motion_restaurant2"
        app:layout_constraintTop_toTopOf="parent" >

        <!-- left hidden -->
        <ImageView
            android:id="@+id/iv_left_hidden_bg"
            android:scaleType="fitEnd"
            tools:src="@drawable/bg_plate"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_left_hidden"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <!-- left -->
        <ImageView
            android:id="@+id/iv_left_bg"
            android:scaleType="fitEnd"
            tools:src="@drawable/bg_plate"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"  />

        <!-- middle -->
        <ImageView
            android:id="@+id/iv_middle_bg"
            android:scaleType="fitEnd"
            tools:src="@drawable/bg_plate"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_middle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <!-- right -->
        <ImageView
            android:id="@+id/iv_right_bg"
            android:scaleType="fitEnd"
            tools:src="@drawable/bg_plate"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

       <!-- right hidden -->
        <ImageView
            android:id="@+id/iv_right_hidden_bg"
            android:scaleType="fitEnd"
            tools:src="@drawable/bg_plate"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_right_hidden"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </com.ricepo.style.behavior.CustomMotionLayout>

    <TextView
        android:id="@+id/tv_restaurant_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="left"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginRight="@dimen/restaurant_text_margin"
        style="@style/LargeTitleStyle" />

    <TextView
        android:id="@+id/tv_restaurant_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginRight="@dimen/restaurant_text_margin"
        style="@style/SubTitleMenuStyle"
        android:layout_marginTop="10dp" />

    <TextView
        android:id="@+id/tv_restaurant_vip_promotion"
        style="@style/SubTitleMenuStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginTop="5dp"
        android:layout_marginRight="@dimen/restaurant_text_margin"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_restaurant_promotion"
        style="@style/SubTitleMenuStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginTop="5dp"
        android:layout_marginRight="@dimen/restaurant_text_margin"
        android:visibility="gone"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/ll_restaurant_match_food"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="8dp" />

</LinearLayout>