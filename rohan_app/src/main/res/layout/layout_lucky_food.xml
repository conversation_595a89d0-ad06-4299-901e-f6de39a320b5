<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_food_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="140dp"
        android:layout_height="140dp"
        tools:src="@drawable/bg_plate" />

    <ImageView
        android:id="@+id/iv_food"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_food_bg"
        android:layout_width="140dp"
        android:layout_height="140dp"  />

    <TextView
        android:id="@id/tv_food_name"
        app:layout_constraintLeft_toRightOf="@+id/iv_food_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_food_bg"
        app:layout_constraintBottom_toTopOf="@+id/tv_food_price"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintVertical_bias="0.1"
        tools:text="蜂蜜玫瑰桃葫芦肉"
        android:textSize="@dimen/font_size_s13"
        android:textColor="@color/subText"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginLeft="@dimen/sw_8dp"
        android:layout_width="100dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@id/tv_food_price"
        app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_food_name"
        app:layout_constraintBottom_toBottomOf="@+id/iv_food_bg"
        android:textColor="@color/subText"
        android:textSize="@dimen/font_size_s13"
        android:layout_marginTop="@dimen/sw_4dp"
        tools:text="20.00"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />


    <ImageView
        android:id="@+id/btn_food_option"
        app:layout_constraintTop_toBottomOf="@id/tv_food_price"
        app:layout_constraintLeft_toLeftOf="@id/tv_food_name"
        android:layout_marginTop="@dimen/sw_4dp"
        android:scaleType="fitCenter"
        android:adjustViewBounds="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />


</androidx.constraintlayout.widget.ConstraintLayout>