<?xml version="1.0" encoding="utf-8"?>

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_pickup_container"
    android:layout_marginBottom="4dp"
    android:layout_height="match_parent"
    android:layout_width="match_parent">

    <include
        android:id="@+id/in_restaurant_vertical"
        layout="@layout/restaurant_item_vertical" />

    <FrameLayout
        android:id="@+id/fl_bundle_tick"
        android:layout_gravity="right|top"
        android:layout_marginRight="50dp"
        android:layout_marginTop="20dp"
        android:layout_width="40dp"
        android:layout_height="40dp">

<!--        <com.ricepo.style.round.RoundLayout-->
<!--            android:id="@+id/round_bundle_tick"-->
<!--            app:stroke_width="1dp"-->
<!--            app:stroke_color="@color/restaurant_see_all_shadow"-->
<!--            app:round_corner="20dp"-->
<!--            android:layout_width="40dp"-->
<!--            android:layout_height="40dp" />-->

        <com.ricepo.style.round.RoundLayout
            android:id="@+id/round_bundle_tick_placeholder"
            app:stroke_width="0dp"
            app:round_corner="19dp"
            android:background="@color/card_background"
            android:layout_margin="0.5dp"
            android:layout_width="39dp"
            android:layout_height="39dp" />

        <ImageView
            android:id="@+id/iv_bundle_tick"
            android:src="@drawable/ic_plus_sign"
            android:scaleType="fitCenter"
            android:layout_gravity="center"
            android:layout_width="40dp"
            android:layout_height="40dp" />

    </FrameLayout>

</FrameLayout>

