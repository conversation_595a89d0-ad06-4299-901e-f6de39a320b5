<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_back" />

    <com.ricepo.style.view.StateNestedScrollView
        android:id="@+id/nsv_restaurant_list"
        app:layout_constraintTop_toBottomOf="@+id/lay_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:fillViewport="true"
        android:layout_width="match_parent"
        android:layout_height="0dp" >

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_restaurant_search"
                style="@style/SearchStyle"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:layout_marginTop="30dp"
                android:layout_marginLeft="@dimen/dip_40"
                android:layout_marginRight="@dimen/dip_40"
                android:hint="@string/search_rest"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <!-- drawable unrestricted expansion in pixel 2 xl -->
            <ImageView
                android:id="@+id/iv_search_tag_bg"
                android:layout_alignLeft="@+id/tcl_search_tag"
                android:layout_alignRight="@+id/tcl_search_tag"
                android:layout_alignBottom="@+id/tcl_search_tag"
                android:layout_below="@+id/et_restaurant_search"
                android:background="@drawable/bg_search_keyword"
                android:layout_marginTop="10dp"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <com.ricepo.style.tag.TagCloudView
                android:id="@+id/tcl_search_tag"
                android:layout_below="@+id/et_restaurant_search"
                android:layout_marginTop="48dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <TextView
                android:id="@+id/tv_search_empty"
                android:layout_alignLeft="@+id/et_restaurant_search"
                android:layout_alignRight="@+id/et_restaurant_search"
                android:layout_below="@+id/et_restaurant_search"
                style="@style/TitleStyle"
                android:alpha="@fraction/medium3"
                android:text="@string/search_not_found"
                android:layout_marginTop="20dp"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <!-- need after tag layout otherwise click item is invalid -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_restaurant_prediction"
                android:layout_below="@+id/et_restaurant_search"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                android:layout_marginTop="1dp"
                android:visibility="gone"
                android:layout_marginLeft="@dimen/dip_40"
                android:layout_marginRight="@dimen/dip_40"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_restaurant_list"
                android:layout_below="@+id/et_restaurant_search"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                android:layout_marginTop="20dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </RelativeLayout>

    </com.ricepo.style.view.StateNestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>