<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/sw_295dp"
    android:layout_height="@dimen/sw_317dp"
    android:layout_marginLeft="@dimen/sw_card_margin"
    android:layout_marginTop="@dimen/sw_12dp"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <FrameLayout
        android:layout_width="@dimen/sw_295dp"
        android:layout_height="@dimen/sw_175dp">

        <com.ricepo.style.round.RoundImageView
            android:id="@+id/big_image"
            android:layout_width="@dimen/sw_295dp"
            android:layout_height="@dimen/sw_175dp"
            android:scaleType="centerCrop"
            app:round_corner_top_left="12dp"
            app:round_corner_top_right="12dp" />

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal">

            <include
                android:id="@+id/in_restaurant_closed"
                layout="@layout/layout_restaurant_closed" />

        </FrameLayout>

        <com.ricepo.base.view.RicePoolHomeView
            android:id="@+id/rtv_pool"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/restaurant_pool_height"
            android:layout_gravity="end"
            android:layout_marginTop="@dimen/sw_15dp"
            android:visibility="gone"
            tools:visibility="visible" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/tv_feature_motd"
            style="@style/MotdLabelStyle"
            android:minHeight="@dimen/sw_21dp"
            android:textSize="@dimen/font_size_h6"
            android:backgroundTint="@color/shape_color"
            android:singleLine="true"
            />

    </FrameLayout>

    <include
        android:id="@+id/in_restaurant_info"
        layout="@layout/layout_restaurant_big_image_horizontal_info" />

</LinearLayout>