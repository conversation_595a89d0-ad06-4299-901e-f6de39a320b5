<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/new_card"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_close"
        app:layout_constraintTop_toTopOf="@id/iv_close" />

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/et_focus_new_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="10dp"
        android:focusable="false"
        android:cursorVisible="false"
        style="@style/EditTextStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/input_new_card"/>

    <com.stripe.android.view.CardInputWidget
        android:id="@+id/input_new_card"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/EditTextNoBottomLineStyle"
        app:cardHintText="4242 4242 4242 4242"
        android:layout_marginTop="60dp"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_add_card"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/input_new_card"
        app:layout_constraintLeft_toLeftOf="@+id/input_new_card"
        app:layout_constraintRight_toRightOf="@+id/input_new_card"
        android:text="@string/add"
        style="@style/ButtonPrimaryStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>