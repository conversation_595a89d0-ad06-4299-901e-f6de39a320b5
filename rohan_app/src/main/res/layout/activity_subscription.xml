<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_close" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsl_subscription_content"
        app:layout_constraintTop_toBottomOf="@+id/lay_title"
        app:layout_constraintBottom_toTopOf="@id/tv_divider_subscription"
        android:layout_width="match_parent"
        android:layout_height="0dp" >

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/tv_subscription_icon"
                    android:layout_width="match_parent"
                    android:layout_height="280dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:scaleType="centerInside"
                    android:visibility="gone"
                    android:src="@drawable/ic_membership_info"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_subscription_title"
                    app:layout_constraintTop_toBottomOf="@+id/tv_subscription_icon"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    style="@style/LargeTitleStyle"
                    android:textSize="@dimen/font_size_h1"
                    android:gravity="center"
                    android:layout_marginTop="@dimen/sw_8dp"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_subscription_subtitle"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_subscription_title"
                    style="@style/SubTitleStyle"
                    android:textColor="@color/goldSubText"
                    android:gravity="center"
                    android:layout_marginTop="@dimen/sw_5dp"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_subscription_description"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_subscription_subtitle"
                    app:layout_constraintBottom_toBottomOf="parent"
                    style="@style/ItemStyle"
                    android:layout_marginTop="20dp"
                    android:layout_marginBottom="30dp"
                    android:gravity="center"
                    android:lineSpacingExtra="1dp"
                    android:lineSpacingMultiplier="1.1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <View
                    android:id="@+id/divider"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_subscription_subtitle"
                    android:layout_marginTop="@dimen/sw_41dp"
                    style="@style/DividerHorizontalStyle" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_subscription_details"
                    app:layout_constraintTop_toBottomOf="@+id/divider"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    android:layout_marginTop="@dimen/sw_20dp"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:layout_marginRight="@dimen/sw_26dp"
                android:layout_marginLeft="@dimen/sw_26dp"
                android:layout_marginTop="@dimen/sw_36dp"
                android:background="@drawable/bg_restaurant_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <include
                    android:id="@+id/in_checkout_payment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/sw_24dp"
                    android:layout_marginRight="@dimen/sw_24dp"
                    layout="@layout/item_checkout_payment" />

            </FrameLayout>


            <include
                android:id="@+id/in_checkout_policy"
                layout="@layout/item_checkout_policy" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tv_divider_subscription"
        app:layout_constraintBottom_toTopOf="@+id/btn_subscription"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginBottom="@dimen/sw_bottom_bar_divider_margin_bottom"
        style="@style/DividerHorizontalStyle" />

    <TextView
        android:id="@+id/btn_subscription"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/button_checkout_gold"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginBottom="@dimen/sw_bottom_bar_margin_bottom"
        android:lineSpacingExtra="6dp"
        android:layout_width="0dp"
        android:layout_height="@dimen/sw_bottom_bar_button_height" />

    <TextView
        android:id="@+id/tv_subscription_price"
        app:layout_constraintTop_toTopOf="@+id/btn_subscription"
        app:layout_constraintBottom_toBottomOf="@+id/btn_subscription"
        app:layout_constraintRight_toRightOf="@+id/btn_subscription"
        android:textSize="@dimen/font_size_s19"
        android:textColor="@color/white"
        android:text="$0"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_subscription_place"
        app:layout_constraintTop_toTopOf="@+id/btn_subscription"
        app:layout_constraintBottom_toBottomOf="@+id/btn_subscription"
        app:layout_constraintLeft_toLeftOf="@+id/btn_subscription"
        android:textSize="@dimen/font_size_s19"
        android:textColor="@color/white"
        android:layout_marginLeft="20dp"
        android:layout_marginBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>