<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingBottom="@dimen/sw_10dp"
    android:paddingLeft="@dimen/sw_20dp"
    android:paddingRight="@dimen/sw_20dp"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <!-- dish -->
    <ImageView
        android:id="@+id/iv_dish_food_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/dip_15"
        android:layout_width="@dimen/sw_food_big_width"
        android:layout_height="@dimen/sw_food_big_height"
        android:scaleType="fitXY"
        android:adjustViewBounds="true"
        tools:src="@drawable/bg_plate" />

    <ImageView
        android:id="@+id/iv_dish_food"
        app:layout_constraintLeft_toLeftOf="@+id/iv_dish_food_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_dish_food_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_dish_food_bg"
        android:layout_width="@dimen/sw_food_big_width"
        android:layout_height="@dimen/sw_food_big_width"  />

    <TextView
        android:id="@+id/tv_dish_food_name"
        app:layout_constraintLeft_toLeftOf="@+id/iv_dish_food_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_dish_food_bg"
        app:layout_constraintTop_toBottomOf="@+id/iv_dish_food"
        android:layout_marginTop="@dimen/sw_25dp"
        android:layout_marginLeft="@dimen/sw_5dp"
        android:layout_marginRight="@dimen/sw_5dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="left"
        android:textColor="@color/mainText"
        android:textSize="@dimen/font_size_s15"
        android:lineSpacingMultiplier="0.9" />

    <TextView
        android:id="@+id/tv_dish_restaurant_name"
        app:layout_constraintLeft_toLeftOf="@+id/tv_dish_food_name"
        app:layout_constraintRight_toRightOf="@+id/tv_dish_food_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_dish_food_name"
        android:layout_marginTop="@dimen/dip_5"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="left"
        android:lineSpacingMultiplier="0.9"
        style="@style/RestaurantInfoStyle" />

    <LinearLayout
        android:id="@+id/lay_dish_food_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="@+id/tv_dish_food_name"
        app:layout_constraintRight_toRightOf="@+id/tv_dish_food_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_dish_restaurant_name"
        android:layout_marginTop="@dimen/dip_5">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_dish_food_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:textSize="@dimen/sw_11sp"
            style="@style/SubLabelStyle" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_dish_food_original_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_1"
            android:gravity="left"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="@dimen/sw_9sp"
            style="@style/SubLabelStyle" />

    </LinearLayout>



<!--    <TextView-->
<!--        android:id="@+id/btn_dish_button"-->
<!--        app:layout_constraintTop_toTopOf="@+id/tv_dish_button"-->
<!--        app:layout_constraintBottom_toBottomOf="@+id/tv_dish_button"-->
<!--        app:layout_constraintLeft_toLeftOf="@+id/tv_dish_button"-->
<!--        app:layout_constraintRight_toRightOf="@+id/iv_dish_arrow"-->
<!--        android:layout_marginRight="@dimen/dip_10"-->
<!--        style="@style/ButtonSecondaryStyle"-->
<!--        android:minHeight="32dp"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="wrap_content" />-->

<!--    <TextView-->
<!--        android:id="@+id/tv_dish_button"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:textSize="@dimen/font_size_h6"-->
<!--        android:maxWidth="90dp"-->
<!--        android:textColor="@drawable/button_secondary_color"-->
<!--        android:layout_marginTop="68dp"-->
<!--        android:paddingLeft="@dimen/dip_8"-->
<!--        android:maxLines="1"-->
<!--        android:ellipsize="end"-->
<!--        tools:text="@string/try_other_restaurant"-->
<!--        app:layout_constraintLeft_toLeftOf="@+id/tv_dish_food_name"-->
<!--        app:layout_constraintTop_toBottomOf="@+id/iv_dish_food_bg"-->
<!--        app:layout_constraintBottom_toBottomOf="parent" />-->

<!--    <ImageView-->
<!--        android:id="@+id/iv_dish_arrow"-->
<!--        app:layout_constraintTop_toTopOf="@+id/tv_dish_button"-->
<!--        app:layout_constraintBottom_toBottomOf="@+id/tv_dish_button"-->
<!--        app:layout_constraintLeft_toRightOf="@+id/tv_dish_button"-->
<!--        android:paddingTop="3dp"-->
<!--        android:paddingBottom="3dp"-->
<!--        android:layout_marginRight="@dimen/dip_30"-->
<!--        android:src="@drawable/ic_right_arrow"-->
<!--        android:scaleType="fitStart"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="0dp" />-->

<!--    <androidx.constraintlayout.widget.Group-->
<!--        android:id="@+id/group_banner_button"-->
<!--        app:constraint_referenced_ids="btn_dish_button, tv_dish_button, iv_dish_arrow"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content" />-->


</androidx.constraintlayout.widget.ConstraintLayout>