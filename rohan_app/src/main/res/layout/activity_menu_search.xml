<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_title"
        android:orientation="vertical"
        android:layout_alignParentTop="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_title_icon"
            android:src="@drawable/ic_back"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@id/tv_title"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:includeFontPadding="false"
            android:gravity="center"
            android:paddingLeft="@dimen/margin_left"
            android:layout_marginRight="40dp"
            android:singleLine="true"
            android:ellipsize="end"
            style="@style/TitleStyle"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/iv_back"
            app:layout_constraintTop_toTopOf="@id/iv_back" />

        <com.ricepo.base.view.RicePoolView
            android:id="@+id/rtv_pool"
            app:layout_constraintLeft_toRightOf="@+id/iv_back"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintTop_toTopOf="@+id/iv_back"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="@dimen/dip_40"
            tools:visibility="visible"
            android:visibility="gone"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.ricepo.style.view.StateNestedScrollView
        android:id="@+id/nsv_restaurant_list"
        android:layout_below="@+id/lay_title"
        android:layout_alignParentBottom="true"
        android:fillViewport="true"
        android:layout_width="match_parent"
        android:layout_height="0dp" >

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_menu_search"
                style="@style/SearchStyle"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:layout_marginTop="30dp"
                android:layout_marginLeft="@dimen/dip_40"
                android:layout_marginRight="@dimen/dip_40"
                android:hint="@string/search_menu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_menu"
                android:layout_below="@+id/et_menu_search"
                android:layout_marginTop="5dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </RelativeLayout>

    </com.ricepo.style.view.StateNestedScrollView>


    <include
        android:id="@+id/in_menu_cart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        layout="@layout/layout_menu_cart_bar" />

    <FrameLayout
        android:id="@+id/fl_menu_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        app:layout_anchorGravity="center" />

</RelativeLayout>