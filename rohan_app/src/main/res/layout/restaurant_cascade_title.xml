<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_title_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="Popular\n Food"
        android:textSize="@dimen/sw_24sp"
        android:autoSizeTextType="uniform"
        android:autoSizeMinTextSize="12sp"
        android:autoSizeMaxTextSize="@dimen/sw_24sp"
        android:autoSizeStepGranularity="1sp"
        android:textColor="@color/mainText"
        android:maxLines="2"
        android:layout_marginTop="@dimen/sw_56dp"
        android:layout_marginLeft="@dimen/sw_26dp"
        android:layout_width="@dimen/sw_90dp"
        android:layout_marginRight="@dimen/sw_18dp"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>