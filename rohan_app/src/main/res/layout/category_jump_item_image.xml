<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingTop="@dimen/sw_16dp"
    android:paddingBottom="@dimen/sw_14dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_jump_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="@dimen/sw_32dp"
        android:layout_height="@dimen/sw_41dp"
        tools:src="@drawable/bg_plate" />

    <ImageView
        android:id="@+id/iv_jump"
        app:layout_constraintLeft_toLeftOf="@+id/iv_jump_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_jump_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_jump_bg"
        android:layout_width="@dimen/sw_32dp"
        android:layout_height="@dimen/sw_32dp"  />

    <TextView
        android:id="@+id/tv_jump_name"
        app:layout_constraintTop_toTopOf="@+id/iv_jump_bg"
        app:layout_constraintLeft_toRightOf="@+id/iv_jump_bg"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        android:textColor="@color/subText"
        android:textSize="@dimen/sw_13dp"
        android:layout_marginLeft="@dimen/sw_2dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>