<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_restaurant_recommend_divider"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        style="@style/DividerHorizontalStyle" />

    <LinearLayout
        android:id="@+id/ll_random_menu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dip_30"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/motion_lucky_menu"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_feeling_lucky"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/mainText"
            android:textSize="@dimen/font_size_h2"
            android:shadowRadius="1.0"
            android:shadowColor="@android:color/black"
            android:shadowDx="1.0"
            android:shadowDy="3.0"
            android:gravity="left" />

        <TextView
            android:id="@+id/tv_start_now"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_size_h8"
            android:layout_marginTop="5dp"
            android:drawablePadding="@dimen/order_fees_margin_end"
            android:visibility="gone"
            app:drawableEndCompat="@drawable/ic_lucky_right" />

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_lucky"
        android:layout_width="54dp"
        android:layout_height="54dp"
        app:layout_constraintBottom_toBottomOf="@+id/ll_random_menu"
        app:layout_constraintLeft_toRightOf="@id/ll_random_menu"
        app:layout_constraintTop_toTopOf="@+id/ll_random_menu" />

    <ImageView
        android:id="@+id/iv_lucky_refresh"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:padding="12dp"
        android:layout_marginRight="30dp"
        android:src="@drawable/ic_lucky_refresh"
        app:layout_constraintBottom_toBottomOf="@+id/ll_random_menu"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ll_random_menu" />


    <androidx.constraintlayout.motion.widget.MotionLayout
        android:id="@+id/motion_lucky_menu"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layoutDescription="@xml/motion_lucky_menu"
        app:applyMotionScene="true"
        android:layout_width="0dp"
        android:layout_height="@dimen/dip_300">

        <ImageView
            android:id="@+id/iv_lucky_no_card"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="20dp"
            android:src="@drawable/ic_lucky_no_card"
            android:layout_width="190dp"
            android:layout_height="110dp" />

        <TextView
            android:id="@+id/tv_lucky_no_card"
            android:text="@string/lucky_no_card"
            style="@style/SubLabelStyle"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_lucky_no_card"
            android:layout_marginTop="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_lucky_no_card_bg"
            app:layout_constraintLeft_toLeftOf="@+id/iv_lucky_no_card_refresh"
            app:layout_constraintRight_toRightOf="@+id/tv_lucky_no_card_refresh"
            app:layout_constraintTop_toTopOf="@+id/iv_lucky_no_card_refresh"
            app:layout_constraintBottom_toBottomOf="@+id/iv_lucky_no_card_refresh"
            android:background="@drawable/button_secondary"
            android:clickable="true"
            android:layout_width="0dp"
            android:layout_height="0dp" />

        <ImageView
            android:id="@+id/iv_lucky_no_card_refresh"
            android:layout_width="34dp"
            android:layout_height="44dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="10dp"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:paddingLeft="10dp"
            android:src="@drawable/ic_lucky_refresh"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintTop_toBottomOf="@+id/tv_lucky_no_card"
            app:layout_constraintRight_toLeftOf="@+id/tv_lucky_no_card_refresh"
            app:layout_constraintLeft_toLeftOf="parent" />

        <TextView
            android:id="@+id/tv_lucky_no_card_refresh"
            android:text="@string/lucky_refresh"
            style="@style/SubLabelStyle"
            app:layout_constraintLeft_toRightOf="@+id/iv_lucky_no_card_refresh"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_lucky_no_card_refresh"
            app:layout_constraintBottom_toBottomOf="@+id/iv_lucky_no_card_refresh"
            android:paddingRight="10dp"
            android:layout_marginBottom="3dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_lucky_menu"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/sw_section_space"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />


    </androidx.constraintlayout.motion.widget.MotionLayout>


</androidx.constraintlayout.widget.ConstraintLayout>