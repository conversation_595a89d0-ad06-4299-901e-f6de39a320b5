<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_restaurant_card"
    android:visibility="gone"
    tools:visibility="visible"
    android:layout_marginLeft="@dimen/card_side_margin"
    android:layout_marginRight="@dimen/card_side_margin"
    android:layout_marginBottom="@dimen/sw_section_space"
    android:padding="@dimen/dip_20"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_order_address"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constrainedWidth="true"
        style="@style/ItemStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_order_phone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_order_address"
        app:layout_constrainedWidth="true"
        style="@style/LabelItemStyle"
        android:layout_marginTop="14dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_order_payment_method"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_order_phone"
        android:layout_marginTop="14dp"
        android:visibility="gone"
        android:layout_width="60dp"
        android:layout_height="30dp" />

    <TextView
        android:id="@+id/tv_order_payment_method"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_order_payment_method"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constrainedWidth="true"
        style="@style/LabelItemStyle"
        android:layout_marginTop="14dp"
        android:layout_marginBottom="5dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />


</androidx.constraintlayout.widget.ConstraintLayout>