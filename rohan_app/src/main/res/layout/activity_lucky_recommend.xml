<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="bean"
            type="com.ricepo.app.features.luckymenu.data.LuckyModel" />

        <variable
            name="viewmodel"
            type="com.ricepo.app.features.luckymenu.LuckyRecommendViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".features.luckymenu.LuckyRecommendActivity">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dip_10"
            android:layout_marginTop="@dimen/dip_10"
            android:padding="@dimen/dip_10"
            android:src="@drawable/ic_back"
            android:tint="@color/fun_n6"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_lucky_restaurant"
            style="@style/TitleStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_5"
            android:text="@{viewmodel.luckyModelCommand.restaurantName}"
            android:textSize="@dimen/font_size_s16"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="@string/test_text_length"
            android:layout_marginRight="@dimen/dip_40"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/iv_back"
            app:layout_constraintTop_toBottomOf="@+id/iv_back" />

        <TextView
            android:id="@+id/tv_lucky_tags"
            style="@style/SubTitleMenuStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_10"
            android:layout_marginEnd="@dimen/dip_10"
            android:maxLines="1"
            android:ellipsize="end"
            android:text="@{viewmodel.luckyModelCommand.restaurantTags}"
            android:visibility="@{viewmodel.luckyModelCommand.restaurantTags.length()>0?View.VISIBLE:View.GONE}"
            android:layout_marginRight="@dimen/dip_40"
            tools:text="@string/test_text_length"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toLeftOf="@+id/tv_lucky_restaurant"
            app:layout_constraintTop_toBottomOf="@+id/tv_lucky_restaurant" />

        <TextView
            android:id="@+id/tv_divider"
            style="@style/DividerHorizontalStyle"
            android:layout_marginTop="@dimen/dip_15"
            app:layout_constraintLeft_toLeftOf="@+id/tv_lucky_tags"
            app:layout_constraintRight_toRightOf="@+id/tv_lucky_tags"
            app:layout_constraintTop_toBottomOf="@+id/tv_lucky_tags" />


        <com.ricepo.app.features.luckymenu.view.RandomLayout
            android:id="@+id/lucky_menu"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/bg_lucky_menu_v1"
            app:layout_constraintBottom_toTopOf="@+id/tv_divider_bottom"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_divider" />

        <ImageView
            android:id="@+id/iv_loading"
            android:layout_width="@dimen/dip_160"
            android:layout_height="@dimen/dip_100"
            android:src="@drawable/dice_animation"
            android:visibility="@{viewmodel.refresh ? View.VISIBLE : View.GONE}"
            tools:visibility="visible"
            app:animation="@{viewmodel.refresh}"
            app:layout_constraintTop_toBottomOf="@+id/tv_divider"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tv_white_bg" />


        <TextView
            android:id="@+id/tv_lucky_delivery"
            style="@style/TitleStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dip_5"
            android:layout_marginRight="@dimen/dip_10"
            android:layout_marginBottom="@dimen/dip_10"
            android:text="@{viewmodel.luckyModelCommand.deliveryText}"
            android:textColor="@color/green2"
            android:textSize="@dimen/font_size_s12"
            app:layout_constraintBottom_toTopOf="@+id/tv_white_bg"
            app:layout_constraintRight_toRightOf="parent" />


        <TextView
            android:id="@+id/tv_white_bg"
            android:layout_width="match_parent"
            android:layout_height="@dimen/sw_bottom_bar_height"
            android:background="@color/colorPrimary"
            android:textSize="@dimen/font_size_s20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <TextView
            android:id="@+id/tv_divider_bottom"
            style="@style/DividerHorizontalStyle"
            app:layout_constraintTop_toTopOf="@+id/tv_white_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <RelativeLayout
            android:id="@+id/rl_shopping_car"
            android:layout_width="match_parent"
            android:layout_height="@dimen/sw_bottom_bar_button_height"
            android:layout_marginLeft="@dimen/dip_20"
            android:layout_marginRight="@dimen/dip_20"
            android:layout_marginBottom="@dimen/sw_bottom_bar_margin_bottom"
            android:paddingBottom="3dp"
            android:alpha="@{viewmodel.emptySelectedFood ? 1f : 0.5f}"
            android:background="@drawable/button_checkout_red"
            android:gravity="center_vertical"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" >

            <TextView
                android:id="@+id/tv_price"
                style="@style/TitleStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginLeft="@dimen/dip_20"
                android:layout_marginRight="@dimen/dip_20"
                android:text="@{viewmodel.luckyModelCommand.price}"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_s19" />

            <TextView
                android:id="@+id/tv_shopping_car"
                style="@style/TitleStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="@dimen/dip_20"
                android:layout_marginRight="@dimen/dip_20"
                android:gravity="center"
                android:text="@string/add_cart"
                android:textColor="@color/white"
                android:textSize="@dimen/font_size_s19" />
        </RelativeLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
