<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<!--    <include layout="@layout/layout_header_close" />-->

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tl_title"
        android:background="@color/colorPrimary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:padding="@dimen/padding_title_icon"
                app:srcCompat="@drawable/ic_back"
                android:tint="@color/fun_n6"
                />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:gravity="center_vertical"
                style="@style/TitleStyle"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/iv_back"
                app:layout_constraintTop_toTopOf="@id/iv_back" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>


    <FrameLayout
        android:id="@+id/frame_chat"
        android:background="#888"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

        <WebView
            android:id="@+id/wv_chat"
            android:alpha="0"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </FrameLayout>



</LinearLayout>