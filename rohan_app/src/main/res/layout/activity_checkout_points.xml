<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <TextView
        android:id="@+id/tv_point"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/redeem_ricepo_point"
        android:textColor="@color/subText"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_close"
        app:layout_constraintTop_toTopOf="@id/iv_close" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_points"
        android:layout_marginTop="@dimen/dip_40"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_close">

        <TextView
            android:id="@+id/tv_current_point"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:textColor="@color/subText"
            android:includeFontPadding="false"
            android:gravity="center_vertical"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_comment_right_end"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:focusableInTouchMode="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/et_points" />

        <TextView
            android:id="@+id/tv_points"
            app:layout_constraintTop_toBottomOf="@+id/tv_current_point"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            style="@style/RegularStyle"
            android:clickable="true"
            android:layout_marginTop="@dimen/sw_33dp"
            android:layout_marginLeft="@dimen/dip_40"
            android:layout_marginRight="@dimen/dip_40"
            android:layout_width="0dp"
            android:layout_height="40dp" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/et_points"
            app:layout_constraintBottom_toBottomOf="@+id/tv_points"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/guideline_half"
            android:textColor="@color/mainText"
            android:textSize="@dimen/font_size_s15"
            android:background="@null"
            android:layout_marginBottom="@dimen/dip_15"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="4dp"
            android:gravity="right"
            android:singleLine="true"
            android:ellipsize="end"
            android:scrollHorizontally="false"
            android:imeOptions="actionSearch"
            android:inputType="number"
            android:hint="0"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_half"
            app:layout_constraintGuide_percent="0.5"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_points_label"
            app:layout_constraintBottom_toBottomOf="@+id/tv_points"
            app:layout_constraintLeft_toRightOf="@+id/guideline_half"
            android:layout_marginBottom="@dimen/dip_15"
            android:textColor="@color/mainText"
            android:textSize="@dimen/font_size_s15"
            android:text="@string/redeem_points"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_point_value"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:textColor="@color/subText"
            android:includeFontPadding="false"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/dip_25"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_points"  />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save_comment"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_point_value"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:text="@string/ok"
            style="@style/ButtonPrimaryStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login"
        android:layout_marginTop="28dp"
        android:stateListAnimator="@null"
        android:visibility="gone"
        style="@style/ButtonSecondaryStyle"
        app:layout_constraintTop_toBottomOf="@+id/tv_point"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>