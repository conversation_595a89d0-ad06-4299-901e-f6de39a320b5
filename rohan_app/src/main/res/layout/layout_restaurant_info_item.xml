<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/RestaurantInfoStyle"
        android:gravity="left"
        android:layout_marginTop="10dp"
        tools:text="@string/test_text_length"
        android:layout_marginLeft="@dimen/sw_22dp"
        android:layout_marginRight="@dimen/sw_22dp"
        tools:showIn="@layout/menu_restaurant_item_container" />

</FrameLayout>
