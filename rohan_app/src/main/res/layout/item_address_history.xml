<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:paddingTop="10dp"
    android:paddingBottom="10dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        style="@style/ItemStyle"
        android:id="@+id/tv_address_name"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/text_left_padding"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_height="wrap_content"
        android:layout_width="match_parent">
    </TextView>

    <TextView
        android:id="@+id/tv_address_unit"
        android:gravity="center_vertical"
        android:textColor="@color/subText"
        android:textSize="@dimen/font_size_h6"
        android:paddingLeft="@dimen/text_left_padding"
        android:maxLines="1"
        android:ellipsize="end"
        style="@style/SubTitleStyle"
        android:layout_height="wrap_content"
        android:layout_width="match_parent">

    </TextView>

</LinearLayout>