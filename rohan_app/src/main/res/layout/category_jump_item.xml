<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:paddingLeft="@dimen/dip_15"
    android:paddingRight="@dimen/dip_15"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_divider"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="0dp"
        android:layout_height="2dp"
        style="@style/RestaurantDividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_jump_category"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:textColor="@color/mainText"
        android:textSize="@dimen/font_size_s15"
        android:gravity="left"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginTop="@dimen/dip_15"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_jump_category"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="left"
        android:maxLines="2"
        android:ellipsize="end"
        android:visibility="gone"
        android:layout_marginRight="5dp"
        android:layout_width="@dimen/sw_20dp"
        android:layout_height="@dimen/sw_20dp" />

    <LinearLayout
        android:id="@+id/ll_jump_image"
        app:layout_constraintLeft_toRightOf="@+id/tv_jump_category"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:orientation="horizontal"
        android:gravity="left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>