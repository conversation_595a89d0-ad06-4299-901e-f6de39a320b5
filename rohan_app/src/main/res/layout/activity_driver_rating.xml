<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <include layout="@layout/layout_header_close"/>

    <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/bottom_back"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lay_title">

        <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="24dp"
                android:orientation="vertical">

            <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:cardElevation="0dp"
                    app:cardCornerRadius="12dp">

                <androidx.appcompat.widget.LinearLayoutCompat
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                    <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                        <TextView
                                android:id="@+id/tv_rate_driver_title"
                                style="@style/LargeTitleStyle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/hows_your_delivery"/>

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                style="@style/SubTitleStyle"
                                android:text="@string/anonymous"/>

                    </LinearLayout>

                    <View
                            android:background="@color/background"
                            android:layout_width="match_parent"
                            android:layout_marginVertical="12dp"
                            android:layout_height="@dimen/divider_height"/>

                    <com.google.android.material.chip.ChipGroup
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            app:singleSelection="true"
                            app:chipSpacingHorizontal="30dp"
                            android:gravity="center">

                        <com.google.android.material.chip.Chip
                                android:id="@+id/btn_rate_like"
                                style="@style/Widget.MaterialComponents.Chip.Action"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/like"
                                android:checkable="true"
                                app:chipIcon="@drawable/ic_like"
                                android:textSize="@dimen/font_size_h4"
                                app:chipStartPadding="20dp"
                                app:chipEndPadding="20dp"
                                app:chipMinHeight="40dp"
                                android:textColor="@color/chip_like_text_color"
                                app:chipBackgroundColor="@color/chip_color"
                                app:checkedIcon="@drawable/ic_like_select"
                        />

                        <com.google.android.material.chip.Chip
                                android:id="@+id/btn_rate_dislike"
                                style="@style/Widget.MaterialComponents.Chip.Action"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/dislike"
                                android:checkable="true"
                                app:chipStartPadding="20dp"
                                app:chipEndPadding="20dp"
                                app:chipMinHeight="40dp"
                                app:chipIcon="@drawable/ic_unlike"
                                android:textSize="@dimen/font_size_h4"
                                android:textColor="@color/chip_unlike_text_color"
                                app:chipBackgroundColor="@color/chip_color"
                                app:checkedIcon="@drawable/ic_unlike_select"
                        />

                    </com.google.android.material.chip.ChipGroup>

                    <com.google.android.material.chip.ChipGroup
                            android:id="@+id/ll_rate_container"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:animateLayoutChanges="true"
                        />


                </androidx.appcompat.widget.LinearLayoutCompat>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardElevation="0dp"
                    android:layout_marginTop="24dp"
                    app:cardCornerRadius="12dp">
                <androidx.appcompat.widget.LinearLayoutCompat
                        android:layout_width="wrap_content"
                        android:orientation="vertical"
                        android:paddingHorizontal="20dp"
                        android:paddingVertical="12dp"
                        android:layout_height="wrap_content">
                    <LinearLayout
                            android:orientation="horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                        <TextView
                                android:layout_weight="1"
                                style="@style/LargeTitleStyle"
                                android:text="@string/rating_dishes_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        <TextView
                                android:text="@string/anonymous"
                                style="@style/SubTitleStyle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                    </LinearLayout>

                    <View
                            android:background="@color/background"
                            android:layout_width="match_parent"
                            android:layout_marginVertical="12dp"
                            android:layout_height="@dimen/divider_height"/>

                    <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/itemsRecyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:overScrollMode="never"
                            android:paddingVertical="12dp"/>

                </androidx.appcompat.widget.LinearLayoutCompat>

            </androidx.cardview.widget.CardView>


        </androidx.appcompat.widget.LinearLayoutCompat>


    </androidx.core.widget.NestedScrollView>

    <androidx.cardview.widget.CardView
            android:id="@+id/bottom_back"
            android:layout_width="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_height="88dp"/>

    <androidx.cardview.widget.CardView
            android:id="@+id/btn_rate_submit"
            android:layout_width="0dp"
            android:layout_height="36dp"
            app:cardBackgroundColor="@color/mr"
            android:layout_marginHorizontal="20dp"
            android:layout_marginBottom="40dp"
            android:paddingBottom="3dp"
            android:text="@string/submit"
            app:cardCornerRadius="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">
        <TextView
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:textColor="@color/w"
                android:text="@string/submit"
        />
    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>