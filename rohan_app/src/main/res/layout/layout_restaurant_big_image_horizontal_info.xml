<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_restaurant_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_restaurant_name"
            style="@style/TitleMenuStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:minHeight="26dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/guideline_tags"
            tools:text="@string/test_text_length" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_tags"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/tv_restaurant_tags"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical|right"
            android:maxLines="1"
            android:layout_weight="1"
            android:ellipsize="end"
            style="@style/RestaurantInfoStyle"
            app:layout_constraintLeft_toRightOf="@+id/guideline_tags"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="222" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_restaurant_info"
        style="@style/RestaurantInfoStyle"
        android:layout_marginHorizontal="16dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        tools:text="111" />

    <TextView
        android:id="@+id/tv_restaurant_sub_info"
        style="@style/RestaurantInfoStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="3dp"
        android:ellipsize="marquee"
        android:gravity="center_vertical"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:visibility="gone"
        tools:text="@string/test_text_length" />

</LinearLayout>