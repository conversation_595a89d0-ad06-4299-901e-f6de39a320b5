<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_close" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsl_refer_content"
        app:layout_constraintTop_toBottomOf="@+id/lay_title"
        app:layout_constraintBottom_toTopOf="@id/tv_divider_refer"
        android:layout_width="match_parent"
        android:layout_height="0dp" >

        <LinearLayout
            android:id="@+id/ll_refer_info"
            android:visibility="gone"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_refer_icon"
                    android:layout_width="match_parent"
                    android:layout_height="280dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_refer"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_refer_title"
                    app:layout_constraintTop_toTopOf="@+id/iv_refer_icon"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginTop="260dp"
                    android:textColor="@color/refer_main_text"
                    android:textSize="@dimen/font_size_h1"
                    android:gravity="center"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_refer_subtitle"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_refer_title"
                    android:textColor="@color/refer_main_text"
                    android:textSize="@dimen/font_size_s13"
                    android:text="@string/refer_earn_reward"
                    android:gravity="center"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_refer_description"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_refer_subtitle"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:textColor="@color/refer_main_text"
                    android:textSize="@dimen/font_size_s15"
                    android:layout_marginTop="20dp"
                    android:layout_marginBottom="30dp"
                    android:gravity="center"
                    android:lineSpacingExtra="1dp"
                    android:lineSpacingMultiplier="1.1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_refer_record"
                android:layout_marginRight="@dimen/card_side_margin"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:background="@drawable/bg_restaurant_card"
                android:padding="@dimen/dip_20"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_refer_total_price"
                    style="@style/ItemStyle"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginLeft="@dimen/dip_25"
                    android:textColor="@color/refer_main_text"
                    android:textSize="@dimen/font_size_s24"
                    android:gravity="center"
                    tools:text="$00.00"
                    android:minWidth="@dimen/dip_65"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_refer_total_price_label"
                    app:layout_constraintTop_toBottomOf="@+id/tv_refer_total_price"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_refer_total_price"
                    app:layout_constraintRight_toRightOf="@+id/tv_refer_total_price"
                    android:text="@string/refer_earned"
                    android:textSize="@dimen/font_size_s13"
                    android:textColor="@color/refer_sub_text"
                    android:layout_marginTop="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_refer_total_number"
                    style="@style/ItemStyle"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_refer_total_price"
                    android:layout_marginRight="@dimen/dip_25"
                    android:textColor="@color/refer_main_text"
                    android:textSize="@dimen/font_size_s24"
                    android:gravity="center"
                    android:minWidth="@dimen/dip_65"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_refer_total_number_label"
                    app:layout_constraintTop_toBottomOf="@+id/tv_refer_total_number"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_refer_total_number"
                    app:layout_constraintRight_toRightOf="@+id/tv_refer_total_number"
                    android:text="@string/refer_people"
                    android:textSize="@dimen/font_size_s13"
                    android:textColor="@color/refer_sub_text"
                    android:layout_marginTop="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_refer_divider"
                    app:layout_constraintTop_toBottomOf="@+id/tv_refer_total_price_label"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginTop="@dimen/dip_20"
                    style="@style/RestaurantDividerHorizontalStyle" />


                <TextView
                    android:id="@+id/tv_refer_history_title"
                    app:layout_constraintTop_toBottomOf="@+id/tv_refer_divider"
                    app:layout_constraintLeft_toLeftOf="parent"
                    android:text="@string/refer_history"
                    android:textColor="@color/refer_main_text"
                    android:textSize="@dimen/font_size_s19"
                    android:layout_marginTop="@dimen/dip_20"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_refer_history_empty"
                    app:layout_constraintTop_toBottomOf="@+id/tv_refer_history_title"
                    app:layout_constraintLeft_toLeftOf="parent"
                    android:text="@string/refer_no_record"
                    android:textSize="@dimen/font_size_s13"
                    android:textColor="@color/refer_sub_text"
                    android:layout_marginTop="@dimen/dip_20"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <LinearLayout
                    android:id="@+id/ll_refer_history"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_refer_history_title"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dip_20" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tv_divider_refer"
        app:layout_constraintBottom_toTopOf="@+id/btn_refer"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginBottom="@dimen/sw_bottom_bar_divider_margin_bottom"
        style="@style/DividerHorizontalStyle" />

    <TextView
        android:id="@+id/btn_refer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/button_refer_red"
        android:layout_marginRight="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginBottom="@dimen/sw_bottom_bar_margin_bottom"
        android:lineSpacingExtra="6dp"
        android:layout_width="0dp"
        android:layout_height="@dimen/sw_bottom_bar_button_height" />

    <TextView
        android:id="@+id/tv_refer_price_label"
        app:layout_constraintTop_toTopOf="@+id/tv_refer_price"
        app:layout_constraintBottom_toBottomOf="@+id/tv_refer_price"
        app:layout_constraintRight_toLeftOf="@+id/tv_refer_price"
        android:textSize="@dimen/font_size_s19"
        android:textColor="@color/refer_button_text"
        android:text="@string/refer_get"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_refer_price"
        app:layout_constraintTop_toTopOf="@+id/btn_refer"
        app:layout_constraintBottom_toBottomOf="@+id/btn_refer"
        app:layout_constraintRight_toRightOf="@+id/btn_refer"
        android:textSize="@dimen/font_size_s19"
        android:textColor="@color/refer_button_text"
        android:text="$0"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_refer_place"
        app:layout_constraintTop_toTopOf="@+id/btn_refer"
        app:layout_constraintBottom_toBottomOf="@+id/btn_refer"
        app:layout_constraintLeft_toLeftOf="@+id/btn_refer"
        android:textSize="@dimen/font_size_s19"
        android:textColor="@color/refer_button_text"
        android:text="@string/refer_share"
        android:layout_marginLeft="20dp"
        android:layout_marginBottom="3dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_refer_place"
        app:constraint_referenced_ids="btn_refer, tv_refer_price_label, tv_refer_price, tv_refer_place"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <FrameLayout
        android:id="@+id/fl_menu_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        app:layout_anchor="@+id/rv_menu"
        app:layout_anchorGravity="center" />

</androidx.constraintlayout.widget.ConstraintLayout>