<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_delivery_address"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_divider_top"
        style="@style/DividerHorizontalStyle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="0dp" />

    <TextView
        android:id="@+id/tv_quote_note"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/AssertiveStyle"
        android:textSize="@dimen/sw_13sp"
        android:visibility="gone"
        android:paddingTop="30dp"
        android:paddingBottom="20dp"
        android:gravity="left"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_height="wrap_content"
        android:layout_width="0dp" />

    <com.ricepo.style.round.RoundLayout
        android:id="@+id/round_delivery_address"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_quote_note"
        app:stroke_width="1dp"
        app:stroke_color="@color/map_circle_border_shadow"
        app:round_corner="@dimen/sw_46dp"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:alpha="0"
        tools:alpha="1"
        android:layout_width="@dimen/sw_92dp"
        android:layout_height="@dimen/sw_92dp" >

        <ImageView
            android:id="@+id/iv_delivery_address"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_margin="0.5dp"
            android:alpha="0"
            android:layout_width="0dp"
            android:layout_height="0dp" />

    </com.ricepo.style.round.RoundLayout>

    <com.ricepo.style.round.RoundLayout
        android:id="@+id/round_delivery_address_placeholder"
        app:layout_constraintLeft_toLeftOf="@+id/round_delivery_address"
        app:layout_constraintRight_toRightOf="@+id/round_delivery_address"
        app:layout_constraintBottom_toBottomOf="@+id/round_delivery_address"
        app:layout_constraintTop_toTopOf="@+id/round_delivery_address"
        app:stroke_width="1dp"
        app:stroke_color="@color/dividerHighlight"
        android:layout_marginTop="1dp"
        app:round_corner="@dimen/sw_46dp"
        android:layout_width="@dimen/sw_92dp"
        android:layout_height="@dimen/sw_92dp" />


    <TextView
        android:id="@+id/tv_delivery_address_left_click"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_delivery_address"
        app:layout_constraintTop_toTopOf="@+id/round_delivery_address"
        app:layout_constraintBottom_toBottomOf="@+id/round_delivery_address"
        android:layout_width="0dp"
        android:layout_height="0dp" />
    
    <TextView
        android:id="@+id/tv_delivery_address_right_click"
        app:layout_constraintLeft_toLeftOf="@+id/tv_delivery_address"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/round_delivery_address"
        app:layout_constraintBottom_toTopOf="@+id/tv_delivery_address_error"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <TextView
        android:id="@+id/tv_delivery_address"
        app:layout_constraintTop_toTopOf="@+id/round_delivery_address"
        app:layout_constraintLeft_toRightOf="@+id/round_delivery_address"
        app:layout_constraintRight_toLeftOf="@+id/iv_delivery_address_arrow"
        android:layout_marginLeft="@dimen/sw_22dp"
        android:maxLines="2"
        android:ellipsize="end"
        style="@style/ItemStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_delivery_address_arrow"
        android:layout_width="@dimen/right_arrow_size"
        android:layout_height="@dimen/right_arrow_size"
        android:layout_marginRight="@dimen/sw_19dp"
        android:src="@drawable/ic_right_arrow"
        android:scaleType="fitEnd"
        app:layout_constraintTop_toTopOf="@+id/tv_delivery_address"
        app:layout_constraintBottom_toBottomOf="@+id/tv_delivery_address"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_delivery_city"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:maxLines="1"
        android:ellipsize="end"
        style="@style/ItemStyle"
        app:layout_constraintRight_toRightOf="@+id/iv_delivery_address_arrow"
        app:layout_constraintLeft_toLeftOf="@+id/tv_delivery_address"
        app:layout_constraintBottom_toTopOf="@+id/tv_divider_address"
        app:layout_constraintTop_toBottomOf="@+id/tv_delivery_address" />

    <TextView
        android:id="@+id/tv_divider_address"
        android:layout_width="0dp"
        android:layout_height="@dimen/dip_2"
        style="@style/RestaurantDividerHorizontalStyle"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_delivery_city"
        app:layout_constraintBottom_toTopOf="@+id/barrier_delivery_time_top"
        app:layout_constraintLeft_toLeftOf="@+id/tv_delivery_address"
        app:layout_constraintRight_toRightOf="@+id/iv_delivery_address_arrow" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_delivery_time_top"
        app:barrierDirection="top"
        app:constraint_referenced_ids="tv_delivery_time, tv_delivery_address_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_delivery_time"
        app:layout_constraintLeft_toLeftOf="@+id/tv_delivery_address"
        app:layout_constraintTop_toBottomOf="@+id/tv_divider_address"
        app:layout_constraintBottom_toTopOf="@+id/tv_delivery_guarantee"
        app:layout_constraintRight_toLeftOf="@+id/iv_delivery_time_arrow"
        android:paddingTop="10dp"
        tools:visibile="visible"
        style="@style/ItemStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_delivery_time_arrow"
        android:layout_width="@dimen/right_arrow_size"
        android:layout_height="0dp"
        android:paddingTop="10dp"
        android:visibility="gone"
        android:scaleType="fitEnd"
        android:layout_marginRight="@dimen/sw_19dp"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/tv_delivery_time"
        app:layout_constraintTop_toTopOf="@+id/tv_delivery_time" />


    <TextView
        android:id="@+id/tv_delivery_address_error_click"
        app:layout_constraintLeft_toLeftOf="@+id/tv_delivery_address_error"
        app:layout_constraintBottom_toTopOf="@+id/tv_delivery_type"
        app:layout_constraintTop_toTopOf="@+id/tv_delivery_address_error"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <TextView
        android:id="@+id/tv_delivery_address_error"
        app:layout_constraintLeft_toLeftOf="@+id/tv_delivery_address"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_divider_address"
        app:layout_constraintBottom_toTopOf="@+id/tv_delivery_type"
        android:paddingTop="10dp"
        style="@style/AssertiveStyle"
        android:visibility="gone"
        android:layout_marginRight="@dimen/card_side_margin"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_delivery_time_bottom"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_delivery_time, tv_delivery_address_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_delivery_guarantee"
        app:layout_constraintLeft_toLeftOf="@+id/tv_delivery_address"
        app:layout_constraintTop_toTopOf="@+id/tv_delivery_guarantee"
        app:layout_constraintBottom_toBottomOf="@+id/tv_delivery_guarantee"
        android:src="@drawable/ic_shield"
        android:layout_width="16dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_delivery_guarantee"
        app:layout_constraintLeft_toRightOf="@+id/iv_delivery_guarantee"
        app:layout_constraintTop_toBottomOf="@+id/tv_delivery_time"
        app:layout_constraintBottom_toTopOf="@+id/tv_delivery_type"
        style="@style/SubTitleStyle"
        android:text="@string/delivery_guarantee"
        android:paddingBottom="@dimen/dip_3"
        android:layout_marginTop="@dimen/dip_10"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_delivery_guarantee_info"
        app:layout_constraintLeft_toRightOf="@+id/tv_delivery_guarantee"
        app:layout_constraintTop_toTopOf="@+id/iv_delivery_guarantee"
        app:layout_constraintBottom_toBottomOf="@+id/iv_delivery_guarantee"
        android:src="@drawable/ic_remind"
        android:layout_marginLeft="@dimen/dip_3"
        android:layout_marginBottom="@dimen/dip_3"
        android:layout_width="14dp"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_delivery_guarantee"
        app:constraint_referenced_ids="iv_delivery_guarantee, tv_delivery_guarantee, iv_delivery_guarantee_info"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <TextView
        android:id="@+id/tv_delivery_type"
        app:layout_constraintLeft_toLeftOf="@+id/tv_delivery_address"
        app:layout_constraintTop_toBottomOf="@id/tv_delivery_guarantee"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="center_vertical"
        android:text="@string/delivery_change_to_package"
        style="@style/SubTitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="40dp" />

    <ImageView
        android:id="@+id/iv_delivery_type_arrow"
        android:layout_width="@dimen/right_arrow_size"
        android:layout_height="0dp"
        android:paddingLeft="2dp"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintBottom_toBottomOf="@+id/tv_delivery_type"
        app:layout_constraintTop_toTopOf="@+id/tv_delivery_type"
        app:layout_constraintLeft_toRightOf="@+id/tv_delivery_type" />

    <TextView
        android:id="@+id/tv_delivery_type_placeholder"
        app:layout_constraintTop_toTopOf="@+id/tv_delivery_type"
        app:layout_constraintBottom_toBottomOf="@+id/tv_delivery_type"
        app:layout_constraintLeft_toRightOf="@+id/iv_delivery_type_arrow"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_delivery_type"
        app:constraint_referenced_ids="tv_delivery_type, iv_delivery_type_arrow, tv_delivery_type_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>