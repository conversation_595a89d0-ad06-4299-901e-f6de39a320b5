<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_section_language"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_language_hans"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginRight="@dimen/margin_right"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_width="0dp"
        style="@style/TitleStyle"
        android:gravity="center_vertical"
        android:layout_height="@dimen/setting_item_height"
        android:text="@string/select_language" />

    <TextView
        app:layout_constraintBottom_toBottomOf="@+id/tv_language_hans"
        app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
        app:layout_constraintRight_toRightOf="@+id/tv_section_language"
        android:layout_width="0dp"
        style="@style/DividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_language_hans"
        android:layout_width="match_parent"
        android:layout_height="@dimen/setting_item_height"
        app:layout_constraintTop_toBottomOf="@+id/tv_section_language"
        app:layout_constraintBottom_toTopOf="@+id/tv_language_hk"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/margin_left"
        style="@style/ItemStyle"
        android:text="简体中文" />


    <TextView
        app:layout_constraintBottom_toBottomOf="@+id/tv_language_hk"
        app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
        app:layout_constraintRight_toRightOf="@+id/tv_section_language"
        android:layout_width="0dp"
        style="@style/DividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_language_hk"
        android:layout_width="match_parent"
        android:layout_height="@dimen/setting_item_height"
        app:layout_constraintTop_toBottomOf="@+id/tv_language_hans"
        app:layout_constraintBottom_toTopOf="@+id/tv_language_en"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/margin_left"
        style="@style/ItemStyle"
        android:text="繁体中文" />


    <TextView
        app:layout_constraintBottom_toBottomOf="@+id/tv_language_en"
        app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
        app:layout_constraintRight_toRightOf="@+id/tv_section_language"
        android:layout_width="0dp"
        style="@style/DividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_language_en"
        android:layout_width="match_parent"
        android:layout_height="@dimen/setting_item_height"
        app:layout_constraintTop_toBottomOf="@+id/tv_language_hk"
        app:layout_constraintBottom_toTopOf="@id/tv_language_es"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/margin_left"
        style="@style/ItemStyle"
        android:text="English" />

    <TextView
            android:id="@+id/tv_language_es"
            android:layout_width="match_parent"
            android:layout_height="@dimen/setting_item_height"
            app:layout_constraintTop_toBottomOf="@+id/tv_language_en"
            app:layout_constraintBottom_toBottomOf="parent"
            android:gravity="center_vertical"
            android:paddingLeft="@dimen/margin_left"
            android:layout_marginBottom="40dp"
            style="@style/ItemStyle"
            android:text="Español"/>


    <FrameLayout
        android:id="@+id/fl_motion_launch"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="0dp"
        android:layout_height="0dp">

        <androidx.constraintlayout.motion.widget.MotionLayout
            app:layoutDescription="@xml/motion_launch"
            android:id="@+id/motion_launch"
            android:transitionName="motion_launch"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_splash_screen"
                android:src="@drawable/layer_launcher"
                android:contentDescription="@string/help"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="0dp"
                android:layout_height="0dp" />

        </androidx.constraintlayout.motion.widget.MotionLayout>

    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>