<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_back" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsl_coins"
        app:layout_constraintTop_toBottomOf="@+id/lay_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_gravity="fill_vertical"
        android:layout_width="match_parent"
        android:layout_height="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent" >

            <ImageView
                android:id="@+id/iv_coins"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:scaleType="fitStart"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_point_banner" />


            <TextView
                android:id="@+id/tv_points_title"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_coins"
                android:textSize="@dimen/sw_36sp"
                android:textColor="@color/mainText"
                android:text="0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_points_subtitle"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_points_title"
                android:text="@string/ricepo_points_total"
                android:textSize="@dimen/sw_13sp"
                android:textColor="@color/mainText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_points_desc"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_points_subtitle"
                android:layout_marginTop="@dimen/sw_30dp"
                android:gravity="center"
                android:textSize="@dimen/sw_15sp"
                android:textColor="@color/mainText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="@+id/iv_coins"
                android:visibility="gone"
                style="@style/DividerHorizontalStyle" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_coins_list"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toBottomOf="@+id/tv_points_desc"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:background="@drawable/bg_restaurant_card"
                android:layout_marginTop="@dimen/sw_45dp"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:layout_marginBottom="?attr/actionBarSize"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>



    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login"
        android:layout_marginTop="28dp"
        android:stateListAnimator="@null"
        style="@style/ButtonSecondaryStyle"
        app:layout_constraintTop_toBottomOf="@+id/lay_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <FrameLayout
        android:id="@+id/fl_menu_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        app:layout_anchor="@+id/rv_menu"
        app:layout_anchorGravity="center" />

</androidx.constraintlayout.widget.ConstraintLayout>