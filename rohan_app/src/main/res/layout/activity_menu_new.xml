<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    android:id="@+id/coordinator_menu"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:fitsSystemWindows="false"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar_menu"
        app:elevation="0dp"
        android:background="@color/background"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" >

        <androidx.constraintlayout.motion.widget.MotionLayout
            android:id="@+id/collapse_menu"
            app:layout_scrollFlags="exitUntilCollapsed"
            app:layoutDescription="@xml/motion_menu_search"
            app:applyMotionScene="true"
            app:motionProgress="0"
            app:motionDebug="NO_DEBUG"
            android:minHeight="?attr/actionBarSize"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/padding_title_icon"
                android:src="@drawable/ic_back"
                android:tint="@color/fun_n6"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                app:layout_constraintLeft_toRightOf="@id/iv_back"
                app:layout_constraintRight_toLeftOf="@+id/iv_group_order"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/iv_back"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:gravity="center"
                style="@style/TitleStyle" />

            <com.ricepo.base.view.RicePoolView
                android:id="@+id/rtv_pool"
                app:layout_constraintLeft_toRightOf="@+id/tv_title"
                app:layout_constraintRight_toLeftOf="@+id/iv_group_order"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:visibility="visible"
                android:visibility="gone"
                android:layout_width="0dp"
                android:layout_height="wrap_content" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_search"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:paddingLeft="@dimen/padding_title_icon"
                android:paddingRight="@dimen/dip_12"
                android:paddingTop="@dimen/dip_15"
                android:paddingBottom="@dimen/dip_12"
                app:layout_constraintBottom_toBottomOf="@+id/iv_back"
                app:layout_constraintRight_toLeftOf="@+id/iv_group_order"
                app:layout_constraintTop_toTopOf="@+id/iv_back"
                android:src="@drawable/ic_search"
                android:tint="@color/fun_n6"
                />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_group_order"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="@dimen/dip_8"
                android:paddingRight="@dimen/padding_title_icon"
                android:paddingTop="@dimen/padding_title_icon"
                android:paddingBottom="@dimen/padding_title_icon"
                android:src="@drawable/ic_group_order"
                android:tint="@color/fun_n6"
                />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/et_menu_search"
                app:layout_constraintTop_toBottomOf="@+id/tv_title"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginLeft="40dp"
                android:layout_marginRight="40dp"
                android:imeOptions="actionSearch"
                android:visibility="gone"
                android:singleLine="true"
                style="@style/RegularStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </androidx.constraintlayout.motion.widget.MotionLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <com.ricepo.style.view.headerlayout.SectionHeaderLayout
        android:id="@+id/shl_menu_layout"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.ricepo.style.view.rv.NestingRecyclerView
            android:id="@+id/rv_menu"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" >
        </com.ricepo.style.view.rv.NestingRecyclerView>

    </com.ricepo.style.view.headerlayout.SectionHeaderLayout>


    <include layout="@layout/layout_menu_cart_bar"
        android:id="@+id/in_menu_cart" />

    <LinearLayout
        android:layout_gravity="right|center_vertical"
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" >

        <com.ricepo.style.round.RoundLayout
            android:id="@+id/cl_sidebar"
            android:layout_gravity="right|center_vertical"
            android:gravity="center_horizontal"
            android:visibility="gone"
            tools:visibility="visible"
            app:round_corner_top_left="11dp"
            app:round_corner_bottom_left="11dp"
            app:stroke_color="@color/bundle_bar_border"
            app:stroke_width="1dp"
            app:clip_background="true"
            android:background="@color/bundle_bar_background"
            android:layout_width="32dp"
            android:layout_height="wrap_content" >

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_sidebar"
                app:layout_constraintTop_toBottomOf="@+id/iv_bundle_up"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/iv_bundle_down"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="0dp" >

            </androidx.recyclerview.widget.RecyclerView>

            <ImageView
                android:id="@+id/iv_bundle_up"
                android:visibility="gone"
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:paddingTop="3dp"
                android:paddingBottom="3dp"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:scaleType="fitCenter"
                android:background="@color/colorPrimary"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/rv_sidebar"
                android:src="@drawable/ic_bundle_arrow_up" />

            <ImageView
                android:id="@+id/tv_bundle_bg"
                android:src="@color/primary_satisfied_button_fill"
                android:layout_width="30dp"
                android:layout_height="wrap_content" />

            <ImageView
                android:id="@+id/iv_bundle_down"
                android:visibility="gone"
                android:layout_width="0dp"
                android:layout_height="24dp"
                android:paddingBottom="3dp"
                android:paddingTop="3dp"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:scaleType="fitCenter"
                android:background="@color/colorPrimary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rv_sidebar"
                android:src="@drawable/ic_bundle_arrow_down" />

        </com.ricepo.style.round.RoundLayout>

        <com.ricepo.style.round.RoundLayout
            android:id="@+id/cl_sidebar_opt"
            android:layout_gravity="right|center_vertical"
            android:visibility="gone"
            tools:visibility="visible"
            app:round_corner_top_left="11dp"
            app:round_corner_bottom_left="11dp"
            app:stroke_color="@color/bundle_bar_border"
            app:stroke_width="1dp"
            app:clip_background="true"
            android:background="@color/bundle_bar_background"
            android:layout_width="32dp"
            android:layout_height="wrap_content" >

            <com.ricepo.style.view.VerticalTextView
                android:id="@+id/tv_bundle_opt"
                app:textSize="@dimen/font_size_h6"
                app:maxLines="1"
                app:lineSpace="0dp"
                app:normalCharPadding="-3dp"
                app:text="@string/bundle_title"
                app:textColor="@color/bundle_bar_text"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </com.ricepo.style.round.RoundLayout>

    </LinearLayout>


    <FrameLayout
        android:id="@+id/fl_menu_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?attr/actionBarSize"
        app:layout_anchor="@+id/rv_menu"
        app:layout_anchorGravity="center" />


</androidx.coordinatorlayout.widget.CoordinatorLayout>



