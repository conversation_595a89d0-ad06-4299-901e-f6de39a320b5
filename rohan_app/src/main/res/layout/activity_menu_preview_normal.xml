<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/colorPrimary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_restaurant_menu_bg"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:src="@drawable/bg_plate"
        android:layout_width="@dimen/sw_360dp"
        android:layout_height="@dimen/sw_468dp" />

    <ImageView
        android:id="@+id/iv_restaurant_menu"
        app:layout_constraintLeft_toLeftOf="@+id/iv_restaurant_menu_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_restaurant_menu_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_restaurant_menu_bg"
        android:layout_width="@dimen/sw_360dp"
        android:layout_height="@dimen/sw_360dp" />


</androidx.constraintlayout.widget.ConstraintLayout>