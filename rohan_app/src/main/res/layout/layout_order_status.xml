<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="@dimen/margin_left"
    android:paddingRight="@dimen/margin_right"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_order_status"
        android:ellipsize="end"
        android:maxLines="1"
        style="@style/TitleStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_order_pending_warning"
        style="@style/ItemStyle"
        android:visibility="gone"
        android:layout_marginTop="8dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_order_message"
        style="@style/SubTitleStyle"
        android:layout_marginTop="8dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_order_pay"
        android:layout_marginTop="8dp"
        android:text="@string/pay"
        android:visibility="gone"
        android:minWidth="65dp"
        android:minHeight="40dp"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:backgroundTint="@color/mr"
        android:textColor="@color/w"
        app:cornerRadius="@dimen/button_radius"
        android:layout_height="@dimen/button_normal_height" />

    <TextView
        android:id="@+id/tv_order_delivery_ricepo"
        android:visibility="gone"
        android:drawableLeft="@drawable/ic_delivery"
        style="@style/SubTitleStyle"
        android:layout_marginTop="8dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.ricepo.style.view.AutoSplitTextView
        android:id="@+id/tv_order_delivery"
        android:visibility="gone"
        android:layout_marginTop="8dp"
        style="@style/SubTitleStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_delivery_proof"
        android:visibility="gone"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        android:layout_marginTop="@dimen/dip_15"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_delivery_proof"
        android:visibility="gone"
        style="@style/SubTitleStyle"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_order_rating"
        android:layout_marginTop="@dimen/dip_15"
        android:text="@string/rate_driver"
        android:visibility="gone"
        style="@style/ButtonPrimaryStyle"
        android:textSize="@dimen/font_size_h6"
        android:minWidth="0dp"
        android:gravity="center"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/button_normal_height" />

</LinearLayout>