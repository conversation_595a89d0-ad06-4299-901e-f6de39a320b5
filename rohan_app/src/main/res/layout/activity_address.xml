<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <TextView
        android:id="@id/tv_address"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/address"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_close"
        app:layout_constraintTop_toTopOf="@id/iv_close" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_address_right_end"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:focusableInTouchMode="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/et_address" />

    <androidx.appcompat.widget.AppCompatEditText
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/RegularStyle"
        android:layout_marginTop="60dp"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        android:singleLine="true"
        android:ellipsize="end"
        android:scrollHorizontally="false"
        android:imeOptions="actionSearch"
        android:id="@+id/et_address"
        android:hint="@string/enter_address"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <LinearLayout
        app:layout_constraintTop_toBottomOf="@+id/et_address"
        app:layout_constraintLeft_toLeftOf="@+id/et_address"
        app:layout_constraintRight_toRightOf="@+id/et_address"
        android:id="@+id/ll_address_history"
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_height="wrap_content" >
        <TextView
            android:text="@string/history"
            android:gravity="center"
            style="@style/ItemStyle"
            android:alpha="@fraction/half"
            android:layout_marginTop="25dp"
            android:layout_marginLeft="@dimen/text_left_padding"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_address_history"
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

    <LinearLayout
        app:layout_constraintTop_toBottomOf="@+id/et_address"
        app:layout_constraintLeft_toLeftOf="@+id/et_address"
        app:layout_constraintRight_toRightOf="@+id/et_address"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/ll_address_prediction"
        android:orientation="vertical"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="0dp" >

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_address_prediction"
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

    <LinearLayout
        app:layout_constraintTop_toBottomOf="@+id/et_address"
        app:layout_constraintLeft_toLeftOf="@+id/et_address"
        app:layout_constraintRight_toRightOf="@+id/et_address"
        android:id="@+id/ll_address_form"
        android:orientation="vertical"
        android:visibility="gone"
        android:layout_width="0dp"
        android:layout_height="wrap_content" >

        <androidx.appcompat.widget.AppCompatEditText
            style="@style/RegularStyle"
            android:layout_marginTop="22dp"
            android:id="@+id/et_address_apt"
            android:hint="@string/enter_apt"
            android:singleLine="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.appcompat.widget.AppCompatEditText
            style="@style/RegularStyle"
            android:layout_marginTop="20dp"
            android:id="@+id/et_address_code"
            android:hint="@string/enter_postcode"
            android:singleLine="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>


        <androidx.appcompat.widget.AppCompatEditText
            style="@style/RegularStyle"
            android:layout_marginTop="20dp"
            android:id="@+id/et_address_note"
            android:hint="@string/note"
            android:singleLine="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.google.android.material.button.MaterialButton
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:id="@+id/btn_save_address"
            android:text="@string/ok"
            style="@style/ButtonPrimaryStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>