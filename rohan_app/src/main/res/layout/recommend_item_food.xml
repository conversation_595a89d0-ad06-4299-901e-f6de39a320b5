<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_recommend_divider"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        style="@style/DividerHorizontalStyle" />

    <ImageView
        android:id="@+id/iv_recommend_dish_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="25dp"
        tools:src="@drawable/bg_plate"
        android:layout_width="80dp"
        android:layout_height="80dp" />


    <ImageView
        android:id="@+id/iv_recommend_dish"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_dish_bg"
        app:layout_constraintLeft_toLeftOf="@+id/iv_recommend_dish_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_recommend_dish_bg"
        android:layout_width="65dp"
        android:layout_height="65dp" />

    <TextView
        android:id="@+id/tv_recommend_food"
        app:layout_constraintTop_toTopOf="@+id/iv_recommend_dish"
        app:layout_constraintLeft_toRightOf="@+id/iv_recommend_dish"
        app:layout_constraintRight_toLeftOf="@id/tv_recommend_price"
        app:layout_constraintBottom_toTopOf="@+id/tv_recommend_restaurant"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginLeft="10dp"
        style="@style/ItemStyle"
        android:ellipsize="end"
        android:maxLines="1"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />
    
    <TextView
        android:id="@+id/tv_recommend_restaurant"
        app:layout_constraintTop_toBottomOf="@+id/tv_recommend_food"
        app:layout_constraintLeft_toLeftOf="@+id/tv_recommend_food"
        app:layout_constraintRight_toRightOf="@+id/tv_recommend_food"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_dish"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="8dp"
        style="@style/SubTitleStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_recommend_price"
        app:layout_constraintTop_toTopOf="@+id/tv_recommend_food"
        app:layout_constraintLeft_toRightOf="@+id/tv_recommend_food"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="30dp"
        style="@style/ItemStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />


</androidx.constraintlayout.widget.ConstraintLayout>