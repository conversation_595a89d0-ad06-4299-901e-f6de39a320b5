<?xml version="1.0" encoding="utf-8"?>

<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_marginTop="@dimen/sw_13dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/in_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/sw_14dp"
            layout="@layout/layout_header_close" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_bundle_confirm"
            android:text="@string/bundle_confirm"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/in_title"
            app:layout_constraintBottom_toBottomOf="@+id/in_title"
            android:layout_marginRight="@dimen/sw_24dp"
            android:backgroundTint="@color/mr"
            android:textColor="@color/white"
            app:cornerRadius="12dp"
            android:minHeight="35dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_bundle_restaurant"
            app:layout_constraintTop_toBottomOf="@+id/in_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_height="0dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
