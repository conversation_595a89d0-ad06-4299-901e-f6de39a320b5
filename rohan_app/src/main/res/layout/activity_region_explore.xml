<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_close" />

    <androidx.core.widget.NestedScrollView
        android:fillViewport="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                style="@style/StableStyle"
                android:text="@string/explore"
                android:layout_marginLeft="@dimen/margin_left"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_explore_list"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="?attr/actionBarSize"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>


</LinearLayout>