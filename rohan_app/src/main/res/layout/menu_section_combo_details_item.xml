<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_section_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/sw_10dp"
        android:paddingLeft="@dimen/sw_20dp"
        android:paddingRight="@dimen/sw_20dp">

        <ImageView
            android:id="@+id/iv_food_bg"
            android:layout_width="@dimen/sw_food_big_width"
            android:layout_height="@dimen/sw_food_big_height"
            android:layout_marginTop="@dimen/sw_15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/bg_plate" />

        <ImageView
            android:id="@+id/iv_food"
            android:layout_width="@dimen/sw_food_big_width"
            android:layout_height="@dimen/sw_food_big_width"
            app:layout_constraintLeft_toLeftOf="@+id/iv_food_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_food_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_food_bg" />

        <TextView
            android:id="@+id/tv_food_name"
            style="@style/MenuFoodNameStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/sw_12dp"
            android:layout_marginTop="@dimen/sw_18dp"
            app:layout_constraintLeft_toRightOf="@+id/iv_food_bg"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_food_info"
            style="@style/SubTitleStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:lineSpacingExtra="0dp"
            android:textFontWeight="500"
            android:textSize="@dimen/font_size_h6"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
            app:layout_constraintLeft_toRightOf="@+id/iv_food_bg"
            app:layout_constraintTop_toBottomOf="@+id/tv_food_name" />

        <TextView
            android:id="@+id/tv_food_price"
            style="@style/SubItemStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/inputLineText"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_food_info"
            tools:text="20.00" />


        <TextView
            android:id="@+id/tv_food_original_price"
            style="@style/SubItemStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:textColor="@color/inputLineText"
            app:layout_constraintBottom_toBottomOf="@+id/tv_food_price"
            app:layout_constraintLeft_toRightOf="@+id/tv_food_price"
            tools:text="20.00" />

        <TextView
            android:id="@+id/tv_divider"
            style="@style/RestaurantDividerHorizontalStyle"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>