<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:paddingLeft="@dimen/dip_15"
    android:paddingRight="@dimen/dip_15"
    
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_jump_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/mainText"
        android:textSize="@dimen/sw_24sp"
        android:gravity="left"
        android:layout_marginTop="@dimen/dip_15"
        android:layout_width="wrap_content"
        android:layout_height="40dp" />


</androidx.constraintlayout.widget.ConstraintLayout>