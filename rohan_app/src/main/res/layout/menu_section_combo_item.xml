<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/lay_section_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toStartOf="@+id/btn_food_option"
            android:paddingLeft="@dimen/sw_20dp"
            android:paddingRight="@dimen/sw_20dp">

            <View
                android:id="@+id/divider_food"
                style="@style/RestaurantDividerHorizontalStyle"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_food_name"
                style="@style/MenuFoodNameStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/sw_18dp"
                android:layout_marginRight="15dp"
                android:ellipsize="end"
                android:maxLines="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_container"
                android:layout_width="0dp"
                android:layout_height="2dp"
                app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_food_name" />

            <TextView
                android:id="@+id/tv_food_info_one"
                style="@style/SubTitleStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="0dp"
                android:maxLines="1"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_container"
                tools:visibility="visible" />


            <TextView
                android:id="@+id/tv_food_info_two"
                style="@style/SubTitleStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:singleLine="true"
                android:visibility="gone"
                app:layout_constrainedWidth="true"
                android:layout_marginRight="2dp"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
                app:layout_constraintRight_toLeftOf="@id/card"
                app:layout_constraintTop_toBottomOf="@+id/tv_food_info_one"
                tools:visibility="visible" />

            <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toRightOf="@+id/tv_food_info_two"
                    app:layout_constraintRight_toRightOf="parent"
                    app:cardCornerRadius="@dimen/button_radius"
                    app:strokeColor="@color/disableText"
                    app:strokeWidth="1dp"
                    app:cardElevation="0dp"
                    app:layout_constraintTop_toBottomOf="@+id/tv_food_info_one">
                <LinearLayout
                        android:id="@+id/ll_combo_details"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/sw_20dp"
                        android:layout_marginTop="1dp"
                        android:gravity="center"
                        android:paddingBottom="1.5dp"
                        android:paddingHorizontal="8dp"
                        android:orientation="horizontal">

                    <TextView
                            android:id="@+id/tv_see_more"
                            android:textColor="@color/subText"
                            android:textSize="@dimen/sw_11sp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/sw_5dp"
                            android:gravity="center" />

                    <ImageView
                            android:layout_width="@dimen/sw_8dp"
                            android:layout_height="@dimen/sw_9dp"
                            android:layout_marginRight="@dimen/sw_4dp"
                            android:src="@drawable/ic_right_arrow" />
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/tv_food_price"
                style="@style/SubItemStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="@color/inputLineText"
                app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
                app:layout_constraintTop_toBottomOf="@+id/card"
                tools:text="20.00" />

            <TextView
                android:id="@+id/tv_food_bottom"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dip_10"
                app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_food_price" />

            <ImageView
                android:id="@+id/iv_reward_icon"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:scaleType="centerInside"
                android:src="@drawable/ic_coin"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/tv_food_price"
                app:layout_constraintLeft_toRightOf="@+id/tv_food_price"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_food_original_price"
                style="@style/SubItemStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:textColor="@color/inputLineText"
                app:layout_constraintBottom_toBottomOf="@+id/tv_food_price"
                app:layout_constraintLeft_toRightOf="@+id/iv_reward_icon"
                tools:text="20.00" />


        </androidx.constraintlayout.widget.ConstraintLayout>


        <!-- minWidth resulting in the right side not being aligned after adding the quantity -->
        <com.ricepo.style.button.ChocolateButton
            android:id="@+id/btn_food_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/sw_20dp"
            android:layout_marginRight="@dimen/sw_26dp"
            android:clickable="false"
            android:minHeight="26dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rl_combo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="@dimen/sw_4dp"
        android:paddingRight="@dimen/sw_4dp"
        android:paddingLeft="@dimen/sw_4dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_food_price">

        <ImageView
            android:id="@+id/iv_combo_bg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:background="@drawable/bg_combo"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_combo_food"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            tools:background="@drawable/bg_combo" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
