<?xml version="1.0" encoding="utf-8"?>

<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardBackgroundColor="@color/colorPrimary"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <com.youth.banner.Banner
        android:id="@+id/banner"
        android:gravity="center"
        android:layout_width="match_parent"
        app:banner_indicator_normal_color="@color/n4"
        app:banner_indicator_selected_color="@color/mr"
        android:layout_height="match_parent"
        />

    <ImageView
        android:id="@+id/iv_promo_close"
        android:src="@drawable/ic_close"
        android:layout_marginLeft="@dimen/dip_8"
        android:layout_marginTop="@dimen/dip_8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</com.google.android.material.card.MaterialCardView>
