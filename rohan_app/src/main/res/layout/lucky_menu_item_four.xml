<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_lucky_card"
    android:id="@+id/cl_lucky_menu_item_four"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_restaurant_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/mainText"
        android:textSize="@dimen/font_size_h5"
        android:shadowRadius="1.0"
        android:shadowColor="@android:color/black"
        android:shadowDx="1.0"
        android:shadowDy="2.0"
        android:gravity="left"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_add_cart"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_restaurant_desc"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintVertical_bias="0.5"
        tools:text="@string/restaurant_open"
        android:layout_marginBottom="@dimen/dip_3"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/dip_5" />

    <TextView
        android:id="@+id/tv_restaurant_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/subText"
        android:textSize="@dimen/font_size_h6"
        android:gravity="left"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_add_cart"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_name"
        app:layout_constraintBottom_toTopOf="@+id/guide_lucky_header"
        tools:text="@string/restaurant_open"
        android:layout_marginTop="@dimen/dip_3"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/dip_5" />

    <TextView
        android:id="@+id/tv_add_cart"
        android:background="@drawable/button_secondary"
        android:text="@string/add_cart"
        style="@style/LuckyButtonStyle"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_restaurant_name"
        app:layout_constraintBottom_toBottomOf="@+id/tv_restaurant_desc"
        android:layout_marginRight="@dimen/dip_40"
        android:paddingTop="@dimen/dip_10"
        android:paddingBottom="@dimen/dip_10"
        android:paddingLeft="@dimen/dip_15"
        android:paddingRight="@dimen/dip_15"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_lucky_header"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintGuide_percent="0.25"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guide_lucky_header"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/bg_lucky_menu"
        android:scaleType="fitStart"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <ImageView
        android:id="@+id/iv_food_one_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/iv_food_two_bg"
        app:layout_constraintTop_toTopOf="@+id/guide_lucky_header"
        android:layout_marginTop="50dp"
        android:layout_marginLeft="20dp"
        android:layout_width="@dimen/lucky_plate_small_width"
        android:layout_height="@dimen/lucky_plate_small_width"
        tools:src="@drawable/bg_plate" />

    <ImageView
        android:id="@+id/iv_food_one"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_one_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_one_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_food_one_bg"
        app:layout_constraintDimensionRatio="w,1:1.1"
        android:layout_width="0dp"
        android:layout_height="0dp"  />

    <TextView
        android:id="@+id/tv_food_one_name"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_one_bg"
        app:layout_constraintTop_toBottomOf="@+id/iv_food_one_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_one_bg"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        tools:text="@string/search_menu"
        style="@style/SubLabelStyle" />

    <ImageView
        android:id="@+id/iv_food_two_bg"
        app:layout_constraintLeft_toRightOf="@+id/iv_food_one_bg"
        app:layout_constraintTop_toTopOf="@+id/guide_lucky_header"
        android:layout_marginTop="120dp"
        android:layout_width="@dimen/lucky_plate_small_width"
        android:layout_height="@dimen/lucky_plate_small_height"
        tools:src="@drawable/bg_plate" />

    <ImageView
        android:id="@+id/iv_food_two"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_two_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_two_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_food_two_bg"
        app:layout_constraintDimensionRatio="w,1:1.1"
        android:layout_width="0dp"
        android:layout_height="0dp"  />

    <TextView
        android:id="@+id/tv_food_two_name"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_two_bg"
        app:layout_constraintTop_toBottomOf="@+id/iv_food_two_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_two_bg"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        tools:text="@string/search_menu"
        style="@style/SubLabelStyle" />

    <ImageView
        android:id="@+id/iv_food_three_bg"
        app:layout_constraintRight_toLeftOf="@+id/iv_food_four_bg"
        app:layout_constraintTop_toTopOf="@+id/guide_lucky_header"
        android:layout_marginTop="30dp"
        android:layout_width="@dimen/lucky_plate_small_width"
        android:layout_height="@dimen/lucky_plate_small_height"
        tools:src="@drawable/bg_plate" />

    <ImageView
        android:id="@+id/iv_food_three"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_three_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_three_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_food_three_bg"
        app:layout_constraintDimensionRatio="w,1:1.1"
        android:layout_width="0dp"
        android:layout_height="0dp"  />

    <TextView
        android:id="@+id/tv_food_three_name"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_three_bg"
        app:layout_constraintTop_toBottomOf="@+id/iv_food_three_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_three_bg"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        tools:text="@string/search_menu"
        style="@style/SubLabelStyle" />

    <ImageView
        android:id="@+id/iv_food_four_bg"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guide_lucky_header"
        android:layout_marginTop="100dp"
        android:layout_marginRight="20dp"
        android:layout_width="@dimen/lucky_plate_small_width"
        android:layout_height="@dimen/lucky_plate_small_height"
        tools:src="@drawable/bg_plate" />

    <ImageView
        android:id="@+id/iv_food_four"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_four_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_four_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_food_four_bg"
        app:layout_constraintDimensionRatio="w,1:1.1"
        android:layout_width="0dp"
        android:layout_height="0dp"  />

    <TextView
        android:id="@+id/tv_food_four_name"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_four_bg"
        app:layout_constraintTop_toBottomOf="@+id/iv_food_four_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_four_bg"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        tools:text="@string/search_menu"
        style="@style/SubLabelStyle" />


</androidx.constraintlayout.widget.ConstraintLayout>