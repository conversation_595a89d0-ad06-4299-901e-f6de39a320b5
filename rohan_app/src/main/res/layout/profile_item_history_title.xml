<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_history_title"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_history_empty_divider"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginTop="@dimen/sw_section_space"
        android:layout_marginBottom="@dimen/dip_15"
        style="@style/TitleStyle"
        android:textSize="@dimen/sw_19sp"
        android:text="@string/past_order"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_history_empty_divider"
        app:layout_constraintTop_toBottomOf="@+id/tv_history_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_history_empty"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/RestaurantDividerHorizontalStyle" />


    <TextView
        android:id="@+id/tv_history_empty"
        app:layout_constraintTop_toBottomOf="@+id/tv_history_empty_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:text="@string/no_order"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_history_empty"
        app:constraint_referenced_ids="tv_history_empty"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>