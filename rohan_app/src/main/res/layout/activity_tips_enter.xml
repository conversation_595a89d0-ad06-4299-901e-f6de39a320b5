<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <TextView
        android:id="@+id/tv_tips_enter_title"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/custom"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_close"
        app:layout_constraintTop_toTopOf="@id/iv_close" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_tips_right_end"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:focusableInTouchMode="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/et_tips" />

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/et_focus_tips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="40dp"
        android:layout_marginLeft="40dp"
        android:layout_marginTop="10dp"
        android:focusable="false"
        android:cursorVisible="false"
        style="@style/EditTextStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_close"/>

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAlignment="inherit"
        android:hint="@string/hint_tip_enter"
        android:gravity="center"
        android:inputType="numberDecimal"
        style="@style/EditTextNoBottomLineStyle"
        android:layout_marginBottom="@dimen/text_bottom_padding"
        app:layout_constraintLeft_toLeftOf="@+id/et_focus_tips"
        app:layout_constraintRight_toRightOf="@+id/et_focus_tips"
        app:layout_constraintTop_toTopOf="@+id/et_focus_tips" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save_tips"
        android:layout_gravity="center"
        android:layout_marginTop="35dp"
        app:layout_constraintTop_toBottomOf="@+id/et_tips"
        app:layout_constraintLeft_toLeftOf="@+id/et_tips"
        app:layout_constraintRight_toRightOf="@+id/et_tips"
        android:text="@string/ok"
        style="@style/ButtonPrimaryStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>