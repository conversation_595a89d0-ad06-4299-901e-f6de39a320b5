<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        android:tint="@color/fun_n6"
        app:srcCompat="@drawable/ic_back" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/title_profile"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/iv_setting"
        app:layout_constraintTop_toTopOf="@+id/iv_setting" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_setting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_setting"
        android:tint="@color/fun_n6"
        />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login"
        android:layout_marginTop="28dp"
        android:stateListAnimator="@null"
        android:visibility="gone"
        style="@style/ButtonSecondaryStyle"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/lay_profile_refresh"
        android:layout_marginTop="14dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp" >

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_profile"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="@dimen/sw_40dp"
            android:clipToPadding="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <FrameLayout
        android:id="@+id/fl_profile_page"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="14dp"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>