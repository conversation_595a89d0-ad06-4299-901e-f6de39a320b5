<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_tips_select"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="10dp">

    <ImageButton
        android:id="@+id/iv_tips_icon"
        android:layout_width="@dimen/checkout_button_width"
        android:layout_height="@dimen/checkout_button_height"
        android:layout_marginTop="@dimen/sw_23dp"
        android:layout_marginBottom="@dimen/sw_19dp"
        android:adjustViewBounds="true"
        android:background="@null"
        android:clickable="false"
        android:focusable="false"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_tips" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="@dimen/sw_32dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_tips_title"
            style="@style/ItemStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_tips_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/right_arrow_size"
            android:color="@color/subText"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingTop="@dimen/sw_2dp"
            android:textSize="@dimen/font_size_s13"
            tools:text="@string/test_text_length" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_tips_arrow"
        android:layout_width="@dimen/right_arrow_size"
        android:layout_height="@dimen/right_arrow_size"
        android:src="@drawable/ic_right_arrow" />

</LinearLayout>