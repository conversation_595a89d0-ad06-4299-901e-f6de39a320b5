<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/add_coupon"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/iv_close"
        app:layout_constraintTop_toTopOf="@+id/iv_close" />

    <ImageView
        android:id="@+id/iv_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:padding="@dimen/padding_title_icon"
        android:visibility="gone"
        app:srcCompat="@drawable/ic_nav_ico_exchange" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_coupon_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:layout_marginTop="5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/et_focus_add_coupon"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="40dp"
        android:layout_marginLeft="40dp"
        android:layout_marginTop="10dp"
        android:focusable="true"
        android:cursorVisible="false"
        style="@style/EditTextStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_close"/>

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_add_coupon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAlignment="inherit"
        android:hint="1234-ABCD"
        android:singleLine="true"
        style="@style/EditTextNoBottomLineStyle"
        android:layout_marginBottom="@dimen/text_bottom_padding"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toLeftOf="@+id/et_focus_add_coupon"
        app:layout_constraintRight_toRightOf="@+id/et_focus_add_coupon"
        app:layout_constraintTop_toTopOf="@+id/et_focus_add_coupon" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_add_coupon"
        android:layout_gravity="center"
        android:layout_marginTop="35dp"
        app:layout_constraintTop_toBottomOf="@+id/et_add_coupon"
        app:layout_constraintLeft_toLeftOf="@+id/et_add_coupon"
        app:layout_constraintRight_toRightOf="@+id/et_add_coupon"
        android:text="@string/ok"
        style="@style/ButtonPrimaryStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_coupon_add"
        app:constraint_referenced_ids="et_focus_add_coupon, et_add_coupon, btn_add_coupon"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"  />

</androidx.constraintlayout.widget.ConstraintLayout>