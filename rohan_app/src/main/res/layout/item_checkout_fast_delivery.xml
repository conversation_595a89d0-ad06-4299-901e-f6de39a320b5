<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_fast_delivery"
    android:visibility="gone"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_divider_top"
        style="@style/DividerHorizontalStyle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="0dp" />

    <ImageButton
        android:id="@+id/iv_fast_delivery_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_divider_top"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/bg_outline_checkout"
        android:layout_marginTop="25dp"
        android:layout_marginBottom="85dp"
        android:clickable="false"
        android:focusable="false"
        android:padding="10dp"
        android:adjustViewBounds="true"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_express_delivery"
        android:layout_width="@dimen/checkout_button_width"
        android:layout_height="@dimen/checkout_button_height" />

    <TextView
        android:id="@+id/tv_fast_delivery_time_label"
        app:layout_constraintTop_toTopOf="@+id/iv_fast_delivery_icon"
        app:layout_constraintLeft_toLeftOf="@+id/isb_fast_delivery"
        android:layout_marginTop="4dp"
        android:layout_marginLeft="10dp"
        android:text="@string/delivery_time"
        android:textColor="@color/fast_delivery_label_color"
        android:textSize="@dimen/font_size_h8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_fast_delivery_time"
        app:layout_constraintLeft_toLeftOf="@+id/tv_fast_delivery_time_label"
        app:layout_constraintTop_toBottomOf="@+id/tv_fast_delivery_time_label"
        android:layout_marginTop="4dp"
        tools:text="40-50 min"
        style="@style/SubItemStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_fast_delivery_fee_label"
        app:layout_constraintTop_toTopOf="@+id/tv_fast_delivery_time_label"
        app:layout_constraintLeft_toRightOf="@+id/tv_fast_delivery_time_label"
        android:layout_marginLeft="@dimen/dip_65"
        android:text="@string/fees_delivery"
        android:textColor="@color/fast_delivery_label_color"
        android:textSize="@dimen/font_size_h8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_fast_delivery_fee"
        app:layout_constraintLeft_toLeftOf="@+id/tv_fast_delivery_fee_label"
        app:layout_constraintTop_toBottomOf="@+id/tv_fast_delivery_fee_label"
        android:layout_marginTop="4dp"
        tools:text="$3"
        style="@style/SubItemStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.warkiz.widget.IndicatorSeekBar
        android:id="@+id/isb_fast_delivery_background_shadow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginRight="0.5dp"
        android:layout_marginLeft="0.5dp"
        app:layout_constraintLeft_toLeftOf="@+id/isb_fast_delivery"
        app:layout_constraintRight_toRightOf="@+id/isb_fast_delivery"
        app:layout_constraintTop_toTopOf="@+id/isb_fast_delivery"
        app:isb_thumb_color="@android:color/transparent"
        app:isb_track_background_color="@color/fast_delivery_background_shadow_color"
        app:isb_track_progress_color="@color/fast_delivery_progress_color"
        app:isb_track_background_size="@dimen/fast_delivery_slider_size"
        app:isb_track_progress_size="@dimen/fast_delivery_slider_size"
        app:isb_track_rounded_corners="true"
        app:isb_progress="0"
        app:isb_show_indicator="none"
        app:isb_show_tick_texts="false"
        app:isb_seek_smoothly="true" />

    <com.warkiz.widget.IndicatorSeekBar
        android:id="@+id/isb_fast_delivery_shadow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="2dp"
        android:layout_marginLeft="0.5dp"
        app:layout_constraintLeft_toLeftOf="@+id/isb_fast_delivery"
        app:layout_constraintRight_toRightOf="@+id/isb_fast_delivery"
        app:layout_constraintBottom_toBottomOf="@+id/isb_fast_delivery"
        app:isb_thumb_color="@android:color/transparent"
        app:isb_track_background_color="@color/fast_delivery_background_color"
        app:isb_track_progress_color="@color/fast_delivery_progress_shadow_color"
        app:isb_track_background_size="@dimen/fast_delivery_slider_size"
        app:isb_track_progress_size="@dimen/fast_delivery_slider_size"
        app:isb_track_rounded_corners="true"
        app:isb_progress="0"
        app:isb_show_indicator="none"
        app:isb_show_tick_texts="false"
        app:isb_seek_smoothly="true" />

    <com.warkiz.widget.IndicatorSeekBar
        android:id="@+id/isb_fast_delivery"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="55dp"
        app:layout_constraintLeft_toRightOf="@+id/iv_fast_delivery_icon"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:isb_thumb_drawable="@drawable/ic_express_delivery_tick"
        app:isb_thumb_size="@dimen/dip_24"
        app:isb_track_background_color="@color/fast_delivery_background_color"
        app:isb_track_progress_color="@color/fast_delivery_progress_color"
        app:isb_track_background_size="@dimen/fast_delivery_slider_size"
        app:isb_track_progress_size="@dimen/fast_delivery_slider_size"
        app:isb_track_rounded_corners="true"
        app:isb_progress="0"
        app:isb_show_indicator="none"
        app:isb_show_tick_texts="false"
        app:isb_thumb_adjust_auto="true"
        app:isb_seek_smoothly="true"
        app:isb_ticks_count="2" />

    <TextView
        android:id="@+id/tv_fast_delivery_start"
        app:layout_constraintTop_toBottomOf="@+id/isb_fast_delivery"
        app:layout_constraintLeft_toLeftOf="@+id/isb_fast_delivery"
        android:layout_marginTop="@dimen/dip_5"
        android:layout_marginLeft="10dp"
        tools:text="$0"
        android:textColor="@color/fast_delivery_label_color"
        android:textSize="@dimen/font_size_h8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_fast_delivery_end"
        app:layout_constraintTop_toBottomOf="@+id/isb_fast_delivery"
        app:layout_constraintRight_toRightOf="@+id/isb_fast_delivery"
        android:layout_marginTop="@dimen/dip_5"
        android:layout_marginRight="10dp"
        tools:text="$5"
        android:textColor="@color/fast_delivery_label_color"
        android:textSize="@dimen/font_size_h8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />



</androidx.constraintlayout.widget.ConstraintLayout>