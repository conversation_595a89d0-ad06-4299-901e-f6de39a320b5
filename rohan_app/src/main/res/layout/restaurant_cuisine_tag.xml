<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <FrameLayout
        android:id="@+id/round_cuisine_highlight"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:background="@drawable/bg_restaurant_card"
        android:layout_marginLeft="@dimen/sw_card_margin"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="@dimen/sw_20dp"
        android:layout_width="66dp"
        android:layout_height="66dp" />

    <ImageView
        android:id="@+id/iv_cuisine"
        app:layout_constraintLeft_toLeftOf="@+id/round_cuisine_highlight"
        app:layout_constraintTop_toTopOf="@+id/round_cuisine_highlight"
        app:layout_constraintRight_toRightOf="@+id/round_cuisine_highlight"
        app:layout_constraintBottom_toBottomOf="@+id/round_cuisine_highlight"
        android:layout_width="64dp"
        android:layout_height="64dp" />

    <TextView
        android:id="@+id/tv_cuisine_label"
        app:layout_constraintLeft_toRightOf="@+id/round_cuisine_highlight"
        app:layout_constraintTop_toTopOf="@+id/round_cuisine_highlight"
        app:layout_constraintBottom_toBottomOf="@+id/round_cuisine_highlight"
        android:layout_marginLeft="15dp"
        android:textColor="@color/mainText"
        style="@style/FontH2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    
    <TextView
        style="@style/RestaurantDividerHorizontalStyle"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>