<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="76dp"
        android:layout_height="22dp"
        android:background="@drawable/pickup_switch_bg"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_pickup"
            style="@style/TitleStyle"
            android:layout_width="38dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingTop="4dp"
            android:paddingRight="8dp"
            android:paddingBottom="4dp"
            android:text="@string/restaurant_pickup"
            android:textColor="@color/subText"
            android:textSize="@dimen/font_size_s10" />

        <TextView
            android:id="@+id/tv_delivery"
            style="@style/TitleStyle"
            android:layout_width="38dp"
            android:layout_height="wrap_content"
            android:background="@drawable/pickup_switch"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingTop="4dp"
            android:paddingRight="8dp"
            android:paddingBottom="4dp"
            android:text="@string/restaurant_delivery"
            android:textColor="@color/mainText"
            android:textSize="@dimen/font_size_s10" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>