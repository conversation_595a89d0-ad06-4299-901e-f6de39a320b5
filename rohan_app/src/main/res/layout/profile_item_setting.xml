<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/lay_membership"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginTop="@dimen/sw_section_space"
        android:layout_marginRight="@dimen/card_side_margin"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <include
            android:id="@+id/in_membership"
            layout="@layout/restaurant_item_plan" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginTop="@dimen/sw_section_space"
        android:layout_marginRight="@dimen/card_side_margin"
        android:background="@drawable/bg_restaurant_card">

        <TextView
            android:id="@+id/tv_section_person"
            style="@style/TitleStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/setting_item_height"
            android:layout_marginLeft="@dimen/card_side_margin"
            android:layout_marginRight="@dimen/card_side_margin"
            android:gravity="center_vertical"
            android:text="@string/Personal_info"
            android:textSize="@dimen/sw_19sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_section_person_divider"
            style="@style/RestaurantDividerHorizontalStyle"
            android:layout_marginLeft="@dimen/card_side_margin"
            android:layout_marginRight="@dimen/card_side_margin"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_section_person" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_subscription_profile"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerInside"
            android:src="@drawable/ic_membership_profile"
            app:layout_constraintDimensionRatio="h, 10:3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_section_person_divider" />

        <TextView
            android:id="@+id/tv_subscription_vip"
            style="@style/LargeTitleStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/subscription_vip"
            android:textColor="@color/mainText"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_subscription_profile" />

        <TextView
            android:id="@+id/tv_subscription_check_detail"
            style="@style/SubTitleStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/subscription_check_detail"
            android:textColor="@color/goldSubText"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/iv_subscription_arrow"
            app:layout_constraintTop_toBottomOf="@+id/tv_subscription_vip" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_subscription_arrow"
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:scaleType="centerInside"
            android:src="@drawable/ic_membership_arrow"
            app:layout_constraintBottom_toBottomOf="@+id/tv_subscription_check_detail"
            app:layout_constraintLeft_toRightOf="@+id/tv_subscription_check_detail"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_subscription_check_detail" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_subscription_vip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="iv_subscription_profile, tv_subscription_vip, tv_subscription_check_detail, iv_subscription_arrow" />


        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_vip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_subscription_check_detail"
            tools:visibility="visible" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="@dimen/sw_8dp"
            app:layout_constraintTop_toBottomOf="@+id/barrier_vip">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="20dp">

                <TextView
                    android:id="@+id/tv_person_coupon"
                    style="@style/ItemStyle"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:drawableLeft="@drawable/ic_cuppon"
                    android:gravity="center_vertical"
                    android:drawablePadding="5dp"
                    android:text="@string/my_coupon" />

                <ImageView
                    android:layout_width="@dimen/setting_arrow_icon_size"
                    android:layout_height="@dimen/setting_arrow_icon_size"
                    android:layout_marginRight="@dimen/card_side_margin"
                    android:src="@drawable/ic_right_arrow_small" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="20dp"
                android:visibility="visible">

                <TextView
                    android:id="@+id/tv_ricepo_point"
                    style="@style/ItemStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:drawableLeft="@drawable/ic_rice_icon"
                    android:gravity="center_vertical"
                    android:text="@string/my_coins"
                    android:drawablePadding="5dp"
                    app:layout_constraintTop_toBottomOf="@+id/tv_person_coupon" />

                <ImageView
                    android:layout_width="@dimen/setting_arrow_icon_size"
                    android:layout_height="@dimen/setting_arrow_icon_size"
                    android:layout_marginRight="@dimen/card_side_margin"
                    android:visibility="gone"
                    android:src="@drawable/ic_right_arrow_small" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="20dp">

                <TextView
                    android:id="@+id/tv_person_coin"
                    style="@style/ItemStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:drawableLeft="@drawable/ic_restaurant_coin"
                    android:gravity="center_vertical"
                    android:text="@string/restaurant_reward"
                    android:drawablePadding="5dp"
                    app:layout_constraintTop_toBottomOf="@+id/tv_ricepo_point" />

                <ImageView
                    android:layout_width="@dimen/setting_arrow_icon_size"
                    android:layout_height="@dimen/setting_arrow_icon_size"
                    android:layout_marginRight="@dimen/card_side_margin"
                    android:src="@drawable/ic_right_arrow_small" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lay_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="20dp">

                <LinearLayout
                    android:id="@+id/tv_email_coin"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingEnd="48dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_person_coin">

                    <TextView
                        style="@style/ItemStyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:drawableLeft="@drawable/ic_email"
                        android:drawablePadding="5dp"
                        android:text="@string/email" />

                    <TextView
                        android:id="@+id/tv_email_reward"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/email_reward"
                        android:textColor="@color/mr"
                        android:visibility="gone" />
                </LinearLayout>

                <ImageView
                    android:layout_width="@dimen/setting_arrow_icon_size"
                    android:layout_height="@dimen/setting_arrow_icon_size"
                    android:layout_marginRight="@dimen/card_side_margin"
                    android:src="@drawable/ic_right_arrow_small" />

            </LinearLayout>

        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
