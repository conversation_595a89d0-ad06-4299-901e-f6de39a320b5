<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".features.luckymenu.LuckyMenuActivity">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dip_20"
        android:src="@drawable/ic_close"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_dice"
        android:layout_width="@dimen/dip_165"
        android:layout_height="@dimen/dip_100"
        android:layout_marginTop="@dimen/dip_40"
        android:background="@drawable/ic_lucky_dice_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_lucky_menu"
        style="@style/TitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_5"
        android:text="@string/lucky_counter_title"
        android:textSize="@dimen/font_size_s16"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_dice" />

    <TextView
        android:id="@+id/tv_lucky_people"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dip_10"
        android:layout_marginTop="@dimen/dip_10"
        android:layout_marginRight="@dimen/dip_10"
        android:drawablePadding="@dimen/dip_3"
        android:gravity="center"
        android:text="@string/lucky_counter_description"
        android:textSize="@dimen/sw_13sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_lucky_menu" />


    <TextView
        android:id="@+id/tv_divider"
        style="@style/DividerHorizontalStyle"
        android:layout_marginTop="@dimen/dip_15"
        app:layout_constraintLeft_toLeftOf="@+id/tv_lucky_people"
        app:layout_constraintRight_toRightOf="@+id/tv_lucky_people"
        app:layout_constraintTop_toBottomOf="@+id/tv_lucky_people" />

    <TextView
        android:id="@+id/tv_lucky_select"
        style="@style/TitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_25"
        android:text="@string/lucky_counter_select_count"
        android:textSize="@dimen/font_size_s20"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_divider" />

    <com.ricepo.style.round.RoundLayout
        android:id="@+id/round_see_all"
        android:layout_width="@dimen/dip_225"
        android:layout_height="@dimen/dip_225"
        app:layout_constraintBottom_toTopOf="@+id/tv_white_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_lucky_select"
        app:layout_constraintVertical_chainStyle="packed"
        app:round_corner="@dimen/dip_200"
        app:stroke_color="@color/dividerShadow"
        app:stroke_width="@dimen/dip_1" />

    <com.ricepo.style.round.RoundLayout
        android:id="@+id/round_see_all_placeholder"
        android:layout_width="@dimen/dip_225"
        android:layout_height="225dp"
        android:layout_marginTop="1.5dp"
        app:layout_constraintBottom_toBottomOf="@+id/round_see_all"
        app:layout_constraintLeft_toLeftOf="@+id/round_see_all"
        app:layout_constraintRight_toRightOf="@+id/round_see_all"
        app:layout_constraintTop_toTopOf="@+id/round_see_all"
        app:round_corner="@dimen/dip_200"
        app:stroke_color="@color/dividerHighlight"
        app:stroke_width="@dimen/dip_1" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/dip_12"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@+id/tv_white_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_lucky_select">

        <ImageView
            android:id="@+id/bt_minus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@drawable/ic_lucky_minus" />

        <com.ricepo.style.shadow.LuckyShadowLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dip_20"
            android:layout_marginRight="@dimen/dip_20"
            app:luckyShadowColor="#7F000000"
            app:luckyShadowDirection="bottom"
            app:luckyShadowLength="@dimen/dip_12"
            app:luckyShadowRadius="@dimen/dip_100"
            app:luckyShadowTranslationY="@dimen/dip_10">

            <TextView
                android:id="@+id/tv_people_num"
                style="@style/TitleStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/lucky_num_bg"
                android:gravity="center"
                android:text="1"
                android:textSize="@dimen/font_size_s60" />
        </com.ricepo.style.shadow.LuckyShadowLayout>

        <ImageView
            android:id="@+id/bt_plus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ic_lucky_add" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_white_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_bottom_bar_height"
        android:background="@color/colorPrimary"
        android:textSize="@dimen/font_size_s20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        style="@style/DividerHorizontalStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_white_bg" />

    <TextView
        android:id="@+id/tv_generate_menu"
        style="@style/TitleStyle"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_bottom_bar_button_height"
        android:layout_marginLeft="@dimen/dip_20"
        android:layout_marginRight="@dimen/dip_20"
        android:layout_marginBottom="@dimen/sw_bottom_bar_margin_bottom"
        android:paddingBottom="3dp"
        android:background="@drawable/button_checkout_red"
        android:gravity="center"
        android:text="@string/lucky_counter_submit"
        android:textColor="@color/white"
        android:textSize="@dimen/sw_19sp"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>