<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/lay_section_item"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingRight="8dp"
    android:paddingLeft="8dp"
    android:paddingBottom="4dp"
    android:background="@drawable/bg_restaurant_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/divider_food"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginLeft="@dimen/card_side_margin"
        style="@style/RestaurantDividerHorizontalStyle" />

    <ImageView
        android:id="@+id/iv_food_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="@dimen/dip_5"
        android:layout_width="45dp"
        android:layout_height="45dp"
        tools:src="@drawable/bg_plate" />

    <ImageView
        android:id="@+id/iv_food"
        app:layout_constraintLeft_toLeftOf="@+id/iv_food_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_food_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_food_bg"
        app:layout_constraintDimensionRatio="w,1:1.28"
        android:layout_width="0dp"
        android:layout_height="0dp"  />

    <TextView
        android:id="@+id/tv_food_name"
        app:layout_constraintLeft_toRightOf="@+id/iv_food_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_food_price"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginRight="4dp"
        style="@style/MenuFoodNameStyle"
        android:maxLines="1"
        android:ellipsize="end"
        android:minWidth="45dp"
        android:maxWidth="120dp" />

    <TextView
        android:id="@+id/tv_food_price"
        app:layout_constraintTop_toBottomOf="@+id/tv_food_name"
        app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
        app:layout_constraintRight_toLeftOf="@+id/btn_food_option"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="20.00"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="5dp"
        style="@style/SubItemStyle"
        android:textSize="@dimen/font_size_s15"
        android:textColor="@color/inputLineText"
        android:maxLines="1"
        android:ellipsize="end"
        android:minWidth="30dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.ricepo.style.button.ChocolateButton
        android:id="@+id/btn_food_option"
        app:layout_constraintTop_toTopOf="@+id/tv_food_price"
        app:layout_constraintBottom_toBottomOf="@+id/tv_food_price"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/tv_food_price"
        android:clickable="false"
        android:layout_marginLeft="@dimen/dip_5"
        android:layout_marginTop="@dimen/sw_6dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>
