<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_motd_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:maxLines="1"
        android:ellipsize="end"
        style="@style/AssertiveStyle"
        android:visibility="gone"
        android:layout_gravity="center" />

    <TextView
        android:id="@+id/divider_motd_title"
        style="@style/DividerHorizontalStyle" />

    <androidx.viewpager2.widget.ViewPager2
        android:orientation="vertical"
        android:id="@+id/viewpager_restaurant_banner"
        android:layout_width="match_parent"
        android:layout_height="@dimen/banner_height" />

    <TextView
        android:id="@+id/divider_restaurant_banner"
        style="@style/DividerHorizontalStyle" />

</LinearLayout>
