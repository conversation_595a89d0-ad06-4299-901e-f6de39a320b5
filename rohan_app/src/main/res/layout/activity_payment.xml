<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_back"
        android:tint="@color/fun_n6"
        />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/choose_payment"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/iv_close"
        app:layout_constraintTop_toTopOf="@+id/iv_close" />

    <LinearLayout
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        android:orientation="vertical"
        android:layout_marginBottom="10dp"
        android:layout_width="0dp"
        android:layout_height="0dp" >

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_payment_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login"
            android:layout_marginTop="28dp"
            android:layout_gravity="center"
            android:stateListAnimator="@null"
            style="@style/ButtonSecondaryStyle" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>