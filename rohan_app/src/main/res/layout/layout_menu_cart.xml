<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_menu_cart"
    android:layout_gravity="bottom"
    android:visibility="gone"
    tools:visibility="visible"
    android:focusable="true"
    android:clickable="true"
    android:background="@android:color/transparent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_delivery"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/green"
        style="@style/SubTitleStyle"
        android:background="@android:color/transparent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/rcv_menu_cart"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingBottom="8dp"
        android:layout_marginRight="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcv_menu_cart"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/btn_menu_cart"
        android:background="@color/colorPrimary"
        android:layout_width="0dp"
        android:layout_height="@dimen/sw_bottom_bar_height" />

    <TextView
        android:id="@+id/tv_divider_menu_cart"
        app:layout_constraintTop_toTopOf="@+id/rcv_menu_cart"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/DividerHorizontalStyle" />

    <TextView
        app:layout_constraintTop_toTopOf="@+id/rcv_menu_cart"
        app:layout_constraintBottom_toBottomOf="@+id/rcv_menu_cart"
        app:layout_constraintLeft_toRightOf="@+id/rcv_menu_cart"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/colorPrimary"
        android:layout_marginTop="2dp"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <TextView
        android:id="@+id/btn_menu_cart"
        app:layout_constraintBottom_toBottomOf="@+id/rcv_menu_cart"
        app:layout_constraintLeft_toRightOf="@+id/rcv_menu_cart"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/chocolate_bg_selected"
        style="@style/PlaceOrderStyle"
        tools:text="$20"
        android:minWidth="40dp"
        android:minHeight="40dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:paddingBottom="6dp"
        android:gravity="center"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="@dimen/sw_bottom_bar_margin_bottom"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/sw_bottom_bar_button_height" />


</androidx.constraintlayout.widget.ConstraintLayout>