<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_food_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/sw_20dp"
        android:layout_marginLeft="0dp"
        android:layout_marginRight="@dimen/dip_3"
        app:layout_constraintVertical_chainStyle="packed"
        style="@style/MenuFoodNameStyle"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="@string/test_text_length"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/barrier_food_qty"
        app:layout_constraintBottom_toTopOf="@+id/tv_food_info"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_food_info"
        app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_food_name"
        app:layout_constraintBottom_toTopOf="@+id/iv_food_group"
        app:layout_constraintRight_toLeftOf="@+id/barrier_food_qty"
        app:layout_constraintVertical_chainStyle="packed"
        style="@style/SubTitleStyle"
        android:textSize="@dimen/font_size_h6"
        android:textFontWeight="500"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="@string/test_text_length"
        android:layout_marginTop="@dimen/dip_3"
        android:layout_marginRight="@dimen/dip_3"
        android:lineSpacingExtra="0dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_food_group"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_food_info"
        app:layout_constraintRight_toLeftOf="@+id/tv_food_group"
        app:layout_constraintBottom_toTopOf="@+id/ll_food_price"
        android:visibility="gone"
        tools:visibility="visible"
        android:src="@drawable/ic_group_member"
        android:scaleType="fitStart"
        android:layout_marginTop="@dimen/dip_3"
        android:layout_marginLeft="2dp"
        android:layout_width="14dp"
        android:layout_height="12dp" />

    <TextView
        android:id="@+id/tv_food_group"
        app:layout_constraintLeft_toRightOf="@+id/iv_food_group"
        app:layout_constraintTop_toTopOf="@+id/iv_food_group"
        app:layout_constraintRight_toLeftOf="@+id/barrier_food_qty"
        app:layout_constraintBottom_toBottomOf="@+id/iv_food_group"
        android:visibility="gone"
        tools:visibility="visible"
        tools:text="@string/help"
        style="@style/SubTitleStyle"
        android:textSize="@dimen/font_size_h6"
        android:textFontWeight="500"
        android:layout_marginLeft="2dp"
        android:layout_marginBottom="3dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:id="@+id/ll_food_price"
        app:layout_constraintTop_toBottomOf="@+id/tv_food_group"
        app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
        android:gravity="center_vertical"
        android:layout_marginTop="@dimen/dip_3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" >

        <TextView
            android:id="@+id/tv_food_price"
            tools:text="20.00"
            style="@style/SubItemStyle"
            android:textColor="@color/inputLineText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_reward_icon"
            android:src="@drawable/ic_coin"
            android:visibility="gone"
            android:layout_width="@dimen/reward_icon_width"
            android:layout_height="@dimen/reward_icon_height" />

    </LinearLayout>

    <TextView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_food_price"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/sw_15dp" />

    <TextView
        android:id="@+id/tv_food_original_price"
        app:layout_constraintBottom_toBottomOf="@+id/ll_food_price"
        app:layout_constraintLeft_toRightOf="@+id/ll_food_price"
        app:layout_constraintRight_toRightOf="@+id/tv_food_name"
        tools:text="20.00"
        style="@style/SubItemStyle"
        android:layout_marginLeft="@dimen/dip_3"
        android:textColor="@color/inputLineText"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_food_qty"
        app:constraint_referenced_ids="btn_food_qty"
        app:barrierDirection="start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.ricepo.style.button.ChocolateButton
        android:id="@+id/btn_food_qty"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginTop="@dimen/sw_20dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />


</androidx.constraintlayout.widget.ConstraintLayout>