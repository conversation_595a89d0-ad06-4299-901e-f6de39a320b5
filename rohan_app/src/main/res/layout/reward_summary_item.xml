<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_restaurant_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_redeem"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginTop="25dp"
        style="@style/TitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_redeem"
        app:layout_constraintLeft_toLeftOf="@+id/tv_restaurant_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_name"
        app:layout_constraintBottom_toBottomOf="parent"
        style="@style/SubTitleStyle"
        android:text="@string/redeem"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="25dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:layout_width="10dp"
        android:layout_height="12dp"
        app:layout_constraintTop_toTopOf="@+id/tv_redeem"
        app:layout_constraintBottom_toBottomOf="@+id/tv_redeem"
        app:layout_constraintLeft_toRightOf="@+id/tv_redeem"
        android:layout_marginLeft="4dp"
        android:src="@drawable/ic_right_arrow" />

    <TextView
        android:id="@+id/tv_point"
        app:layout_constraintRight_toLeftOf="@+id/ic_coin"
        app:layout_constraintTop_toTopOf="@+id/ic_coin"
        app:layout_constraintBottom_toBottomOf="@+id/ic_coin"
        style="@style/TitleStyle"
        android:layout_marginRight="2dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/ic_coin"
        android:layout_width="30dp"
        android:layout_height="30dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_marginLeft="4dp"
        android:src="@drawable/ic_coin" />

    <TextView
        android:id="@+id/tv_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        style="@style/RestaurantDividerHorizontalStyle" />

</androidx.constraintlayout.widget.ConstraintLayout>