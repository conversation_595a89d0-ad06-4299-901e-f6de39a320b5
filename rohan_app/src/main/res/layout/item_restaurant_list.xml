<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginBottom="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_restaurant_menu_middle_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="30dp"
        android:layout_width="@dimen/restaurant_plate_middle_width"
        android:layout_height="@dimen/restaurant_plate_middle_height" />

    <ImageView
        android:id="@+id/iv_restaurant_menu_middle"
        app:layout_constraintLeft_toLeftOf="@+id/iv_restaurant_menu_middle_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_restaurant_menu_middle_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_restaurant_menu_middle_bg"
        app:layout_constraintBottom_toBottomOf="@+id/iv_restaurant_menu_middle_bg"
        android:layout_marginBottom="30dp"
        android:layout_width="@dimen/restaurant_food_middle_width"
        android:layout_height="@dimen/restaurant_food_middle_height" />

    <ImageView
        android:id="@+id/iv_restaurant_menu_left_bg"
        app:layout_constraintRight_toLeftOf="@+id/iv_restaurant_menu_middle"
        app:layout_constraintTop_toTopOf="@+id/iv_restaurant_menu_middle_bg"
        app:layout_constraintBottom_toBottomOf="@+id/iv_restaurant_menu_middle_bg"
        android:layout_marginRight="10dp"
        android:layout_width="@dimen/restaurant_plate_side_width"
        android:layout_height="@dimen/restaurant_plate_middle_height" />

    <ImageView
        android:id="@+id/iv_restaurant_menu_left"
        app:layout_constraintLeft_toLeftOf="@+id/iv_restaurant_menu_left_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_restaurant_menu_left_bg"
        app:layout_constraintBottom_toBottomOf="@+id/iv_restaurant_menu_left_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_restaurant_menu_left_bg"
        android:layout_marginBottom="@dimen/restaurant_food_margin_bottom"
        android:layout_width="@dimen/restaurant_food_side_width"
        android:layout_height="@dimen/restaurant_food_side_height" />

    <ImageView
        android:id="@+id/iv_restaurant_menu_right_bg"
        app:layout_constraintLeft_toRightOf="@+id/iv_restaurant_menu_middle"
        app:layout_constraintTop_toTopOf="@+id/iv_restaurant_menu_middle_bg"
        app:layout_constraintBottom_toBottomOf="@+id/iv_restaurant_menu_middle_bg"
        android:layout_marginLeft="10dp"
        android:layout_width="@dimen/restaurant_plate_side_width"
        android:layout_height="@dimen/restaurant_plate_middle_height" />

    <ImageView
        android:id="@+id/iv_restaurant_menu_right"
        app:layout_constraintLeft_toLeftOf="@+id/iv_restaurant_menu_right_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_restaurant_menu_right_bg"
        app:layout_constraintBottom_toBottomOf="@+id/iv_restaurant_menu_right_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_restaurant_menu_right_bg"
        android:layout_marginBottom="@dimen/restaurant_food_margin_bottom"
        android:layout_width="@dimen/restaurant_food_side_width"
        android:layout_height="@dimen/restaurant_food_side_height" />

    <TextView
        android:id="@+id/tv_restaurant_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginLeft="@dimen/restaurant_text_margin"
        android:layout_marginRight="@dimen/restaurant_text_margin"
        style="@style/LargeTitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_restaurant_menu_middle" />

    <TextView
        android:id="@+id/tv_restaurant_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/SubTitleMenuStyle"
        android:layout_marginTop="5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_name" />

    <TextView
        android:id="@+id/tv_restaurant_vip_promotion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/SubTitleMenuStyle"
        android:visibility="gone"
        android:layout_marginTop="5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_info" />

    <TextView
        android:id="@+id/tv_restaurant_promotion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/SubTitleMenuStyle"
        android:visibility="gone"
        android:layout_marginTop="5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_vip_promotion" />


</androidx.constraintlayout.widget.ConstraintLayout>

