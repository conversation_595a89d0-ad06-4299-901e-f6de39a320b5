<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    
    <include android:id="@+id/lay_title"
        layout="@layout/layout_header_close" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsl_checkout_content"
        app:layout_constraintTop_toBottomOf="@+id/lay_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_divider_checkout_order"
        android:layout_width="match_parent"
        android:layout_height="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_image_info"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/ic_red_link"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_image_info"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_image_info"
                android:textColor="@color/mainText"
                android:textSize="@dimen/sw_24sp"
                android:gravity="center"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_image_message"
                android:text="@string/diary_image_save"
                android:layout_marginTop="@dimen/sw_section_space"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_image_info"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />


            <TextView
                android:id="@+id/tv_image_detail"
                android:text="@string/diary_image_detail"
                android:layout_marginTop="@dimen/sw_section_space"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_image_message"
                android:textColor="@color/rest_address_fill"
                android:layout_marginLeft="@dimen/card_side_margin"
                android:layout_marginRight="@dimen/card_side_margin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <ImageView
                android:id="@+id/iv_image_detail"
                android:src="@drawable/ic_right_red"
                app:layout_constraintLeft_toRightOf="@+id/tv_image_detail"
                app:layout_constraintTop_toTopOf="@+id/tv_image_detail"
                app:layout_constraintBottom_toBottomOf="@+id/tv_image_detail"
                android:textColor="@color/rest_address_fill"
                android:layout_marginLeft="2dp"
                android:layout_width="12dp"
                android:layout_height="12dp" />

<!--            <FrameLayout-->
<!--                android:layout_marginTop="@dimen/dip_40"-->
<!--                app:layout_constraintLeft_toLeftOf="parent"-->
<!--                app:layout_constraintRight_toRightOf="parent"-->
<!--                app:layout_constraintTop_toBottomOf="@+id/tv_image_info"-->
<!--                android:layout_marginLeft="@dimen/dip_20"-->
<!--                android:layout_marginRight="@dimen/dip_20"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="wrap_content">-->

                <com.ricepo.style.shadow.LuckyShadowLayout
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_image_detail"
                    android:layout_marginTop="@dimen/sw_33dp"
                    android:layout_marginLeft="@dimen/dip_20"
                    android:layout_marginRight="@dimen/dip_20"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    app:luckyShadowColor="@color/bg_diary_image"
                    app:luckyShadowDirection="all"
                    app:luckyShadowLength="@dimen/dip_15"
                    app:luckyShadowRadius="@dimen/dip_20"
                    app:luckyShadowTranslationX="0dp"
                    app:luckyShadowTranslationY="0dp">

                    <com.ricepo.style.round.RoundLayout
                        app:round_corner="@dimen/dip_8"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" >

                        <ImageView
                            android:id="@+id/iv_diary_image"
                            android:scaleType="fitStart"
                            android:adjustViewBounds="true"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </com.ricepo.style.round.RoundLayout>

                </com.ricepo.style.shadow.LuckyShadowLayout>

<!--            </FrameLayout>-->


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="@+id/tv_divider_checkout_order"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tv_divider_checkout_order"
        app:layout_constraintBottom_toTopOf="@+id/btn_download_image"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginBottom="@dimen/sw_bottom_bar_divider_margin_bottom"
        style="@style/DividerHorizontalStyle" />

    <TextView
        android:id="@+id/btn_download_image"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/placeholder_checkout_order_bottom"
        android:background="@drawable/button_checkout_red"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginBottom="@dimen/sw_bottom_bar_margin_bottom"
        android:layout_width="0dp"
        android:layout_height="@dimen/sw_bottom_bar_button_height" />

    <TextView
        android:id="@+id/tv_download_image"
        app:layout_constraintTop_toTopOf="@+id/btn_download_image"
        app:layout_constraintBottom_toBottomOf="@+id/btn_download_image"
        app:layout_constraintLeft_toLeftOf="@+id/btn_download_image"
        app:layout_constraintRight_toRightOf="@+id/btn_download_image"
        style="@style/PlaceOrderStyle"
        android:textColor="@color/white"
        android:text="@string/download"
        android:layout_marginBottom="@dimen/dip_5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/placeholder_checkout_order_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:clickable="true"
        android:layout_width="0dp"
        android:layout_height="0dp" />
    

</androidx.constraintlayout.widget.ConstraintLayout>