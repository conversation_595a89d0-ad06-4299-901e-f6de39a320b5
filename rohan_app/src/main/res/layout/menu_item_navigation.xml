<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<!--    <TextView-->
<!--        android:id="@+id/tv_divider"-->
<!--        style="@style/DividerHorizontalStyle"-->
<!--        android:layout_marginBottom="@dimen/dip_15"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/fl_menu_navigation"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent" />-->

    <FrameLayout
        android:id="@+id/fl_menu_navigation"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_menu_navigation_translate"
            android:src="@drawable/button_secondary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcv_menu_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </FrameLayout>



</androidx.constraintlayout.widget.ConstraintLayout>