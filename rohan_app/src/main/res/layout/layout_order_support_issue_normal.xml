<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_support_desc"
        style="@style/ItemStyle"
        android:layout_marginTop="15dp"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_support_input_title"
        style="@style/ItemStyle"
        android:visibility="gone"
        android:layout_marginTop="15dp"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_support_input_large_title"
        style="@style/TitleStyle"
        android:visibility="gone"
        android:layout_marginTop="15dp"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_support_input"
        style="@style/EditTextSupportBorderStyle"
        android:gravity="top"
        android:visibility="gone"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_width="match_parent"
        android:layout_height="120dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_submit"
        android:visibility="gone"
        android:layout_marginTop="@dimen/button_margin_top"
        android:minWidth="@dimen/button_min_width"
        style="@style/ButtonSecondaryStyle"
        android:stateListAnimator="@null"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_link_to_cancel"
        android:visibility="gone"
        android:layout_marginTop="@dimen/button_margin_top"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:minWidth="@dimen/button_min_width"
        style="@style/ButtonSecondaryStyle"
        android:stateListAnimator="@null"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_cancel"
        android:visibility="gone"
        android:layout_marginTop="@dimen/button_margin_top"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:minWidth="@dimen/button_min_width"
        style="@style/ButtonSecondaryStyle"
        android:stateListAnimator="@null"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_call_support"
        android:visibility="gone"
        android:layout_marginTop="@dimen/button_margin_top"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:minWidth="@dimen/button_min_width"
        style="@style/ButtonSecondaryStyle"
        android:stateListAnimator="@null"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_call_restaurant"
        android:visibility="gone"
        android:layout_marginTop="@dimen/button_margin_top"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:minWidth="@dimen/button_min_width"
        style="@style/ButtonSecondaryStyle"
        android:stateListAnimator="@null"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_call_driver"
        android:visibility="gone"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:minWidth="@dimen/button_min_width"
        android:layout_marginTop="@dimen/button_margin_top"
        style="@style/ButtonSecondaryStyle"
        android:stateListAnimator="@null"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <View
        android:layout_width="wrap_content"
        android:layout_height="100dp" />

</LinearLayout>