<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:background="@drawable/bg_restaurant_card"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_cuisine"
        android:layout_marginLeft="@dimen/sw_9dp"
        android:layout_width="@dimen/sw_48dp"
        android:layout_height="@dimen/sw_48dp" />

    <TextView
        android:id="@+id/tv_cuisine_label"
        android:layout_marginRight="@dimen/sw_16dp"
        style="@style/SubItemStyle"
        android:gravity="center"
        android:layout_gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</LinearLayout>