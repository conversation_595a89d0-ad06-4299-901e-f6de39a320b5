<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_pickup_container"
    android:orientation="vertical"
    android:layout_height="wrap_content"
    android:layout_width="match_parent">

    <include
        android:id="@+id/in_restaurant_vertical"
        layout="@layout/restaurant_item_vertical" />

    <LinearLayout
        android:id="@+id/lay_restaurant_info"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:id="@+id/lay_restaurant_autoinfo"
        android:orientation="vertical"
        android:background="@drawable/bg_restaurant_card"
        android:layout_marginLeft="@dimen/sw_22dp"
        android:layout_marginRight="@dimen/sw_22dp"
        android:layout_marginTop="@dimen/dip_15"
        android:paddingBottom="@dimen/dip_15"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_divider"
        style="@style/DividerHorizontalStyle"
        android:layout_marginTop="@dimen/dip_30" />

<!--    <include layout="@layout/layout_restaurant_info_item" />-->

<!--    <TextView-->
<!--        android:id="@+id/tv_restaurant_closed"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        style="@style/RestaurantInfoStyle"-->
<!--        android:gravity="left"-->
<!--        android:layout_marginTop="10dp"-->
<!--        tools:text="@string/test_text_length"-->
<!--        android:layout_marginLeft="@dimen/dip_20"-->
<!--        android:layout_marginRight="@dimen/dip_20" />-->


</LinearLayout>

