<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_payment_select"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_divider_top"
        style="@style/DividerHorizontalStyle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="0dp" />

    <ImageButton
        android:id="@+id/iv_payment_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_divider_top"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@null"
        android:layout_marginTop="@dimen/sw_23dp"
        android:layout_marginBottom="@dimen/sw_19dp"
        android:clickable="false"
        android:focusable="false"
        android:paddingTop="@dimen/sw_6dp"
        android:paddingBottom="@dimen/sw_10dp"
        android:adjustViewBounds="true"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_payment_none"
        android:layout_width="@dimen/sw_80dp"
        android:layout_height="@dimen/sw_51dp" />

    <TextView
        android:id="@+id/tv_payment_title"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/iv_payment_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginLeft="@dimen/sw_32dp"
        android:text="@string/choose_payment"
        android:textSize="@dimen/sw_15sp"
        android:textColor="@color/mainText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_payment_arrow"
        android:layout_width="@dimen/right_arrow_size"
        android:layout_height="@dimen/right_arrow_size"
        android:src="@drawable/ic_right_arrow"
        android:scaleType="fitEnd"
        app:layout_constraintTop_toTopOf="@+id/tv_payment_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_payment_title"
        app:layout_constraintRight_toRightOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>