<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_membership"
    android:visibility="gone"
    android:layout_width="match_parent"
    android:layout_height="190dp" >

    <TextView
        android:id="@+id/tv_divider_top"
        style="@style/DividerHorizontalStyle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="0dp" />

    <ImageView
        android:id="@+id/iv_membership_checkout"
        android:src="@drawable/ic_membership_checkout"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:scaleType="centerInside"
        android:layout_width="330dp"
        android:layout_height="0dp" />

    <TextView
        android:id="@+id/tv_membership_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_membership_subtitle"
        app:layout_constraintVertical_chainStyle="packed"
        style="@style/SubTitleStyle"
        android:textColor="@color/mainText"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_membership_subtitle"
        app:layout_constraintLeft_toLeftOf="@+id/tv_membership_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_membership_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_membership_apply"
        style="@style/LargeTitleStyle"
        android:textColor="@color/mainText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_membership_apply"
        app:layout_constraintLeft_toLeftOf="@+id/tv_membership_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_membership_subtitle"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="20dp"
        style="@style/SubTitleStyle"
        android:textColor="@color/goldSubText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_membership_apply_arrow"
        android:layout_width="@dimen/right_arrow_size"
        android:layout_height="12dp"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="8dp"
        android:src="@drawable/ic_membership_arrow"
        app:layout_constraintTop_toTopOf="@+id/tv_membership_apply"
        app:layout_constraintBottom_toBottomOf="@+id/tv_membership_apply"
        app:layout_constraintLeft_toRightOf="@+id/tv_membership_apply" />


</androidx.constraintlayout.widget.ConstraintLayout>