<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_title_icon"
        android:tint="@color/fun_n6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_back" />

    <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/title_profile"
            android:includeFontPadding="false"
            android:gravity="center_vertical"
            style="@style/TitleStyle"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintTop_toTopOf="@+id/iv_back"/>

    <androidx.core.widget.NestedScrollView
            app:layout_constraintTop_toBottomOf="@+id/iv_back"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="0dp"
            android:layout_height="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            <TextView
                    android:id="@+id/tv_section_language"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginRight="@dimen/margin_right"
                    android:layout_marginLeft="@dimen/margin_left"
                    android:layout_width="0dp"
                    style="@style/TitleStyle"
                    android:gravity="center_vertical"
                    android:layout_height="@dimen/setting_item_height"
                    android:text="@string/Language"/>

            <TextView
                    app:layout_constraintTop_toTopOf="@+id/tv_language_hans"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                    android:layout_width="0dp"
                    style="@style/DividerHorizontalStyle"/>

            <TextView
                    android:id="@+id/tv_language_hans"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/setting_item_height"
                    app:layout_constraintTop_toBottomOf="@+id/tv_section_language"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/margin_left"
                    style="@style/ItemStyle"
                    android:text="简体中文"/>

            <ImageView
                    android:id="@+id/iv_language_hans"
                    android:layout_width="@dimen/setting_select_icon_size"
                    android:layout_height="@dimen/setting_select_icon_size"
                    app:layout_constraintTop_toTopOf="@+id/tv_language_hans"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_language_hans"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginRight="@dimen/margin_right"
                    android:src="@drawable/ic_check_black_24dp"/>

            <TextView
                    app:layout_constraintTop_toTopOf="@+id/tv_language_hk"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                    android:layout_width="0dp"
                    style="@style/DividerHorizontalStyle"/>

            <TextView
                    android:id="@+id/tv_language_hk"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/setting_item_height"
                    app:layout_constraintTop_toBottomOf="@+id/tv_language_hans"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/margin_left"
                    style="@style/ItemStyle"
                    android:text="繁体中文"/>

            <ImageView
                    android:id="@+id/iv_language_hk"
                    android:layout_width="@dimen/setting_select_icon_size"
                    android:layout_height="@dimen/setting_select_icon_size"
                    app:layout_constraintTop_toTopOf="@+id/tv_language_hk"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_language_hk"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginRight="@dimen/margin_right"
                    android:src="@drawable/ic_check_black_24dp"/>

            <TextView
                    app:layout_constraintTop_toTopOf="@+id/tv_language_en"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                    android:layout_width="0dp"
                    style="@style/DividerHorizontalStyle"/>

            <TextView
                    android:id="@+id/tv_language_en"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/setting_item_height"
                    app:layout_constraintTop_toBottomOf="@+id/tv_language_hk"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/margin_left"
                    style="@style/ItemStyle"
                    android:text="English"/>

            <TextView
                    app:layout_constraintTop_toTopOf="@+id/tv_language_es"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                    android:layout_width="0dp"
                    style="@style/DividerHorizontalStyle"/>

            <ImageView
                    android:id="@+id/iv_language_en"
                    android:layout_width="@dimen/setting_select_icon_size"
                    android:layout_height="@dimen/setting_select_icon_size"
                    app:layout_constraintTop_toTopOf="@+id/tv_language_en"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_language_en"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginRight="@dimen/margin_right"
                    android:src="@drawable/ic_check_black_24dp"/>

            <TextView
                    android:id="@+id/tv_language_es"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/setting_item_height"
                    app:layout_constraintTop_toBottomOf="@+id/tv_language_en"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/margin_left"
                    style="@style/ItemStyle"
                    android:text="Español"/>

            <ImageView
                    android:id="@+id/iv_language_es"
                    android:layout_width="@dimen/setting_select_icon_size"
                    android:layout_height="@dimen/setting_select_icon_size"
                    app:layout_constraintTop_toTopOf="@+id/tv_language_es"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_language_es"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginRight="@dimen/margin_right"
                    android:src="@drawable/ic_check_black_24dp"/>

            <TextView
                android:id="@+id/tv_section_person"
                app:layout_constraintTop_toBottomOf="@+id/tv_language_es"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginRight="@dimen/margin_right"
                android:layout_marginLeft="@dimen/margin_left"
                android:layout_width="0dp"
                style="@style/TitleStyle"
                android:gravity="center_vertical"
                android:layout_height="@dimen/setting_item_height"
                android:visibility="gone"
                android:text="@string/Personal_info"/>

            <TextView
                    app:layout_constraintTop_toTopOf="@+id/tv_person_coupon"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                    android:layout_width="0dp"
                    android:visibility="gone"
                    style="@style/DividerHorizontalStyle"/>

            <TextView
                    android:id="@+id/tv_person_coupon"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/setting_item_height"
                    app:layout_constraintTop_toBottomOf="@+id/tv_section_person"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/margin_left"
                    style="@style/ItemStyle"
                    android:visibility="gone"
                    android:text="@string/my_coupon"/>

            <ImageView
                    android:id="@+id/iv_person_coupon"
                    android:layout_width="@dimen/setting_arrow_icon_size"
                    android:layout_height="@dimen/setting_arrow_icon_size"
                    app:layout_constraintTop_toTopOf="@+id/tv_person_coupon"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_person_coupon"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginRight="@dimen/margin_right"
                    android:visibility="gone"
                    android:src="@drawable/ic_right_arrow"/>

            <TextView
                    app:layout_constraintTop_toTopOf="@+id/tv_person_coin"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                    android:layout_width="0dp"
                    android:visibility="gone"
                    style="@style/DividerHorizontalStyle"/>

            <TextView
                    android:id="@+id/tv_person_coin"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/setting_item_height"
                    app:layout_constraintTop_toBottomOf="@+id/tv_person_coupon"
                    android:gravity="center_vertical"
                    android:paddingLeft="@dimen/margin_left"
                    style="@style/ItemStyle"
                    android:visibility="gone"
                    android:text="@string/my_coins"/>

            <ImageView
                    android:id="@+id/iv_peron_coin"
                    android:layout_width="@dimen/setting_arrow_icon_size"
                    android:layout_height="@dimen/setting_arrow_icon_size"
                    app:layout_constraintTop_toTopOf="@+id/tv_person_coin"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_person_coin"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginRight="@dimen/margin_right"
                    android:visibility="gone"
                    android:src="@drawable/ic_right_arrow"/>

            <TextView
                app:layout_constraintTop_toTopOf="@+id/tv_person_card"
                app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                android:layout_width="0dp"
                android:visibility="visible"
                style="@style/DividerHorizontalStyle"/>

            <TextView
                android:id="@+id/tv_person_card"
                android:layout_width="match_parent"
                android:layout_height="@dimen/setting_item_height"
                app:layout_constraintTop_toBottomOf="@+id/tv_person_coin"
                android:gravity="center_vertical"
                android:paddingLeft="@dimen/margin_left"
                style="@style/ItemStyle"
                android:visibility="visible"
                android:text="@string/edit_payment"/>

            <ImageView
                android:id="@+id/iv_person_card"
                android:layout_width="@dimen/setting_arrow_icon_size"
                android:layout_height="@dimen/setting_arrow_icon_size"
                app:layout_constraintTop_toTopOf="@+id/tv_person_card"
                app:layout_constraintBottom_toBottomOf="@+id/tv_person_card"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginRight="@dimen/margin_right"
                android:visibility="visible"
                android:src="@drawable/ic_right_arrow"/>

            <TextView
                    android:id="@+id/tv_section_account"
                    app:layout_constraintTop_toBottomOf="@+id/tv_person_card"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                    android:layout_width="0dp"
                    style="@style/TitleStyle"
                    android:gravity="center_vertical"
                    android:layout_height="@dimen/setting_item_height"
                    android:text="@string/account"/>

            <TextView
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_account"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_account"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_section_account"
                    android:layout_width="0dp"
                    style="@style/DividerHorizontalStyle"/>

            <TextView
                    android:id="@+id/tv_account_safety"
                    app:layout_constraintTop_toBottomOf="@+id/tv_section_account"
                    android:text="@string/account_safety"
                    android:gravity="center_vertical"
                    style="@style/ItemStyle"
                    android:paddingLeft="@dimen/margin_left"
                    android:layout_height="@dimen/setting_item_height"
                    android:layout_width="match_parent"/>

            <TextView
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                    app:layout_constraintTop_toTopOf="@+id/tv_login_logout"
                    android:layout_width="0dp"
                    style="@style/DividerHorizontalStyle"/>

            <TextView
                android:id="@+id/tv_login_logout"
                app:layout_constraintTop_toBottomOf="@+id/tv_account_safety"
                android:text="@string/login"
                android:gravity="center_vertical"
                style="@style/ItemStyle"
                android:paddingLeft="@dimen/margin_left"
                android:layout_height="@dimen/setting_item_height"
                android:layout_width="match_parent"/>

            <TextView
                app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                app:layout_constraintTop_toTopOf="@+id/tv_feedback"
                android:layout_width="0dp"
                style="@style/DividerHorizontalStyle"/>

            <TextView
                    android:id="@+id/tv_feedback"
                    app:layout_constraintTop_toBottomOf="@+id/tv_login_logout"
                    android:text="@string/feedback"
                    android:gravity="center_vertical"
                    style="@style/ItemStyle"
                    android:paddingLeft="@dimen/margin_left"
                    android:layout_height="@dimen/setting_item_height"
                    android:layout_width="match_parent"/>

            <TextView
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_language"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_language"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_feedback"
                    android:layout_width="0dp"
                    style="@style/DividerHorizontalStyle"/>

            <TextView
                    android:id="@+id/tv_version"
                    app:layout_constraintTop_toBottomOf="@+id/tv_feedback"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:text="@string/ricepo"
                    android:textColor="@color/subTextMenu"
                    android:textSize="@dimen/font_size_h8"
                    android:layout_marginTop="30dp"
                    android:paddingBottom="30dp"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>

            <TextView
                    android:id="@+id/tv_test_memory"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_section_account"
                    app:layout_constraintRight_toRightOf="@+id/tv_section_account"
                    app:layout_constraintTop_toBottomOf="@+id/tv_version"
                    android:text="@string/TEST"
                    android:visibility="gone"
                    android:gravity="center_vertical"
                    style="@style/ItemStyle"
                    android:layout_height="@dimen/setting_item_height"
                    android:layout_width="0dp"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>