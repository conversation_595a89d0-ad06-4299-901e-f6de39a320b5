<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/ic_shot"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.ricepo.style.round.RoundLayout
        app:round_corner="2dp"
        android:layout_margin="1.5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_image"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:scaleType="centerCrop"
            android:adjustViewBounds="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </com.ricepo.style.round.RoundLayout>

</FrameLayout>
