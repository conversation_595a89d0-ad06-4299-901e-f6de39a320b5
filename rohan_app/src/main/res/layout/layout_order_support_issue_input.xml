<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_support_input"
        style="@style/EditTextSupportBorderStyle"
        android:gravity="top"
        android:layout_marginTop="15dp"
        android:layout_width="match_parent"
        android:layout_height="120dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_submit_primary"
        android:layout_marginTop="@dimen/button_margin_top"
        android:layout_marginBottom="100dp"
        android:visibility="gone"
        style="@style/ButtonPrimaryStyle"
        android:minWidth="@dimen/button_min_width"
        android:stateListAnimator="@null"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_submit_secondary"
        android:layout_marginTop="@dimen/button_margin_top"
        android:layout_marginBottom="100dp"
        android:visibility="gone"
        style="@style/ButtonSecondaryStyle"
        android:minWidth="@dimen/button_min_width"
        android:stateListAnimator="@null"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</LinearLayout>