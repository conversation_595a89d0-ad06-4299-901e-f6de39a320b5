<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_menu_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginLeft="@dimen/sw_22dp"
        android:layout_marginRight="@dimen/sw_22dp"
        android:background="@drawable/bg_restaurant_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />


</androidx.constraintlayout.widget.ConstraintLayout>