<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_back" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_restaurant_submore"
        android:layout_below="@+id/lay_title"
        android:layout_above="@+id/in_menu_cart"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        android:id="@+id/in_menu_cart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        layout="@layout/layout_menu_cart_bar" />

    <FrameLayout
        android:id="@+id/fl_restaurant_page"
        android:layout_below="@+id/lay_title"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


</RelativeLayout>