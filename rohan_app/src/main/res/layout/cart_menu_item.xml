<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/cart_menu_plate_height">

    <ImageView
        android:id="@+id/iv_restaurant_menu_vert"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/barrier_restaurant_menu"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:clickable="false"
        android:src="@drawable/ic_vert_line"
        android:layout_marginLeft="@dimen/sw_11dp"
        android:layout_marginRight="@dimen/sw_11dp"
        android:layout_marginBottom="@dimen/sw_4dp"
        android:scaleType="fitXY"
        android:layout_width="@dimen/sw_3dp"
        android:layout_height="@dimen/sw_17dp" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_restaurant_menu"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:constraint_referenced_ids="iv_restaurant_menu, tv_menu_cart"
        app:barrierDirection="start"
        android:layout_width="@dimen/sw_11dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_restaurant_menu_bg"
        app:layout_constraintRight_toLeftOf="@+id/tv_menu_cart"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        tools:src="@drawable/bg_plate"
        android:src="@drawable/bg_plate"
        android:clickable="false"
        android:layout_width="@dimen/sw_24dp"
        android:layout_height="@dimen/sw_30dp" />

    <ImageView
        android:id="@+id/iv_restaurant_menu"
        app:layout_constraintLeft_toLeftOf="@+id/iv_restaurant_menu_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_restaurant_menu_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_restaurant_menu_bg"
        android:clickable="false"
        android:layout_width="@dimen/sw_23dp"
        android:layout_height="@dimen/sw_23dp" />

    <TextView
        android:id="@+id/tv_menu_cart"
        android:singleLine="true"
        style="@style/SubTitleMenuStyle"
        android:textSize="@dimen/sw_15sp"
        app:layout_constraintLeft_toRightOf="@+id/iv_restaurant_menu_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="@string/search_menu"
        android:gravity="center_vertical"
        android:layout_marginLeft="@dimen/sw_3dp"
        android:layout_marginBottom="@dimen/sw_4dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>