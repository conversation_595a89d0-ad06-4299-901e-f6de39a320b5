<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@id/iv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_title_icon"
        android:src="@drawable/ic_back"
        android:tint="@color/fun_n6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/tv_order_help"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:includeFontPadding="false"
        android:gravity="center"
        style="@style/TitleStyle"
        android:textSize="@dimen/font_size_h5"
        android:textFontWeight="650"
        android:visibility="gone"
        android:layout_marginRight="20dp"
        android:src="@drawable/ic_connect"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_back"
        app:layout_constraintTop_toTopOf="@id/iv_back" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/srl_order_content"
        app:layout_constraintTop_toBottomOf="@id/iv_back"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp" >

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nsl_checkout_content"
            android:fillViewport="false"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <include
                    android:id="@+id/in_order_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/sw_section_space"
                    layout="@layout/layout_order_status" />

                <TextView
                    android:id="@+id/tv_divider_pickup_top"
                    android:layout_width="match_parent"
                    android:visibility="gone"
                    style="@style/DividerHorizontalStyle" />

                <LinearLayout
                    android:id="@+id/lay_order_pickup"
                    android:layout_marginLeft="@dimen/margin_right"
                    android:layout_marginRight="@dimen/margin_right"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <include
                        android:id="@+id/in_checkout_pickup"
                        layout="@layout/item_checkout_pickup" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tv_divider_pickup"
                    android:layout_width="match_parent"
                    android:visibility="gone"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="@dimen/sw_section_space"
                    style="@style/DividerHorizontalStyle" />

<!--                <TextView-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="10dp" />-->

                <!-- map -->
                <include
                    android:id="@+id/in_order_map"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/sw_section_space"
                    layout="@layout/layout_order_map" />

                <include
                    android:id="@+id/in_red_link"
                    android:layout_marginLeft="@dimen/card_side_margin"
                    android:layout_marginRight="@dimen/card_side_margin"
                    android:layout_marginBottom="@dimen/sw_section_space"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    layout="@layout/layout_share_red" />

                <com.ricepo.app.view.ShareCardView
                    android:id="@+id/sc_share"
                    android:visibility="gone"
                    android:layout_marginLeft="@dimen/card_side_margin"
                    android:layout_marginRight="@dimen/card_side_margin"
                    android:layout_marginBottom="@dimen/sw_section_space"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <LinearLayout
                    android:id="@+id/ll_order_info"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:orientation="vertical"
                    android:background="@drawable/bg_restaurant_card"
                    android:layout_marginLeft="@dimen/card_side_margin"
                    android:layout_marginRight="@dimen/card_side_margin"
                    android:layout_marginBottom="@dimen/sw_section_space"
                    android:paddingTop="@dimen/dip_15"
                    android:paddingBottom="@dimen/dip_15"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <include
                        android:id="@+id/in_order_restaurant"
                        layout="@layout/layout_order_restaurant" />

                    <LinearLayout
                        android:id="@+id/ll_order_items"
                        android:orientation="vertical"
                        android:paddingLeft="@dimen/card_side_margin"
                        android:paddingRight="@dimen/card_side_margin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_order_fees"
                    android:visibility="gone"
                    android:orientation="vertical"
                    android:background="@drawable/bg_restaurant_card"
                    android:layout_marginLeft="@dimen/card_side_margin"
                    android:layout_marginRight="@dimen/card_side_margin"
                    android:layout_marginBottom="@dimen/sw_section_space"
                    android:paddingTop="@dimen/dip_15"
                    android:paddingBottom="@dimen/dip_15"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <include
                    android:id="@+id/in_order_payment"
                    layout="@layout/layout_order_payment" />

                <include
                    android:id="@+id/in_order_operator"
                    layout="@layout/layout_order_operator" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/sw_section_space" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <ImageView
        android:id="@+id/iv_diary_button"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/diary_button_cn"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <FrameLayout
        android:id="@+id/fl_order_page"
        android:layout_marginTop="45dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>