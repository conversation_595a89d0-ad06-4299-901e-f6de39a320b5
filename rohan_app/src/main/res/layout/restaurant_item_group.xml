<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_restaurant_group_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/RestaurantDividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_restaurant_jump_click"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ll_restaurant_group_name"
        app:layout_constraintBottom_toBottomOf="@+id/ll_restaurant_group_name"
        app:layout_constraintRight_toLeftOf="@+id/restaurant_sort"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <ImageView
        android:id="@+id/iv_restaurant_name"
        android:layout_gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ll_restaurant_group_name"
        app:layout_constraintBottom_toBottomOf="@+id/ll_restaurant_group_name"
        android:layout_marginBottom="@dimen/dip_3"
        android:layout_marginLeft="@dimen/sw_26dp"
        android:layout_width="@dimen/sw_24dp"
        android:layout_height="@dimen/sw_24dp" />

    <LinearLayout
        android:id="@+id/ll_restaurant_group_name"
        app:layout_constraintLeft_toRightOf="@+id/iv_restaurant_name"
        app:layout_constraintRight_toLeftOf="@+id/restaurant_sort"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_restaurant_group_desc"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginRight="@dimen/restaurant_text_margin"
        android:orientation="horizontal"
        android:gravity="left"
        android:layout_width="0dp"
        android:layout_height="40dp" >

        <TextView
            android:id="@+id/tv_restaurant_group_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textColor="@color/mainText"
            android:textSize="@dimen/font_size_h2"
            android:shadowRadius="1.0"
            android:shadowColor="@color/restaurant_name_shadow"
            android:shadowDx="1.0"
            android:shadowDy="3.0"
            android:gravity="left"
            android:ellipsize="marquee"
            android:singleLine="true"
            android:marqueeRepeatLimit="marquee_forever"
            android:drawableLeft="@drawable/ic_img_title"
            android:layout_marginBottom="@dimen/dip_3" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_category_jump"
            android:src="@drawable/ic_arrow_down"
            android:layout_gravity="center_vertical"
            android:paddingTop="7dp"
            android:paddingBottom="9dp"
            android:paddingLeft="2dp"
            android:scaleType="fitStart"
            android:layout_height="40dp"
            android:tint="@color/fun_n6"
            android:layout_width="40dp" />

    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/restaurant_sort"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="sort"
        android:layout_marginRight="@dimen/sw_36dp"
        android:textColor="@color/fun_n5"
        android:backgroundTint="@color/card_background"
        app:cornerRadius="12dp"
        android:minHeight="@dimen/sw_34dp"
        app:icon="@drawable/ic_sort"
        app:iconGravity="end"
        app:iconTint="@color/fun_n5"
        />

    <TextView
        android:id="@+id/tv_restaurant_group_desc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/subText"
        android:textSize="@dimen/font_size_h6"
        android:gravity="left"
        app:layout_constraintLeft_toLeftOf="@+id/ll_restaurant_group_name"
        app:layout_constraintRight_toRightOf="@+id/ll_restaurant_group_name"
        app:layout_constraintTop_toBottomOf="@+id/ll_restaurant_group_name"
        app:layout_constraintBottom_toTopOf="@+id/tv_restaurant_group_bottom_space"
        android:layout_marginTop="@dimen/_sw_2dp"
        android:layout_marginBottom="@dimen/sw_9dp" />


    <!-- 13dp because of name bottom have space -->
    <TextView
        android:id="@+id/tv_restaurant_group_bottom_space"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="visible"
        tools:visibility="visible"
        android:layout_height="@dimen/sw_6dp"
        android:layout_width="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>