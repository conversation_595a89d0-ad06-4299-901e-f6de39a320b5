<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_close" />

    <androidx.core.widget.NestedScrollView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lay_title"
        app:layout_constraintBottom_toBottomOf="parent"
        android:fillViewport="true"
        android:layout_gravity="fill_vertical"
        android:layout_width="match_parent"
        android:layout_height="0dp" >

        <LinearLayout
            android:orientation="vertical"
            android:layout_marginLeft="@dimen/card_side_margin"
            android:layout_marginRight="@dimen/card_side_margin"
            android:layout_marginBottom="@dimen/dip_72"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/ll_support_food_group"
                android:orientation="vertical"
                android:layout_marginTop="25dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" >

                <TextView
                    android:id="@+id/tv_support_food"
                    style="@style/TitleStyle"
                    android:paddingBottom="20dp"
                    android:paddingLeft="@dimen/card_side_margin"
                    android:paddingRight="@dimen/card_side_margin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <LinearLayout
                    android:id="@+id/ll_support_food"
                    android:background="@drawable/bg_restaurant_card"
                    android:paddingLeft="@dimen/card_side_margin"
                    android:paddingRight="@dimen/card_side_margin"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_support_method_group"
                android:orientation="vertical"
                android:layout_marginTop="25dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" >

                <TextView
                    android:id="@+id/tv_support_method"
                    style="@style/TitleStyle"
                    android:paddingBottom="20dp"
                    android:paddingLeft="@dimen/card_side_margin"
                    android:paddingRight="@dimen/card_side_margin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <LinearLayout
                    android:id="@+id/ll_support_method"
                    android:background="@drawable/bg_restaurant_card"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/card_side_margin"
                    android:paddingRight="@dimen/card_side_margin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_support_problem_group"
                android:orientation="vertical"
                android:layout_marginTop="25dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" >

                <TextView
                    android:id="@+id/tv_support_problem"
                    style="@style/TitleStyle"
                    android:paddingBottom="20dp"
                    android:paddingLeft="@dimen/card_side_margin"
                    android:paddingRight="@dimen/card_side_margin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <LinearLayout
                    android:id="@+id/ll_support_problem"
                    android:background="@drawable/bg_restaurant_card"
                    android:paddingLeft="@dimen/card_side_margin"
                    android:paddingRight="@dimen/card_side_margin"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_support_uppic"
                android:orientation="vertical"
                android:layout_marginTop="25dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" >

                <TextView
                    android:id="@+id/tv_support_uppic"
                    style="@style/TitleStyle"
                    android:paddingBottom="10dp"
                    android:paddingLeft="@dimen/card_side_margin"
                    android:paddingRight="@dimen/card_side_margin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tv_support_uppic_subtitle"
                    style="@style/ItemStyle"
                    android:paddingBottom="20dp"
                    android:paddingLeft="@dimen/card_side_margin"
                    android:paddingRight="@dimen/card_side_margin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_support_input"
                android:orientation="vertical"
                android:layout_marginTop="25dp"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" >

                <TextView
                    android:id="@+id/tv_support_input"
                    style="@style/TitleStyle"
                    android:paddingLeft="@dimen/card_side_margin"
                    android:paddingRight="@dimen/card_side_margin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <include
                android:id="@+id/in_support_normal"
                layout="@layout/layout_order_support_issue_normal" />


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>