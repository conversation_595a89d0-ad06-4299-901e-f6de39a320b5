<?xml version="1.0" encoding="utf-8"?>

<com.ricepo.style.round.RoundLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:round_corner="@dimen/dip_12"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dip_260" >

    <com.ricepo.style.shadow.ShadowLayout
        app:yOffset="4dp"
        app:xOffset="-1dp"
        app:shadowType="1"
        app:blurRadius="2dp"
        app:shadowRadius="0dp"
        app:bgColor="@color/colorPrimary"
        app:shadowColor="@color/second_satisfied_button_shadow"
        android:layout_height="match_parent"
        android:layout_width="match_parent">

            <!-- left -->
            <ImageView
                android:id="@+id/iv_left_bg"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/iv_middle_bg"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="@dimen/dip_15"
                android:layout_width="@dimen/plate_big_width"
                android:layout_height="@dimen/plate_big_height"
                tools:src="@drawable/bg_plate" />

            <ImageView
                android:id="@+id/iv_left"
                app:layout_constraintLeft_toLeftOf="@+id/iv_left_bg"
                app:layout_constraintRight_toRightOf="@+id/iv_left_bg"
                app:layout_constraintBottom_toBottomOf="@+id/iv_left_bg"
                android:layout_marginBottom="35dp"
                android:layout_width="0dp"
                android:layout_height="90dp"  />

            <!-- middle -->
            <ImageView
                android:id="@+id/iv_middle_bg"
                app:layout_constraintLeft_toRightOf="@+id/iv_left_bg"
                app:layout_constraintBottom_toBottomOf="@+id/iv_left_bg"
                android:layout_marginBottom="10dp"
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                tools:src="@drawable/bg_plate" />

            <ImageView
                android:id="@+id/iv_middle"
                app:layout_constraintLeft_toLeftOf="@+id/iv_middle_bg"
                app:layout_constraintBottom_toBottomOf="@+id/iv_middle_bg"
                app:layout_constraintRight_toRightOf="@+id/iv_middle_bg"
                android:layout_marginBottom="25dp"
                android:layout_width="wrap_content"
                android:layout_height="60dp" />

            <TextView
                android:id="@+id/tv_restaurant_name"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_left_bg"
                app:layout_constraintBottom_toTopOf="@+id/tv_restaurant_info"
                android:layout_marginLeft="@dimen/restaurant_text_margin"
                android:layout_width="210dp"
                android:layout_height="wrap_content"
                android:maxLines="2"
                android:ellipsize="end"
                android:gravity="left"
                style="@style/LargeTitleStyle" />

            <TextView
                android:id="@+id/tv_restaurant_info"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="@+id/tv_restaurant_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_name"
                app:layout_constraintBottom_toTopOf="@+id/tv_restaurant_vip_promotion"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/restaurant_text_margin"
                android:maxLines="1"
                android:ellipsize="end"
                style="@style/SubTitleMenuStyle"
                android:layout_marginTop="10dp" />

            <TextView
                android:id="@+id/tv_restaurant_vip_promotion"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="@+id/tv_restaurant_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_info"
                app:layout_constraintBottom_toTopOf="@+id/tv_restaurant_promotion"
                android:maxLines="1"
                android:ellipsize="end"
                style="@style/SubTitleMenuStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/restaurant_text_margin"
                android:layout_marginTop="5dp"
                android:visibility="invisible"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_restaurant_promotion"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="@+id/tv_restaurant_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_vip_promotion"
                android:maxLines="1"
                android:ellipsize="end"
                style="@style/SubTitleMenuStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/restaurant_text_margin"
                android:layout_marginTop="5dp"
                android:visibility="invisible"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_restaurant_closed"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="@+id/tv_restaurant_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_promotion"
                android:maxLines="1"
                android:ellipsize="end"
                style="@style/SubTitleMenuStyle"
                android:textColor="@color/alert"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/restaurant_text_margin"
                android:layout_marginTop="5dp"
                android:visibility="gone"
                tools:visibility="visible" />

    </com.ricepo.style.shadow.ShadowLayout>

</com.ricepo.style.round.RoundLayout>