<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TableLayout
        android:id="@+id/ll_order_restaurant"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_order_created_at"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        android:orientation="horizontal"
        android:gravity="left"
        android:shrinkColumns="0"
        android:stretchColumns="1"
        android:layout_width="0dp"
        android:layout_height="wrap_content" >

        <TableRow>
            <TextView
                android:id="@+id/tv_order_restaurant_name"
                tools:text="@string/restaurant_open"
                style="@style/TitleStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_order_group_order"
                android:text="@string/label_group_order"
                style="@style/SubItemStyle"
                android:layout_marginLeft="@dimen/dip_3"
                android:maxLines="1"
                android:visibility="gone"
                android:layout_gravity="bottom"
                android:layout_marginBottom="@dimen/dip_1"
                tools:visibility="visible"
                android:textSize="@dimen/font_size_h9"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </TableRow>

    </TableLayout>


    <TextView
        android:id="@+id/tv_order_created_at"
        style="@style/SubTitleStyle"
        app:layout_constraintLeft_toLeftOf="@+id/ll_order_restaurant"
        app:layout_constraintTop_toBottomOf="@+id/ll_order_restaurant"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="@string/status_created"
        android:layout_marginTop="@dimen/sw_8dp"
        android:layout_marginBottom="15dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_order_earn_point"
        style="@style/SubTitleStyle"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_order_restaurant"
        android:layout_marginTop="@dimen/sw_3dp"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/sw_22dp" />

    <TextView
        android:id="@+id/tv_order_restaurnat_divider"
        style="@style/RestaurantDividerHorizontalStyle"
        android:visibility="gone"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>