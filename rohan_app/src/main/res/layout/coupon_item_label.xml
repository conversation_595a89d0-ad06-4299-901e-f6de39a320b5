<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dip_80"
        android:layout_marginLeft="@dimen/dip_40"
        android:layout_marginTop="@dimen/dip_12"
        android:layout_marginRight="@dimen/dip_40"
        android:layout_marginBottom="@dimen/dip_12"
        android:gravity="center"
        android:orientation="horizontal"
        app:cardCornerRadius="@dimen/card_radius"
        app:cardBackgroundColor="@color/card_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_item_label"
            style="@style/ItemStyle"
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/order_fees_margin_end"
            android:textSize="@dimen/font_size_h5"
            app:drawableEndCompat="@drawable/ic_lucky_right"
            android:drawableTint="@color/mainText"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </com.google.android.material.card.MaterialCardView>


</androidx.constraintlayout.widget.ConstraintLayout>