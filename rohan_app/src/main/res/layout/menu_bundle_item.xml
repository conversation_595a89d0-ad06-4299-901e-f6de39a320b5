<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:layout_marginRight="-2dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.ricepo.style.view.VerticalTextView
        android:id="@+id/tv_bundle_name"
        app:textSize="@dimen/font_size_h6"
        app:maxLines="1"
        app:lineSpace="0dp"
        app:normalCharPadding="-4dp"
        android:gravity="center"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <FrameLayout
        android:id="@+id/fl_bundle_line"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="4dp">

        <ImageView
            android:id="@+id/iv_bundle_line"
            android:src="@drawable/ic_hor_line"
            android:layout_gravity="center"
            android:scaleType="centerCrop"
            android:layout_width="match_parent"
            android:layout_height="2dp" />

    </FrameLayout>

</LinearLayout>