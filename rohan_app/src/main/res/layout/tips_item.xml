<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:gravity="center_vertical"
            android:paddingLeft="30dp"
            android:paddingRight="30dp"
            style="@style/ItemStyle" />

        <TextView
            app:layout_constraintLeft_toLeftOf="@+id/tv_tip"
            app:layout_constraintRight_toRightOf="@+id/tv_tip"
            app:layout_constraintTop_toBottomOf="@id/tv_tip"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            style="@style/DividerHorizontalStyle" />

</androidx.constraintlayout.widget.ConstraintLayout>