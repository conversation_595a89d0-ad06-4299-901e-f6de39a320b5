<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <TextView
        android:id="@id/tv_address"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/feedback"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_close"
        app:layout_constraintTop_toTopOf="@id/iv_close" />

    <TextView
        android:id="@+id/tv_image_helpful"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        app:layout_constraintTop_toBottomOf="@id/iv_close"
        app:layout_constraintLeft_toLeftOf="parent"
        android:gravity="center_vertical"
        android:layout_marginTop="30dp"
        android:paddingLeft="30dp"
        style="@style/ItemStyle"
        android:text="@string/image_is_helpful" />

    <ImageView
        android:id="@+id/iv_image_helpful"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/tv_image_helpful"
        app:layout_constraintBottom_toBottomOf="@+id/tv_image_helpful"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="30dp"
        android:src="@drawable/ic_check_black_24dp" />

    <TextView
        app:layout_constraintLeft_toLeftOf="@+id/tv_image_helpful"
        app:layout_constraintRight_toRightOf="@+id/iv_image_helpful"
        app:layout_constraintTop_toBottomOf="@id/tv_image_helpful"
        android:layout_marginLeft="30dp"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        style="@style/DividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_wrong_image"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        app:layout_constraintTop_toBottomOf="@id/tv_image_helpful"
        app:layout_constraintLeft_toLeftOf="parent"
        android:gravity="center_vertical"
        android:paddingLeft="30dp"
        style="@style/ItemStyle"
        android:text="@string/wrong_image" />

    <ImageView
        android:id="@+id/iv_wrong_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/tv_wrong_image"
        app:layout_constraintBottom_toBottomOf="@+id/tv_wrong_image"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginRight="30dp"
        android:visibility="invisible"
        android:src="@drawable/ic_check_black_24dp" />

    <TextView
        app:layout_constraintLeft_toLeftOf="@+id/tv_wrong_image"
        app:layout_constraintRight_toRightOf="@+id/iv_wrong_image"
        app:layout_constraintTop_toBottomOf="@+id/tv_wrong_image"
        android:layout_marginLeft="30dp"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        style="@style/DividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_other_feedback"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        app:layout_constraintTop_toBottomOf="@id/tv_wrong_image"
        app:layout_constraintLeft_toLeftOf="parent"
        android:gravity="center_vertical"
        android:paddingLeft="30dp"
        style="@style/ItemStyle"
        android:text="@string/other_feedback" />

    <ImageView
        android:id="@+id/iv_other_feedback"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/tv_other_feedback"
        app:layout_constraintBottom_toBottomOf="@+id/tv_other_feedback"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="invisible"
        android:layout_marginRight="30dp"
        android:src="@drawable/ic_check_black_24dp" />

    <androidx.appcompat.widget.AppCompatEditText
        app:layout_constraintTop_toBottomOf="@+id/tv_other_feedback"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/RegularStyle"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="30dp"
        android:singleLine="true"
        android:ellipsize="end"
        android:scrollHorizontally="false"
        android:imeOptions="actionSearch"
        android:id="@+id/et_other_feedback"
        android:hint="@string/reason_or_suggestion"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_feedback_sumit"
        app:layout_constraintTop_toBottomOf="@+id/et_other_feedback"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:text="@string/submit"
        style="@style/ButtonPrimaryStyle" />

</androidx.constraintlayout.widget.ConstraintLayout>