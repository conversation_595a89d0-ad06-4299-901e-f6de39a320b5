<?xml version="1.0" encoding="utf-8"?>

<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginRight="@dimen/card_side_margin"
    android:layout_marginLeft="@dimen/card_side_margin"
    android:layout_marginTop="10dp"
    app:cardBackgroundColor="@color/card_background"
    app:cardCornerRadius="@dimen/card_radius"
    app:cardElevation="0dp"
    android:layout_height="wrap_content"
    android:layout_width="match_parent">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:orientation="vertical"
        app:divider="@color/divider"
        app:showDividers="middle"
        app:dividerPadding="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.ricepo.style.round.RoundLayout
                android:id="@+id/round_order_address"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:stroke_width="1dp"
                app:stroke_color="@color/map_circle_border_shadow"
                app:round_corner="@dimen/sw_28dp"
                android:layout_marginTop="@dimen/sw_20dp"
                android:layout_marginBottom="@dimen/sw_16dp"
                android:layout_marginLeft="@dimen/sw_26dp"
                android:alpha="0"
                android:layout_width="@dimen/sw_56dp"
                android:layout_height="@dimen/sw_56dp" >

                <ImageView
                    android:id="@+id/iv_order_address"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_width="0dp"
                    android:layout_height="0dp" />

            </com.ricepo.style.round.RoundLayout>

            <com.ricepo.style.round.RoundLayout
                android:id="@+id/round_order_address_placeholder"
                app:layout_constraintTop_toTopOf="@+id/round_order_address"
                app:layout_constraintBottom_toBottomOf="@+id/round_order_address"
                app:layout_constraintLeft_toLeftOf="@+id/round_order_address"
                app:stroke_width="1dp"
                app:stroke_color="@color/dividerHighlight"
                android:layout_marginTop="1dp"
                app:round_corner="@dimen/sw_28dp"
                android:layout_width="@dimen/sw_56dp"
                android:layout_height="@dimen/sw_56dp" />

            <TextView
                android:id="@+id/tv_order_status"
                app:layout_constraintLeft_toRightOf="@+id/round_order_address"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/tv_restaurant_label"
                app:layout_constraintVertical_chainStyle="packed"
                android:layout_marginLeft="20dp"
                android:layout_marginBottom="4dp"
                android:layout_marginRight="8dp"
                android:maxLines="1"
                android:ellipsize="end"
                style="@style/TitleStyle"
                android:textSize="@dimen/sw_24sp"
                android:layout_width="0dp"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_restaurant_label"
                app:layout_constraintLeft_toLeftOf="@+id/tv_order_status"
                app:layout_constraintRight_toRightOf="@+id/tv_order_status"
                app:layout_constraintTop_toBottomOf="@+id/tv_order_status"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginBottom="8dp"
                android:maxLines="1"
                android:ellipsize="end"
                style="@style/ItemStyle"
                android:alpha="0.5"
                android:layout_width="0dp"
                android:layout_height="wrap_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:padding="16dp"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                android:textSize="@dimen/font_size_h7"
                android:textColor="@color/fun_n5"
                android:id="@+id/tv_order_number"
                android:layout_weight="1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:textSize="@dimen/font_size_h7"
                android:textColor="@color/fun_n5"
                android:id="@+id/tv_order_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>


</com.google.android.material.card.MaterialCardView>