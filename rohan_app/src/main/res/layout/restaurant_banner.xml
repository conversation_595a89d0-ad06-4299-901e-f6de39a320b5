<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingLeft="1dp"
    android:paddingRight="1dp"
    android:layout_width="@dimen/sw_383dp"
    android:layout_height="@dimen/sw_153dp"
    app:cardCornerRadius="@dimen/card_radius"
    app:cardBackgroundColor="@color/card_background"
    app:cardElevation="0dp"
    android:orientation="vertical">


    <com.youth.banner.Banner
        android:id="@+id/banner_restaurant"
        app:banner_indicator_gravity="center"
        app:banner_indicator_height="@dimen/dip_8"
        app:banner_indicator_space="@dimen/dip_12"
        app:banner_indicator_marginBottom="@dimen/dip_8"
        app:banner_indicator_normal_width="7dp"
        app:banner_indicator_selected_width="7dp"
        app:banner_indicator_normal_color="@color/n4"
        app:banner_indicator_selected_color="@color/mr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />


</com.google.android.material.card.MaterialCardView>
