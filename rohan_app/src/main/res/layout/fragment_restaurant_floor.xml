<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ricepo.app.view.SmartExtRefreshLayout
        android:id="@+id/srl_floor_list"
        android:visibility="visible"
        app:srlFooterHeight="0dp"
        app:srlEnableLoadMore="false"
        app:srlEnableLoadMoreWhenContentNotFull="false"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

        <com.ricepo.style.view.rv.NestingRecyclerView
            android:id="@+id/recycler_floor_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.ricepo.app.view.SmartExtRefreshLayout>

    <include android:id="@+id/in_restaurant_sort"
        layout="@layout/layout_restaurant_sort_float" />


    <FrameLayout
        android:id="@+id/fl_restaurant_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true" />

</RelativeLayout>