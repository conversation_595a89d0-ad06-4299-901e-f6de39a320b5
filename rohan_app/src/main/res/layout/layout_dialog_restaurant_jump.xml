<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@android:color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ricepo.style.shadow.ShadowLayout
        app:shadowColor="@color/second_satisfied_button_shadow"
        app:shadowRadius="@dimen/restaurant_jump_radius"
        app:yOffset="1dp"
        app:xOffset="0dp"
        app:blurRadius="1dp"
        app:bgColor="@color/colorPrimary"
        android:background="@drawable/button_selector"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_restaurant_jump"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintHeight_max="400dp"
                android:scrollbars="none"
                android:scrollbarSize="@dimen/dip_8"
                android:layout_width="match_parent"
                android:layout_height="0dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.ricepo.style.shadow.ShadowLayout>

    <com.ricepo.style.shadow.ShadowLayout
        app:shadowColor="@color/second_satisfied_button_shadow"
        app:shadowRadius="8dp"
        app:yOffset="1dp"
        app:xOffset="0dp"
        app:blurRadius="1dp"
        app:bgColor="@color/colorPrimary"
        android:layout_marginTop="15dp"
        android:background="@drawable/button_selector"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" >

        <TextView
            android:id="@+id/tv_restaurant_jump_back"
            android:gravity="center"
            style="@style/SubTitleStyle"
            android:text="@string/back"
            android:layout_width="match_parent"
            android:layout_height="50dp" />

    </com.ricepo.style.shadow.ShadowLayout>


</LinearLayout>