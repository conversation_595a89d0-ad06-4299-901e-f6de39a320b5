<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <TextView
        android:id="@+id/tv_tips_title"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/choose_tip"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        android:textColor="@color/subText"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_close"
        app:layout_constraintTop_toTopOf="@id/iv_close" />

<!--    <TextView-->
<!--        android:id="@+id/tv_tips_first"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="56dp"-->
<!--        app:layout_constraintTop_toBottomOf="@id/iv_close"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        android:gravity="center_vertical"-->
<!--        android:paddingLeft="30dp"-->
<!--        android:paddingRight="30dp"-->
<!--        style="@style/ItemStyle" />-->

<!--    <TextView-->
<!--        app:layout_constraintLeft_toLeftOf="@+id/tv_tips_first"-->
<!--        app:layout_constraintRight_toRightOf="@+id/tv_tips_first"-->
<!--        app:layout_constraintTop_toBottomOf="@id/tv_tips_first"-->
<!--        android:layout_marginLeft="30dp"-->
<!--        android:layout_marginRight="30dp"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0.5dp"-->
<!--        style="@style/DividerHorizontalStyle" />-->

<!--    <TextView-->
<!--        android:id="@+id/tv_tips_second"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="56dp"-->
<!--        app:layout_constraintTop_toBottomOf="@id/tv_tips_first"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        android:gravity="center_vertical"-->
<!--        android:paddingLeft="30dp"-->
<!--        android:paddingRight="30dp"-->
<!--        style="@style/ItemStyle" />-->

<!--    <TextView-->
<!--        app:layout_constraintLeft_toLeftOf="@+id/tv_tips_second"-->
<!--        app:layout_constraintRight_toRightOf="@+id/tv_tips_second"-->
<!--        app:layout_constraintTop_toBottomOf="@id/tv_tips_second"-->
<!--        android:layout_marginLeft="30dp"-->
<!--        android:layout_marginRight="30dp"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0.5dp"-->
<!--        style="@style/DividerHorizontalStyle" />-->

<!--    <TextView-->
<!--        android:id="@+id/tv_tips_third"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="56dp"-->
<!--        app:layout_constraintTop_toBottomOf="@id/tv_tips_second"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        android:gravity="center_vertical"-->
<!--        android:paddingLeft="30dp"-->
<!--        style="@style/ItemStyle" />-->

<!--    <TextView-->
<!--        app:layout_constraintLeft_toLeftOf="@+id/tv_tips_third"-->
<!--        app:layout_constraintRight_toRightOf="@+id/tv_tips_third"-->
<!--        app:layout_constraintTop_toBottomOf="@id/tv_tips_third"-->
<!--        android:layout_marginLeft="30dp"-->
<!--        android:layout_marginRight="30dp"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0.5dp"-->
<!--        style="@style/DividerHorizontalStyle" />-->

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_tips_select"
        app:layout_constraintTop_toBottomOf="@id/iv_close"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_tips_custom"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        app:layout_constraintTop_toBottomOf="@+id/rv_tips_select"
        app:layout_constraintLeft_toLeftOf="parent"
        android:gravity="center_vertical"
        android:paddingLeft="30dp"
        style="@style/ItemStyle"
        android:text="@string/custom" />


    <TextView
        android:id="@+id/tv_tip_prompt"
        app:layout_constraintLeft_toLeftOf="@+id/tv_tips_custom"
        app:layout_constraintRight_toRightOf="@+id/tv_tips_custom"
        app:layout_constraintTop_toBottomOf="@id/tv_tips_custom"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="20dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/SubTitleStyle" />

</androidx.constraintlayout.widget.ConstraintLayout>