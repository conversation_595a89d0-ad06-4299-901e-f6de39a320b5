<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_restaurant_card"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_restaurant_recommend_divider"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        style="@style/RestaurantDividerHorizontalStyle" />



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_random_menu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dip_24"
        android:layout_marginBottom="@dimen/dip_24"
        android:layout_marginLeft="@dimen/dip_20"
        android:layout_marginRight="@dimen/dip_20"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_feeling_lucky"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            style="@style/TitleStyle"
            android:textSize="@dimen/font_size_s20" />

        <TextView
            android:id="@+id/btn_button"
            app:layout_constraintTop_toTopOf="@+id/tv_start_now"
            app:layout_constraintBottom_toBottomOf="@+id/tv_start_now"
            app:layout_constraintLeft_toLeftOf="@+id/tv_start_now"
            app:layout_constraintRight_toRightOf="@+id/tv_start_now"
            style="@style/ButtonOutlineFiveStyle"
            android:minHeight="32dp"
            android:layout_width="0dp"
            android:layout_height="32dp" />

        <TextView
            android:id="@+id/tv_start_now"
            app:layout_constraintTop_toBottomOf="@+id/tv_feeling_lucky"
            app:layout_constraintLeft_toLeftOf="@+id/tv_feeling_lucky"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/RestaurantInfoStyle"
            android:paddingLeft="@dimen/dip_8"
            android:paddingRight="@dimen/dip_8"
            android:layout_marginTop="@dimen/dip_24"
            android:drawablePadding="@dimen/order_fees_margin_end"
            app:drawableEndCompat="@drawable/ic_lucky_right" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_dice"
        android:layout_width="135dp"
        android:layout_height="80dp"
        android:background="@drawable/ic_lucky_dice_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_restaurant_recommend_divider_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        style="@style/RestaurantDividerHorizontalStyle" />

</androidx.constraintlayout.widget.ConstraintLayout>