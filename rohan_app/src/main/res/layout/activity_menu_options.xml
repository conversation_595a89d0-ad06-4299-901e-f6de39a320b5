<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_close" />

    <TextView
        android:id="@+id/tv_title_food_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="-2dp"
        style="@style/AssertiveStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/recycler_options_list"
        app:layout_constraintTop_toBottomOf="@id/lay_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_options_list"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lay_title"
        app:layout_constraintBottom_toTopOf="@+id/in_menu_cart"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <include layout="@layout/layout_menu_cart_opt_bar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/btn_menu_options_cart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/in_menu_cart" />


</androidx.constraintlayout.widget.ConstraintLayout>