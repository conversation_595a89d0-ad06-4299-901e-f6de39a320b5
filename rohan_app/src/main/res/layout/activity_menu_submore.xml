<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_title"
        android:orientation="vertical"
        android:layout_alignParentTop="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_title_icon"
            android:src="@drawable/ic_back"
            android:tint="@color/fun_n6"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.ricepo.base.view.RicePoolView
            android:id="@+id/rtv_pool"
            app:layout_constraintLeft_toRightOf="@+id/iv_back"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintTop_toTopOf="@+id/iv_back"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginRight="@dimen/dip_40"
            tools:visibility="visible"
            android:visibility="gone"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_menu_submore"
        android:layout_below="@+id/lay_title"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        android:id="@+id/in_menu_cart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        layout="@layout/layout_menu_cart_bar" />

</RelativeLayout>