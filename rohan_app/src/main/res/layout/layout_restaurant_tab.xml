<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/lay_restaurant_cart"
        android:visibility="gone"
        tools:visibility="visible"
        android:gravity="center"
        android:layout_gravity="center"
        android:background="@drawable/button_plate_floating_3d"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/dip_30"
        android:paddingRight="@dimen/dip_30"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/restaurant_tab_height">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_marginLeft="@dimen/dip_15"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" >

        <ImageView
            android:id="@+id/iv_cart_count"
            app:layout_constraintLeft_toLeftOf="@+id/tv_cart_count"
            app:layout_constraintTop_toTopOf="@+id/tv_cart_count"
            app:layout_constraintBottom_toBottomOf="@+id/tv_cart_count"
            app:layout_constraintRight_toRightOf="@+id/tv_cart_count"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:src="@drawable/ic_bag"
            android:layout_width="@dimen/tab_item_size"
            android:layout_height="@dimen/tab_item_size" />

        <TextView
            android:id="@+id/tv_cart_count"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textColor="@color/w"
            android:textSize="@dimen/sw_13sp"
            android:minWidth="26dp"
            android:gravity="center"
            android:paddingTop="5dp"
            tools:text="20"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_cart_name"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="@string/test_text_length"
            android:textSize="@dimen/sw_15sp"
            android:textColor="@color/cart_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_cart_arrow"
            android:src="@drawable/ic_right_arrow"
            android:layout_marginRight="@dimen/dip_10"
            android:layout_width="18dp"
            android:layout_height="16dp" />

    </LinearLayout>


    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/lay_restaurant_tab"
        android:background="@drawable/button_plate_floating_3d"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_gravity="center"
        android:paddingLeft="@dimen/dip_20"
        android:paddingRight="@dimen/dip_20"
        android:paddingTop="@dimen/dip_8"
        android:layout_marginBottom="@dimen/_sw_5dp"
        android:layout_marginTop="0dp"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/restaurant_tab_height"/>

</LinearLayout>
