<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_see_all"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        style="@style/ButtonSecondaryStyle"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        android:layout_marginBottom="30dp"
        android:layout_marginTop="30dp"
        android:layout_width="0dp"
        android:layout_height="40dp" />

    <TextView
        android:id="@+id/tv_see_all"
        app:layout_constraintLeft_toLeftOf="@+id/btn_see_all"
        app:layout_constraintRight_toLeftOf="@+id/iv_see_all_arrow"
        app:layout_constraintTop_toTopOf="@+id/btn_see_all"
        app:layout_constraintBottom_toBottomOf="@+id/btn_see_all"
        app:layout_constraintHorizontal_chainStyle="packed"
        android:text="@string/restaurant_sections_loadmore_title"
        style="@style/SubTitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_see_all_arrow"
        android:src="@drawable/ic_right_arrow"
        android:layout_width="18dp"
        android:layout_height="14dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_see_all"
        app:layout_constraintRight_toRightOf="@+id/btn_see_all"
        app:layout_constraintTop_toTopOf="@+id/tv_see_all"
        app:layout_constraintBottom_toBottomOf="@+id/tv_see_all" />


</androidx.constraintlayout.widget.ConstraintLayout>