<?xml version="1.0" encoding="utf-8"?>

<!--<com.ricepo.style.shadow.ShadowLayout-->
<!--    xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:app="http://schemas.android.com/apk/res-auto"-->
<!--    android:id="@+id/layout_pickup_container"-->
<!--    app:yOffset="3dp"-->
<!--    app:xOffset="0dp"-->
<!--    app:shadowType="1"-->
<!--    app:blurRadius="0dp"-->
<!--    app:shadowRadius="11dp"-->
<!--    app:bgColor="@color/pickup_item_bg"-->
<!--    app:shadowColor="@color/pickup_item_shadow"-->
<!--    android:layout_marginBottom="4dp"-->
<!--    android:layout_height="match_parent"-->
<!--    android:layout_width="260dp">-->


<!--</com.ricepo.style.shadow.ShadowLayout>-->

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_pickup_container"
    android:background="@drawable/button_plate_floating_3d"
    android:layout_marginBottom="4dp"
    android:layout_height="wrap_content"
    android:layout_width="260dp">

</FrameLayout>