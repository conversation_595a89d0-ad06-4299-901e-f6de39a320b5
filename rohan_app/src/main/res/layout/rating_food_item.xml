<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:paddingBottom="30dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/food_image"
            android:layout_width="56dp"
            android:layout_height="36dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_food_placeholder"/>

    <TextView
            android:id="@+id/tv"
            android:layout_width="56dp"
            android:gravity="center"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/ratingBar"
            app:layout_constraintEnd_toEndOf="@+id/food_image"
            app:layout_constraintStart_toStartOf="@id/food_image"
            app:layout_constraintTop_toTopOf="@+id/ratingBar"
            android:text="@string/food_ratings"
            android:maxLines="2"
            style="@style/TitleStyle"
            tools:text="@string/food_ratings"/>

    <TextView
            android:id="@+id/food_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:ellipsize="end"
            style="@style/TitleStyle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/food_image"
            app:layout_constraintStart_toEndOf="@id/food_image"
            app:layout_constraintTop_toTopOf="@id/food_image"
            tools:text="this is the food name here his is the food name herehis is the food name here"/>

    <me.zhanghai.android.materialratingbar.MaterialRatingBar
            android:id="@+id/ratingBar"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            app:layout_constraintStart_toEndOf="@+id/tv"
            android:stepSize="1"
            app:mrb_progressTint="@color/rating_color"
            app:mrb_progressBackgroundTint="@color/unrating_color"
            app:layout_constraintTop_toBottomOf="@+id/food_image"/>

    <TextView
            android:id="@+id/rating_Desc"
            app:layout_constraintStart_toEndOf="@+id/ratingBar"
            app:layout_constraintTop_toTopOf="@id/ratingBar"
            app:layout_constraintBottom_toBottomOf="@id/ratingBar"
            app:layout_constraintEnd_toEndOf="@id/holder"
            android:layout_width="wrap_content"
            style="@style/TitleStyle"
            android:layout_height="wrap_content"/>
    <View
            android:id="@+id/holder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>


</androidx.constraintlayout.widget.ConstraintLayout>