<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_title_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_back"
        android:tint="@color/fun_n6"
        />

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/et_focus_add_coupon"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="40dp"
        android:layout_marginLeft="40dp"
        android:layout_marginTop="10dp"
        android:focusable="true"
        android:cursorVisible="false"
        style="@style/EditTextStyle"
        android:editable="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_close"/>


    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_add_coupon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAlignment="inherit"
        android:hint="1234-ABCD"
        android:singleLine="true"
        style="@style/EditTextNoBottomLineStyle"
        android:layout_marginBottom="@dimen/text_bottom_padding"
        app:layout_constrainedWidth="true"
        app:layout_constraintLeft_toLeftOf="@+id/et_focus_add_coupon"
        app:layout_constraintRight_toRightOf="@+id/et_focus_add_coupon"
        app:layout_constraintTop_toTopOf="@+id/et_focus_add_coupon" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_add_coupon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="35dp"
        android:backgroundTint="@color/mr"
        android:minHeight="40dp"
        android:text="@string/ok"
        android:textColor="@color/white"
        app:cornerRadius="@dimen/button_radius"
        app:layout_constraintLeft_toLeftOf="@+id/et_add_coupon"
        app:layout_constraintRight_toRightOf="@+id/et_add_coupon"
        app:layout_constraintTop_toBottomOf="@+id/et_add_coupon" />

    <View
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_add_coupon"
        />

</androidx.constraintlayout.widget.ConstraintLayout>