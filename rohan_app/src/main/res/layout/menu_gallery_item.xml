<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_item_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:scaleType="fitXY"
        android:adjustViewBounds="true"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_184dp" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_gallery_item"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <ImageView
            android:id="@+id/iv_food_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="@dimen/sw_119dp"
            android:layout_height="@dimen/sw_155dp"
            tools:src="@drawable/bg_plate" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/iv_food"
            app:layout_constraintLeft_toLeftOf="@+id/iv_food_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_food_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_food_bg"
            android:layout_width="@dimen/sw_119dp"
            android:layout_height="@dimen/sw_119dp"  />

        <TextView
            android:id="@id/tv_food_name"
            app:layout_constraintLeft_toRightOf="@+id/iv_food_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_food_bg"
            app:layout_constraintVertical_chainStyle="packed"
            style="@style/TitleStyle"
            tools:text="蜂蜜玫瑰桃葫芦肉"
            android:textSize="@dimen/font_size_s15"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginTop="@dimen/sw_6dp"
            android:layout_width="@dimen/sw_106dp"
            android:layout_marginLeft="@dimen/sw_18dp"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:id="@+id/ll_food_recommend"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_food_name"
            app:layout_constraintRight_toRightOf="@+id/tv_food_name"
            android:layout_marginTop="5dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content" >

            <TextView
                android:id="@+id/tv_food_recommend"
                style="@style/SubTitleMenuStyle"
                android:ellipsize="marquee"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <TextView
            android:id="@id/tv_food_price"
            app:layout_constraintLeft_toLeftOf="@+id/tv_food_name"
            app:layout_constraintTop_toBottomOf="@+id/ll_food_recommend"
            style="@style/SubItemStyle"
            android:textColor="@color/inputLineText"
            tools:text="20.00"
            android:layout_marginTop="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_food_original_price"
            app:layout_constraintBottom_toBottomOf="@+id/tv_food_price"
            app:layout_constraintTop_toTopOf="@+id/tv_food_price"
            app:layout_constraintLeft_toRightOf="@+id/tv_food_price"
            tools:text="20.00"
            style="@style/SubItemStyle"
            android:layout_marginLeft="2dp"
            android:textColor="@color/inputLineText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    <!--    <TextView-->
    <!--        android:id="@+id/btn_food_option_click"-->
    <!--        app:layout_constraintLeft_toLeftOf="@+id/btn_food_option"-->
    <!--        app:layout_constraintTop_toTopOf="@+id/btn_food_option"-->
    <!--        android:visibility="gone"-->
    <!--        android:layout_width="@dimen/menu_button_width"-->
    <!--        android:layout_height="@dimen/menu_button_size" >-->
    <!--    </TextView>-->

        <com.ricepo.style.button.ChocolateButton
            android:id="@+id/btn_food_option"
            app:layout_constraintTop_toBottomOf="@id/tv_food_price"
            app:layout_constraintLeft_toLeftOf="@id/tv_food_name"
            android:layout_marginTop="4dp"
            android:minHeight="26dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>