<?xml version="1.0" encoding="utf-8"?>

<!--<androidx.coordinatorlayout.widget.CoordinatorLayout-->
<!--    xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--    xmlns:app="http://schemas.android.com/apk/res-auto"-->
<!--    android:layout_width="match_parent"-->
<!--    android:layout_height="match_parent">-->

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:layout_behavior="@string/bottom_sheet_behavior"
    android:background="@color/colorPrimary"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_category_jump"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_title_icon"
        android:src="@drawable/ic_close"
        android:layout_marginTop="@dimen/dip_10"
        android:layout_marginRight="@dimen/dip_24"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

<!--        <TextView-->
<!--            android:id="@id/tv_title"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="0dp"-->
<!--            android:includeFontPadding="false"-->
<!--            android:gravity="center_vertical"-->
<!--            android:singleLine="true"-->
<!--            android:ellipsize="end"-->
<!--            android:layout_marginLeft="@dimen/dip_40"-->
<!--            style="@style/TitleStyle"-->
<!--            app:layout_constrainedWidth="true"-->
<!--            app:layout_constraintLeft_toLeftOf="parent"-->
<!--            app:layout_constraintRight_toLeftOf="@+id/iv_close"-->
<!--            app:layout_constraintBottom_toBottomOf="@+id/iv_close"-->
<!--            app:layout_constraintTop_toTopOf="@+id/iv_close" />-->



    </androidx.constraintlayout.widget.ConstraintLayout>

<!--</androidx.coordinatorlayout.widget.CoordinatorLayout>-->
