<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/card_background"
    android:layout_height="match_parent">

    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <TextView
        android:id="@+id/tv_comment"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/comments"
        android:includeFontPadding="false"
        android:gravity="center_vertical"
        style="@style/TitleStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_close"
        app:layout_constraintTop_toTopOf="@id/iv_close" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_comment_right_end"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:focusableInTouchMode="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/et_comment" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_comment"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/RegularStyle"
        android:layout_marginTop="60dp"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:singleLine="true"
        android:ellipsize="end"
        android:scrollHorizontally="false"
        android:imeOptions="actionSearch"
        android:hint="@string/add_comments"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_save_comment"
        android:layout_gravity="center"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/et_comment"
        app:layout_constraintLeft_toLeftOf="@+id/et_comment"
        app:layout_constraintRight_toRightOf="@+id/et_comment"
        android:text="@string/ok"
        style="@style/ButtonPrimaryStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_comment_history"
        android:text="@string/comments_history"
        android:gravity="center"
        style="@style/ItemStyle"
        android:alpha="@fraction/half"
        app:layout_constraintTop_toBottomOf="@+id/btn_save_comment"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="@dimen/dip_20"
        android:layout_marginLeft="@dimen/dip_40"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_comment_history"
        android:layout_marginTop="@dimen/dip_5"
        android:layout_marginLeft="@dimen/dip_40"
        android:layout_marginRight="@dimen/dip_40"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@+id/tv_comment_history"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>