<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_subscription_detail"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginLeft="@dimen/sw_50dp"
        android:layout_marginTop="@dimen/sw_25dp"
        android:src="@drawable/ic_vip_bullet"
        android:layout_width="@dimen/sw_70dp"
        android:layout_height="@dimen/sw_70dp" />

    <TextView
        android:id="@+id/tv_subscription_title"
        app:layout_constraintLeft_toRightOf="@+id/iv_subscription_detail"
        app:layout_constraintTop_toTopOf="@+id/iv_subscription_detail"
        app:layout_constraintBottom_toTopOf="@+id/tv_subscription_subtitle"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        android:textColor="@color/mainText"
        android:textSize="@dimen/sw_15sp"
        android:layout_marginRight="@dimen/sw_50dp"
        android:layout_marginLeft="@dimen/sw_42dp"
        android:layout_marginTop="@dimen/sw_0dp"
        app:layout_goneMarginBottom="@dimen/sw_5dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_subscription_subtitle"
        app:layout_constraintLeft_toRightOf="@+id/iv_subscription_detail"
        app:layout_constraintTop_toBottomOf="@+id/tv_subscription_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/iv_subscription_detail"
        android:textColor="@color/subText"
        android:textSize="@dimen/sw_13sp"
        android:layout_marginRight="@dimen/sw_50dp"
        android:layout_marginLeft="@dimen/sw_42dp"
        android:layout_marginTop="@dimen/sw_0dp"
        android:layout_marginBottom="@dimen/sw_5dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>