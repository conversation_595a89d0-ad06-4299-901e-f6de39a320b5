<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_restaurant_card"
    android:layout_width="match_parent"
    android:layout_height="@dimen/restaurant_plan_height" >

    <TextView
        android:id="@+id/tv_divider_top"
        style="@style/RestaurantDividerHorizontalStyle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        android:layout_width="0dp" />

    <ImageView
        android:src="@drawable/ic_membership_checkout"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_membership_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_membership_subtitle"
        style="@style/SubTitleStyle"
        android:textColor="@color/mainText"
        android:layout_marginTop="@dimen/dip_20"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_membership_subtitle"
        app:layout_constraintLeft_toLeftOf="@+id/tv_membership_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_membership_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_membership_apply"
        style="@style/LargeTitleStyle"
        android:textColor="@color/mainText"
        android:layout_marginTop="@dimen/dip_15"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/btn_button"
        app:layout_constraintTop_toTopOf="@+id/tv_membership_apply"
        app:layout_constraintBottom_toBottomOf="@+id/tv_membership_apply"
        app:layout_constraintLeft_toLeftOf="@+id/tv_membership_apply"
        app:layout_constraintRight_toRightOf="@+id/iv_membership_apply_arrow"
        android:background="@drawable/banner_button_outline"
        android:minHeight="32dp"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <TextView
        android:id="@+id/tv_membership_apply"
        app:layout_constraintLeft_toLeftOf="@+id/tv_membership_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_membership_subtitle"
        android:layout_marginTop="@dimen/dip_24"
        style="@style/SubTitleStyle"
        tools:text="aaaa"
        android:paddingLeft="8dp"
        android:gravity="center"
        android:textColor="@color/goldSubText"
        android:layout_width="wrap_content"
        android:layout_height="32dp" />

    <ImageView
        android:id="@+id/iv_membership_apply_arrow"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:src="@drawable/ic_membership_arrow"
        app:layout_constraintTop_toTopOf="@+id/tv_membership_apply"
        app:layout_constraintBottom_toBottomOf="@+id/tv_membership_apply"
        app:layout_constraintLeft_toRightOf="@+id/tv_membership_apply" />

    <TextView
        android:id="@+id/tv_divider_bottom"
        style="@style/RestaurantDividerHorizontalStyle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        android:layout_width="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>