<?xml version="1.0" encoding="utf-8"?>

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_support_image"
    android:layout_width="160dp"
    android:layout_height="100dp">

        <com.ricepo.style.round.RoundImageView
            android:id="@+id/iv_support_issue"
            android:scaleType="centerCrop"
            android:padding="1dp"
            android:layout_gravity="center"
            app:round_corner="@dimen/dip_10"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/iv_support_issue_del"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:padding="2dp"
            android:src="@drawable/ic_cross_photo"
            android:layout_gravity="right|bottom"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp" />
</FrameLayout>




