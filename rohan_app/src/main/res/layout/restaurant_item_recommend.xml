<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_restaurant_recommend_divider"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        style="@style/DividerHorizontalStyle" />

    <FrameLayout
        android:id="@+id/round_recommend_week"
        app:layout_constraintTop_toBottomOf="@+id/tv_restaurant_recommend_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/round_recommend_top"
        app:layout_constraintBottom_toTopOf="@+id/tv_recommend_week"
        android:background="@drawable/button_cuisine"
        android:layout_marginTop="30dp"
        android:layout_marginRight="5dp"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <ImageView
        android:id="@+id/iv_recommend_week"
        app:layout_constraintLeft_toLeftOf="@+id/round_recommend_week"
        app:layout_constraintTop_toTopOf="@+id/round_recommend_week"
        app:layout_constraintRight_toRightOf="@+id/round_recommend_week"
        app:layout_constraintBottom_toBottomOf="@+id/round_recommend_week"
        android:background="@drawable/bg_weekly_recommend"
        android:layout_width="0dp"
        android:layout_height="110dp" />

    <ImageView
        android:id="@+id/iv_recommend_dish_week_left"
        app:layout_constraintLeft_toLeftOf="@+id/iv_recommend_week"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_week"
        android:src="@drawable/leaderboard_dish"
        android:layout_marginLeft="12dp"
        android:layout_marginBottom="15dp"
        android:layout_width="50dp"
        android:layout_height="50dp" />

    <ImageView
        android:id="@+id/iv_recommend_week_left"
        app:layout_constraintLeft_toLeftOf="@+id/iv_recommend_dish_week_left"
        app:layout_constraintTop_toTopOf="@+id/iv_recommend_dish_week_left"
        app:layout_constraintRight_toRightOf="@+id/iv_recommend_dish_week_left"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_dish_week_left"
        android:layout_marginBottom="10dp"
        android:layout_width="38dp"
        android:layout_height="38dp" />

    <ImageView
        android:id="@+id/iv_recommend_dish_week_right"
        app:layout_constraintRight_toRightOf="@+id/iv_recommend_week"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_week"
        android:src="@drawable/leaderboard_dish"
        android:layout_marginRight="8dp"
        android:layout_marginBottom="20dp"
        android:layout_width="70dp"
        android:layout_height="70dp" />

    <ImageView
        android:id="@+id/iv_recommend_week_right"
        app:layout_constraintLeft_toLeftOf="@+id/iv_recommend_dish_week_right"
        app:layout_constraintTop_toTopOf="@+id/iv_recommend_dish_week_right"
        app:layout_constraintRight_toRightOf="@+id/iv_recommend_dish_week_right"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_dish_week_right"
        android:layout_marginBottom="16dp"
        android:layout_width="55dp"
        android:layout_height="55dp" />

    <TextView
        android:id="@+id/tv_recommend_week"
        app:layout_constraintLeft_toLeftOf="@+id/round_recommend_week"
        app:layout_constraintRight_toRightOf="@+id/round_recommend_week"
        app:layout_constraintTop_toBottomOf="@+id/round_recommend_week"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="30dp"
        android:layout_marginTop="10dp"
        style="@style/TitleStyle"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_recommend_week_click"
        app:layout_constraintLeft_toLeftOf="@+id/round_recommend_week"
        app:layout_constraintRight_toRightOf="@+id/round_recommend_week"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <FrameLayout
        android:id="@+id/round_recommend_top"
        app:layout_constraintTop_toTopOf="@+id/round_recommend_week"
        app:layout_constraintLeft_toRightOf="@+id/round_recommend_week"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/round_recommend_week"
        android:background="@drawable/button_cuisine"
        android:layout_marginRight="@dimen/margin_right"
        android:layout_marginLeft="5dp"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <ImageView
        android:id="@+id/iv_recommend_top"
        app:layout_constraintLeft_toLeftOf="@+id/round_recommend_top"
        app:layout_constraintTop_toTopOf="@+id/round_recommend_top"
        app:layout_constraintRight_toRightOf="@+id/round_recommend_top"
        app:layout_constraintBottom_toBottomOf="@+id/round_recommend_top"
        android:background="@drawable/bg_weekly_top"
        android:layout_width="0dp"
        android:layout_height="110dp" />

    <ImageView
        android:id="@+id/iv_recommend_dish_top_left"
        app:layout_constraintLeft_toLeftOf="@+id/iv_recommend_top"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_top"
        android:src="@drawable/leaderboard_dish"
        android:layout_marginLeft="12dp"
        android:layout_marginBottom="15dp"
        android:layout_width="50dp"
        android:layout_height="50dp" />

    <ImageView
        android:id="@+id/iv_recommend_top_left"
        app:layout_constraintLeft_toLeftOf="@+id/iv_recommend_dish_top_left"
        app:layout_constraintTop_toTopOf="@+id/iv_recommend_dish_top_left"
        app:layout_constraintRight_toRightOf="@+id/iv_recommend_dish_top_left"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_dish_top_left"
        android:layout_marginBottom="10dp"
        android:layout_width="38dp"
        android:layout_height="38dp" />

    <ImageView
        android:id="@+id/iv_recommend_dish_top_right"
        app:layout_constraintRight_toRightOf="@+id/iv_recommend_top"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_top"
        android:src="@drawable/leaderboard_dish"
        android:layout_marginRight="8dp"
        android:layout_marginBottom="20dp"
        android:layout_width="70dp"
        android:layout_height="70dp" />

    <ImageView
        android:id="@+id/iv_recommend_top_right"
        app:layout_constraintLeft_toLeftOf="@+id/iv_recommend_dish_top_right"
        app:layout_constraintTop_toTopOf="@+id/iv_recommend_dish_top_right"
        app:layout_constraintRight_toRightOf="@+id/iv_recommend_dish_top_right"
        app:layout_constraintBottom_toBottomOf="@+id/iv_recommend_dish_top_right"
        android:layout_marginBottom="16dp"
        android:layout_width="55dp"
        android:layout_height="55dp" />

    <TextView
        android:id="@+id/tv_recommend_top"
        app:layout_constraintLeft_toLeftOf="@+id/round_recommend_top"
        app:layout_constraintRight_toRightOf="@+id/round_recommend_top"
        app:layout_constraintTop_toTopOf="@+id/tv_recommend_week"
        style="@style/TitleStyle"
        android:maxLines="2"
        android:ellipsize="end"
        android:gravity="center"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_recommend_top_click"
        app:layout_constraintLeft_toLeftOf="@+id/round_recommend_top"
        app:layout_constraintRight_toRightOf="@+id/round_recommend_top"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="0dp"
        android:layout_height="0dp" />

</androidx.constraintlayout.widget.ConstraintLayout>