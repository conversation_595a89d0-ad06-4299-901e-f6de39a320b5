<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        style="@style/RestaurantDividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_show_all"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/iv_show_all"
        app:layout_constraintHorizontal_chainStyle="packed"
        android:text="@string/show_all"
        android:gravity="center_vertical"
        style="@style/SubTitleStyle"
        android:textSize="@dimen/font_size_h6"
        android:layout_width="wrap_content"
        android:layout_height="50dp" />

    <ImageView
        android:id="@+id/iv_show_all"
        app:layout_constraintLeft_toRightOf="@+id/tv_show_all"
        app:layout_constraintTop_toTopOf="@+id/tv_show_all"
        app:layout_constraintBottom_toBottomOf="@+id/tv_show_all"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginLeft="@dimen/dip_8"
        android:src="@drawable/ic_bundle_arrow_down"
        android:layout_width="20dp"
        android:layout_height="14dp" />

</androidx.constraintlayout.widget.ConstraintLayout>