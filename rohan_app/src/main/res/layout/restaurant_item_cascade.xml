<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <ImageView
        android:id="@+id/iv_cascade_bg"
        app:layout_constraintTop_toTopOf="@+id/rv_cascade_container"
        app:layout_constraintBottom_toBottomOf="@+id/rv_cascade_container"
        app:layout_constraintLeft_toLeftOf="@+id/rv_cascade_container"
        app:layout_constraintRight_toRightOf="@+id/rv_cascade_container"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <TextView
        android:id="@+id/tv_divider_cascade_top"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/food_cascade_top_shadow"
        android:layout_width="0dp"
        android:layout_height="@dimen/sw_1dp" />

    <com.ricepo.style.view.rv.extend.AutoPollRecyclerView
        android:id="@+id/rv_cascade_container"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_divider_cascade_top"
        app:layout_constraintRight_toRightOf="parent"
        android:focusableInTouchMode="true"
        android:focusable="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rv_cascade_container"
        android:background="@color/food_cascade_bottom_shadow"
        android:layout_width="0dp"
        android:layout_height="@dimen/sw_1dp" />

    <ProgressBar
        android:id="@+id/pb_cascade"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        tools:visibility="visible"
        style="?android:attr/progressBarStyle"
        android:indeterminateTint="@color/inputLineText"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/pull_icon_size"
        android:layout_gravity="center" />



</androidx.constraintlayout.widget.ConstraintLayout>