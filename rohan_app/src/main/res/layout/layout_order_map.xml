<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:id="@+id/ll_order_map"
    android:visibility="gone"
    tools:visibility="visible"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        style="@style/DividerHorizontalStyle"
        android:layout_gravity="top" />

    <FrameLayout
        android:id="@+id/frame_order_map"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="2dp"
        android:layout_width="match_parent"
        android:layout_height="360dp" />

<!--    <fragment-->
<!--        android:id="@+id/fragment_order_map"-->
<!--        android:name="com.ricepo.app.features.order.OrderMapFragment"-->
<!--        android:tag="fragment_order_delivery_map"-->
<!--        android:layout_marginTop="2dp"-->
<!--        android:layout_marginBottom="2dp"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="360dp" />-->

<!--    <fragment-->
<!--        android:id="@+id/fragment_order_mapbox"-->
<!--        android:name="com.ricepo.app.features.order.OrderMapboxFragment"-->
<!--        android:tag="fragment_order_delivery_mapbox"-->
<!--        android:layout_marginTop="2dp"-->
<!--        android:layout_marginBottom="2dp"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="360dp" />-->

    <LinearLayout
        android:id="@+id/card_delivery_note"
        android:layout_gravity="top|center"
        android:layout_marginRight="@dimen/dip_30"
        android:layout_marginLeft="@dimen/dip_30"
        android:background="@drawable/button_plate_floating_3d"
        android:gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/sw_70dp">

        <LinearLayout
            android:layout_marginLeft="@dimen/sw_30dp"
            android:layout_marginRight="@dimen/sw_30dp"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_delivery_note"
                android:src="@drawable/ic_remind"
                android:layout_marginRight="@dimen/dip_3"
                android:visibility="gone"
                android:layout_width="@dimen/sw_13dp"
                android:layout_height="@dimen/sw_13dp" />

            <TextView
                android:id="@+id/tv_delivery_note"
                style="@style/SubLabelStyle"
                android:singleLine="true"
                android:ellipsize="marquee"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        style="@style/DividerHorizontalStyle"
        android:layout_gravity="bottom" />

</FrameLayout>