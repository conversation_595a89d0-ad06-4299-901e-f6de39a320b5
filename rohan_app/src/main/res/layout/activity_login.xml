<?xml version="1.0" encoding="utf-8"?>


<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:padding="@dimen/padding_title_icon"
        app:srcCompat="@drawable/ic_close" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_login_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="tv_phone_label, et_phone_region, et_focus_phone_number, et_phone_number" />

    <TextView
        android:id="@+id/tv_phone_label"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/enter_phone"
        android:layout_marginBottom="14dp"
        style="@style/TitleStyle"
        app:layout_constraintBottom_toTopOf="@+id/et_phone_region"
        app:layout_constraintLeft_toLeftOf="@+id/et_phone_region" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_phone_region"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:focusable="false"
        android:text="+34"
        style="@style/EditTextStyle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <!-- only to show bottom line style -->
    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/et_focus_phone_number"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="40dp"
        android:layout_marginLeft="20dp"
        android:focusable="false"
        style="@style/EditTextStyle"
        app:layout_constraintLeft_toRightOf="@+id/et_phone_region"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_phone_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAlignment="inherit"
        android:hint="600 12 34 56"
        android:inputType="number"
        style="@style/EditTextNoBottomLineStyle"
        android:layout_marginBottom="@dimen/text_bottom_padding"
        app:layout_constraintLeft_toRightOf="@+id/et_phone_region"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_login_vcode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_vcode_label, tv_vcode_phone, ct_vcode, tv_vcode_resend" />

    <TextView
        android:id="@+id/tv_vcode_label"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/enter_code"
        android:layout_marginBottom="14dp"
        style="@style/TitleStyle"
        app:layout_constraintBottom_toTopOf="@+id/tv_vcode_phone"
        app:layout_constraintLeft_toLeftOf="@+id/ct_vcode" />

    <TextView
        android:id="@+id/tv_vcode_phone"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginBottom="24dp"
        app:layout_constraintBottom_toTopOf="@+id/ct_vcode"
        app:layout_constraintLeft_toLeftOf="@+id/ct_vcode" />

    <com.ricepo.style.view.CaptchaCodeTextView
        android:id="@+id/ct_vcode"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_vcode_resend"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/sw_48dp"
        android:paddingRight="@dimen/sw_24dp"
        android:text="@string/resend"
        android:textColor="@color/mainText"
        android:layout_marginTop="10dp"
        style="@style/SubTitleStyle"
        app:layout_constraintTop_toBottomOf="@+id/ct_vcode"
        app:layout_constraintLeft_toLeftOf="@+id/ct_vcode" />


</androidx.constraintlayout.widget.ConstraintLayout>