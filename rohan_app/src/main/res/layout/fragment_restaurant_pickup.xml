<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/layout_map"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >
        <fragment
            android:id="@+id/fragment_map"
            class="com.ricepo.map.fragment.MapFragment"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/layout_mapbox"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >
        <fragment
            android:id="@+id/fragment_mapbox"
            class="com.ricepo.map.fragment.MapboxFragment"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

    <com.ricepo.map.overlay.PulseOverlayLayout
        android:id="@+id/map_overlay"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

    </com.ricepo.map.overlay.PulseOverlayLayout>

    <com.ricepo.map.overlay.mapbox.PulseBoxOverlayLayout
        android:id="@+id/map_overlay_box"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

    </com.ricepo.map.overlay.mapbox.PulseBoxOverlayLayout>

    <FrameLayout
        android:id="@+id/map_search"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"
        android:visibility="gone"
        android:background="@drawable/button_plate_floating_3d"
        android:paddingLeft="@dimen/sw_29dp"
        android:paddingRight="@dimen/sw_28dp"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/sw_75dp">

        <TextView
            android:gravity="center"
            style="@style/SubItemStyle"
            android:textSize="@dimen/font_size_s15"
            android:text="@string/pickup_map_search"
            android:layout_marginLeft="@dimen/dip_5"
            android:layout_marginRight="@dimen/dip_5"
            android:paddingLeft="@dimen/dip_3"
            android:paddingRight="@dimen/dip_3"
            android:paddingTop="@dimen/sw_2dp"
            android:layout_width="wrap_content"
            android:layout_height="match_parent" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/map_location"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible"
        android:visibility="gone"
        android:background="@drawable/button_plate_floating_3d"
        android:layout_width="@dimen/sw_75dp"
        android:layout_height="@dimen/sw_75dp">

        <ImageView
            android:layout_gravity="center"
            android:src="@drawable/ic_map_loc"
            android:scaleType="fitCenter"
            android:layout_marginTop="@dimen/sw_2dp"
            android:layout_marginRight="@dimen/sw_1dp"
            android:layout_width="@dimen/sw_27dp"
            android:layout_height="@dimen/sw_27dp" />

    </FrameLayout>

    <RelativeLayout
        android:id="@+id/frame_restaurant_horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="bottom"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_restaurant_horizontal"
            android:layout_width="match_parent"
            android:layout_marginBottom="50dp"
            android:layout_height="wrap_content" />


    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>