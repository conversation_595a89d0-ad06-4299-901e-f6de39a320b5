<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_search_prompt"
        app:layout_constraintTop_toTopOf="@+id/tv_search_prompt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_search_prompt"
        android:layout_marginLeft="@dimen/dip_40"
        android:src="@drawable/ic_search"
        android:tint="@color/fun_n6"
        android:layout_width="18dp"
        android:layout_height="20dp" />

    <TextView
        android:id="@+id/tv_search_prompt"
        app:layout_constraintLeft_toRightOf="@+id/iv_search_prompt"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingTop="2dp"
        android:layout_marginRight="@dimen/dip_40"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>