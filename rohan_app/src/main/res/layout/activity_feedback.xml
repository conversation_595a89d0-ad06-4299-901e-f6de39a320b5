<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_close" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_feedback_input"
        style="@style/EditTextSupportBorderStyle"
        android:gravity="top"
        android:layout_marginTop="25dp"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        android:hint="@string/feedback_text_placeholder"
        android:layout_width="match_parent"
        android:layout_height="120dp" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_feedback_phone"
        style="@style/EditTextSupportBorderStyle"
        android:gravity="top"
        android:layout_marginTop="25dp"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        android:hint="@string/feedback_phone_placeholder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_feedback"
        android:layout_marginTop="@dimen/button_margin_top"
        android:layout_gravity="center"
        android:text="@string/submit"
        style="@style/ButtonPrimaryStyle"
        android:layout_marginLeft="@dimen/margin_left"
        android:layout_marginRight="@dimen/margin_right"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout>