<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_restaurant_name"
        android:layout_marginHorizontal="16dp"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_restaurant_name"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_restaurant_tags"
            android:minHeight="16dp"
            android:gravity="center_vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="@string/test_text_length"
            style="@style/TitleMenuStyle"/>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_tags"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/tv_restaurant_tags"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical|right"
            android:maxLines="1"
            android:layout_weight="1"
            android:ellipsize="end"
            style="@style/RestaurantInfoStyle"
            app:layout_constraintLeft_toRightOf="@+id/guideline_tags"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="222" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_restaurant_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="@dimen/sw_3dp"
        style="@style/RestaurantInfoStyle"
        tools:text="111"/>

    <TextView
        android:layout_marginHorizontal="16dp"
        android:id="@+id/tv_restaurant_sub_info"
        android:singleLine="true"
        android:ellipsize="marquee"
        android:layout_marginTop="@dimen/sw_3dp"
        android:marqueeRepeatLimit="marquee_forever"
        style="@style/RestaurantInfoStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:visibility="gone"
        tools:text="@string/test_text_length"/>

</LinearLayout>