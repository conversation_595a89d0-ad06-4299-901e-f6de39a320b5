<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/layout_header_close" />

    <androidx.core.widget.NestedScrollView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lay_title"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp" >

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_order_code"
                style="@style/TitleStyle"
                android:layout_marginTop="15dp"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:id="@+id/ll_order_support"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

<!--            <LinearLayout-->
<!--                android:id="@+id/ll_order_support_other"-->
<!--                android:orientation="vertical"-->
<!--                android:layout_marginTop="15dp"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content" />-->

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>