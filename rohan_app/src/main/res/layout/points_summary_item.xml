<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_points_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:textColor="@color/mainText"
        android:textSize="@dimen/sw_19sp"
        android:gravity="center_vertical"
        android:text="@string/ricepo_points_history"
        android:paddingTop="@dimen/sw_22dp"
        android:paddingBottom="@dimen/sw_18dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_divider"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/tv_points_title"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginRight="@dimen/card_side_margin"
        style="@style/RestaurantDividerHorizontalStyle" />

    <TextView
        android:id="@+id/tv_points_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_points_title"
        app:layout_constraintBottom_toTopOf="@id/tv_points_date"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginLeft="@dimen/card_side_margin"
        android:layout_marginTop="@dimen/sw_18dp"
        android:textColor="@color/mainText"
        android:textSize="@dimen/sw_15sp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_points_date"
        app:layout_constraintLeft_toLeftOf="@+id/tv_points_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_points_name"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/mainText"
        android:textSize="@dimen/sw_13sp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="25dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />


    <TextView
        android:id="@+id/tv_point"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_points_name"
        android:textColor="@color/mainText"
        android:textSize="@dimen/sw_15sp"
        android:layout_marginRight="@dimen/card_side_margin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />


</androidx.constraintlayout.widget.ConstraintLayout>