<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_pickup_address"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_divider_top"
        style="@style/DividerHorizontalStyle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        android:layout_width="0dp" />

    <TextView
        android:id="@+id/tv_quote_note"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        style="@style/AssertiveStyle"
        android:textSize="@dimen/font_size_h7"
        android:visibility="gone"
        android:paddingTop="30dp"
        android:paddingBottom="20dp"
        android:gravity="left"
        android:layout_height="wrap_content"
        android:layout_width="0dp" />

    <ImageButton
        android:id="@+id/iv_pickup_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_pickup_title"
        android:background="@drawable/bg_outline_checkout"
        android:layout_marginTop="2dp"
        android:paddingTop="@dimen/sw_6dp"
        android:paddingBottom="@dimen/sw_10dp"
        android:adjustViewBounds="true"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_pickup_checkout"
        android:layout_width="@dimen/checkout_button_width"
        android:layout_height="@dimen/checkout_button_height" />

    <TextView
        android:id="@+id/tv_pickup_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_quote_note"
        app:layout_constraintLeft_toRightOf="@+id/iv_pickup_icon"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="@dimen/sw_32dp"
        android:text="@string/delivery_title"
        style="@style/TitleStyle"
        android:textSize="@dimen/sw_19sp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_pickup_address"
        app:layout_constraintLeft_toLeftOf="@+id/tv_pickup_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_pickup_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_pickup_time"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="5dp"
        style="@style/SubItemStyle"
        android:textSize="@dimen/sw_15sp"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_pickup_time"
        app:layout_constraintLeft_toLeftOf="@+id/tv_pickup_address"
        app:layout_constraintTop_toBottomOf="@+id/tv_pickup_address"
        app:layout_constraintBottom_toTopOf="@+id/tv_pickup_error"
        app:layout_constraintRight_toLeftOf="@+id/iv_pickup_time_arrow"
        android:paddingTop="10dp"
        style="@style/ItemStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/iv_pickup_time_arrow"
        android:layout_width="@dimen/right_arrow_size"
        android:layout_height="0dp"
        android:paddingTop="10dp"
        android:visibility="gone"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/tv_pickup_time"
        app:layout_constraintTop_toTopOf="@+id/tv_pickup_time" />

    <TextView
        android:id="@+id/tv_pickup_error"
        app:layout_constraintLeft_toLeftOf="@+id/tv_pickup_address"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_pickup_time"
        app:layout_constraintBottom_toTopOf="@+id/tv_pickup_type"
        android:paddingTop="10dp"
        style="@style/AssertiveStyle"
        android:visibility="gone"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_width="0dp"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_pickup_type"
        app:layout_constraintLeft_toLeftOf="@+id/tv_pickup_title"
        app:layout_constraintTop_toBottomOf="@id/tv_pickup_error"
        app:layout_constraintBottom_toBottomOf="parent"
        android:text="@string/delivery_change_to_delivery"
        android:gravity="center_vertical"
        style="@style/SubTitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="50dp" />

    <ImageView
        android:id="@+id/iv_pickup_type_arrow"
        android:layout_width="@dimen/right_arrow_size"
        android:layout_height="0dp"
        android:paddingLeft="2dp"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintBottom_toBottomOf="@+id/tv_pickup_type"
        app:layout_constraintTop_toTopOf="@+id/tv_pickup_type"
        app:layout_constraintLeft_toRightOf="@+id/tv_pickup_type" />

    <TextView
        android:id="@+id/tv_pickup_type_placeholder"
        app:layout_constraintTop_toTopOf="@+id/tv_pickup_type"
        app:layout_constraintBottom_toBottomOf="@+id/tv_pickup_type"
        app:layout_constraintLeft_toRightOf="@+id/iv_pickup_type_arrow"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_pickup_type"
        app:constraint_referenced_ids="tv_pickup_type, iv_pickup_type_arrow, tv_pickup_type_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</androidx.constraintlayout.widget.ConstraintLayout>