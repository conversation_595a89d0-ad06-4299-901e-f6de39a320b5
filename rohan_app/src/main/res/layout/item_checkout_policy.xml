<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_policy"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_divider_top"
        style="@style/DividerHorizontalStyle"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:visibility="gone"
        android:layout_width="0dp" />

    <TextView
        android:id="@+id/tv_policy_cannot_order"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/cannot_order"
        style="@style/SubTitleStyle"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toBottomOf="@+id/tv_divider_top"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_policy_call_support" />

    <TextView
        android:id="@+id/tv_policy_call_support"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/call_support"
        style="@style/SubTitleStyle"
        android:layout_marginLeft="4dp"
        app:layout_constraintTop_toTopOf="@+id/tv_policy_cannot_order"
        app:layout_constraintLeft_toRightOf="@+id/tv_policy_cannot_order"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_policy_i_have_read"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/i_have_read"
        style="@style/SubTitleStyle"

        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintTop_toBottomOf="@+id/tv_policy_cannot_order"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_policy_terms" />

    <TextView
        android:id="@+id/tv_policy_terms"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/terms"
        style="@style/SubTitleStyle"
        android:layout_marginLeft="4dp"
        app:layout_constraintTop_toTopOf="@+id/tv_policy_i_have_read"
        app:layout_constraintLeft_toRightOf="@+id/tv_policy_i_have_read"
        app:layout_constraintRight_toLeftOf="@+id/tv_policy_and" />

    <TextView
        android:id="@+id/tv_policy_and"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/and"
        style="@style/SubTitleStyle"
        android:layout_marginLeft="4dp"
        app:layout_constraintTop_toTopOf="@+id/tv_policy_i_have_read"
        app:layout_constraintLeft_toRightOf="@+id/tv_policy_terms"
        app:layout_constraintRight_toLeftOf="@+id/tv_policy_info" />

    <TextView
        android:id="@+id/tv_policy_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/policy"
        style="@style/SubTitleStyle"
        android:layout_marginLeft="4dp"
        android:layout_marginBottom="100dp"
        app:layout_constraintTop_toTopOf="@+id/tv_policy_i_have_read"
        app:layout_constraintLeft_toRightOf="@+id/tv_policy_and"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>