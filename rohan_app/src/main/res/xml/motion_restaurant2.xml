<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/base_state">

        <!-- plate bg -->
        <Constraint android:id="@id/iv_left_hidden_bg"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="parent" />

        <Constraint android:id="@id/iv_left_bg"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iv_middle_bg"
            app:layout_constraintStart_toStartOf="parent"  />

        <Constraint android:id="@id/iv_middle_bg"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/plate_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iv_right_bg"
            app:layout_constraintStart_toEndOf="@+id/iv_left_bg" />


        <Constraint android:id="@id/iv_right_bg"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/plate_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_middle_bg" />

        <Constraint android:id="@id/iv_right_hidden_bg"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/plate_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="parent" />

        <!-- food icon -->
        <Constraint android:id="@id/iv_left_hidden"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            android:layout_marginBottom="@dimen/food_big_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="parent" />

        <Constraint android:id="@id/iv_left"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            android:layout_marginBottom="@dimen/food_big_margin_bottom"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iv_middle"
            app:layout_constraintStart_toStartOf="parent"  />

        <Constraint android:id="@id/iv_middle"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/food_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iv_right"
            app:layout_constraintStart_toEndOf="@+id/iv_left" />

        <Constraint android:id="@id/iv_right"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/food_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/iv_middle" />

        <Constraint android:id="@id/iv_right_hidden"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/food_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="parent" />

    </ConstraintSet>

    <ConstraintSet android:id="@+id/move_left_to_right">
        <!-- plate bg -->
        <Constraint android:id="@id/iv_left_hidden_bg"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_left_bg" />

        <Constraint android:id="@id/iv_left_bg"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/plate_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_left_hidden_bg"
            app:layout_constraintRight_toLeftOf="@id/iv_middle_bg" >
        </Constraint>

        <Constraint android:id="@id/iv_middle_bg"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/plate_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_left_bg"
            app:layout_constraintRight_toRightOf="parent" />

        <Constraint android:id="@id/iv_right_bg"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/plate_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_right_hidden_bg" />

        <Constraint android:id="@id/iv_right_hidden_bg"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/plate_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_right_bg" />

        <!-- food icon -->
        <Constraint android:id="@id/iv_left_hidden"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            android:layout_marginBottom="@dimen/food_big_margin_bottom"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_left" />

        <Constraint android:id="@id/iv_left"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/food_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_left_hidden"
            app:layout_constraintRight_toLeftOf="@id/iv_middle" />

        <Constraint android:id="@id/iv_middle"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/food_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_left"
            app:layout_constraintRight_toRightOf="parent" />

        <Constraint android:id="@id/iv_right"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/food_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_right_hidden" />

        <Constraint android:id="@id/iv_right_hidden"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/food_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_right" />

    </ConstraintSet>

    <ConstraintSet android:id="@+id/move_right_to_left">
        <!-- plate bg -->
        <Constraint android:id="@id/iv_left_hidden_bg"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_left_bg" />

        <Constraint android:id="@id/iv_left_bg"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="parent" >
        </Constraint>

        <Constraint android:id="@id/iv_middle_bg"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/iv_right_bg" />

        <Constraint android:id="@id/iv_right_bg"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/plate_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_middle_bg"
            app:layout_constraintRight_toLeftOf="@id/iv_right_hidden_bg" />

        <Constraint android:id="@id/iv_right_hidden_bg"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/plate_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_right_bg"
            app:layout_constraintRight_toRightOf="parent" />

        <!-- food icon -->
        <Constraint android:id="@id/iv_left_hidden"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            android:layout_marginBottom="@dimen/food_big_margin_bottom"
            app:layout_constraintVertical_chainStyle="spread"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_left" />

        <Constraint android:id="@id/iv_left"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            android:layout_marginBottom="@dimen/food_big_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="parent" />

        <Constraint android:id="@id/iv_middle"
            android:layout_width="@dimen/plate_big_width"
            android:layout_height="@dimen/plate_big_height"
            android:layout_marginBottom="@dimen/food_big_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_right" />

        <Constraint android:id="@id/iv_right"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            android:layout_marginBottom="@dimen/food_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_middle"
            app:layout_constraintRight_toLeftOf="@id/iv_right_hidden" />

        <Constraint android:id="@id/iv_right_hidden"
            android:layout_width="@dimen/plate_small_width"
            android:layout_height="@dimen/plate_small_height"
            app:layout_constraintHorizontal_chainStyle="spread"
            android:layout_marginBottom="@dimen/food_small_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/iv_right"
            app:layout_constraintRight_toRightOf="parent" />

    </ConstraintSet>

    <Transition
        app:layoutDuringTransition="ignoreRequest"
        app:constraintSetEnd="@id/move_left_to_right"
        app:constraintSetStart="@id/base_state">
        <OnSwipe
            app:dragDirection="dragRight"
            app:onTouchUp="autoCompleteToStart"
            app:touchAnchorId="@id/iv_left_hidden_bg"
            app:touchAnchorSide="right" />

    </Transition>

    <Transition
        app:constraintSetEnd="@id/move_right_to_left"
        app:constraintSetStart="@id/base_state">
        <OnSwipe
            app:dragDirection="dragLeft"
            app:onTouchUp="autoCompleteToEnd"
            app:touchAnchorId="@id/iv_right_hidden_bg"
            app:touchAnchorSide="left" />

    </Transition>

</MotionScene>