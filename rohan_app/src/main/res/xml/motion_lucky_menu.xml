<?xml version="1.0" encoding="utf-8"?>
<MotionScene
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:motion="http://schemas.android.com/apk/res-auto">

    <Transition
        android:id="@+id/transition_lucky_translate_out"
        motion:constraintSetStart="@+id/lucky_end"
        motion:constraintSetEnd="@+id/lucky_out"
        motion:duration="100">

    </Transition>

    <Transition
        android:id="@+id/transition_lucky_translate_in"
        motion:constraintSetStart="@+id/lucky_start"
        motion:constraintSetEnd="@+id/lucky_end"
        motion:duration="400">

    </Transition>



    <ConstraintSet android:id="@+id/lucky_start">
        <Constraint
            android:id="@+id/rv_lucky_menu"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/dip_40"
            motion:layout_constraintTop_toTopOf="parent"
            motion:layout_constraintBottom_toBottomOf="parent"
            motion:layout_constraintLeft_toRightOf="parent"
            motion:transitionEasing="accelerate" />

    </ConstraintSet>

    <ConstraintSet
        android:id="@+id/lucky_end">

        <Constraint
            android:id="@+id/rv_lucky_menu"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/dip_40"
            motion:layout_constraintLeft_toLeftOf="parent"
            motion:layout_constraintTop_toTopOf="parent"
            motion:layout_constraintBottom_toBottomOf="parent"
            motion:layout_constraintRight_toRightOf="parent"
            motion:transitionEasing="accelerate" />

    </ConstraintSet>

    <ConstraintSet
        android:id="@+id/lucky_out">
        <Constraint
            android:id="@+id/rv_lucky_menu"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginBottom="@dimen/dip_40"
            motion:layout_constraintTop_toTopOf="parent"
            motion:layout_constraintBottom_toBottomOf="parent"
            motion:layout_constraintRight_toLeftOf="parent"
            motion:transitionEasing="accelerate" />
    </ConstraintSet>

</MotionScene>