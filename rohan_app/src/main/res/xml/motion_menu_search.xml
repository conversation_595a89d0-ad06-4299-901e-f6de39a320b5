<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <Transition
        android:id="@+id/menu_transition"
        app:duration="300"
        app:constraintSetStart="@+id/menu_start"
        app:constraintSetEnd="@+id/menu_end" >

        <KeyFrameSet>

<!--            <KeyPosition-->
<!--                app:motionTarget="@+id/et_menu_search"-->
<!--                app:percentX="0"-->
<!--                app:sizePercent="1"-->
<!--                app:framePosition="10" />-->

<!--            <KeyPosition-->
<!--                app:motionTarget="@+id/et_menu_search"-->
<!--                app:percentX=".5"-->
<!--                app:sizePercent="1"-->
<!--                app:framePosition="50" />-->

<!--            <KeyAttribute-->
<!--                app:motionTarget="@+id/et_menu_search"-->
<!--                app:framePosition="50" />-->

        </KeyFrameSet>

    </Transition>

    <ConstraintSet android:id="@+id/menu_start">

        <Constraint
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Constraint
            android:id="@+id/iv_group_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_back" />

        <Constraint
            android:id="@+id/rtv_pool"
            app:layout_constraintLeft_toRightOf="@+id/iv_back"
            app:layout_constraintRight_toLeftOf="@+id/iv_group_order"
            app:layout_constraintTop_toTopOf="@+id/iv_back"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            android:visibility="gone"
            android:layout_marginTop="0dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />

        <Constraint
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintLeft_toRightOf="@+id/iv_back"
            app:layout_constraintRight_toLeftOf="@+id/iv_group_order"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintTop_toTopOf="@+id/iv_back" />

        <Constraint
            android:id="@+id/et_menu_search"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    </ConstraintSet>

    <ConstraintSet android:id="@+id/menu_end">

        <Constraint
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="?attr/actionBarSize"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Constraint
            android:id="@+id/iv_group_order"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_back" />

        <Constraint
            android:id="@+id/rtv_pool"
            app:layout_constraintLeft_toRightOf="@+id/iv_back"
            app:layout_constraintRight_toLeftOf="@+id/iv_group_order"
            app:layout_constraintTop_toTopOf="@+id/iv_back"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            android:layout_marginTop="0dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />

        <Constraint
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintRight_toLeftOf="@+id/iv_group_order"
            app:layout_constraintLeft_toRightOf="@+id/iv_back"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintTop_toTopOf="@+id/iv_back" />

        <Constraint
            android:id="@+id/et_menu_search"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/parent"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:layout_width="0dp"
            android:alpha="0"
            android:visibility="visible" />

    </ConstraintSet>

</MotionScene>