<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:motion="http://schemas.android.com/apk/res-auto">
    <ConstraintSet android:id="@+id/base_state">

        <Constraint android:id="@id/leftHiddenView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toStartOf="parent"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>

        <Constraint android:id="@id/leftView">
            <Layout
                android:layout_width="@dimen/plate_big_width"
                android:layout_height="@dimen/plate_big_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toStartOf="@+id/centerView"
                motion:layout_constraintStart_toStartOf="parent"
                motion:layout_constraintTop_toTopOf="parent"  />
        </Constraint>

        <Constraint android:id="@id/centerView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toStartOf="@+id/rightView"
                motion:layout_constraintStart_toEndOf="@+id/leftView"
                motion:layout_constraintTop_toTopOf="parent" />

        </Constraint>

        <Constraint android:id="@id/rightView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toEndOf="parent"
                motion:layout_constraintStart_toEndOf="@+id/centerView"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>

        <Constraint android:id="@id/rightHiddenView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintStart_toEndOf="parent"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>

    </ConstraintSet>

    <ConstraintSet android:id="@+id/move_left_to_right">
        <Constraint android:id="@id/leftHiddenView">
            <Layout
                android:layout_width="@dimen/plate_big_width"
                android:layout_height="@dimen/plate_big_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toStartOf="@+id/centerView"
                motion:layout_constraintStart_toStartOf="parent"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>

        <Constraint android:id="@id/leftView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toStartOf="@+id/centerView"
                motion:layout_constraintStart_toEndOf="@+id/leftHiddenView"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>

        <Constraint android:id="@id/centerView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toEndOf="parent"
                motion:layout_constraintStart_toEndOf="@+id/leftView"
                motion:layout_constraintTop_toTopOf="parent" />

        </Constraint>

        <Constraint android:id="@id/rightView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintStart_toEndOf="parent"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>

        <Constraint android:id="@id/rightHiddenView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintStart_toEndOf="parent"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>
    </ConstraintSet>

    <ConstraintSet android:id="@+id/move_right_to_left">
        <Constraint android:id="@id/leftHiddenView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toStartOf="parent"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>

        <Constraint android:id="@id/leftView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toStartOf="parent"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>

        <Constraint android:id="@id/centerView">
            <Layout
                android:layout_width="@dimen/plate_big_width"
                android:layout_height="@dimen/plate_big_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toStartOf="@+id/rightView"
                motion:layout_constraintStart_toStartOf="parent"
                motion:layout_constraintTop_toTopOf="parent" />

        </Constraint>

        <Constraint android:id="@id/rightView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toStartOf="@+id/rightHiddenView"
                motion:layout_constraintStart_toEndOf="@+id/centerView"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>

        <Constraint android:id="@id/rightHiddenView">
            <Layout
                android:layout_width="@dimen/plate_small_width"
                android:layout_height="@dimen/plate_small_height"
                motion:layout_constraintBottom_toBottomOf="parent"
                motion:layout_constraintEnd_toEndOf="parent"
                motion:layout_constraintStart_toEndOf="@+id/centerView"
                motion:layout_constraintTop_toTopOf="parent" />
        </Constraint>
    </ConstraintSet>

    <Transition
        motion:constraintSetEnd="@id/move_left_to_right"
        motion:constraintSetStart="@id/base_state">
        <OnSwipe
            motion:dragDirection="dragRight"
            motion:onTouchUp="autoCompleteToStart"
            motion:touchAnchorId="@id/leftHiddenView"
            motion:touchAnchorSide="right" />

<!--        <OnClick-->
<!--            motion:clickAction="transitionToEnd"-->
<!--            motion:targetId="@+id/leftView" />-->
    </Transition>

    <Transition
        motion:constraintSetEnd="@id/move_right_to_left"
        motion:constraintSetStart="@id/base_state">
        <OnSwipe
            motion:dragDirection="dragLeft"
            motion:onTouchUp="autoCompleteToEnd"
            motion:touchAnchorId="@id/rightHiddenView"
            motion:touchAnchorSide="left" />

<!--        <OnClick-->
<!--            motion:clickAction="transitionToEnd"-->
<!--            motion:targetId="@+id/leftView" />-->
    </Transition>

</MotionScene>