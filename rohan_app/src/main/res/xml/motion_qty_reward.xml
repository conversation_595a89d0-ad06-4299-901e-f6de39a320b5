<?xml version="1.0" encoding="utf-8"?>
<MotionScene xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ConstraintSet android:id="@+id/qty_reward_start"
        app:transitionEasing="accelerate">

        <Constraint android:id="@+id/tv_food_click"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_reward_subtitle0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <Constraint android:id="@+id/iv_reward_icon"
            android:layout_marginRight="40dp"
            android:layout_width="@dimen/reward_icon_width"
            android:layout_height="@dimen/reward_icon_height"
            app:layout_constraintBottom_toBottomOf="@+id/tv_food_name"
            app:layout_constraintTop_toTopOf="@+id/tv_food_name"
            app:layout_constraintRight_toRightOf="parent" />

        <Constraint android:id="@+id/btn_food_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0"
            android:layout_marginLeft="-20dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_food_name"
            app:layout_constraintTop_toTopOf="@+id/tv_food_name"
            app:layout_constraintLeft_toRightOf="parent" >
        </Constraint>

        <Constraint android:id="@+id/btn_food_option_click"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="parent" >
        </Constraint>

    </ConstraintSet>

    <ConstraintSet android:id="@+id/qty_reward_end"
        app:transitionEasing="accelerate">

        <Constraint android:id="@+id/tv_food_click"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_reward_subtitle0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/btn_food_option" />

        <Constraint android:id="@+id/iv_reward_icon"
            android:layout_marginRight="20dp"
            android:layout_width="@dimen/reward_icon_width"
            android:layout_height="@dimen/reward_icon_height"
            app:layout_constraintBottom_toBottomOf="@+id/tv_food_name"
            app:layout_constraintTop_toTopOf="@+id/tv_food_name"
            app:layout_constraintRight_toLeftOf="@+id/btn_food_option" />

        <Constraint android:id="@+id/btn_food_option"
            android:layout_marginRight="20dp"
            android:alpha="1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/tv_food_name"
            app:layout_constraintTop_toTopOf="@+id/tv_food_name"
            app:layout_constraintRight_toRightOf="parent" >
        </Constraint>

        <Constraint android:id="@+id/btn_food_option_click"
            android:layout_width="0dp"
            android:layout_height="@dimen/menu_button_size"
            app:layout_constraintLeft_toLeftOf="@+id/btn_food_option"
            app:layout_constraintTop_toTopOf="@+id/btn_food_option"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/btn_food_option" >
        </Constraint>

    </ConstraintSet>


    <Transition
        app:constraintSetStart="@+id/qty_reward_start"
        app:constraintSetEnd="@+id/qty_reward_end"
        app:duration="300"
        app:motionInterpolator="linear"
        android:id="@+id/transition_qty_normal" >
        <KeyFrameSet>
        </KeyFrameSet>
    </Transition>


</MotionScene>