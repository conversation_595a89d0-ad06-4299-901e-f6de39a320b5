<?xml version="1.0" encoding="utf-8"?>
<MotionScene
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:motion="http://schemas.android.com/apk/res-auto">

    <Transition
        motion:constraintSetEnd="@+id/launch_end"
        motion:constraintSetStart="@+id/launch_start"
        motion:duration="600"
        motion:autoTransition="animateToEnd">
        <KeyFrameSet>

            <KeyAttribute
                motion:motionTarget="@+id/iv_splash_screen"
                motion:framePosition="15"
                android:translationZ="-1dp"
                android:scaleX="1.1" />
            <KeyAttribute
                motion:motionTarget="@+id/iv_splash_screen"
                motion:framePosition="15"
                android:translationZ="-1dp"
                android:scaleY="1.1" />

            <KeyAttribute
                motion:motionTarget="@+id/iv_splash_screen"
                motion:framePosition="50"
                android:translationZ="5dp"
                android:scaleX="2" />
            <KeyAttribute
                motion:motionTarget="@+id/iv_splash_screen"
                motion:framePosition="50"
                android:translationZ="5dp"
                android:scaleY="2" />

            <KeyAttribute
                motion:motionTarget="@+id/iv_splash_screen"
                motion:framePosition="98"
                android:translationZ="10dp"
                android:scaleX="6" />
            <KeyAttribute
                motion:motionTarget="@+id/iv_splash_screen"
                motion:framePosition="98"
                android:translationZ="10dp"
                android:scaleY="6" />

            <KeyAttribute
                motion:motionTarget="@+id/iv_splash_screen"
                motion:framePosition="99"
                android:translationZ="10dp"
                android:scaleX="20" />
            <KeyAttribute
                motion:motionTarget="@+id/iv_splash_screen"
                motion:framePosition="99"
                android:translationZ="10dp"
                android:scaleY="20" />


        </KeyFrameSet>
    </Transition>

    <ConstraintSet android:id="@+id/launch_start">
        <Constraint
            android:id="@+id/iv_splash_screen"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="1.2"
            android:scaleY="1.2"
            motion:layout_constraintBottom_toBottomOf="parent"
            motion:layout_constraintTop_toTopOf="parent"
            motion:layout_constraintStart_toStartOf="parent"
            motion:layout_constraintEnd_toEndOf="parent"
            motion:transitionEasing="accelerate" />
    </ConstraintSet>

    <ConstraintSet
        android:id="@+id/launch_end">
        <Constraint
            android:id="@+id/iv_splash_screen"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="90"
            android:scaleY="90"
            motion:layout_constraintBottom_toBottomOf="parent"
            motion:layout_constraintTop_toTopOf="parent"
            motion:layout_constraintStart_toStartOf="parent"
            motion:layout_constraintEnd_toEndOf="parent"
            motion:transitionEasing="accelerate" />
    </ConstraintSet>
</MotionScene>