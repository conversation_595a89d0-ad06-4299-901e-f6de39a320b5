package com.ricepo.app.wxapi

import android.content.Intent
import androidx.lifecycle.lifecycleScope
import com.ricepo.app.R
import com.ricepo.app.data.kv.OrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.base.BroadcastConst
import com.ricepo.style.ResourcesUtil
import com.ricepo.tripartite.wechat.BaseWXPayEntryActivity
import com.ricepo.tripartite.wechat.WeChatPay
import com.tencent.mm.opensdk.modelbase.BaseResp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class WXPayEntryActivity : BaseWXPayEntryActivity() {

  override fun onResp(resp: BaseResp?) {
    super.onResp(resp)

    // navigate to order page, but not order data
    OrderCache.getOrder {
      var paymentError: String? = null
      val errorCode = resp?.errCode ?: WeChatPay.RESULT_ERROR
      if (it?.rechargeOrder == null) {
        if (errorCode != WeChatPay.RESULT_CANCELED &&
          errorCode != WeChatPay.RESULT_SUCCESS
        ) {
          paymentError = ResourcesUtil.getString(com.ricepo.style.R.string.wechat_payment_failed)
        }
        lifecycleScope.launch {
          val restCart = withContext(Dispatchers.IO) {
            RestaurantCartCache.getRestaurantCart(it?.restaurant)
          }
          FeaturePageRouter.navigateOrderAfterCheckout(it, paymentError, restCart?.restaurant)
          // clear cart after place success
          RestaurantCartCache.deleteRestaurantCart(it?.restaurant)
          OrderCache.deleteOrder()
        }
      } else {
        if (errorCode != WeChatPay.RESULT_SUCCESS) {
          paymentError = ResourcesUtil.getString(com.ricepo.style.R.string.wechat_payment_failed)
        }
        // tip order delta
        notifyPaymentResult(paymentError == null)
        lifecycleScope.launch {
          // clear cart after place success
          RestaurantCartCache.deleteRestaurantCart(it?.restaurant)
          OrderCache.deleteOrder()
        }
      }
      finish()
    }
  }

  private fun notifyPaymentResult(completed: Boolean) {
    val intent = Intent()
    intent.putExtra(BroadcastConst.PARAM_PAYMENT_COMPLETED, completed)
    intent.action = BroadcastConst.ACTION_DRIVER_RATING_PAYMENT
    sendBroadcast(intent)
  }
}
