package com.ricepo.app.wxapi

import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler

//
// Created by <PERSON><PERSON> on 21/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class WXEntryActivity : AppCompatActivity(), IWXAPIEventHandler {

  private val TAG: String = "WXEntry"

  public override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
  }

  override fun onReq(req: BaseReq) {
    when (req.type) {
      ConstantsAPI.COMMAND_GETMESSAGE_FROM_WX -> Log.i(TAG, "get message")
      ConstantsAPI.COMMAND_SHOWMESSAGE_FROM_WX -> Log.i(TAG, "show message")
      ConstantsAPI.COMMAND_LAUNCH_BY_WX -> Log.i(TAG, "launch by wx")
      else -> {}
    }
  }

  override fun onResp(resp: BaseResp) {
    if (resp.type == ConstantsAPI.COMMAND_SENDAUTH) {
      Log.i(TAG, "send auth code = ${(resp as SendAuth.Resp).code}")
    }
    var result = when (resp.errCode) {
      BaseResp.ErrCode.ERR_OK -> "err code success"
      BaseResp.ErrCode.ERR_USER_CANCEL -> "err code cancel"
      BaseResp.ErrCode.ERR_AUTH_DENIED -> "err code deny"
      else -> "err code unknown"
    }
    Log.i(TAG, "wx share resp result = $result")
  }

  companion object {
    private const val TIMELINE_SUPPORTED_VERSION = 0x21020001
  }
}
