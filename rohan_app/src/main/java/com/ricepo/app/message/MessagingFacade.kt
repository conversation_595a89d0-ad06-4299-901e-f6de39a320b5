package com.ricepo.app.message

import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.iid.FirebaseInstanceId
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.di.entrypoint.CombineApiPoint
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseApplication
import com.ricepo.monitor.MonitorFacade
import com.ricepo.monitor.log.Logger
import dagger.hilt.android.EntryPointAccessors
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

//
// Created by <PERSON><PERSON> on 7/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object MessagingFacade {

  private const val TAG = "MessagingFacade"

  private val api: CombineRestApi by lazy {
    EntryPointAccessors.fromApplication(
      BaseApplication.context,
      CombineApiPoint::class.java
    ).injectCombineApi()
  }

  suspend fun pushRegister(token: String? = null) {
    val customerId = CustomerCache.getCustomerSuspend()?.id ?: return
    Logger.i(TAG, "customerId = $customerId")

    try {
      if (token != null) {
        Logger.i(TAG, "message token = $token")
//                api.postApn(customerId, listOf(token))
        api.postApnFcm(customerId, mapOf("fcmToken" to token))
      } else {
        val instanceId = getInstanceId()
        Logger.i(TAG, "message instance id = $instanceId")
        if (instanceId.isNotEmpty()) {
//                    api.postApn(customerId, listOf(instanceId))
          api.postApnFcm(customerId, mapOf("fcmToken" to instanceId))
        }
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }

    // register onesignal
//    OneSignalFacade.registerOneSignal(customerId)
  }

  private suspend fun getInstanceId(): String {
    return suspendCoroutine { cont ->
      try {
        FirebaseInstanceId.getInstance().instanceId
          .addOnCompleteListener(
            OnCompleteListener { task ->
              if (!task.isSuccessful) {
                return@OnCompleteListener
              }

              // Get new Instance ID token
              val token = task.result?.token ?: return@OnCompleteListener

              // emit token
              cont.resume(token)
            }
          )
          .addOnFailureListener {
            it.printStackTrace()
            cont.resume("")
          }
      } catch (e: Exception) {
        e.printStackTrace()
//        MonitorFacade.captureException(e)
        cont.resume("")
      }
    }
  }

  /**
   * unregister firebase messaging
   */
  suspend fun pushUnregister(): Boolean {
    return suspendCoroutine { cont ->
      try {
        FirebaseInstanceId.getInstance().deleteInstanceId()
        cont.resume(true)
      } catch (e: Exception) {
        e.printStackTrace()
//        MonitorFacade.captureException(e)
        cont.resume(false)
      }
    }
  }
}
