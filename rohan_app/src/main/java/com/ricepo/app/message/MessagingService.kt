package com.ricepo.app.message

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.ricepo.app.R
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.MenuActivity
import com.ricepo.app.features.support.chat.ChatActivity
import com.ricepo.app.view.GlobalDialogFacade
import com.ricepo.app.view.GlobalDialogMethod
import com.ricepo.base.tools.SystemUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext

class MessagingService : FirebaseMessagingService() {

  private var coroutineJob: Job = Job()

  private val coroutineContext: CoroutineContext =
    Dispatchers.IO + coroutineJob

  private val lifecycleScope = CoroutineScope(coroutineContext)

  /**
   * Called when message is received.
   *
   * @param remoteMessage Object representing the message received from Firebase Cloud Messaging.
   */
  // [START receive_message]
  override fun onMessageReceived(remoteMessage: RemoteMessage) {
    // [START_EXCLUDE]
    // There are two types of messages data messages and notification messages. Data messages are handled
    // here in onMessageReceived whether the app is in the foreground or background. Data messages are the type
    // traditionally used with GCM. Notification messages are only received here in onMessageReceived when the app
    // is in the foreground. When the app is in the background an automatically generated notification is displayed.
    // When the user taps on the notification they are returned to the app. Messages containing both notification
    // and data payloads are treated as notification messages. The Firebase console always sends notification
    // messages. For more see: https://firebase.google.com/docs/cloud-messaging/concept-options
    // [END_EXCLUDE]

    // Handle FCM messages here.
    // Not getting messages here? See why this may be: https://goo.gl/39bRNJ
    Log.d(TAG, "From: ${remoteMessage.from}")

    // Check if message contains a data payload.
    if (remoteMessage.data.isNotEmpty()) {
      Log.d(TAG, "Message data payload: ${remoteMessage.data}")

      if (/* Check if data needs to be processed by long running job */ true) {
        // For long-running tasks (10 seconds or more) use WorkManager.
        scheduleJob()
      } else {
        // Handle message within 10 seconds
        handleNow()
      }
    }

    // Check if message contains a notification payload.
    remoteMessage.notification?.let {
      Log.d(TAG, "Message Notification Body: ${it.body}")
    }

    // Also if you intend on generating your own notifications as a result of a received FCM
    // message, here is where that should be initiated. See sendNotification method below.

    initNotification(remoteMessage)
  }
  // [END receive_message]

  /**
   * handle default notification with background
   */
  override fun handleIntent(intent: Intent?) {
    val extras = intent?.extras
    if (extras != null) {
      // change the default notification
      val remoteBuilder = RemoteMessage.Builder(TAG)
      for (key in extras.keySet()) {
        remoteBuilder.addData(key, extras.get(key).toString())
      }
      initNotification(remoteBuilder.build())
    } else {
      super.handleIntent(intent)
    }
  }

  // [START on_new_token]
  /**
   * Called if InstanceID token is updated. This may occur if the security of
   * the previous token had been compromised. Note that this is called when the InstanceID token
   * is initially generated so this is where you would retrieve the token.
   */
  override fun onNewToken(token: String) {
    Log.d(TAG, "Refreshed token: $token")

    // If you want to send messages to this application instance or
    // manage this apps subscriptions on the server side, send the
    // Instance ID token to your app server.
    sendRegistrationToServer(token)
  }
  // [END on_new_token]

  /**
   * Schedule async work using WorkManager.
   */
  private fun scheduleJob() {
    // [START dispatch_job]
//        val work = OneTimeWorkRequest.Builder(MyWorker::class.java).build()
//        WorkManager.getInstance().beginWith(work).enqueue()
    // [END dispatch_job]
  }

  /**
   * Handle time allotted to BroadcastReceivers.
   */
  private fun handleNow() {
    Log.d(TAG, "Short lived task is done.")
  }

  /**
   * Persist token to third-party servers.
   *
   * Modify this method to associate the user's FCM InstanceID token with any server-side account
   * maintained by your application.
   *
   * @param token The new token.
   */
  private fun sendRegistrationToServer(token: String?) {
    //  send token to app server.
    Log.d(TAG, "sendRegistrationTokenToServer($token)")

    lifecycleScope.launch {
//            val customerId = CustomerCache.getCustomerSuspend()?.id ?: return@launch
//            val token = token ?: return@launch
//            val api = Injection.provideCombineApi()
//            api.postApn(customerId, token)
      MessagingFacade.pushRegister(token)
    }
  }

  override fun onDestroy() {
    super.onDestroy()
    coroutineJob.cancel()
  }

  private fun initNotification(remoteMessage: RemoteMessage) {
    val data = remoteMessage.data ?: return

    val twiBody = data.get("twi_body")
    val page = data.get("page")
    val messageBody = remoteMessage.notification?.body ?: twiBody ?: ""

    val context = this

    lifecycleScope.launch {
      val groupOrder = GroupOrderCache.getOrderSuspend()
      // group order interception
      if (SystemUtils.isAppOnForeground() && groupOrder != null &&
        SystemUtils.getTopActivityName() == MenuActivity.ACTIVITY_NAME
      ) {
        // intent to cancel notification bar
        val intent = PendingIntent.getBroadcast(context, 0, Intent(), PendingIntent.FLAG_IMMUTABLE)
        sendNotification(intent, messageBody)
      } else if (SystemUtils.isAppOnForeground() && (twiBody != null || page == "chat")) {
        // show chat prompt dialog when app foreground
        if (SystemUtils.getTopActivityName() == ChatActivity.ACTIVITY_NAME) {
          // don't show dialog when on chat page
          return@launch
        }
        GlobalDialogFacade.showGlobalDialog(
          context, messageBody,
          methodType = GlobalDialogMethod.METHOD_CHAT, negativeId = com.ricepo.style.R.string.cancel,
          positiveId = com.ricepo.style.R.string.chat_enter
        )
      } else {
        if (twiBody != null) {
          data["page"] = "chat"
        }
        try {
          var params = hashMapOf<String, String>()
          for (d in data) {
            params[d.key] = d.value
          }
          val intent = FeaturePageRouter.defaultHomePendingIntent(
            context, params
          )
          // get the twilio body message if body is null
          sendNotification(intent, messageBody)
        } catch (e: Exception) {
          e.printStackTrace()
        }
      }
    }
  }

  /**
   * Create and show a simple notification containing the received FCM message.
   *
   * @param messageBody FCM message body received.
   */
  private fun sendNotification(pendingIntent: PendingIntent, messageBody: String) {
    // empty message not show
    if (messageBody.isNullOrEmpty()) return

    val channelId = getString(R.string.default_notification_channel_id)
    val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
    val notificationBuilder = NotificationCompat.Builder(this, channelId)
      .setSmallIcon(R.drawable.ic_stat_notification)
//            .setContentTitle(getString(com.ricepo.style.R.string.fcm_message))
      .setContentText(messageBody)
      .setAutoCancel(true)
      .setSound(defaultSoundUri)
      .setContentIntent(pendingIntent)

    val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

    // Since android Oreo notification channel is needed.
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      val channel = NotificationChannel(
        channelId,
        "Channel Ricepo Messaging",
        NotificationManager.IMPORTANCE_DEFAULT
      )
      notificationManager.createNotificationChannel(channel)
    }

    notificationManager.notify(0 /* ID of notification */, notificationBuilder.build())
  }

  companion object {

    private const val TAG = "MessagingService"
  }
}
