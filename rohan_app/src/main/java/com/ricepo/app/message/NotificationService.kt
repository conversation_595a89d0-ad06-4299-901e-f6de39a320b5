package com.ricepo.app.message

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.core.app.NotificationManagerCompat


//
// Created by <PERSON><PERSON> on 20/09/2024.
//
object NotificationService {

    fun checkNotifyPermission(mContext: Context?): Boolean {
        mContext ?: return true;
        val manager = NotificationManagerCompat.from(mContext)
        val isOpened = manager.areNotificationsEnabled()
        return isOpened
    }

    fun toNotificationSettings(context: Context?) {
        if (context == null) {
            return
        }
        try {
            val appInfo = context.applicationInfo
            val pkg = context.applicationContext.packageName
            val uid = appInfo.uid
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val intent = Intent()
                intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
                // 26 +
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, pkg)
                intent.putExtra(Settings.EXTRA_CHANNEL_ID, uid)
                // 21 ~ 25
                intent.putExtra("app_package", pkg)
                intent.putExtra("app_uid", uid)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            } else if (Build.VERSION.SDK_INT == Build.VERSION_CODES.KITKAT) {
                val intent = Intent()
                intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.addCategory(Intent.CATEGORY_DEFAULT)
                intent.setData(Uri.parse("package:" + context.packageName))
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            } else {
                context.startActivity(Intent(Settings.ACTION_SETTINGS).addFlags(Intent.FLAG_ACTIVITY_NEW_TASK))
            }
        } catch (e: Exception) {
            try {
                context.startActivity(Intent(Settings.ACTION_SETTINGS).addFlags(Intent.FLAG_ACTIVITY_NEW_TASK))
            } catch (e1: Exception) { /**/
            }
        }
    }

}