package com.ricepo.app.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import kotlin.math.abs

//
// Created by <PERSON><PERSON> on 2021/9/30.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class SmartExtRefreshLayout : SmartRefreshLayout {

  constructor(context: Context) : super(context) {
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
  }

  /**
   * default false
   * disable the horizontal scroll when is refreshing
   */
  var disableHorizontalScroll = false

  var startX = 0f
  var startY = 0f

  override fun dispatchTouchEvent(e: MotionEvent): Bo<PERSON>an {

    when (e.action) {
      MotionEvent.ACTION_DOWN -> {
        startX = e.x
        startY = e.y
      }
      MotionEvent.ACTION_MOVE -> {
        val dx = abs(e.x - startX)
        val dy = abs(e.y - startY)
        if (dx > dy && disableHorizontalScroll) {
          return true
        }
      }
    }

    return super.dispatchTouchEvent(e)
  }
}
