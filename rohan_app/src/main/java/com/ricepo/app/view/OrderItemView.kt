package com.ricepo.app.view

import android.content.Context
import android.os.CountDownTimer
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.ricepo.app.R
import com.ricepo.app.databinding.ItemOrderInfoBinding
import com.ricepo.app.features.checkout.data.OrderCellInfo
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.onRightDrawableListener
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.base.tools.SimpleDateUtils
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.span.CenteredImageSpan
import kotlin.math.ceil

//
// Created by <PERSON><PERSON> on 28/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OrderItemView : ConstraintLayout {

  constructor(context: Context) : super(context) {
    init(null)
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
    init(attrs)
  }

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(attrs)
    }

  lateinit var binding: ItemOrderInfoBinding

  private lateinit var mapper: BaseMapper

  private var cellData: OrderCellInfo? = null

  // init of view
  private fun init(attrs: AttributeSet?) {
    binding = ItemOrderInfoBinding.inflate(LayoutInflater.from(context), this, true)

    mapper = BaseMapper()
  }

  fun setMarginEnd(marginEnd: Int) {
    binding.guidelineItemRight.apply {
      setGuidelineEnd(marginEnd)
    }
  }

  fun initiate(with: OrderCellInfo?) {
    cellData = with ?: return

    binding.tvLeftText.apply {
      text = with.leftText
    }

      binding.tvRightText.apply {
        text = with.rightSpan ?: with.rightText
    }

    if (with.large == true) {
      binding.tvLeftText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f)
      binding.tvLeftText.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.mainText,
          rootView.context
        )
      )
      binding.tvRightText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f)
      binding.tvRightText.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.mainText,
          rootView.context
        )
      )
    }

    if (with.leftIcon != null) {
      binding.ivLeftIcon.visibility = View.VISIBLE
      binding.ivLeftIcon.setImageDrawable(with.leftIcon)
    } else {
      binding.ivLeftIcon.visibility = View.GONE
    }

    if (with.rightIcon != null) {
      binding.ivRightIcon.setImageDrawable(with.rightIcon)
      if (with.rightIconSize > 0) {
        binding.ivRightIcon.layoutParams.width = with.rightIconSize
        binding.ivRightIcon.layoutParams.height = with.rightIconSize
      }
      if (with.rightIconEndMargin > 0) {
//        (binding.gui.layoutParams as MarginLayoutParams)
//          .marginEnd = with.rightIconEndMargin
        binding.guidelineItemRight.setGuidelineEnd(with.rightIconEndMargin)
      }
    }

    with.color?.let {
      binding.tvLeftText.setTextColor(it)
      binding.tvRightText.setTextColor(it)
    }

    if (with.click != null) {
      binding.root.clickWithTrigger {
        with.click?.run {
          this()
        }
      }
    }

    // left text
    when {
      with.leftTips?.isNullOrEmpty() == false -> {
        // tips
        showLeftTextIcon(com.ricepo.style.R.drawable.ic_fee_info)
        binding.tvLeftText.onRightDrawableListener {
          DialogFacade.showAlert(binding.root.context, with.leftTips ?: "")
        }
      }
      with.coupon != null -> {
        // coupon cross
        showLeftTextIcon(com.ricepo.style.R.drawable.ic_cross)

        binding.tvLeftText.onRightDrawableListener {
          with.rightIconClick?.run {
            this()
          }
        }
      }
      else -> {
        hideLeftTextIcon()
      }
    }

    // pool discount
    if (with.expiredAt != null) {
      initExpireAt(with.expiredAt)
    }

    // delivery notes
    binding.tvLeftInfo.isVisible = with.leftDetail != null
    if (with.leftDetail != null) {
      val size = ResourcesUtil.getDimenPixelOffset(rootView, com.ricepo.style.R.dimen.sw_17dp)
      val infoImage = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_fee_info, rootView.context)
      infoImage.setBounds(0, 0, size, size)
      val imageSpan = CenteredImageSpan(infoImage)
      val spanText = SpannableStringBuilder(" ")
      spanText.append(with.leftDetail)
      spanText.setSpan(imageSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
      binding.tvLeftInfo.text = spanText
    }

    // alpha
    binding.root.alpha = with.alpha
  }

  private fun showLeftTextIcon(resId: Int) {
    val drawable = ResourcesUtil.getDrawable(resId, rootView.context)
    val size = ResourcesUtil.getDimenPixelOffset(rootView, com.ricepo.style.R.dimen.sw_17dp)
    drawable.setBounds(0, 0, size, size)
    binding.tvLeftText.setCompoundDrawables(
      null, null,
      drawable, null
    )
  }

  private fun hideLeftTextIcon() {
    binding.tvLeftText.setCompoundDrawables(null, null, null, null)
  }

  private fun initExpireAt(expiredAt: String?) {
    setExpireAt(expiredAt)
  }

  private var countDownTimer: CountDownTimer? = null

  private fun setExpireAt(date: String?) {
    val expireAt = date ?: return
    setExpireAt(SimpleDateUtils.toDateDiff(expireAt))
  }

  private fun setExpireAt(millis: Long) {
    val isExpired = (millis <= 0)
    changeExpired(isExpired)
    if (isExpired) return
  }

  private fun getMinute(ms: Long): String {
    val minute = ceil(ms / 1000f).toInt() / 60
    return if (minute < 10) {
      "0$minute"
    } else {
      minute.toString()
    }
  }

  private fun getSeconds(ms: Long): String {
    val seconds = ceil(ms / 1000f).toInt() % 60
    return if (seconds < 10) {
      "0$seconds"
    } else {
      seconds.toString()
    }
  }

  private fun changeExpired(isExpired: Boolean) {
    binding.ivLeftIcon.isVisible = true
    if (isExpired) {
      binding.ivLeftIcon.setImageResource(com.ricepo.style.R.drawable.ic_timer_expired)
      binding.tvLeftText.text = "${cellData?.leftText} : ${ResourcesUtil.getString(com.ricepo.style.R.string.restaurant_pool_expired)}"
      binding.tvLeftText.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.gray7, rootView.context))
      binding.tvRightText.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.gray7, rootView.context))
    } else {
      binding.ivLeftIcon.setImageResource(com.ricepo.style.R.drawable.ic_timer)
      binding.tvLeftText.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.mr, rootView.context))
      binding.tvRightText.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.mr, rootView.context))
    }
  }
}
