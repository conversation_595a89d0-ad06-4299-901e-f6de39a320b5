package com.ricepo.app.view

import android.content.Context
import android.graphics.Paint
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.ricepo.app.R
import com.ricepo.app.databinding.ViewMenuFoodItemBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.model.ExtraFee
import com.ricepo.app.model.OrderItem
import com.ricepo.base.BaseApplication
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import java.lang.StringBuilder

//
// Created by <PERSON><PERSON> on 28/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuFoodItemView : ConstraintLayout {

  constructor(context: Context) : super(context) {
    init(null)
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
    init(attrs)
  }

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(attrs)
    }

//    private lateinit var mView: View

  private lateinit var binding: ViewMenuFoodItemBinding

  private lateinit var mapper: MenuMapper

  // init of view
  private fun init(attrs: AttributeSet?) {
    binding = ViewMenuFoodItemBinding.inflate(LayoutInflater.from(context), this, true)
    mapper = MenuMapper()
  }

  fun initiate(with: ExtraFee, restaurant: Restaurant?) {
    binding.tvFoodName.apply {
      text = with.name?.localize()
    }

    binding.tvFoodPrice.apply {
      text = mapper.formatPriceByRestaurant(with.price ?: 0, restaurant)
    }

    var topMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_15dp)
    val nameParams = binding.tvFoodName.layoutParams as ConstraintLayout.LayoutParams
    nameParams.topMargin = topMargin
    binding.tvFoodName.layoutParams = nameParams
  }

  fun initiate(
    with: Cart,
    itemIndex: Int,
    restaurant: Restaurant?,
    minus: (food: Cart, foodIndex: Int) -> Unit,
    plus: (food: Cart, foodIndex: Int) -> Unit,
    editOption: (food: Cart, foodIndex: Int) -> Unit
  ) {

    // if group order, show nickname
    if (!with.nickName.isNullOrEmpty()) {
      binding.tvFoodGroup.text = with.nickName
      binding.ivFoodGroup.visibility = View.VISIBLE
      binding.tvFoodGroup.visibility = View.VISIBLE
    }

    var topMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_15dp)
    if (itemIndex == 0) {
      topMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_20dp)
    }

    val nameParams = binding.tvFoodName.layoutParams as ConstraintLayout.LayoutParams
    nameParams.topMargin = topMargin
    binding.tvFoodName.layoutParams = nameParams

    val btnParams = binding.btnFoodQty.layoutParams as ConstraintLayout.LayoutParams
    btnParams.topMargin = topMargin
    binding.btnFoodQty.layoutParams = btnParams

    binding.tvFoodName.apply {
      text = with.name.localize()
    }

    val foodInfo = with.opt.foldIndexed(StringBuilder()) { index, result, p ->
      if (index == 0) {
        result.append(p.name.localize())
      } else {
        result.append(" • ${p.name.localize()}")
      }
      result
    }
    binding.tvFoodInfo.apply {
      visibility = if (foodInfo.isNotEmpty()) View.VISIBLE else View.GONE
      val showModify = (with.ownerId == null || with.ownerId == BaseApplication.mDeviceId)
      val modifySpan = mapper.getModifySpan(binding.root.context, showModify)
      text = if (modifySpan != null) {
        binding.tvFoodInfo.clickWithTrigger {
          editOption(with, itemIndex)
        }
        val spanText = SpannableStringBuilder(foodInfo)
        spanText.append("  ")
        spanText.setSpan(
          modifySpan, spanText.length - 1, spanText.length,
          Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        spanText
      } else {
        foodInfo.toString()
      }
    }

    binding.tvFoodPrice.apply {
      text = if (with.reward == true) {
        // reward point
        "${mapper.calcCoinCount(with.point)}"
      } else {
        // food price
        mapper.formatPriceByRestaurant(with.price, restaurant)
      }
    }
    // show reward icon
    if (with.reward == true) {
      binding.ivRewardIcon.visibility = View.VISIBLE
    } else {
      // original price
      with.originalPrice?.let {
        binding.tvFoodOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or
          Paint.ANTI_ALIAS_FLAG
        binding.tvFoodOriginalPrice.text = mapper.formatPriceByRestaurant(it, restaurant)
        binding.tvFoodOriginalPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)

        binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.green, binding.root.context))
      }
    }

    setCartHb(binding, with, isEnable = true)

    setItemOptionsListener(binding, with, itemIndex, minus, plus)
  }

  fun initiate(with: OrderItem, restaurant: Restaurant?) {

    binding.tvFoodName.apply {
      text = with.name?.localize()
    }

    val foodInfo = with.options?.foldIndexed(StringBuilder()) { index, result, p ->
      if (index == 0) {
        result.append(p.name.localize())
      } else {
        result.append(" · ${p.name.localize()}")
      }
      result
    }
    binding.tvFoodInfo.apply {
      visibility = if (foodInfo?.isNotEmpty() == true) View.VISIBLE else View.GONE
      text = foodInfo.toString()
    }

    binding.tvFoodPrice.apply {
      text = if (with.reward == true) {
        // reward point
        "${mapper.calcCoinCount(with.point)}"
      } else {
        mapper.formatPriceByRestaurant(with.price ?: 0, restaurant)
      }
    }

    // show reward icon
    if (with.reward == true) {
      binding.ivRewardIcon.visibility = View.VISIBLE
    } else {
      // original price
      with.originalPrice?.let {
        binding.tvFoodOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or
          Paint.ANTI_ALIAS_FLAG
        binding.tvFoodOriginalPrice.text = mapper.formatPriceByRestaurant(it, restaurant)
        binding.tvFoodOriginalPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)

        binding.tvFoodPrice.setTextColor(
          ResourcesUtil.getColor(
            com.ricepo.style.R.color.green,
            binding.root.context
          )
        )
      }
    }

    setOrderHb(binding, with, isRight = true, isEnable = false)
  }

  private fun setCartHb(
    binding: ViewMenuFoodItemBinding,
    item: Cart,
    isRight: Boolean = true,
    isEnable: Boolean = true
  ) {
    binding.btnFoodQty.setReward((item.reward == true))

    val selectedCount = item.qty ?: 0

    if (isEnable) {
      binding.btnFoodQty.isVisible = true
    }
    binding.btnFoodQty.setEnable(isEnable)

    binding.btnFoodQty.setSelectedCount(selectedCount)
  }

  private fun setOrderHb(
    binding: ViewMenuFoodItemBinding,
    item: OrderItem,
    isRight: Boolean = true,
    isEnable: Boolean = true
  ) {

    binding.btnFoodQty.setReward((item.reward == true))

    val selectedCount = item.qty ?: 0
    if (selectedCount == null || selectedCount == 0) {
      binding.btnFoodQty.visibility = View.GONE
      return
    }
    if (selectedCount > 0) {
      binding.btnFoodQty.visibility = View.VISIBLE
    }

    binding.btnFoodQty.setEnable(isEnable)
    binding.btnFoodQty.setSelectedCount(selectedCount)
  }

  private fun setItemOptionsListener(
    binding: ViewMenuFoodItemBinding,
    item: Cart,
    itemIndex: Int,
    minus: (food: Cart, foodIndex: Int) -> Unit,
    plus: (food: Cart, foodIndex: Int) -> Unit
  ) {

    binding.btnFoodQty.setMinusListener {
      minus(item, itemIndex)
    }

    binding.btnFoodQty.setPlusListener {
      plus(item, itemIndex)
    }
  }
}
