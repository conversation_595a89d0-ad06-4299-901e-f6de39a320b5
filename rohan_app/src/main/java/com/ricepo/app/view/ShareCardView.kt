package com.ricepo.app.view

import android.content.Context
import android.graphics.BitmapFactory
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.model.ReferInfo
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.consts.BaseConstant
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Customer
import com.ricepo.base.model.localize
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.base.remoteconfig.RiceRemoteConfig
import com.ricepo.base.tools.IntentUtils
import com.ricepo.base.view.DialogFacade
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseReferEvent
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.sheet.BaseBottomSheetFragment
import com.ricepo.tripartite.wechat.WeChatShare
import com.ricepo.tripartite.wechat.WeChatTarget
import dagger.hilt.android.internal.managers.ViewComponentManager.FragmentContextWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

//
// Created by Thomsen on 23/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ShareCardView : FrameLayout {

  constructor(context: Context) : super(context) {
    init(context, null, 0)
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
    init(context, attrs, 0)
  }

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(context, attrs, defStyleAttr)
    }

  private lateinit var mView: View
  private lateinit var topTitleView: TextView
  private lateinit var subtitleView: TextView

  var isCardShowing = false

  private fun init(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
    mView = LayoutInflater.from(context).inflate(R.layout.layout_share_card, this)

    topTitleView = mView.findViewById(R.id.tv_share_top_title)
    subtitleView = mView.findViewById(R.id.tv_share_subtitle)

    initListener()
//    initTitle()
  }

  private fun initListener() {
    mView.clickWithTrigger(800) {
      isCardShowing = true
      ShareRicepoUtil.checkAndShowActionSheet(context) {
          isCardShowing = false
      }
//      FeaturePageRouter.navigateRefer(context)
    }
  }

  fun initTitle(referInfo: ReferInfo?) {
//    ShareRicepoUtil.referRewardAmount { customer, priceString ->
//      setTopTitle(customer, priceString)
//    }
    topTitleView.text = referInfo?.title?.localize()
  }

  private fun setTopTitle(customer: Customer?, priceString: String) {
    topTitleView.text = ShareRicepoUtil.getBannerTitle(customer, priceString)
  }
}

object ShareRicepoUtil {
  /**
   * refer reward amount
   */
  fun referRewardAmount(block: (customer: Customer?, priceStr: String) -> Unit) {
    GlobalScope.launch(Dispatchers.Main) {
      val customerDef = async(Dispatchers.IO) { CustomerCache.getCustomer() }
//      val country = async(Dispatchers.IO) { AddressCache.getCountrySuspend() }
//      val referralConfig = RiceRemoteConfig.referralConfig()
//      val mapper = BaseMapper()
      val customer = customerDef.await()
      val point = "${customer?.refer?.point}"
//      val price =
//        (customer?.refer?.reward ?: referralConfig.amount).times(customer?.refer?.multiplier ?: referralConfig.count)
//      val priceString = mapper.formatPrice(price, country?.await(), null)
      block(customer, point)
    }
  }

  /**
   * before present refer action sheet, check login status and customer info
   */
  private fun checkoutCustomer(block: (customer: Customer) -> Unit) {
    CustomerCache.liveCustomer {
      if (it == null) {
        // stop share and redirect to login page if not login
        FeaturePageRouter.navigateLogin()
      } else {
        block(it)
      }
    }
  }

  /**
   * check the login and show select share
   */
  fun checkAndShowActionSheet(context: Context, onDetach: () -> Unit) {
    checkoutCustomer { customer ->
      showActionSheet(customer, context, onDetach)
    }
  }

  /**
   * present refer action sheet
   */
  fun showActionSheet(customer: Customer?, context: Context, onDetach: () -> Unit) {
    val shareLink = getShareLink(customer)
    val shareTitle = getShareTitle(customer)
    val shareDescription = getShareDesc(customer)
    val shareOtherMessage = getShareOtherMessage(customer)
    val shareImage = BitmapFactory.decodeResource(context.resources, com.ricepo.style.R.drawable.ic_logo)

    referRewardAmount { _, priceStr ->
      val datas = listOf(
        ResourcesUtil.getString(com.ricepo.style.R.string.refer_wechat),
        ResourcesUtil.getString(com.ricepo.style.R.string.refer_wechat_moment),
        ResourcesUtil.getString(com.ricepo.style.R.string.group_share_other),
        ResourcesUtil.getString(com.ricepo.style.R.string.refer_copy_title)
      )

      val title = getBannerDesc(customer, priceStr)
      val bottomSheet = BaseBottomSheetFragment.newInstance<String>(datas, title = null)
      bottomSheet.onSheetDetach = onDetach
      bottomSheet.onItemTextClickListener = object : BaseBottomSheetFragment.OnItemTextClickListener {

        override fun onItemClick(text: String) {
          if (ResourcesUtil.getString(com.ricepo.style.R.string.refer_wechat) == text) {
            // share to wechat friend
            WeChatShare.shareStringToWx(
              context, shareLink, shareTitle,
              shareDescription, shareImage, target = WeChatTarget.SESSION
            )
            AnalyticsFacade.logEvent(
              FirebaseReferEvent(FirebaseReferEvent.WECHAT),
              FirebaseEventName.rRefer
            )
          }
          if (ResourcesUtil.getString(com.ricepo.style.R.string.refer_wechat_moment) == text) {
            // share to wechat moment
            WeChatShare.shareStringToWx(
              context, shareLink, shareTitle,
              shareDescription, shareImage, target = WeChatTarget.TIMELINE
            )
            AnalyticsFacade.logEvent(
              FirebaseReferEvent(FirebaseReferEvent.WECHAT_MOMENT),
              FirebaseEventName.rRefer
            )
          }
          if (ResourcesUtil.getString(com.ricepo.style.R.string.group_share_other) == text) {
            IntentUtils.shareToOthers(context, "$shareOtherMessage $shareLink")
          }
          if (ResourcesUtil.getString(com.ricepo.style.R.string.refer_copy_title) == text) {
            val content = getCopyText(customer)
            IntentUtils.shareWithClip(context, content)
            val alert = ResourcesUtil.getString(
              com.ricepo.style.R.string.refer_copy_alert,
              customer?.refer?.share?.url ?: ""
            )
            DialogFacade.showAlert(context, alert)
          }
        }
      }
      val activity = context
      if (activity is AppCompatActivity) {
        bottomSheet.show(activity.supportFragmentManager, "share_friend")
      } else if (activity is FragmentContextWrapper) {
        val baseContext = activity.baseContext
        if (baseContext is AppCompatActivity) {
          baseContext.supportFragmentManager.let {
            bottomSheet.show(it, "share_friend")
          }
        }
      }
    }
  }

  private fun getShareLink(customer: Customer?): String {
    val defaultValue = "http://${BaseConstant.S_RICE_ROCKS}/refer/?code=${customer?.refer?.code ?: ""}"
    return customer?.refer?.share?.url ?: defaultValue
  }

  private fun getShareTitle(customer: Customer?): String {
    val defaultValue = ResourcesUtil.getString(com.ricepo.style.R.string.refer_share_title)
    return customer?.refer?.share?.title?.localize() ?: defaultValue
  }

  private fun getShareDesc(customer: Customer?): String {
    val defaultValue = "iOS · Android · RICE.rocks"
    return customer?.refer?.share?.description?.localize() ?: defaultValue
  }

  private fun getShareOtherMessage(customer: Customer?): String {
    val defaultValue = getShareTitle(customer)
    return customer?.refer?.share?.message?.localize() ?: defaultValue
  }

  private fun getCopyText(customer: Customer?): String {
    val defaultValue = ResourcesUtil.getString(
      com.ricepo.style.R.string.refer_copy_text,
      customer?.refer?.code ?: ""
    )
    return customer?.refer?.share?.copy?.localize() ?: defaultValue
  }

  fun getBannerTitle(customer: Customer?, priceStr: String): String {
//    val defaultValue = "${ResourcesUtil.getString(com.ricepo.style.R.string.refer_friend)}" +
//      " ${ResourcesUtil.getString(com.ricepo.style.R.string.get)} $priceStr ${ResourcesUtil.getString(com.ricepo.style.R.string.my_coins)}"
    val defaultValue = ResourcesUtil.getString(com.ricepo.style.R.string.refer_friend_n_coins, priceStr)
    return customer?.refer?.banner?.title?.localize() ?: defaultValue
  }

  private fun getBannerDesc(customer: Customer?, priceStr: String): String {
    val defaultValue = ResourcesUtil.getString(com.ricepo.style.R.string.refer_now, priceStr)
    return customer?.refer?.banner?.descrpition?.localize() ?: defaultValue
  }
}
