package com.ricepo.app.view

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.base.R
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.view.DialogParams

object GlobalDialogFacade {

  /**
   * show global dialog
   */
  fun showGlobalDialog(
    context: Context,
    message: String,
    title: String? = null,
    methodType: String? = null,
    negativeId: Int? = null,
    positiveId: Int? = null
  ) {
    // return if dialog is already showing
    if (isGlobalShowing) return
    isGlobalShowing = true
    val intent = Intent(context, GlobalDialogActivity::class.java)
    intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or
      Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
    intent.putExtra(GLOBAL_DIALOG_MESSAGE, message)
    if (title != null) {
      intent.putExtra(GLOBAL_DIALOG_TITLE, title)
    }
    methodType?.let {
      intent.putExtra(GLOBAL_DIALOG_METHOD, it)
    }
    negativeId?.let {
      intent.putExtra(GLOBAL_DIALOG_NEGATIVE, it)
    }
    positiveId?.let {
      intent.putExtra(GLOBAL_DIALOG_POSITIVE, it)
    }
    context.startActivity(intent)
    if (context is Activity) {
      context.startActivityForResult(intent, 1)
    }
  }

  private var isGlobalShowing = false

  /**
   * show global dialog with transparent activity
   */
  class GlobalDialogActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
      super.onCreate(savedInstanceState)
      val message = intent.getStringExtra(GLOBAL_DIALOG_MESSAGE)
      val title = intent.getStringExtra(GLOBAL_DIALOG_TITLE)
      val methodType = intent.getStringExtra(GLOBAL_DIALOG_METHOD)
      var negativeId: Int? = intent.getIntExtra(GLOBAL_DIALOG_NEGATIVE, 0)
      val positiveId = intent.getIntExtra(GLOBAL_DIALOG_POSITIVE, com.ricepo.style.R.string.ok)

      if (negativeId == 0) {
        negativeId = null
      }

      val dialogParams = DialogParams(
        context = this,
        message = message,
        title = title,
        negativeId = negativeId,
        negative = {
          backEvent()
        },
        positiveId = positiveId,
        positive = {
          handleMethodType(methodType)
          backEvent()
        }
      )
      // reset create dialog
      DialogFacade.showDialog(dialogParams, true)
    }

    private fun handleMethodType(methodType: String?) {
      val methodType = methodType ?: return
      when (methodType) {
        GlobalDialogMethod.METHOD_CHAT -> {
          FeaturePageRouter.navigateOrderSupportChat(this)
        }
      }
    }

    override fun onPause() {
      isGlobalShowing = false
      // cancel animation with finish event
      overridePendingTransition(0, 0)
      super.onPause()
    }

    private fun backEvent() {
      onBackPressed()
    }
  }
}

/**
 * the bundle key of global message
 */
private const val GLOBAL_DIALOG_MESSAGE = "global_dialog_message"

private const val GLOBAL_DIALOG_TITLE = "global_dialog_title"

private const val GLOBAL_DIALOG_METHOD = "global_dialog_method"

private const val GLOBAL_DIALOG_NEGATIVE = "global_dialog_negative"

private const val GLOBAL_DIALOG_POSITIVE = "global_dialog_positive"

object GlobalDialogMethod {

  /**
   * navigate chat when firebase cloud message
   */
  const val METHOD_CHAT = "navigate_chat"
}
