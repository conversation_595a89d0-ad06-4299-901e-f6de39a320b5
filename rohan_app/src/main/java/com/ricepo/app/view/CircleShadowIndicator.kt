package com.ricepo.app.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import com.ricepo.app.R
import com.ricepo.map.utils.DpUtils
import com.ricepo.style.ResourcesUtil
import com.youth.banner.indicator.BaseIndicator

//
// Created by <PERSON><PERSON> on 23/6/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class CircleShadowIndicator : BaseIndicator {

  private var mNormalRadius = 0
  private var mSelectedRadius = 0
  private var maxRadius = 0

  private var shadowOffset = 0.5f

  constructor(context: Context) : this(context, null) {
  }

  constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0) {
  }

  constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      mNormalRadius = config.normalWidth / 2
      mSelectedRadius = config.selectedWidth / 2
      shadowOffset = DpUtils.dpToPx(context, 1)
    }

  override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
    super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    val count = config.indicatorSize
    if (count <= 1) {
      return
    }
    mNormalRadius = config.normalWidth / 2
    mSelectedRadius = config.selectedWidth / 2
    maxRadius = Math.max(mSelectedRadius, mNormalRadius)
    val width = (count - 1) * config.indicatorSpace + config.selectedWidth + config.normalWidth * (count - 1)
    val normalHeight = (config.normalWidth + (2 * shadowOffset)).toInt()
    setMeasuredDimension(width, Math.max(normalHeight, config.selectedWidth))
  }

  override fun onDraw(canvas: Canvas) {
    super.onDraw(canvas)

    val count = config.indicatorSize
    if (count <= 1) {
      return
    }
    var left = 0f

    for (i in 0 until count) {

      val indicatorWidth =
        if (config.currentPosition == i) config.selectedWidth else config.normalWidth
      val radius = if (config.currentPosition == i) mSelectedRadius else mNormalRadius

      mPaint.color = if (config.currentPosition == i) {
        ResourcesUtil.getColor(com.ricepo.style.R.color.mr, context)
      } else {
        ResourcesUtil.getColor(com.ricepo.style.R.color.fun_n4, context)
      }
      // circle
      val cy = maxRadius.toFloat() + shadowOffset
      canvas?.drawCircle(left + radius, cy, radius.toFloat(), mPaint)

      left += (indicatorWidth + config.indicatorSpace).toFloat()
    }
  }

  private fun drawableToBitmap(drawable: Drawable): Bitmap? {
    if (drawable is BitmapDrawable) {
      return drawable.bitmap
    }
    val bitmap =
      Bitmap.createBitmap(drawable.intrinsicWidth, drawable.intrinsicHeight, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(bitmap)
    drawable.setBounds(0, 0, canvas.width, canvas.height)
    drawable.draw(canvas)
    return bitmap
  }
}
