package com.ricepo.app.di.module

import android.app.Application
import com.ricepo.app.data.AppDatabase
import com.ricepo.app.features.address.AddressDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

//
// Created by Thomsen on 10/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Module
@InstallIn(SingletonComponent::class)
object DaoModule {

  @Provides
  @Singleton
  fun provideAddressDao(context: Application): AddressDao {
    val database = AppDatabase.getInstance(context)
    return database.addressDao()
  }
}
