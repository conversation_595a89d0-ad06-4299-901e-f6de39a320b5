package com.ricepo.app.di.entrypoint

import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restapi.RestaurantRestApi
import com.ricepo.app.restapi.RiceApi
import com.ricepo.app.restaurant.RestaurantRemote
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@EntryPoint
@InstallIn(SingletonComponent::class)
interface RestaurantRemotePoint {
  fun injectRestaurantRemotePoint(): RestaurantRemote
}

@EntryPoint
@InstallIn(SingletonComponent::class)
interface CombineApiPoint {
  fun injectCombineApi(): CombineRestApi
}

@EntryPoint
@InstallIn(SingletonComponent::class)
interface RestaurantApiPoint {
  fun injectRestaurantApi(): RestaurantRestApi
}

@EntryPoint
@InstallIn(SingletonComponent::class)
interface RiceApiPoint {
  fun injectRiceApi(): RiceApi
}
