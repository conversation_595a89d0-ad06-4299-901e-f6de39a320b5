package com.ricepo.app.di.module.ui

import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.network.executor.RxThread
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

//
// Created by Thomsen on 20/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Module
@InstallIn(SingletonComponent::class)
abstract class UiModule {
  @Binds
  abstract fun bindPostExecutionThread(uiThread: RxThread): PostExecutionThread
}
