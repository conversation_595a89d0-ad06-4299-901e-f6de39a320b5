package com.ricepo.app.di.module

import com.ricepo.base.data.pref.CommonPref
import com.ricepo.base.data.pref.PrefDataSource
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DataSourceModule {

  @Provides
  @Singleton
  fun providePref(): PrefDataSource {
    return CommonPref
  }
}
