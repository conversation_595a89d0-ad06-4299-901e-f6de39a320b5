package com.ricepo.app.di.module

import com.ricepo.app.features.login.repository.AuthRemoteImpl
import com.ricepo.app.features.login.repository.AuthRepository
import com.ricepo.app.restaurant.RestaurantRemote
import com.ricepo.app.restaurant.RestaurantRemoteImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class RepoModule {

  @Binds
  abstract fun bindRestaurantRemote(restaurantRemoteImpl: RestaurantRemoteImpl): RestaurantRemote

  @Binds
  abstract fun bindAuthRepository(authRemoteImpl: AuthRemoteImpl): AuthRepository
}
