package com.ricepo.app.di.module

import com.ricepo.app.consts.GlobalValueHolder
import com.ricepo.app.consts.IGlobalValueHolder
import com.ricepo.base.remoteconfig.IRiceRemoteConfig
import com.ricepo.base.remoteconfig.RiceRemoteConfig
import com.ricepo.style.IResourcesUtil
import com.ricepo.style.ResourcesUtil
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object CommonModule {

  @Provides
  @Singleton
  fun provideResourceUtil(): IResourcesUtil {
    return ResourcesUtil
  }

  @Provides
  @Singleton
  fun provideRemoteConfig(): IRiceRemoteConfig {
    return RiceRemoteConfig
  }

  @Provides
  @Singleton
  fun provideGlobalValueHolder(): IGlobalValueHolder {
    return GlobalValueHolder
  }
}
