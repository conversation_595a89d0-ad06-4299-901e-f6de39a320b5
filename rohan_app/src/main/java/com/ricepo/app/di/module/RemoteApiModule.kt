package com.ricepo.app.di.module

import com.ricepo.app.BuildConfig
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.app.restapi.AuthRestApi
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restapi.GoogleMapApi
import com.ricepo.app.restapi.GoogleMapsRestApi
import com.ricepo.app.restapi.RestaurantRestApi
import com.ricepo.app.restapi.RiceApi
import com.ricepo.base.BaseApplication
import com.ricepo.base.parser.ParserFacade
import com.ricepo.network.NetworkConfiguration
import com.ricepo.network.restapi.RemoteRestApiFactory
import com.ricepo.style.LocaleConst
import com.ricepo.style.LocaleUtil
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

//
// Created by <PERSON><PERSON> on 30/3/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Module
@InstallIn(SingletonComponent::class)
object RemoteApiModule {

  val isDebug = BuildConfig.DEBUG

  @Provides
  @Singleton
  fun provideRestaurantRestApi(): RestaurantRestApi {
    // init remote api
    return RemoteRestApiFactory<RestaurantRestApi>().makeRemoteRestApi(
      isDebug, configuration, RestaurantRestApi::class.java, ParserModelFacade.buildGson()
    )
  }

  @Provides
  @Singleton
  fun provideAuthRestApi(): AuthRestApi {
    return RemoteRestApiFactory<AuthRestApi>().makeRemoteRestApi(
      isDebug, configuration, AuthRestApi::class.java, ParserFacade.buildGson()
    )
  }

  @Provides
  @Singleton
  fun provideRicepoCombineApi(): CombineRestApi {
    return RemoteRestApiFactory<CombineRestApi>().makeRemoteRestApi(
      isDebug, configuration, CombineRestApi::class.java, ParserModelFacade.buildGson()
    )
  }

  @Provides
  @Singleton
  fun provideRiceApi(): RiceApi {
    return RemoteRestApiFactory<RiceApi>().makeCoroutineApi(
      isDebug,
      configuration, RiceApi::class.java
    )
  }

  @Provides
  @Singleton
  fun provideGoogleMapsApi(): GoogleMapsRestApi {
    return RemoteRestApiFactory<GoogleMapsRestApi>().makeGoogleMapsRestApi(
      isDebug, configuration, GoogleMapsRestApi::class.java, ParserModelFacade.buildGson()
    )
  }

  @Provides
  @Singleton
  fun provideMapApi(): GoogleMapApi {
    return RemoteRestApiFactory<GoogleMapApi>().makeGoogleMapsApi(
      isDebug, configuration, GoogleMapApi::class.java
    )
  }

  private val configuration = object : NetworkConfiguration {
    override fun getToken(): String? {
      // PREF: cache
      return CustomerCache.getToken()
    }

    override fun uuid(): String {
      return BaseApplication.mDeviceId
    }

    override fun lang(): String {
      var lang = LocaleUtil.getLanguageMapping()
      if (lang.isEmpty()) {
        lang = LocaleConst.enUS
      }
      return lang
    }
  }
}
