package com.ricepo.app.model

import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.ReferShare

//
// Created by <PERSON><PERSON> on 5/8/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

data class ReferInfo(
  val reward: Int? = null,
  val description: InternationalizationContent? = null,
  val record: ReferRecord? = null,
  var share: ReferShare? = null,
  val title: InternationalizationContent? = null,
)

data class ReferRecord(
  val count: Int? = null,
  val amount: Int? = null,
  val history: List<ReferHistory>? = null
)

data class ReferHistory(
  val referee: String? = null,
  val amount: Int? = null,
)
