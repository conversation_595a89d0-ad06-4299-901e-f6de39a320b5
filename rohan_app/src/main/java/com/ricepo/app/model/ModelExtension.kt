package com.ricepo.app.model

import com.ricepo.base.model.ThemeImage
import com.ricepo.base.model.localize
import com.ricepo.style.ThemeUtil

//
// Created by <PERSON><PERSON> on 26/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * the network image match dark mode
 */
fun ThemeImage.localize(): String? {
  val isDark = ThemeUtil.isDarkMode()
  return if (isDark) this.dark else this.light
}

fun InternationalThemeImage.localize(): String? {
  val isDark = ThemeUtil.isDarkMode()
  return if (isDark) dark?.localize() else light?.localize()
}
