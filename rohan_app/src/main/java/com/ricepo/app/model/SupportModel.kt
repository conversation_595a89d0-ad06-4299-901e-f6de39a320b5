package com.ricepo.app.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.base.model.InternationalizationContent
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 24/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

data class SupportReq(
  // / Type of current issue
  val type: String,
  // / The text that user entered on the help page
  val subject: String,
  // / Customer phone number
  val phone: String?,
  // / Selected food on the help page
  var items: List<OrderItem>? = null,
  // / The page that selects food needs to be added with a optional selection method
  var solution: String? = null,
  // / Driver problems selected on the help page
  var driverIssues: List<String>? = null,
  // / Images uploaded on the help page
  var images: List<String>? = null,
  // / Status of the current user order
  var rule: Rules? = null,
  // / Whether the mark is fraud
  var event: Boolean? = null
)

data class Ticket(
  @SerializedName("_id")
  val id: String
)

/*
 * This Item contain Description, Call Restaurant, Call Driver, Chat, Text Input, Create Ticket Button
 */
@Parcelize
data class SupportItem(
  /* Issue type */
  val type: String = "",
  /* Navigation title */
  val title: String = "",
  var content: List<String>? = null,
  var submitButton: String? = null,
  var text: Boolean? = null,
  /* Whether create ticket require input text */
  var requireText: Boolean? = null,
  /* Change order page button */
  var linkToCancelButton: String? = null,
  var cancelButton: String? = null,
  var placeholder: String? = null,
  var callRest: Boolean? = null,
  var callDriver: Boolean? = null,
  var chatButton: String? = null,
  /* Choose item or problem item's title */
  var chooseItemTitle: String? = null,
  var chooseItem: Boolean? = null,
  var chooseItemContent: List<String>? = null,
  var chooseProcessingMethod: String? = null,
  var chooseProcessingContent: List<String>? = null,
  /* Title above textview */
  var inputTitle: String? = null,
  /* Large font title above textview */
  var largeInputTitle: String? = null,
  /* Title above add images */
  var uploadPicsTitle: String? = null,
  /* Subtitle above add images */
  var uploadPicsSubtitle: String? = null,
  var uploadPics: Boolean? = null,
  /* Whether the mark is fraud */
  var event: Boolean? = null
) : Parcelable

object SupportComponentType {
  val TEXT = "text"
  val TEXTAREA = "textarea"
  val BUTTON = "button"
  val CALL_RESTAURANT_BUTTON = "call-restaurant-button"
  val CALL_DRIVER_BUTTON = "call-driver-button"
  val SELECT_ITEMS = "select-items"
  val SELECT_SOLUTION = "select-solution"
  val SELECT_DRIVER_PROBLEM = "select-driver-problem"
  val UPLOAD_IMAGES = "upload-images"
}

@Parcelize
data class SupportItemComponent(
  /* Issue type */
  val type: String = "",
  /* Navigation title */
  val title: String = "",
  val content: InternationalizationContent? = null,
  val label: InternationalizationContent? = null,
  val ticket: Boolean? = null,
  val optional: Boolean? = null,
) : Parcelable

data class SupportContent(
  val content: String?,
  val button: String?,
  val text: Boolean?,
  val callRest: Boolean?,
  val callSupport: Boolean?,
  val chat: Boolean?
)

object Provider {
  const val TronDelivery = "tron"
  const val RestDelivery = "restaurant"
  const val ThirdDelivery = "3rd"
}

object OrderStatus {
  const val Pending = "pending"
  const val Created = "created"
  const val Confirmed = "confirmed"
  const val Cancelled = "cancelled"
  const val Declined = "declined"
}

object DeliveryStatus {
  const val Dropoff = "dropoff"
  const val Other = "other"
}

/*
 * Display Support page content according to provider, orderStatus, delivered, courier, deliveryStatus
 */
@Parcelize
data class Rules(
  // object Provider
  val provider: String? = null,
  // OrderStatus
  val orderStatus: String? = null,
  val delivered: Boolean? = null,
  val courier: Boolean? = null,
  // DeliveryStatus
  val deliveryStatus: String? = null
) : Parcelable {

    /*
     * Match order and support list rules
     * If support list rule's properties is null or order properties is equal to support list rule's properties return true
     */
  override fun equals(other: Any?): Boolean {
    // return false if other is not Rules
    if (other !is Rules) return false

    val self = this
    var providerMatch: Boolean = false
    var statusMatch: Boolean = false
    var deliveredMatch: Boolean = false
    var courierMatch: Boolean = false
    var delivaryStatusMatch: Boolean = false

    if (other.provider == null || other.provider == self.provider) {
      providerMatch = true
    }

    if (other.orderStatus == null || other.orderStatus == self.orderStatus) {
      statusMatch = true
    }

    if (other.delivered == null || other.delivered == self.delivered) {
      deliveredMatch = true
    }

    if (other.courier == null || other.courier == self.courier) {
      courierMatch = true
    }

    if (other.deliveryStatus == null || other.deliveryStatus == self.deliveryStatus) {
      delivaryStatusMatch = true
    }

    return (providerMatch && statusMatch && deliveredMatch && courierMatch && delivaryStatusMatch)
  }
}

@Parcelize
data class SupportRuleGroup(
  val name: InternationalizationContent? = null,
  val items: List<SupportRuleItem>? = null
) : Parcelable

@Parcelize
data class SupportRuleItem(
  val type: String? = null,
  val rule: Rules? = null,
  val title: InternationalizationContent? = null,
//    val event: Boolean? = null,
//    val requireText: Boolean? = null,
  val components: List<SupportItemComponent>? = null,
) : Parcelable

@Parcelize
data class SupportRule(
  /* Rule to display issue */
  val rule: Rules,
  /* Display issue page detail */
  val item: SupportItem
) : Parcelable

/**
 *  Order orderStatus
 *        +---------------+     +---------------+
 *        |    PENDING    +----->   DECLINED    |
 *        +-------+-------+     +---------------+
 *                |
 *                |
 *                V
 *        +---------------+
 *  +-----+    CREATED    +-------------+
 *  |     +-------+-------+             |
 *  |             |                     |
 *  |             |                     |
 *  |             V                     |
 *  |     +---------------+             |
 *  |     |     SENT      +-------------+
 *  |     +-------+-------+             |
 *  |             |                     |
 *  |             |                     |
 *  |             V                     V
 *  |     +---------------+     +---------------+
 *  +----->   CONFIRMED   <----->   CANCELLED   |
 *        +---------------+     +---------------+
 *
 */

/*
 * Issue type
 */
val changeAddressType = "change-address"
val changePhoneType = "change-phone"
val changeOrderType = "change-order"
val changeItemType = "change-item"
val cancelOrderType = "cancel-order"
val addFoodNotesType = "food-comment"
val addDeliveryNotesType = "delivery-comment"
val missingOrderType = "missing-item"
val wrongOrderType = "wrong-item"
val tastingBadType = "tasting-bad"
val packagingIssueType = "packaging-issue"
val arrivedLateType = "arrived-late"
val neverArrivedType = "never-arrived"
val driverProblemType = "driver-issue"
val refundType = "refund-issue"
val somethingElseType = "other-issue"
val feedbackType = "feedback"
