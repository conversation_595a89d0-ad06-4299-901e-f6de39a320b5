package com.ricepo.app.model.parser

import com.google.gson.Gson
import com.ricepo.app.model.OrderResponse
import com.ricepo.app.model.Quote
import com.ricepo.app.model.QuoteResponse
import com.ricepo.app.model.StripeObj
import com.ricepo.app.model.parser.deserializer.OrderResponseDeserializer
import com.ricepo.app.model.parser.deserializer.QuoteDeserializer
import com.ricepo.app.model.parser.deserializer.QuoteResponseDeserializer
import com.ricepo.app.model.parser.deserializer.StripeObjDeserializer
import com.ricepo.base.model.ExtraData
import com.ricepo.base.model.Food
import com.ricepo.base.model.OrderDelivery
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.parser.ExtraDataDeserializer
import com.ricepo.base.model.parser.ExtraDataSerializer
import com.ricepo.base.model.parser.FoodDeserializer
import com.ricepo.base.model.parser.FoodSerializer
import com.ricepo.base.model.parser.OrderDeliveryDeserializer
import com.ricepo.base.model.parser.RestaurantDeserializer
import com.ricepo.base.model.parser.RestaurantSerializer
import com.ricepo.base.parser.ParserFacade
import com.ricepo.monitor.MonitorFacade
import java.lang.reflect.Type

//
// Created by Thomsen on 8/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object ParserModelFacade : ParserFacade() {

  /**
   * create gson restaurant object with custom deserialize
   */
  fun buildGson(): Gson {
    val builder = builder()
    // restaurant for address and requiretip deserialize
    builder.registerTypeAdapter(
      Restaurant::class.java,
      RestaurantDeserializer()
    )
    builder.registerTypeAdapter(
      Restaurant::class.java,
      RestaurantSerializer()
    )
    builder.registerTypeAdapter(
      Food::class.java,
      FoodDeserializer()
    )
    builder.registerTypeAdapter(
      Food::class.java,
      FoodSerializer()
    )
    builder.registerTypeAdapter(
      QuoteResponse::class.java,
      QuoteResponseDeserializer()
    )
    builder.registerTypeAdapter(
      Quote::class.java,
      QuoteDeserializer()
    )
    builder.registerTypeAdapter(
      ExtraData::class.java,
      ExtraDataSerializer()
    )
    builder.registerTypeAdapter(
      ExtraData::class.java,
      ExtraDataDeserializer()
    )
    builder.registerTypeAdapter(
      OrderResponse::class.java,
      OrderResponseDeserializer()
    )
    builder.registerTypeAdapter(
      OrderDelivery::class.java,
      OrderDeliveryDeserializer()
    )
//        builder.registerTypeAdapter(
//            RestaurantDelivery::class.java,
//            RestaurantDeliveryDeserializer()
//        )
    builder.registerTypeAdapter(
      StripeObj::class.java,
      StripeObjDeserializer()
    )
    return builder.create()
  }

  fun <T> toJson(t: T): String {
    return buildGson().toJson(t)
  }

  fun <T> fromJson(content: String, clazz: Class<T>): T? {
    return try {
      buildGson().fromJson(content, clazz)
    } catch (e: Exception) {
      e.printStackTrace()
//      MonitorFacade.captureException(e, mapOf("json" to content))
      null
    }
  }

  fun <T> fromJson(content: String, type: Type): T? {
    return try {
      buildGson().fromJson(content, type)
    } catch (e: Exception) {
      e.printStackTrace()
//      MonitorFacade.captureException(e, mapOf("json" to content))
      null
    }
  }
}
