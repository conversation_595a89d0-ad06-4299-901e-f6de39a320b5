package com.ricepo.app.model

//
// Created by <PERSON><PERSON> on 21/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

data class ImageFeedbackModel(
  var text: String,
  var status: Boolean
)

data class ImageFeedbackReq(
  val foodId: String,
  val image: String,
  val reason: String,
  val detail: String?,
  val type: String = "image-feedback",
  val subject: String = "image-feedback"
)

data class FeedbackReq(
  val foodId: String? = null,
  val image: String? = null,
  val reason: String? = null,
  val detail: String? = null,
  val phone: String? = null,
  val type: String = "image-feedback",
  val subject: String = "image-feedback"
)
