package com.ricepo.app.model.parser.deserializer

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.ricepo.app.model.Quote
import com.ricepo.base.model.RequireTip
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 12/4/20.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class QuoteDeserializer : JsonDeserializer<Quote> {

  override fun deserialize(
    json: JsonElement?,
    typeOfT: Type?,
    context: JsonDeserializationContext?
  ): Quote {
    json as JsonObject
    val gson = ParserFacade.buildGson()

    val quote = gson.fromJson(json, Quote::class.java)

    var requireTipElement = json.get("requireTip")

    // judge require tip multiple type
    requireTipElement?.let {
      if (it.toString() == "false" || it.toString() == "true") {
        quote.requireTip = RequireTip.bool(it.asBoolean)
      } else {
        try {
          quote.requireTip = if (it.isJsonNull) null else RequireTip.double(it.asDouble)
        } catch (ee: ClassCastException) {
          ee.printStackTrace()
        }
      }
    }

    return quote
  }
}
