package com.ricepo.app.model

import com.google.gson.annotations.SerializedName

//
// Created by <PERSON><PERSON> on 20/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

data class SearchTag(
  val keyword: String?,
  val count: Int?,
  val highlight: Boolean?
)

data class RestaurantPrediction(
  @SerializedName("id")
  val _id: String? = null,
  val keyword: String? = null,
  val count: Int? = null
)
