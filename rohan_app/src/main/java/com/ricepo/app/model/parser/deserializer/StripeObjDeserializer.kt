package com.ricepo.app.model.parser.deserializer

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.ricepo.app.model.StripeObj
import com.ricepo.app.model.StripeSource
import com.ricepo.app.model.StripeSourceObj
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 28/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class StripeObjDeserializer : JsonDeserializer<StripeObj> {

  override fun deserialize(
    json: JsonElement?,
    typeOfT: Type?,
    context: JsonDeserializationContext?
  ): StripeObj {

    json as JsonObject

    // order contain restaurant
    val gson = ParserFacade.gson

    // default value error object
    var stripeObj: StripeObj = gson.fromJson(json, StripeObj::class.java)

    var sourceElement = json.get("source")

    if (sourceElement != null) {
      if (sourceElement.isJsonObject) {
        val source = gson.fromJson(sourceElement, StripeSourceObj::class.java)
        stripeObj.source = StripeSource.innerItem(source)
      } else {
        try {
          stripeObj.source = StripeSource.string(sourceElement.asString)
        } catch (e: Exception) {
          e.printStackTrace()
        }
      }
    }

    return stripeObj
  }

//    inner class OrderDeserializer: JsonDeserializer<Order> {
//        override fun deserialize(json: JsonElement?, typeOfT: Type?, context: JsonDeserializationContext?): Order {
//            json as JsonObject
//
//            val gson = ParserFacade.buildGson()
//            var order: Order = gson.fromJson(json, Order::class.java)
//
//            return order
//        }
//
//    }
}
