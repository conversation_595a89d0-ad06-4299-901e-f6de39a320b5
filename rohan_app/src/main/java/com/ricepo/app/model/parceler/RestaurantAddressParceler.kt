package com.ricepo.app.model.parceler

import android.os.Parcel
import kotlinx.android.parcel.Parceler

//
// Created by <PERSON><PERSON> on 6/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantAddressParceler : Parceler<Any> {

  override fun create(parcel: Parcel): Any {
    return parcel.readString() ?: ""
  }

  override fun Any.write(parcel: Parcel, flags: Int) {
    parcel.writeString("address")
  }
}
