package com.ricepo.app.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.base.model.InternationalizationContent
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 6/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

data class RewardSummaryModel(
  @SerializedName("_id")
  val id: String?,
  val restaurant: RestaurantName?,
  val customer: CustomerId?,
  val balance: Int?,
  val updatedAt: String?,
  val createdAt: String?
)

data class RestaurantName(
  @SerializedName("_id")
  val id: String?,
  val name: InternationalizationContent?
)

data class CustomerId(
  @SerializedName("_id")
  val id: String?,
  val phone: String?
)

data class PointsSummaryModel(
  @SerializedName("_id")
  val id: String? = null,
  val type: String? = null,
  val balance: BalanceHistory? = null,
  val origin: String? = null,
  val hide: String? = null,
  val description: InternationalizationContent? = null,
  val createdAt: String? = null
)

@Parcelize
data class BalanceHistory(
  val available: PointHistory? = null,
  val total: PointHistory? = null,
  val pending: PointHistory? = null,
  val purchase: PointHistory? = null,
) : Parcelable

@Parcelize
data class PointHistory(
  val before: Int? = null,
  val after: Int? = null,
  val diff: Int? = null
) : Parcelable
