package com.ricepo.app.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.MenuHour
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 27/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Parcelize
data class Coupon(
  @SerializedName("_id")
  var id: String,
  var amount: Int? = null,
  var condition: CouponCondition? = null,
  var createdAt: String? = null,
  var customer: CouponCustomer? = null,
  var enabled: Boolean? = null,
  var expiresAt: String? = null,
  var percentage: Double? = null,
  var reason: String? = null,
  var referee: String? = null,
  var remaining: Int? = null,
  var source: String? = null,
  var sponsor: String? = null,
  var type: String? = null,
  var code: String? = null,
  var description: String? = null,
  var invalid: Boolean? = null,
  val detail: InternationalizationContent? = null
) : Parcelable {

  // Init with default id
  fun constructor(id: String) {
    this.id = id
    this.code = id
    this.amount = 0
    this.createdAt = ""
    this.enabled = true
    this.remaining = 1
    this.sponsor = "ricepo"
    this.type = "coupon"
    this.condition = null
    this.customer = null
    this.expiresAt = null
    this.percentage = null
    this.reason = null
    this.referee = null
    this.source = null
    this.description = null
    this.invalid = null
  }
}

@Parcelize
data class CouponCondition(
  val customer: String?,
  val firstTime: Boolean?,
  val hours: List<MenuHour>?,
  val limitPerCustomer: Int?,
  val minimum: Int?,
  val region: String?,
  val restaurant: String?,
  val wechat: Boolean?,
  val restFirstTime: Int?,
  val orderCount: Int?
) : Parcelable

@Parcelize
data class CouponCustomer(
  @SerializedName("_id")
  val id: String
) : Parcelable

data class ValidateCouponReq(
  val restaurant: String,
  val subtotal: Int
)

data class ValidateCouponRes(val coupon: Coupon)

data class PaymentObj(
  val signed: String? = null,
  // / only exist when payment method is applepay
  val order: Order? = null,
  // / Only used for subscription
  // / when create subscription, this paymentId is quote.plan.id
  // / when update subscription payment method, this paymentId is customer.subscription.id
  val paymentId: String? = null,
  val status: String? = null,
  // only paypal pay
  val paypal_order_id: String? = null,
  val orderID: String? = null
)
