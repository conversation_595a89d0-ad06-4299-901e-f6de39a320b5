package com.ricepo.app.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.ThemeImage
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 27/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * user information
 */
data class UserInformation(
  @SerializedName("_id")
  val id: String,
  val phone: String,
  val token: String
)

/*
 * Payment method
 */
@Parcelize
data class PaymentOwnMethod(
  val method: String = "",
  val brand: String? = null,
  val last4: String? = null,
  val stripeId: String? = null,
  val name: InternationalizationContent? = null,
  val image: ThemeImage? = null,
) : Parcelable {

  companion object {
    const val GOOGLE_PAY = "googlePay"
    const val ALIPAY = "alipay"
    const val WECHAT = "wechat"
    const val WECHAT_PAY = "wechatPay"
    const val WECHAT_PAY_PREVIOUSLY = "wechat_pay"
    const val ADD_NEW_CARD = "add_new_card"

    // compatible with previously saved data to equals
    const val CREDIT_PREVIOUSLY = "credit"
    const val CREDIT = "card"

    // union pay card to use up sdk
    const val UNION_PAY = "unionPay"

    const val PAYPAL_PAY = "paypal"

    const val BBVA_PAY = "bbva"

    const val INTENT = "intent"
  }

  enum class Brand(val rawValue: String) {
    amex("amex"),
    discover("discover"),
    masterCard("mastercard"),
    unionPay("unionpay"),
    visa("visa");

    companion object {
      operator fun invoke(rawValue: String) = Brand.values().firstOrNull { it.rawValue.equals(rawValue, true) }
    }
  }
}
