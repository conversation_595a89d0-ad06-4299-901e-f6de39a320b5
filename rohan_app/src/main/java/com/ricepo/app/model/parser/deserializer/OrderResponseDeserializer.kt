package com.ricepo.app.model.parser.deserializer

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.ricepo.app.model.ErrorData
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderResponse
import com.ricepo.base.model.OrderDelivery
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.parser.OrderDeliveryDeserializer
import com.ricepo.base.model.parser.RestaurantDeserializer
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 28/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OrderResponseDeserializer : JsonDeserializer<OrderResponse> {

  override fun deserialize(
    json: JsonElement?,
    typeOfT: Type?,
    context: JsonDeserializationContext?
  ): OrderResponse {

    json as JsonObject

    // order contain restaurant
    val gson = ParserFacade.gsonBuilder
      .registerTypeAdapter(Restaurant::class.java, RestaurantDeserializer())
      .registerTypeAdapter(
        OrderDelivery::class.java,
        OrderDeliveryDeserializer()
      )
      .create()

    // default value error object
    var orderResponse: OrderResponse = OrderResponse.error(ErrorData())

    try {
      val data = gson.fromJson<Order>(json, object : TypeToken<Order>() {}.type)
      orderResponse = OrderResponse.data(data)
    } catch (e: Exception) {
      e.printStackTrace()
      // parse error data
      try {
        val error = gson.fromJson(json, ErrorData::class.java)
        orderResponse = OrderResponse.error(error)
      } catch (ee: Exception) {
        ee.printStackTrace()
      }
    }

    return orderResponse
  }

//    inner class OrderDeserializer: JsonDeserializer<Order> {
//        override fun deserialize(json: JsonElement?, typeOfT: Type?, context: JsonDeserializationContext?): Order {
//            json as JsonObject
//
//            val gson = ParserFacade.buildGson()
//            var order: Order = gson.fromJson(json, Order::class.java)
//
//            return order
//        }
//
//    }
}
