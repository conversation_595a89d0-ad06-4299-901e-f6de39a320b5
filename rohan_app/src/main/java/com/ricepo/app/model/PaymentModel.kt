package com.ricepo.app.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.ThemeImage
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 8/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/*
 * Wechat pay prePay
 */
@Parcelize
data class PrePay(
  val sign: String,
  val timestamp: String,
  val noncestr: String,
  @SerializedName("partnerid")
  val partnerId: String,
  @SerializedName("prepayid")
  val prepayId: String,
  @SerializedName("package")
  val packageStr: String,
  @SerializedName("appid")
  val appId: String
) : Parcelable

/*
 * Card v1 version
 */
// @Parcelize
// data class Card(
//    var brand: String? = null,
//    var fingerprint: String? = null,
//    var id: String? = null,
//    var last4: String? = null,
//    var tokenization_method: String? = null,
//    var defaultMethod: String? = null,  // migrate name
//    var methodString: String? = null): Parcelable {
//
//    constructor(methodType: String): this() {
//        this.defaultMethod = ResourcesUtil.getString(methodType)
//        this.methodString = methodType
//    }
//
// }

/**
 * Card v2
 */
@Parcelize
data class Card(
  val id: String? = null,
  var method: String? = null,
  val name: InternationalizationContent? = null,
  val description: InternationalizationContent? = null,
  val brand: String? = null,
  val last4: String? = null,
  val images: ThemeImage? = null,
) : Parcelable {

  constructor(method: String?) : this() {
    this.method = method
  }

  fun isCredit(): Boolean {
    return id != null && method == "card"
  }
}
