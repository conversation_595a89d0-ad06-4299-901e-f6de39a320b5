package com.ricepo.app.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.base.model.Adjustments
import com.ricepo.base.model.Customer
import com.ricepo.base.model.DeliveryZoneFees
import com.ricepo.base.model.Discount
import com.ricepo.base.model.DiscountCondition
import com.ricepo.base.model.DriverPoint
import com.ricepo.base.model.Estimate
import com.ricepo.base.model.Fee
import com.ricepo.base.model.Food
import com.ricepo.base.model.FoodImage
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.Item
import com.ricepo.base.model.LatLon
import com.ricepo.base.model.OrderDelivery
import com.ricepo.base.model.RecommendationRestaurant
import com.ricepo.base.model.RequireTip
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.Reward
import com.ricepo.base.model.SubscriptionPlan
import com.ricepo.base.parser.Exclude
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.network.resource.NetworkError
import kotlinx.android.parcel.RawValue
import kotlinx.parcelize.Parcelize
import kotlin.math.roundToInt

//
// Created by Thomsen on 17/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

// MARK: - Welcome
@Parcelize
data class Quote(
  @SerializedName("_id")
  val id: String = "",
  val restaurant: String? = null,
  val extraFees: List<ExtraFee>? = null,
  val fee: Fee? = null,
  val fees: List<DeliveryZoneFees>? = null,
  val driverBonus: Int? = null,
  val minimum: Int? = null,
  val estimate: Estimate? = null,
  val address: UserAddress? = null,
  val location: DriverPoint? = null,
  val createdAt: Long? = null,
  val forceTip: Boolean? = null,
  @SerializedName("sig$")
  val sig: String? = null,
  val customer: Customer? = null,
  val plan: SubscriptionPlan? = null,
  // let sig: Int
  // / Choose a delivery time available window
  var windows: List<QuoteWindows>? = null,
  // pickup window time
  var pickupWindows: List<QuoteWindows>? = null,
  var discount: List<DiscountCondition>? = null,
  var unionPayDiscount: DiscountCondition? = null,
  var note: InternationalizationContent? = null,
  val bundles: List<String>? = null,
  val tax: Double? = null,
  val serviceFee: Discount? = null,
  @Exclude
  var requireTip: RequireTip? = null,
  var expenses: Expenses? = null,
  var notes: QuoteNotes? = null,
  var pool: RestaurantPool? = null,
  var coupons: QuoteCoupon? = null,
  val summary: QuoteSummary? = null,
  val isUnionPay: Boolean? = null,
  val provider: String? = null,
  val payment: String? = null,
  val points: DiscountCondition? = null,
  val defaultPayment: Card? = null,
) : Parcelable

@Parcelize
data class QuoteTip(
  val options: ArrayList<TipModel>? = null,
  val amount: Int? = null,
  val percent: String? = null,
  val notes: QuoteNotes? = null,
) : Parcelable

@Parcelize
data class QuoteSummary(
  val foodSubtotal: Int? = null,
  val subtotal: Int? = null,
  val finalSubtotal: Int? = null,
  val total: Int? = null,
  val finalTotal: Int? = null,
  val finalFoodSubtotal: Int? = null
) : Parcelable

@Parcelize
data class QuoteCoupon(
  val options: ArrayList<Coupon>? = null,
  val selected: CouponSelected? = null,
  val note: InternationalizationContent? = null,
) : Parcelable

@Parcelize
data class CouponSelected(
  val coupon: Coupon? = null,
  val adjustments: Adjustments? = null
) : Parcelable

@Parcelize
data class Expenses(
  val delivery: QuoteDelivery? = null,
  val delta: Int? = null,
  val service: Int? = null,
  val tax: Int? = null,
  val amount: Int? = null,
  val notes: QuoteNotes? = null,
  val tip: QuoteTip? = null,
  val expressDelivery: ExpressDelivery? = null,
) : Parcelable

@Parcelize
data class ExpressDelivery(
  val options: List<DeliveryEstimate>? = null,
  val selected: DeliveryEstimate? = null
) : Parcelable

@Parcelize
data class DeliveryEstimate(
  val amount: Int? = null,
  val estimate: Estimate? = null,
) : Parcelable

@Parcelize
data class TipModel(
  val value: Discount? = null,
  val name: InternationalizationContent? = null
) : Parcelable

@Parcelize
data class QuoteDelivery(
  val amount: Int?,
  val originalRests: String? = null,
  // delivery original value
  val delivery: Int? = null,
  val adjustments: Adjustments? = null,
) : Parcelable

@Parcelize
data class QuoteNotes(
  val deliveryFee: InternationalizationContent? = null,
  val requireTip: InternationalizationContent? = null,
  val tip: InternationalizationContent? = null,
  val reachMinimum: InternationalizationContent? = null,
  val taxAndService: InternationalizationContent? = null,
  val warning: InternationalizationContent? = null,
  val guarantee: InternationalizationContent? = null,
) : Parcelable

// common with FormatUserAddress
@Parcelize
data class UserAddress(

    /*
     * Create Equatable
     * will only be trigger when one of below matched
     * 1. address place id changed
     * 2. note changed
     * 3. apt changed
     */
  var name: String? = null,
  var formatted: String? = null,
  var placeId: String? = null,
  var location: Location? = null,
  var unit: String? = null,
  var note: String? = null,
  var zipcode: String? = null,
  var street: String? = null,
  var number: String? = null,
  var city: String? = null,
  var state: String? = null,
  var country: String? = null
) : Parcelable

// MARK: - Location
@Parcelize
data class Location(
  val type: String,
  val coordinates: List<Double>
) : Parcelable {
  val dictionary: Map<String, Any>
    get() = mapOf("type" to type, "coordinates" to coordinates)
}

// MARK: - ExtraFee
@Parcelize
data class ExtraFee(
  val name: InternationalizationContent?,
  val price: Int?,
  val cost: Int?,
  val extraFee: Boolean? = null
) : Parcelable

// Fees
@Parcelize
data class Fees(
  var diff: Int = 0,
  var subtotal: Int = 0,
  var tax: Int = 0,
  var delivery: Int = 0,
  var credit: Int = 0,
  var tip: FeesTip = FeesTip(percent = 0.0, amount = 0, total = 0),
  var service: Int = 0
) : Parcelable {

  companion object {
    /**
     * get the service value
     */
    fun calculateService(total: Int, rate: Discount?): Int {
      if (rate?.factor == null || rate?.flat == null) return 0
      val factor = rate?.factor ?: 0.0
      val flat = rate?.flat ?: 0
      return (flat + factor.times(total.toDouble())).roundToInt()
    }
  }
}

/*
 * Tips of Fees
 * if amount > 0, total = amount
 * else total = percent * subtotal
 */
@Parcelize
data class FeesTip(
  var percent: Double,
  // percentage tip
  var amount: Int,
  // amount of tip
  var total: Int
) : Parcelable

// total price of the tip
@Parcelize
data class OrderFees(
  val credit: Int?,
  val delivery: Int?,
  val delta: Int?,
  val service: Int?,
  var tax: Int? = null,
  var tip: OrderTips? = null,
  var diff: Int? = null
) : Parcelable

@Parcelize
data class OrderTips(
  val amount: Int,
  val cash: Boolean
) : Parcelable

// Request body to get the quote
data class QuoteRequest(
  val location: LatLon? = null,
  val address: FormatUserAddress? = null,
  val items: List<OrderReqItem>? = null,
  // rice pool id
  val pool: String? = null,
  val couponCode: String? = null,
  val tipOption: Discount? = null,
  val payment: String? = null,
  val expressDelivery: DeliveryEstimate? = null,
  // ricepo points value
  val points: Int? = null,
)

// Order Object
public data class OrderReq(
  val tip: OrderReqTip,
  val stripe: OrderReqStripe,
  var quote: Quote? = null,
  val items: List<OrderReqItem>,
  val phone: String?,
  val coupon: String?,
  val comments: String?,
  val language: String?,
  val group: OrderReqGroup?,
  // / Select delivery time for supermarket
  val window: QuoteWindows?
)

// / Group Info in create order body
@Parcelize
data class OrderReqGroup(
  // / UUID of the device
  val deviceId: String,
  // / Order group Id
  val groupId: String
) : Parcelable

data class OrderReqTip(
  val cash: Int,
  val amount: Int
)

data class OrderReqStripe(val source: String?)

data class OrderReqItem(
  @SerializedName("_id")
  val id: String,
  val options: List<String>?
)

@Parcelize
data class Order(
  val stripe: StripeObj?,
  @SerializedName("_id")
  val id: String,
  val status: String,
  var state: String? = null,
  val createdAt: String,
  val delivery: OrderDelivery?,
  val restaurant: Restaurant?,
  val total: Int,
  val adjustments: Adjustments?,
  val adj: List<Adjustments>?,
  var fees: OrderFees? = null,
  var items: List<OrderItem>? = null,
  val message: String?,
  val comments: String?,
  val subtotal: Int,
  val customer: Customer,
  val rating: Rating?,
  // / Order passcode, added on 2020-01-21
  val passcode: String,
  val group: OrderReqGroup?,
  val reward: Reward?,
  val allowChat: Boolean? = null,
  var allowRating: Boolean? = null,
  val allowChangeTip: Boolean? = null,
  val rechargeOrder: Order? = null,
  val original: String? = null,
  val diary: OrderDiary? = null
) : Parcelable {

  /**
   * return status if state is null, otherwise return state
   */
  fun obtainState(): String {
    return this.state ?: return status
  }
}

@Parcelize
data class OrderDiary(
  val image: String? = null,
  val message: InternationalizationContent? = null,
  val title: InternationalizationContent? = null,
  val button: InternationalizationContent? = null,
) : Parcelable

@Parcelize
data class OrderItem(
  @SerializedName("_id")
  val id: String?,
  val color: String?,
  val cost: Int?,
  val description: InternationalizationContent?,
  val meal: Int?,
  val name: InternationalizationContent?,
  val options: List<Item>?,
  // container nil
  val price: Int?,
  val originalPrice: Int?,
  val point: Int?,
  val reward: Boolean?,
  var qty: Int? = null,
  var restaurant: RecommendationRestaurant?,
  // use `DiscountCondition` for avoiding create a new one
  // only `DiscountCondition.minimum` has value now
  val condition: DiscountCondition?,
  val image: FoodImage?
) : Parcelable {
  fun toRatingUi() = UiRatingItem(name, id, foodImage = image)
}

data class UiRatingItem(
  val foodName: InternationalizationContent?,
  val id: String?,
  val foodImage: FoodImage?,
  val rating: Int = 0,
)

@Parcelize
data class StripeObj(
  @Exclude
  var source: StripeSource?,
  val intent: StripeSecret?,
  val alipay: StripeAlipay?,
  val wechat: StripeWechat?
) : Parcelable

sealed class StripeSource : Parcelable {
  @Parcelize
  data class string(val v1: String) : StripeSource()
  @Parcelize
  data class innerItem(val v1: StripeSourceObj) : StripeSource()

  val stringValue: String?
    get() {
      return if (this is string) {
        this.v1
      } else {
        null
      }
    }

  val innerItemValue: StripeSourceObj?
    get() {
      return if (this is innerItem) {
        this.v1
      } else {
        null
      }
    }
}

@Parcelize
data class StripeSourceObj(
  @SerializedName("address_city")
  val addressCity: String?,
  @SerializedName("address_country")
  val addressCountry: String?,
  @SerializedName("address_line1")
  val addressLine1: String?,
  @SerializedName("address_line1_check")
  val addressLine1Check: String?,
  val brand: String?,
  val country: String?,
  val customer: String?,
  @SerializedName("cvc_check")
  val cvcCheck: String?,
  @SerializedName("exp_month")
  val expMonth: Int?,
  @SerializedName("exp_year")
  val expYear: Int?,
  @SerializedName("dynamic_last4")
  val dynamicLast4: String?,
  val fingerprint: String?,
  val funding: String?,
  @SerializedName("_id")
  val id: String?,
  val last4: String?,
  val name: String?
) : StripeSource(), Parcelable

@Parcelize
data class StripeAlipay(val signed: String?) : Parcelable

@Parcelize
data class StripeWechat(
//    val signed: OrderInfo?
  val signed: @RawValue Any?
) : Parcelable

@Parcelize
data class StripeSecret(
  @SerializedName("client_secret")
  val clientSecret: String,
  val charges: IntentCharges?
) : Parcelable

@Parcelize
data class IntentCharges(val data: List<IntentChargesItem>?) : Parcelable

@Parcelize
data class IntentChargesItem(
  val id: String,
  @SerializedName("payment_method_details")
  val paymentMethodDetails: IntentChargesItemDetail?
) : Parcelable

@Parcelize
data class IntentChargesItemDetail(val card: IntentChargePaymentDetailCard?) : Parcelable

@Parcelize
data class IntentChargePaymentDetailCard(
  val brand: String?,
  val country: String?,
  val fingerprint: String?,
  val last4: String?
) : Parcelable

// sealed class QuoteResponse {
//    data class data(val v1: Quote) : QuoteResponse()
//    data class error(val v1: ErrorData) : QuoteResponse()
// }

sealed class QuoteResponse : Parcelable

@Parcelize
class QuoteResponseData(val v1: Quote) : QuoteResponse()

@Parcelize
class QuoteResponseError(val v1: ErrorData) : QuoteResponse()

sealed class OrderResponse {
  data class data(val v1: Order) : OrderResponse()
  data class error(val v1: ErrorData) : OrderResponse()
}

@Parcelize
data class ErrorData(
  var code: String? = null,
  var message: String? = null,
  var details: ErrorMessage? = null,
  var detailsObj: ErrorDetails? = null
) : Parcelable {

  companion object {
    // init with emiiter
    const val CODE_INIT = "init"

    // request error
    const val CODE_REQUEST = "request"
  }

  constructor(error: Throwable?, cc: String? = null) : this() {
    if (error == null) return
    if (error is NetworkError) {
      code = error.code
      message = error.message
      details = ErrorMessage(message = error.error)

      if (error.details != null) {
        val json = ParserModelFacade.toJson(error.details)
        try {
          details = ParserModelFacade.fromJson(json, ErrorMessage::class.java)
        } catch (e: Exception) {
          e.printStackTrace()
        }
        if (details == null) {
          try {
            detailsObj = ParserModelFacade.fromJson(json, ErrorDetails::class.java)
          } catch (e: Exception) {
            e.printStackTrace()
          }
        }
      }
    } else {
      code = cc
      message = error.message
    }
  }

  fun message(): String? {
    return details?.message ?: message
  }
}

@Parcelize
data class ErrorMessage(
  val message: String? = null,
  val entity: String? = null,
  val option: String? = null,
  val food: String? = null,
  val skipSentry: Boolean? = null,
) : Parcelable

@Parcelize
data class ErrorDetails(
  val minimum: Int? = null,
  val option: Item? = null,
  val food: Food? = null,
  val entity: String? = null,
  val id: String? = null,
) : Parcelable

@Parcelize
data class Rating(
  val stars: Int?,
  val reasons: List<String>?,
  val content: String?
) : Parcelable

/**
 * Choose a delivery time
 */
@Parcelize
data class QuoteWindows(
  @SerializedName("_id")
  val id: String? = null,
  val end: String? = null,
  val start: String? = null,
  val capacity: Int? = null,
  val restaurant: WindowsRestaurant? = null,
  val updatedAt: String? = null,
  val createdAt: String? = null,
  val used: Int? = null,
  val formatted: InternationalizationContent? = null,
  val ondemand: Boolean? = null,
  val open: Boolean? = null,
) : Parcelable

// / Food restaurant info
@Parcelize
data class WindowsRestaurant(
  @SerializedName("_id")
  val id: String,
  val name: InternationalizationContent
) : Parcelable
