package com.ricepo.app.model

import android.os.Parcelable
import com.ricepo.base.model.InternationalizationContent
import kotlinx.parcelize.Parcelize

@Parcelize
data class PopUp(
  val _id: String,
  val `data`: PopupData,
  val name: String,
  val type: String
) : Parcelable {
  companion object {
    fun mock(): PopUp {
      return PopUp(
        _id = "111",
        data = PopupData(
          effectiveAt = "",
          expiresAt = "",
          image = InternationalThemeImage(
            dark = InternationalizationContent(
              zhCN = "https://img.sj33.cn/uploads/allimg/201609/7-1609210Q252.jpg",
              zhHK = "https://img.sj33.cn/uploads/allimg/201609/7-1609210Q252.jpg",
              enUS = "https://img.sj33.cn/uploads/allimg/201609/7-1609210Q252.jpg",
              es = "https://img.sj33.cn/uploads/allimg/201609/7-1609210Q252.jpg"
            ),
            light = InternationalizationContent(
              zhCN = "https://img.sj33.cn/uploads/allimg/201609/7-1609210Q252.jpg",
              zhHK = "https://img.sj33.cn/uploads/allimg/201609/7-1609210Q252.jpg",
              enUS = "https://img.sj33.cn/uploads/allimg/201609/7-1609210Q252.jpg",
              es = "https://img.sj33.cn/uploads/allimg/201609/7-1609210Q252.jpg"
            )
          ),
          index = 1,
          regions = listOf()
        ),
        name = "name",
        type = "popUp"
      )
    }
  }
}

@Parcelize
data class PopupData(
  val effectiveAt: String,
  val expiresAt: String,
  val image: InternationalThemeImage,
  val index: Int,
  val regions: List<String>
) : Parcelable

@Parcelize
data class InternationalThemeImage(
  val dark: InternationalizationContent?,
  val light: InternationalizationContent?,
) : Parcelable
