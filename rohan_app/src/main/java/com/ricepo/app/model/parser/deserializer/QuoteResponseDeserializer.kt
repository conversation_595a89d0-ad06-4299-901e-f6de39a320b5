package com.ricepo.app.model.parser.deserializer

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.ricepo.app.model.ErrorData
import com.ricepo.app.model.Quote
import com.ricepo.app.model.QuoteResponse
import com.ricepo.app.model.QuoteResponseData
import com.ricepo.app.model.QuoteResponseError
import com.ricepo.base.parser.ParserFacade
import java.lang.reflect.Type

//
// Created by <PERSON><PERSON> on 28/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class QuoteResponseDeserializer : JsonDeserializer<QuoteResponse> {

  override fun deserialize(
    json: JsonElement?,
    typeOfT: Type?,
    context: JsonDeserializationContext?
  ): QuoteResponse {

    json as JsonObject

    val gson = ParserFacade.buildGson()

    // default value error object
    var quoteResponse: QuoteResponse = QuoteResponseError(ErrorData())

    try {
      val data = gson.fromJson(json, Quote::class.java)
      quoteResponse = QuoteResponseData(data)
    } catch (e: Exception) {
      e.printStackTrace()
      // parse error data
      try {
        val error = gson.fromJson(json, ErrorData::class.java)
        quoteResponse = QuoteResponseError(error)
      } catch (ee: Exception) {
        ee.printStackTrace()
      }
    }

    return quoteResponse
  }
}
