package com.ricepo.app.startup

import android.content.Context
import androidx.startup.AppInitializer
import androidx.startup.Initializer
import com.ricepo.app.BuildConfig
import com.ricepo.tripartite.TripartiteConst
import com.ricepo.tripartite.stripe.StripeClient
import com.stripe.android.PaymentConfiguration

//
// Created by <PERSON><PERSON> on 3/12/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class StripeInitializer : Initializer<Boolean> {

  companion object {
    fun init(context: Context) {
      AppInitializer.getInstance(context)
        .initializeComponent(StripeInitializer::class.java)
    }
  }

  override fun create(context: Context): Boolean {
    setupStripe(context)
    return true
  }

  override fun dependencies(): MutableList<Class<out Initializer<*>>> {
    return mutableListOf()
  }

  private fun setupStripe(context: Context) {
    PaymentConfiguration.init(
      context,
      TripartiteConst.publishableKey
    )
    StripeClient.init(context, BuildConfig.DEBUG)
  }
}
