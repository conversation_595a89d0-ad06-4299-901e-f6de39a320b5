package com.ricepo.app.startup

import android.content.Context
import androidx.startup.AppInitializer
import androidx.startup.Initializer
import leakcanary.AppWatcher

//
// Created by <PERSON><PERSON> on 3/12/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class LeakInitializer : Initializer<Boolean> {

  companion object {
    fun init(context: Context) {
      AppInitializer.getInstance(context)
        .initializeComponent(LeakInitializer::class.java)
    }
  }

  override fun create(context: Context): Boolean {
    setupLeak()
    return true
  }

  override fun dependencies(): MutableList<Class<out Initializer<*>>> {
    return mutableListOf()
  }

  private fun setupLeak() {
    // LeakCanary config and use ContentProvider to init
    AppWatcher.config = AppWatcher.config.copy(watchFragmentViews = true)

    // InputManager leak fixed
//        IMMLeaks.fixFocusedViewLeak(this)
  }
}
