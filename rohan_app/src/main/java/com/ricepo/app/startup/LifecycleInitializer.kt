package com.ricepo.app.startup

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import androidx.startup.AppInitializer
import androidx.startup.Initializer
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.analytics.ScrollDepthFacade
import com.ricepo.monitor.firebase.FirebaseBaseEvent
import com.ricepo.monitor.firebase.FirebaseEventName

//
// Created by <PERSON><PERSON> on 3/12/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class LifecycleInitializer : Initializer<Boolean> {

  companion object {
    fun init(context: Context) {
      AppInitializer.getInstance(context)
        .initializeComponent(LifecycleInitializer::class.java)
    }
  }

  override fun create(context: Context): Boolean {
    if (context.applicationContext is Application) {
      val app = context.applicationContext as Application
      setupLifecycle(app)
    }
    return true
  }

  override fun dependencies(): MutableList<Class<out Initializer<*>>> {
    return mutableListOf()
  }

  private fun setupLifecycle(application: Application) {
    application.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {

      private var pageCount = 0
      private var isForeground = true

      override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        // init scroll depth
        ScrollDepthFacade.scrollDepth = 0.0
      }

      override fun onActivityStarted(activity: Activity) {
        pageCount++
        if (!isForeground) {
          isForeground = true
          // app into foreground
        }
      }

      override fun onActivityResumed(activity: Activity) {
      }

      override fun onActivityPaused(activity: Activity) {
      }

      override fun onActivityStopped(activity: Activity) {
        // features.menu.MenuActivity
        val source = activity.localClassName
//                Log.d("thom", "activity name = $source pageCount = $pageCount")
        pageCount--
        AnalyticsFacade.logEvent(
          FirebaseBaseEvent(
            source,
            ScrollDepthFacade.scrollDepth
          ),
          FirebaseEventName.rLeavePage
        )
        if (pageCount <= 0) {
          isForeground = false
//                    Log.d("thom", "app into background")
          AnalyticsFacade.logEvent(
            FirebaseBaseEvent(
              source,
              ScrollDepthFacade.scrollDepth
            ),
            FirebaseEventName.rExitApp
          )
        }
      }

      override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
      }

      override fun onActivityDestroyed(activity: Activity) {
      }
    })
  }
}
