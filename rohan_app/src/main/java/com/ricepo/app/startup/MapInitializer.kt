package com.ricepo.app.startup

import android.content.Context
import androidx.startup.AppInitializer
import androidx.startup.Initializer
import com.ricepo.map.MapConsts
import com.ricepo.map.PlacesFacade

//
// Created by <PERSON><PERSON> on 3/12/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class MapInitializer : Initializer<Boolean> {

  companion object {
    fun init(context: Context) {
      AppInitializer.getInstance(context)
        .initializeComponent(MapInitializer::class.java)
    }
  }

  override fun create(context: Context): Boolean {
    setupMap(context)
    return true
  }

  override fun dependencies(): MutableList<Class<out Initializer<*>>> {
    return mutableListOf()
  }

  private fun setupMap(context: Context) {
    // android api key
    PlacesFacade.init(context, MapConsts.API_KEY)
  }
}
