package com.ricepo.app.startup

import android.app.Application
import android.content.Context
import androidx.startup.AppInitializer
import androidx.startup.Initializer
import com.alibaba.android.arouter.launcher.ARouter

//
// Created by <PERSON><PERSON> on 3/12/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class RouterInitializer : Initializer<Boolean> {

  companion object {
    fun init(context: Context) {
      AppInitializer.getInstance(context)
        .initializeComponent(RouterInitializer::class.java)
    }
  }

  override fun create(context: Context): Bo<PERSON>an {
    if (context.applicationContext is Application) {
      val app = context.applicationContext as Application
      setupRouter(app)
    }
    return true
  }

  override fun dependencies(): MutableList<Class<out Initializer<*>>> {
    return mutableListOf()
  }

  private fun setupRouter(application: Application) {
    ARouter.openLog()

    ARouter.init(application)
  }
}
