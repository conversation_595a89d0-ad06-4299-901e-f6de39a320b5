package com.ricepo.app.startup

import android.content.Context
import androidx.startup.AppInitializer
import androidx.startup.Initializer
import com.ricepo.app.BuildConfig
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.monitor.MonitorFacade
import com.ricepo.monitor.MonitorHandler
import com.ricepo.monitor.MonitorHooker
import java.lang.Exception

//
// Created by <PERSON><PERSON> on 3/12/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class MonitorInitializer : Initializer<Boolean> {

  companion object {
    fun init(context: Context) {
      AppInitializer.getInstance(context)
        .initializeComponent(MonitorInitializer::class.java)
    }
  }

  override fun create(context: Context): Bo<PERSON><PERSON> {
    setupMonitor(context)
    return true
  }

  override fun dependencies(): MutableList<Class<out Initializer<*>>> {
    return mutableListOf()
  }

  private fun setupMonitor(context: Context) {
//    MonitorFacade.initSentry(context, BuildConfig.DEBUG)
    MonitorHooker.initRxHooker(
      BuildConfig.DEBUG,
      object : Monitor<PERSON>andler {
        override fun error(t: Throwable): Exception {
          // common exception
          return t.parseByBuzNetwork()
        }
      }
    )
  }
}
