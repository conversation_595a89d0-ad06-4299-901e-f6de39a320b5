package com.ricepo.app.startup

import android.content.Context
import androidx.startup.AppInitializer
import androidx.startup.Initializer

//
// Created by <PERSON><PERSON> on 3/12/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class OneSingalInitializer : Initializer<Boolean> {

  companion object {
    fun init(context: Context) {
      AppInitializer.getInstance(context)
        .initializeComponent(OneSingalInitializer::class.java)
    }
  }

  override fun create(context: Context): Boolean {
//    OneSignalFacade.init(context)
    return true
  }

  override fun dependencies(): MutableList<Class<out Initializer<*>>> {
    return mutableListOf()
  }
}
