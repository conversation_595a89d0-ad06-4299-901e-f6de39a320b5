package com.ricepo.app.pattern.payment

import android.content.Context
import androidx.activity.ComponentActivity
import com.google.gson.reflect.TypeToken
import com.ricepo.app.R
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PrePay
import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.base.animation.Loading
import com.ricepo.base.tools.SystemUtils
import com.ricepo.style.ResourcesUtil
import com.ricepo.tripartite.TripartiteConst
import com.ricepo.tripartite.wechat.WeChatPay
import com.tencent.mm.opensdk.modelpay.PayReq

//
// Created by <PERSON><PERSON> on 2/7/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class PaymentWechat(val source: String? = null) : PaymentStrategy() {

  override fun handlePayment(
    context: Context,
    paymentObj: PaymentObj,
    completed: PaymentCompleted
  ) {
    val signed = paymentObj.signed
    if (signed == null) {
      completed(signed, source, null)
      return
    }

    val dic = ParserModelFacade.fromJson<Map<String, Any>>(
      signed,
      object : TypeToken<Map<String, Any>>() {}.type
    )
    val orderInfo = dic?.get("orderInfo")
    if (orderInfo != null) {
      val prePay = ParserModelFacade.fromJson(orderInfo.toString(), PrePay::class.java)
      val payReq = PayReq().apply {
        appId = prePay?.appId
        partnerId = prePay?.partnerId
        packageValue = prePay?.packageStr
        prepayId = prePay?.prepayId
        nonceStr = prePay?.noncestr
        timeStamp = prePay?.timestamp
        sign = prePay?.sign
      }

      val weChatPay = WeChatPay(context, prePay?.appId)
      if (weChatPay.isWeiXinAppInstall()) {
        if (context is ComponentActivity) {
          // Failed to take screenshot. No visible windows for .plugin.wallet_index.ui.OrderHandlerUI
//                    CheckoutLoading.hideLoading()
          Loading.hideLoading()
        }
        // direct handle success ignore wechat result
        val registerError = weChatPay.isRegister(TripartiteConst.WECHAT_APP_SIGNATURE)
        completed(registerError, source, null)

        if (registerError == null || registerError == WeChatPay.PENDING.toString()) {
          weChatPay.launchPay(payReq)

          if (!SystemUtils.isAppOnForeground()) {
            completed(ResourcesUtil.getString(com.ricepo.style.R.string.error_load_failed), source, null)
          }
        }
      } else {
        completed(ResourcesUtil.getString(com.ricepo.style.R.string.error_wechat_notInstalled), source, null)
      }
    }
  }
}
