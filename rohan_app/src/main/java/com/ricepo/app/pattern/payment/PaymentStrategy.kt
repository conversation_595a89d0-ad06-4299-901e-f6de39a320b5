package com.ricepo.app.pattern.payment

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.google.android.gms.wallet.AutoResolveHelper
import com.google.android.gms.wallet.PaymentData
import com.ricepo.app.features.menu.base.BroadcastConst
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.tripartite.stripe.StripeClient
import com.stripe.android.model.PaymentMethodCreateParams
import com.stripe.android.model.StripeIntent
import org.json.JSONObject

//
// Created by <PERSON><PERSON> on 2/7/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

typealias PaymentCompleted = (errMsg: String?, source: String?, signed: String?) -> Unit

abstract class PaymentStrategy {

  /**
   * handle payment and completed callback
   */
  abstract fun handlePayment(
    context: Context,
    paymentObj: PaymentObj,
    completed: PaymentCompleted
  )
}

object PaymentRefer {

  /**
   * get payment strategy by factory method
   */
  fun paymentHandler(source: String?): PaymentStrategy? {
    return when (source) {
      PaymentOwnMethod.CREDIT,
      PaymentOwnMethod.CREDIT_PREVIOUSLY -> {
        PaymentCard(PaymentOwnMethod.CREDIT)
      }
      PaymentOwnMethod.WECHAT_PAY,
      PaymentOwnMethod.WECHAT_PAY_PREVIOUSLY -> {
        PaymentWechat(PaymentOwnMethod.WECHAT_PAY)
      }
      PaymentOwnMethod.ALIPAY -> {
        PaymentAlipay(source)
      }
      PaymentOwnMethod.GOOGLE_PAY -> {
        PaymentGoogle(source)
      }
      PaymentOwnMethod.PAYPAL_PAY -> {
        PaymentPaypal(source)
      }
      PaymentOwnMethod.BBVA_PAY -> {
        PaymentBBVA(source)
      }
      else -> null
    }
  }

  /**
   * create payment setup intent result
   */
  fun onPaymentSetupIntentResult(
    activity: Activity,
    requestCode: Int,
    intent: Intent?,
    capture: () -> Unit,
    completed: () -> Unit,
    failed: () -> Unit
  ): Boolean {
    return StripeClient.instance.onSetupResult(requestCode, intent) { result ->
      result.fold(
        onSuccess = { data ->
          when (data.intent.status) {
            StripeIntent.Status.RequiresPaymentMethod,
            StripeIntent.Status.Canceled -> {
              failed()
            }
            StripeIntent.Status.RequiresCapture -> {
              capture()
            }
            StripeIntent.Status.RequiresAction,
            StripeIntent.Status.RequiresConfirmation -> {
              StripeClient.instance.confirmPayment(activity, data.intent)
            }
            else -> completed()
          }
        },
        onFailure = { _ ->
          failed()
        }
      )
    }
  }

  /**
   * payment card result callback
   */
  fun onPaymentCardResult(
    activity: Activity,
    requestCode: Int,
    intent: Intent?,
    capture: () -> Unit,
    completed: () -> Unit,
    failed: () -> Unit
  ): Boolean {
    return StripeClient.instance.onPaymentResult(requestCode, intent) { result ->
      result.fold(
        onSuccess = { data ->
          when (data.intent.status) {
            StripeIntent.Status.RequiresPaymentMethod,
            StripeIntent.Status.Canceled -> {
              failed()
            }
            StripeIntent.Status.RequiresCapture -> {
              capture()
            }
            StripeIntent.Status.RequiresAction,
            StripeIntent.Status.RequiresConfirmation -> {
              StripeClient.instance.confirmPayment(activity, data.intent)
            }
            else -> completed()
          }
        },
        onFailure = { _ ->
          failed()
        }
      )
    }
  }

  fun onPaymentWechatResult(completed: () -> Unit, failed: () -> Unit): BroadcastReceiver {
    return object : BroadcastReceiver() {
      override fun onReceive(context: Context?, intent: Intent?) {
        val isCompleted = intent?.getBooleanExtra(
          BroadcastConst.PARAM_PAYMENT_COMPLETED, false
        ) ?: false
        if (isCompleted) {
          completed()
        } else {
          failed()
        }
      }
    }
  }

  fun onPaymentGoogleResult(
    resultCode: Int,
    data: Intent?,
    success: (pm: PaymentOwnMethod?) -> Unit,
    failed: () -> Unit
  ) {
    when (resultCode) {
      Activity.RESULT_OK -> {
        if (data != null) {
          handleGooglePayResult(data, success, failed)
        }
      }
      Activity.RESULT_CANCELED -> {
        failed()
      }
      AutoResolveHelper.RESULT_ERROR -> {
        val status = AutoResolveHelper.getStatusFromIntent(data)
        val statusMessage = status?.statusMessage ?: "unknown"
        Log.i("thom", "Got error: $statusMessage")
        failed()
      }
      // Log the status for debugging
      // Generally there is no need to show an error to
      // the user as the Google Payment API will do that
      else -> {
      }
    }
  }

  private fun handleGooglePayResult(
    data: Intent,
    success: (pm: PaymentOwnMethod?) -> Unit,
    failed: () -> Unit
  ) {
    val paymentData = PaymentData.getFromIntent(data) ?: return
    val paymentDataJson = JSONObject(paymentData.toJson())

    Log.i("thom", paymentDataJson.toString(2))

    val paymentMethodCreateParams =
      PaymentMethodCreateParams.createFromGooglePay(paymentDataJson)

    StripeClient.instance.createPaymentMethod(
      paymentMethodCreateParams,

      callback = { result ->
        result.fold(
          onSuccess = {
            Log.d("thom", "Created PaymentMethod ${it.id}")
            val payment = PaymentOwnMethod(
              PaymentOwnMethod.CREDIT,
              it.card?.brand?.name,
              it.card?.last4,
              it.id
            )
            success(payment)
          },
          onFailure = {
            Log.e("thom", "Exception while creating PaymentMethod", it)
            failed()
          }
        )
      }
    )
  }
}
