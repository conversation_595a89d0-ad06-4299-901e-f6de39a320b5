package com.ricepo.app.pattern.payment

import android.app.Application
import android.content.Context
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.lifecycleScope
import com.paypal.checkout.PayPalCheckout
import com.paypal.checkout.approve.OnApprove
import com.paypal.checkout.cancel.OnCancel
import com.paypal.checkout.config.CheckoutConfig
import com.paypal.checkout.config.Environment
import com.paypal.checkout.config.SettingsConfig
import com.paypal.checkout.createorder.CreateOrder
import com.paypal.checkout.createorder.UserAction
import com.paypal.checkout.error.OnError
import com.ricepo.app.BuildConfig
import com.ricepo.app.di.entrypoint.RiceApiPoint
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaypalCaptureReq
import com.ricepo.app.restapi.RiceApi
import com.ricepo.app.utils.log
import com.ricepo.base.BaseApplication
import com.skydoves.sandwich.message
import com.skydoves.sandwich.onError
import com.skydoves.sandwich.onException
import com.skydoves.sandwich.onSuccess
import dagger.hilt.android.EntryPointAccessors
import io.reactivex.rxjava3.disposables.Disposable
import kotlinx.coroutines.launch

//
// Created by Thomsen on 2/7/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class PaymentPaypal(val source: String? = null) : PaymentStrategy() {

  private val riceApi: RiceApi by lazy {
    EntryPointAccessors.fromApplication(
      BaseApplication.context,
      RiceApiPoint::class.java
    ).injectRiceApi()
  }

  override fun handlePayment(
    context: Context,
    paymentObj: PaymentObj,
    completed: PaymentCompleted
  ) {
    val id = paymentObj.paypal_order_id.apply {
      this?.log("paypal order id: ")
    }
    if (id == null || paymentObj.orderID == null) {
      completed(null, source, null)
      return
    }

    val job = DisposableWrapper()

    (context as? LifecycleOwner)?.run {
      val innerObserver = object : LifecycleObserver {
        @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
        fun onDestroy() {
          lifecycle.removeObserver(this)
          job.dispose()
        }
      }
      lifecycle.addObserver(innerObserver)
    }

    PayPalCheckout.start(
      createOrder = CreateOrder { createOrderActions ->
        createOrderActions.set(id)
      },
      onApprove = OnApprove {
        it.data.log()
        (context as? LifecycleOwner)?.lifecycleScope?.launch {
          riceApi.paypalCapture(
            paypal_order_id = id,
            paypalCaptureReq = PaypalCaptureReq(paymentObj.orderID)
          ).onSuccess {
            completed(null, source, null)
            "success".log()
          }.onError {
            completed("payment failed", source, null)
            this.message().log("error")
          }.onException {
            completed("network error", source, null)
            this.message?.log("exception")
          }
        } ?: job.disposable
      },
      onCancel = OnCancel {
        completed("payment canceled", source, null)
        job.dispose()
      },
      onError = OnError {
        completed(it.reason, source, null)
        job.dispose()
      }
    )
  }
}

private data class DisposableWrapper(
  var disposable: Disposable? = null
) {
  fun dispose() = disposable?.dispose()
}

object PaypalInitializer {
  fun init(
    application: Application
  ) {
    PayPalCheckout.setConfig(
      checkoutConfig = CheckoutConfig(
        application = application,
        clientId = if (BuildConfig.DEBUG) {
          "AciWEIg12HuG_3sg6jKOsq56fI1Rt5Hbv7sr9H5_V-62Z-Iht7VXJWcKo-OogSthqxZt4H16Z9iqWReZ"
        } else {
          "AQevggDIdaODddtyx8nIU6Ou2g6gq4VsJ7qxFM8uUneMMV9ICLWZ4QU5-Jquo7GkAZgJW2NHexUN-B68"
        },
        environment = if (BuildConfig.DEBUG) {
          Environment.SANDBOX
        } else {
          Environment.LIVE
        },
        returnUrl = "${BuildConfig.APPLICATION_ID}://paypalpay",
        userAction = UserAction.PAY_NOW,
        settingsConfig = SettingsConfig(
          loggingEnabled = true,
          showWebCheckout = false
        )
      )
    )
  }
}
