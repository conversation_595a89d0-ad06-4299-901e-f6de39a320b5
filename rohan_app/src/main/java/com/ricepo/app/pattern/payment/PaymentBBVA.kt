package com.ricepo.app.pattern.payment

import android.content.Context
import com.ricepo.app.model.PaymentObj
import java.net.URLEncoder


//
// Created by <PERSON><PERSON> on 21/08/2023.
//
class PaymentBBVA(val source: String? = null) : PaymentStrategy() {

    override fun handlePayment(
        context: Context,
        paymentObj: PaymentObj,
        completed: PaymentCompleted
    ) {
        if (!paymentObj.signed.isNullOrEmpty()) {
            val signed = paymentObj.signed
//            val html = URLEncoder.encode(signed, "UTF-8")
            completed(null, source, signed)
        } else {
            completed("payment failed", source, null)
        }
    }
}