package com.ricepo.app.pattern.payment

import android.content.Context
import com.ricepo.app.R
import com.ricepo.app.model.PaymentObj
import com.ricepo.base.tools.SystemUtils
import com.ricepo.style.ResourcesUtil
import com.ricepo.tripartite.stripe.StripeClient
import com.stripe.android.model.StripeIntent

//
// Created by <PERSON><PERSON> on 2/7/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class PaymentCard(val source: String? = null) : PaymentStrategy() {

  override fun handlePayment(
    context: Context,
    paymentObj: PaymentObj,
    completed: PaymentCompleted
  ) {
    val signed = paymentObj.signed
    if (signed == null) {
      completed(null, source, null)
      return
    }

    try {
      if (signed.startsWith("seti")) {
        when (paymentObj.status) {
          StripeIntent.Status.RequiresAction.code -> {
            StripeClient.instance.handleConfirmSetupIntent(context, signed)
          }
          StripeIntent.Status.Succeeded.code -> {
            completed(null, source, null)
          }
          else -> {
            completed(ResourcesUtil.getString(com.ricepo.style.R.string.error_card_declined), source, null)
          }
        }
      } else {
        StripeClient.instance.handleNextAction(context, signed)
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }

    if (!SystemUtils.isAppOnForeground()) {
      completed(ResourcesUtil.getString(com.ricepo.style.R.string.error_load_failed), source, null)
    }
  }
}
