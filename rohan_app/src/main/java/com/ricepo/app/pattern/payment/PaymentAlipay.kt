package com.ricepo.app.pattern.payment

import android.content.Context
import androidx.activity.ComponentActivity
import com.ricepo.app.model.PaymentObj
import com.ricepo.tripartite.alipay.AlipayClient

//
// Created by <PERSON><PERSON> on 2/7/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class PaymentAlipay(val source: String? = null) : PaymentStrategy() {

  override fun handlePayment(
    context: Context,
    paymentObj: PaymentObj,
    completed: PaymentCompleted
  ) {
    val signed = paymentObj.signed
    if (signed == null) {
      completed(null, source, null)
      return
    }

    if (context is ComponentActivity) {
      AlipayClient.pay(context, signed)
        .observe(
          context
        ) {
          completed(it, source, null)
        }
    }
  }
}
