package com.ricepo.app.pattern.payment

import android.app.Activity
import android.content.Context
import android.util.Log
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.wallet.AutoResolveHelper
import com.google.android.gms.wallet.IsReadyToPayRequest
import com.google.android.gms.wallet.PaymentDataRequest
import com.google.android.gms.wallet.Wallet
import com.google.android.gms.wallet.WalletConstants
import com.ricepo.app.BuildConfig
import com.ricepo.app.R
import com.ricepo.app.model.PaymentObj
import com.ricepo.base.consts.BaseConstant
import com.ricepo.base.tools.AssetUtils
import com.ricepo.style.ResourcesUtil
import com.stripe.android.GooglePayJsonFactory
import kotlin.math.max

//
// Created by <PERSON><PERSON> on 2/21/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class PaymentGoogle(val source: String?) : PaymentStrategy() {

  override fun handlePayment(
    context: Context,
    paymentObj: PaymentObj,
    completed: PaymentCompleted
  ) {
    val env = if (BuildConfig.DEBUG) {
      WalletConstants.ENVIRONMENT_TEST
    } else {
      WalletConstants.ENVIRONMENT_PRODUCTION
    }
    val paymentsClient = Wallet.getPaymentsClient(
      context,
      Wallet.WalletOptions.Builder()
        .setEnvironment(env)
        .build()
    )
    val googlePayJsonFactory = GooglePayJsonFactory(context)

    val currencyCode = AssetUtils.getCurrency(
      paymentObj.order?.restaurant
        ?.address?.innerItemValue?.country
    )
    val totalPrice = max(paymentObj.order?.total ?: 0, 1)

    context as Activity
    AutoResolveHelper.resolveTask(
      paymentsClient.loadPaymentData(
        PaymentDataRequest.fromJson(
          googlePayJsonFactory.createPaymentDataRequest(
            transactionInfo = GooglePayJsonFactory.TransactionInfo(
              currencyCode = currencyCode,
              totalPrice = totalPrice,
              totalPriceStatus = GooglePayJsonFactory.TransactionInfo.TotalPriceStatus.Final
            ),
            merchantInfo = GooglePayJsonFactory.MerchantInfo(
              merchantName = BaseConstant.GOOGLE_PAY_MERCHANT_NAME
            ),
            shippingAddressParameters = GooglePayJsonFactory.ShippingAddressParameters(
              isRequired = true,
              allowedCountryCodes = setOf("US", "CN", "ES", "FR", "GB", "DE"),
              phoneNumberRequired = true
            ),
            billingAddressParameters = GooglePayJsonFactory.BillingAddressParameters(
              isRequired = true,
              format = GooglePayJsonFactory.BillingAddressParameters.Format.Full,
              isPhoneNumberRequired = true
            )
          ).toString()
        )
      ),
      context,
      LOAD_PAYMENT_DATA_REQUEST_CODE
    )
  }

  companion object {
    const val LOAD_PAYMENT_DATA_REQUEST_CODE = 5000

    fun isReadyToPay(
      context: Context,
      callback: (s: String?) -> Unit
    ) {
      val env = if (BuildConfig.DEBUG) {
        WalletConstants.ENVIRONMENT_TEST
      } else {
        WalletConstants.ENVIRONMENT_PRODUCTION
      }
      val paymentsClient = Wallet.getPaymentsClient(
        context,
        Wallet.WalletOptions.Builder()
          .setEnvironment(env)
          .build()
      )
      val googlePayJsonFactory = GooglePayJsonFactory(context)

      val request = IsReadyToPayRequest.fromJson(
        // existingPaymentMethodRequired = true for emulator need set GPay
        googlePayJsonFactory.createIsReadyToPayRequest(
          existingPaymentMethodRequired = null
        ).toString()
      )

      val unavailable = ResourcesUtil.getString(com.ricepo.style.R.string.payment_google_not_support)

      paymentsClient.isReadyToPay(request)
        .addOnCompleteListener { task ->
          runCatching {
            task.getResult(ApiException::class.java) == true
          }.fold(
            onSuccess = { isReady ->
              val r = if (isReady) {
                null
              } else {
                unavailable
              }
              callback(r)
            },
            onFailure = {
              Log.e("StripeExample", "Exception in isReadyToPay", it)
              callback(it.message)
            }
          )
        }
        .addOnCanceledListener {
          callback(unavailable)
        }
        .addOnFailureListener {
          callback(it.message)
        }
    }
  }
}
