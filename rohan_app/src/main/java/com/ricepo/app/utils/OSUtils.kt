package com.ricepo.app.utils

import android.content.pm.PackageManager
import com.google.android.gms.common.GoogleApiAvailability
import com.ricepo.base.BaseApplication

//
// Created by <PERSON><PERSON> on 24/6/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
object OSUtils {

  /**
   * support google gms
   */
  fun isGMSInstalledAndEnabled(): <PERSON><PERSON><PERSON> {
    return packageInstalledAndEnabled(GoogleApiAvailability.GOOGLE_PLAY_SERVICES_PACKAGE)
  }

  private fun packageInstalledAndEnabled(packageName: String): <PERSON><PERSON><PERSON> {
    return try {
      val pm = BaseApplication.context.packageManager
      val info = pm.getPackageInfo(packageName, PackageManager.GET_META_DATA)
      info.applicationInfo.enabled
    } catch (e: PackageManager.NameNotFoundException) {
      false
    }
  }
}
