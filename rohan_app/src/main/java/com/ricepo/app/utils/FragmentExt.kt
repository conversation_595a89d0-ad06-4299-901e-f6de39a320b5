package com.ricepo.app.utils

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.view.View
import android.view.ViewGroup
import androidx.core.view.forEach
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.ricepo.base.animation.LoadingView

fun Fragment.activityContext(): Context = (
  context as? ContextWrapper
  )?.baseContext ?: requireActivity()

fun Context.astActivity(block: (Activity) -> Unit) = (
  ((this as? Activity) ?: ((this as? ContextWrapper)?.baseContext) as? Activity)?.let(block)
  )

fun View.findFragmentActivity(): FragmentActivity? {
  return (context as? FragmentActivity) ?: ((context as? ContextWrapper)?.baseContext as? FragmentActivity)
}

fun Fragment.showLoading(root: ViewGroup) {
  hideLoading(root)
  val loadingView = LoadingView(requireContext())
  (root.parent as? ViewGroup)?.addView(
    loadingView
  )
  loadingView.bringToFront()
  loadingView.showLoading()
}

fun Fragment.hideLoading(root: ViewGroup) {
  (root.parent as? ViewGroup)?.let { vp ->
    vp.forEach {
      if (it is LoadingView) {
        it.clear()
        vp.removeView(it)
      }
    }
  }
}
