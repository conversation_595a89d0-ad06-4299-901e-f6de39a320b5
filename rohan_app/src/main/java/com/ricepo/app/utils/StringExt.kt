package com.ricepo.app.utils

import java.util.regex.Pattern

fun String.isEmailValid(): <PERSON><PERSON>an {
  return Pattern.compile(
    "^(([\\w-]+\\.)+[\\w-]+|([a-zA-Z]|[\\w-]{2,}))@" +
      "((([0-1]?[0-9]{1,2}|25[0-5]|2[0-4][0-9])\\.([0-1]?" +
      "[0-9]{1,2}|25[0-5]|2[0-4][0-9])\\." +
      "([0-1]?[0-9]{1,2}|25[0-5]|2[0-4][0-9])\\.([0-1]?" +
      "[0-9]{1,2}|25[0-5]|2[0-4][0-9]))|" +
      "([a-zA-Z]+[\\w-]+\\.)+[a-zA-Z]{2,4})$"
  ).matcher(this).matches() || android.util.Patterns.EMAIL_ADDRESS.matcher(this).matches()
}
