package com.ricepo.app.utils

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.play.core.review.ReviewManagerFactory
import com.ricepo.app.R
import com.ricepo.app.compose.ComposeUiType
import com.ricepo.app.compose.launchComposeUi
import com.ricepo.app.compose.ui.RiceTheme
import com.ricepo.app.compose.widget.DialogButtons
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderStatus
import com.ricepo.base.data.pref.CommonPref

fun FragmentActivity.checkRating(
  order: Order
) {
  // only show one time
  if (CommonPref.getAppRating(this) != null) return
  // need order payment and create count of three
  if (order.customer.orderCount != 2) return
  if (order.status != OrderStatus.Created) return
  RatingDialog().show(supportFragmentManager, null)
}

data class FeedBack(val feedback: String)

class RatingDialog : BottomSheetDialogFragment() {

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View {
    return ComposeView(activityContext()).apply {
      setContent {
        RiceTheme {
          RatingView(
            onBad = {
              with(requireActivity()) {
                launchComposeUi(ComposeUiType.Rating)
              }
              dismiss()
            },
            onGood = {
              launchPlayRatingDialog(requireActivity())
              dismiss()
            },
            onClose = {
              dismiss()
            }
          )
        }
      }
    }
  }

  override fun onStart() {
    super.onStart()
    dialog?.window?.also { window ->
      window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
      val layoutParams = window.attributes
      layoutParams.width = windowWidth()
      window.attributes = layoutParams
    }
  }

  private fun windowWidth(): Int = (context?.widthPx() ?: 1080).toInt()

  private fun Context.widthPx(): Int {
    return applicationContext.resources.displayMetrics.widthPixels.let {
      maxOf(it, 1080)
    }
  }

  override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
    return BottomSheetDialog(
      requireContext(),
      com.ricepo.style.R.style.TransparentBottomSheetDialogTheme
    )
  }

  @Composable
  @Preview
  fun PreviewRating() {
    RatingView(
      onGood = {},
      onBad = {},
    )
  }

  @Composable
  fun RatingView(
    onGood: () -> Unit,
    onBad: () -> Unit,
    onClose: (() -> Unit)? = null
  ) {
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .height(300.dp)
    ) {
      Box(
        modifier = Modifier
          .padding(top = 20.dp)
          .background(colorResource(id = com.ricepo.style.R.color.card_background))
          .fillMaxHeight()
          .fillMaxWidth()
      ) {
        IconButton(
          onClick = {
            onClose?.invoke()
          },
          modifier = Modifier.align(Alignment.TopEnd)
        ) {
          Icon(painter = painterResource(id = com.ricepo.style.R.drawable.ic_close), contentDescription = "")
        }
      }
      Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
      ) {
        Image(
          painter = painterResource(id = com.ricepo.style.R.drawable.ic_rating_thumb),
          contentDescription = "",
        )
        Spacer(modifier = Modifier.height(16.dp))
        Row {
          for (i in 0 until 5) {
            Image(
              painter = painterResource(id = com.ricepo.style.R.drawable.ic_star_app_rating),
              contentDescription = "",
              modifier = Modifier.padding(8.dp)
            )
          }
        }
        Spacer(modifier = Modifier.height(20.dp))
        Text(
          text = stringResource(
            id = com.ricepo.style.R.string.app_rating_dialog_title
          ),
          style = MaterialTheme.typography.h3
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
          text = stringResource(id = com.ricepo.style.R.string.app_rating_dialog_content),
          style = MaterialTheme.typography.body2
        )
        Spacer(modifier = Modifier.weight(1f))
        DialogButtons(
          positiveStr = stringResource(id = com.ricepo.style.R.string.app_rating_dialog_good),
          negativeStr = stringResource(id = com.ricepo.style.R.string.app_rating_dialog_bad),
          onPositiveClick = onGood,
          onNegativeClick = onBad,
        )
      }
    }
  }
}

private fun launchPlayRatingDialog(
  context: Activity
) {
  val manager = ReviewManagerFactory.create(context)
  val request = manager.requestReviewFlow()
  request.addOnCompleteListener { task ->
    task.isSuccessful.log("app rating")
    if (task.isSuccessful) {
      CommonPref.saveAppRating(context)
      val reviewInfo = task.result
      val flow = reviewInfo?.let { manager.launchReviewFlow(context, it) }
      flow?.addOnCompleteListener {
      }
    }
  }
}
