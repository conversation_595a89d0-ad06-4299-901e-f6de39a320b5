package com.ricepo.app.utils

import com.ricepo.app.features.menu.data.MenuPageData
import com.ricepo.base.model.Restaurant

object FoodLargeImageUtil {

  private val bigImageIds by lazy {
    mutableSetOf<String>()
  }

  fun shouldUseBigImage(id: String?): Bo<PERSON>an {
    return bigImageIds.any { it == id }
  }

  fun calculateBigImageIds(
    restaurant: Restaurant?,
    menuPageData: MenuPageData?
  ) {
    bigImageIds.clear()
    if (restaurant?.useLargeImage == true) {
      restaurant.id?.let {
        bigImageIds.add(it)
      }
    }
    menuPageData?.listBundle?.filter { mb ->
      menuPageData.listRestaurant?.any {
        it.id == mb.id && it.useLargeImage == true
      } ?: false
    }?.mapNotNull {
      it.id
    }?.let {
      bigImageIds.addAll(it)
    }
  }
}
