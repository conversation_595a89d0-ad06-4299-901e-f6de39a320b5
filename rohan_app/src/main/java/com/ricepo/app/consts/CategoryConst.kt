package com.ricepo.app.consts

//
// Created by <PERSON><PERSON> on 7/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object CategoryConst {

  // /  category type start ///
  const val NORMAL = "normal"

  const val REWARD = "reward"

  const val GALLERY = "gallery"

  const val TOP = "top"

  const val MARKET = "market"

  /**
   * the search of restaurant
   */
  const val SEARCH = "search"

  // /  category type end ///

  /**
   * market top category
   */
  const val MARKET_TOP = "market-top"

  /**
   * the local search of food
   */
  const val FOOD_SEARCH = "foodSearch"

  /** Promotion:
   *   1-2: vertical
   *   3+: double
   *   Reward:
   *   1: vertical
   *   2+: single
   *   Search Result:
   *   1-3: vertical
   *   4: double
   *   5+ triple
   *   Normal:
   *   Vertical (Follow old limit / expand logic)
   *   In Menu Search (not related to backend):
   *   Vertical
   */
  // / =========== new section ============ ///
  const val VERTICAL = "vertical"
  const val HORIZONTAL = "horizontal"

  const val COMBO = "combo"
}
