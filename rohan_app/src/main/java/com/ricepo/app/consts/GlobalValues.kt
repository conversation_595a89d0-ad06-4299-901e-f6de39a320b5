package com.ricepo.app.consts

import android.content.Context
import com.google.gson.Gson
import com.ricepo.app.features.address.AddressCache
import com.ricepo.base.BaseApplication
import com.ricepo.map.model.FormatUserAddress
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * A state holder for some global observable data （like login state）
 */

interface IGlobalValueHolder {
  val address: MutableStateFlow<FormatUserAddress?>
}

@OptIn(DelicateCoroutinesApi::class)
object GlobalValueHolder : IGlobalValueHolder {

  val gson: Gson by lazy {
    Gson()
  }

  val context: Context by lazy {
    BaseApplication.context
  }

  init {
    GlobalScope.launch {
      address.value = AddressCache.getAddress()
    }
  }

  override val address: MutableStateFlow<FormatUserAddress?> = MutableStateFlow(null)
}
