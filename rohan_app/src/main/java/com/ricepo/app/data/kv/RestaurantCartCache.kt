package com.ricepo.app.data.kv

import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.base.data.common.kv.KEY_RESTAURANT_CART
import com.ricepo.base.data.common.kv.KEY_RESTAURANT_CART_MARKET
import com.ricepo.base.data.common.kv.KeyValueHelper
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import java.lang.StringBuilder

//
// Created by <PERSON><PERSON> on 20/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object RestaurantCartCache : KeyValueHelper() {

  // // restaurant cart

  /**
   * save cart for add remove and clean
   */
  suspend fun saveRestaurantCart(restaurantCart: RestaurantCart, isUpdate: Boolean = true) {
    when {
      restaurantCart.cartList.isNullOrEmpty() -> {
        // clean the cart
        deleteRestaurantCart(restaurantCart.restaurant)
      }
      Restaurant.isMarketCategory(restaurantCart.restaurant) -> {
        saveValue(KEY_RESTAURANT_CART_MARKET, restaurantCart, isUpdate)
      }
      else -> {
        saveValue(KEY_RESTAURANT_CART, restaurantCart, isUpdate)
      }
    }
  }

  suspend fun deleteRestaurantCart(restaurant: Restaurant?): Int {
    return if (Restaurant.isMarketCategory(restaurant)) {
      deleteValue(KEY_RESTAURANT_CART_MARKET)
    } else {
      deleteValue(KEY_RESTAURANT_CART)
    }
  }

  /**
   * clear the restaurant cart
   */
  suspend fun clearRestaurantCart() {
    deleteValue(KEY_RESTAURANT_CART_MARKET)
    deleteValue(KEY_RESTAURANT_CART)
  }

  private fun getValue(
    restaurant: Restaurant?,
    isLastCart: Boolean = false,
    block: (String?) -> Unit
  ) {
    return when {
      Restaurant.isMarketCategory(restaurant) -> {
        getValue(KEY_RESTAURANT_CART_MARKET, block)
      }
      restaurant != null -> {
        getValue(KEY_RESTAURANT_CART, block)
      }
      isLastCart -> {
        // get the newest cart
        getValue(listOf(KEY_RESTAURANT_CART, KEY_RESTAURANT_CART_MARKET), block)
      }
      else -> {
        block(null)
      }
    }
  }

  private fun getValue(restaurant: Restaurant?, isLastCart: Boolean = false): String? {
    return when {
      Restaurant.isMarketCategory(restaurant) -> {
        getValue(KEY_RESTAURANT_CART_MARKET)
      }
      restaurant != null -> {
        getValue(KEY_RESTAURANT_CART)
      }
      isLastCart -> {
        // get the newest cart
        getValue(listOf(KEY_RESTAURANT_CART, KEY_RESTAURANT_CART_MARKET))
      }
      else -> {
        null
      }
    }
  }

  private suspend fun getValueSuspend(restaurant: Restaurant?, isLastCart: Boolean = false): String? {
    return when {
      Restaurant.isMarketCategory(restaurant) -> {
        getValueSuspend(KEY_RESTAURANT_CART_MARKET)
      }
      restaurant != null -> {
        getValueSuspend(KEY_RESTAURANT_CART)
      }
      isLastCart -> {
        // the newest
        getValueSuspend(listOf(KEY_RESTAURANT_CART, KEY_RESTAURANT_CART_MARKET))
      }
      else -> {
        null
      }
    }
  }

  private fun getRestaurantCart(
    restaurant: Restaurant?,
    isLastCart: Boolean = false,
    block: (RestaurantCart?) -> Unit
  ) {
    getValue(restaurant) { value ->
      var restaurantCart: RestaurantCart? = null
      if (value?.isNotEmpty() == true) {
        restaurantCart = ParserModelFacade.fromJson(value, RestaurantCart::class.java)
      }

      if (restaurant?.id == restaurantCart?.restaurant?.id || isLastCart) {
        block(restaurantCart)
      } else {
        block(restaurantCart)
      }
    }
  }

  fun getRestaurantCart(restaurant: Restaurant?): RestaurantCart? {
    var restaurantCart: RestaurantCart? = null
    val value = getValue(listOf(KEY_RESTAURANT_CART))
    if (value?.isNotEmpty() == true) {
      restaurantCart = ParserModelFacade.fromJson(value, RestaurantCart::class.java)
    }
    var restaurantMarketCart: RestaurantCart? = null
    val marketValue = getValue(listOf(KEY_RESTAURANT_CART_MARKET))
    if (!marketValue.isNullOrEmpty()) {
      restaurantMarketCart = ParserModelFacade.fromJson(marketValue, RestaurantCart::class.java)
    }
    // query all is compatibility reorder no restaurant tags
    return when (restaurant?.id) {
      restaurantCart?.restaurant?.id -> {
        restaurantCart
      }
      restaurantMarketCart?.restaurant?.id -> {
        restaurantMarketCart
      }
      else -> {
        null
      }
    }
  }

  suspend fun getRestaurantCartSuspend(
    restaurant: Restaurant?,
    isLastCart: Boolean = false
  ): RestaurantCart? {
    var restaurantCart: RestaurantCart? = null
    val value = getValueSuspend(restaurant, isLastCart)
    if (value?.isNotEmpty() == true) {
      restaurantCart = ParserModelFacade.fromJson(value, RestaurantCart::class.java)
    }
    return if (restaurant?.id == restaurantCart?.restaurant?.id || isLastCart) {
      restaurantCart
    } else {
      null
    }
  }

  suspend fun getRestaurantSuspend(restaurant: Restaurant?): Restaurant? {
    return getRestaurantCartSuspend(restaurant)?.restaurant
  }

  fun getCountry(restaurant: Restaurant?, block: (String) -> Unit) {
    getRestaurantCart(restaurant) {
      val address = restaurant?.address ?: it?.restaurant?.address
      var country = "US"
      if (address is AddressObj) {
        country = address.country ?: "US"
      }
      block(country)
    }
  }

  fun getCartListByGroup(restaurantCart: RestaurantCart? = null): List<Cart>? {
    val restaurantCart = restaurantCart ?: getRestaurantCart(restaurantCart?.restaurant)

    val cartList = restaurantCart?.cartList

    val cartMap = cartList?.groupBy {
      val optStr = it.opt.fold(StringBuilder()) { result, item ->
        result.append(item.id)
      }
      it.id + optStr
    }?.mapNotNull {
      var cart = it.value
      cart[0].qty = cart.count()
      cart[0]
    }

    cartMap?.sortedBy { it.createdAt }

    return cartMap
  }

  /**
   * clean the reward
   */
  suspend fun cleanReward(restaurant: Restaurant?, restIds: List<String>) {
    val restaurant = restaurant ?: return
    val restCart = getRestaurantCartSuspend(restaurant) ?: return
    if (restaurant.id == restCart.restaurant?.id) else return

    val cartList = restCart?.cartList
    val remainCartList = if (restIds.isEmpty()) {
      cartList?.filter { it.reward != true }
    } else {
      val carts = mutableListOf<Cart>()

      val otherCarts = cartList?.filterNot { restIds.contains(it.bundleRestId) }
      if (otherCarts != null) {
        carts.addAll(otherCarts)
      }

      val checkCarts = cartList?.filter { restIds.contains(it.bundleRestId) }
      val remainCarts = checkCarts?.filter { it.reward != true }
      if (remainCarts != null) {
        carts.addAll(remainCarts)
      }

      carts
    }
    if (remainCartList.isNullOrEmpty()) {
      deleteRestaurantCart(restaurant)
    } else {
      restCart.cartList = remainCartList
      saveRestaurantCart(restCart)
    }
  }

  /**
   * delete cart by restaurant id with bundle
   */
  suspend fun deleteCartsWithRestaurantId(hostRestaurant: Restaurant?, restaurant: Restaurant?) {
    val hostRestaurant = hostRestaurant ?: return
    val restaurantId = restaurant?.id ?: return
    val restaurantCart = getRestaurantCartSuspend(hostRestaurant, true)
    if (restaurantCart != null) {
      val carts = restaurantCart.cartList?.filter {
        it.bundleRestId != restaurantId
      }
      if ((carts?.size ?: 0) > 0) {
        restaurantCart.cartList = carts
        saveRestaurantCart(restaurantCart)
      } else {
        deleteRestaurantCart(restaurant)
      }
    }
  }

  /**
   * get bundles which include host restaurant from restaurant cart
   */
  fun getBundles(restaurant: Restaurant?): List<String> {
    val restCart = getRestaurantCart(restaurant) ?: return listOf()
    val restaurantId = restaurant?.id
    if (restaurantId == restCart.restaurant?.id) else return listOf()

    return restCart.cartList?.filter { it.bundleRestId != null }
      ?.distinctBy { it.bundleRestId }
      ?.map {
        it.bundleRestId ?: ""
      } ?: listOf()
  }

  suspend fun removeCart(restaurant: Restaurant?, foodId: String?, optionId: String?): RestaurantCart? {
    val restaurantCart = getRestaurantCartSuspend(restaurant) ?: return null

    val cartList = restaurantCart.cartList
    var carts = mutableListOf<Cart>()
    if (optionId != null && foodId != null) {
      // remove food option item
      carts.addAll(
        cartList?.filterNot {
          it.id == foodId && it.opt.find {
            it.id == optionId
          } != null
        } ?: listOf()
      )
    } else if (foodId != null) {
      // remove food item
      carts.addAll(cartList?.filter { it.id != foodId } ?: listOf())
    }

    restaurantCart.cartList = carts
    saveRestaurantCart(restaurantCart)

    return restaurantCart
  }
}
