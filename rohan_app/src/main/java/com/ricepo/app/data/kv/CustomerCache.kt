package com.ricepo.app.data.kv

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.ricepo.app.message.MessagingFacade
import com.ricepo.app.model.Card
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.data.common.kv.KEY_AUTH_TOKEN
import com.ricepo.base.data.common.kv.KEY_CUSTOMER
import com.ricepo.base.data.common.kv.KEY_PAYMENT
import com.ricepo.base.data.common.kv.KeyValueHelper
import com.ricepo.base.model.Customer
import io.branch.referral.Branch
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

//
// Created by <PERSON><PERSON> on 5/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object CustomerCache : KeyValueHelper() {

  // // token and customer

  suspend fun saveToken(token: String) {
    saveValue(KEY_AUTH_TOKEN, token)
  }

  suspend fun deleteToken(): Int {
    return deleteValue(KEY_AUTH_TOKEN)
  }

  fun getToken(): String? {

    return getValue(KEY_AUTH_TOKEN)
  }

  suspend fun getTokenSuspend(): String? {

    return getValueSuspend(KEY_AUTH_TOKEN)
  }

  fun liveToken(): LiveData<String> {
    val liveData = MutableLiveData<String>()
    GlobalScope.launch {
      val token = getToken()
      token?.let { liveData.postValue(it) }
    }
    return liveData
  }

  suspend fun saveCustomer(customer: Customer?, isPushRegister: Boolean = true) {
    if (customer == null) return
    saveValue(KEY_CUSTOMER, customer)

    // register messaging token
    if (isPushRegister) {
      GlobalScope.async {
        MessagingFacade.pushRegister(null)
      }
    }

    // save customer property for firebase
    try {
      AnalyticsFacade.updateUserProp(customer)
    } catch (e: Exception) {}
  }

  fun saveCustomer(customer: Customer?, block: () -> Unit) {
    GlobalScope.launch {
      if (customer != null) {
        saveValue(KEY_CUSTOMER, customer)

        // register messaging token
        MessagingFacade.pushRegister(null)

        block()
      }
    }
  }

  private suspend fun deleteCustomer(): Int {
//        GlobalScope.async {
//            // unregister notification
//            MessagingFacade.pushUnregister()
//        }

    return deleteValue(KEY_CUSTOMER)
  }

  fun getCustomer(): Customer? {
    val value = getValue(KEY_CUSTOMER)
    if (value.isNullOrEmpty()) return null
    return ParserModelFacade.fromJson(value, Customer::class.java)
  }

  suspend fun getCustomerSuspend(): Customer? {
    val value = getValueSuspend(KEY_CUSTOMER)
    if (value.isNullOrEmpty()) return null
    return ParserModelFacade.fromJson(value, Customer::class.java)
  }

  fun liveCustomer(block: (Customer?) -> Unit) {
    getValue(KEY_CUSTOMER) {
      val value = it
      if (value.isNullOrEmpty()) {
        block(null)
      } else {
        val customer = ParserModelFacade.fromJson(value, Customer::class.java)
        block(customer)
      }
    }
  }

  fun clearWithLogout() {
    GlobalScope.launch {
      clearCacheInfo()
    }
  }

  suspend fun clearWithLogoutSuspend() {
    clearCacheInfo()

    try {
      // logout branch
      Branch.getInstance().logout()
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private suspend fun clearCacheInfo() {
    val token = getTokenSuspend()
    val customer = getCustomerSuspend()
    // login failed not clear the payment
    if (token != null || customer != null) {
      deleteToken()
      deleteCustomer()
      deletePayment()
    }
  }

  suspend fun getPayment(): PaymentOwnMethod? {
    val value = getValueSuspend(KEY_PAYMENT)
    if (value.isNullOrEmpty()) return null
    return ParserModelFacade.fromJson(value, PaymentOwnMethod::class.java)
  }

  fun getPayment(block: (PaymentOwnMethod?) -> Unit) {
    getValue(KEY_PAYMENT) {
      val value = it
      var payment: PaymentOwnMethod? = null
      if (!value.isNullOrEmpty()) {
        payment = ParserModelFacade.fromJson(value, PaymentOwnMethod::class.java)
      }
      block(payment)
    }
  }

  fun savePayment(card: Card, block: () -> Unit) {
    GlobalScope.launch {
      if (card.brand != null) {
        val payment = PaymentOwnMethod(
          PaymentOwnMethod.CREDIT,
          card.brand,
          card.last4,
          card.id,
          image = card.images
        )
        saveValue(KEY_PAYMENT, payment)
      } else if (card.method != null && card.method != PaymentOwnMethod.ADD_NEW_CARD) {
        val payment = PaymentOwnMethod(card.method!!, name = card.name, image = card.images, stripeId = card.id)
        saveValue(KEY_PAYMENT, payment)
      }

      block()
    }
  }

  fun savePaymentBbva(card: Card, block: (it: PaymentOwnMethod) -> Unit) {
    GlobalScope.launch {
      if (card.id != null) {
        val payment = PaymentOwnMethod(
          card.method ?: PaymentOwnMethod.BBVA_PAY,
          card.brand,
          card.last4,
          card.id,
          image = card.images,
          name = card.name
        )
        saveValue(KEY_PAYMENT, payment)
        block(payment)
      }
    }
  }

  fun savePayment(payment: PaymentOwnMethod, block: () -> Unit) {
    GlobalScope.launch {
      saveValue(KEY_PAYMENT, payment)
      block()
    }
  }

  suspend fun deletePayment(): Int {
    return deleteValue(KEY_PAYMENT)
  }
}
