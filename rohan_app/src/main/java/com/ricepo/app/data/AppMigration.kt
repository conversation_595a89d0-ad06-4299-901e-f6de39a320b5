package com.ricepo.app.data

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

//
// Created by <PERSON><PERSON> on 28/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object AppMigration {

  // modify the base entity id can null
  val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
      // Migration didn`t properly handle id is need not null
    }
  }

  val MIGRATION_2_3 = object : Migration(2, 3) {
    override fun migrate(database: SupportSQLiteDatabase) {
      // add remote_keys table
      database.execSQL(
        "create table `remote_keys` (`dataId` Text Not Null default '', `prevKey` Integer Null," +
          "`nextKey` Integer Null, `createdAt` Text Null, primary key(`dataId`))"
      )
    }
  }

  val MIGRATION_3_4 = object : Migration(3, 4) {
    override fun migrate(database: SupportSQLiteDatabase) {
      // add remote_keys table
      database.execSQL("ALTER TABLE t_address ADD COLUMN source Text")
    }
  }
}
