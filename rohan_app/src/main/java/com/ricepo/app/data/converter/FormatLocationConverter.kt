package com.ricepo.app.data.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.ricepo.map.model.FormatLocation

//
// Created by <PERSON><PERSON> on 10/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class FormatLocationConverter {

  @TypeConverter
  fun fromJson(json: String?): FormatLocation? {
    return Gson().fromJson(json, FormatLocation::class.java)
  }

  @TypeConverter
  fun locationToJson(location: FormatLocation?): String? {
    return Gson().toJson(location)
  }
}
