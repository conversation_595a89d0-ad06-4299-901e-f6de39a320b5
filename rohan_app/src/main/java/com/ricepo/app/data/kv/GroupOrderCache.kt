package com.ricepo.app.data.kv

import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.base.BaseApplication
import com.ricepo.base.data.common.kv.KEY_GROUP_ORDER
import com.ricepo.base.data.common.kv.KeyValueHelper
import com.ricepo.base.model.Cart
import com.ricepo.base.model.CustomerCart
import com.ricepo.base.model.OrderGroup
import com.ricepo.base.model.OrderGroupUser
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object GroupOrderCache : KeyValueHelper() {

  suspend fun saveOrder(order: OrderGroup?) {
    if (order?.groupId != null) {
      saveValue(KEY_GROUP_ORDER, order)
    }
  }

  suspend fun deleteOrder(): Int {
    return deleteValue(KEY_GROUP_ORDER)
  }

  /**
   * use in the rx thread
   */
  fun getOrderGroup(): OrderGroup? {
    val value = getValue(KEY_GROUP_ORDER)
    var order: OrderGroup? = null
    if (value?.isNotEmpty() == true) {
      order = ParserModelFacade.fromJson(value, OrderGroup::class.java)
    }
    return order
  }

  suspend fun getOrderSuspend(): OrderGroup? {
    val value = getValueSuspend(KEY_GROUP_ORDER)
    var order: OrderGroup? = null
    if (value?.isNotEmpty() == true) {
      order = ParserModelFacade.fromJson(value, OrderGroup::class.java)
    }
    return order
  }

  suspend fun getGroupId(): String? {
    val order = getOrderSuspend()
    return order?.groupId
  }

  suspend fun isGroupExist(): Boolean {
    return !getGroupId().isNullOrEmpty()
  }

  suspend fun getGroupOwner(): OrderGroupUser {
    val order = getOrderSuspend()
    return order?.user ?: OrderGroupUser("", "", "")
  }

  suspend fun getRestaurantName(): String? {
    val order = getOrderSuspend()
    return order?.restaurant?.name?.localize()
  }

  suspend fun isOwner(restaurantId: String): Boolean {
    val order = getOrderSuspend()
    return if (order == null) {
      // group data is empty means user is in normal model, direct return true
      true
    } else {
      if (order.restaurant?.id == restaurantId) {
        // return owner status
        order.owner
      } else {
        // if user not in grouped restaurant, user is the owner of the entered restaurant
        true
      }
    }
  }

  suspend fun getGroupRestaurantId(): String? {
    val order = getOrderSuspend()
    return order?.restaurant?.id
  }

  suspend fun getGroupList(): List<CustomerCart>? {
    val order = getOrderSuspend()
    return order?.list
  }

  suspend fun getSelfCartList(restaurantId: String?): List<Cart>? {
    val groupOrderList = getGroupList()
    var carts = mutableListOf<Cart>()
    groupOrderList?.forEach { item ->
      if (item.user.deviceId == BaseApplication.mDeviceId &&
        restaurantId == getGroupRestaurantId()
      ) {
        carts.addAll(item.items ?: listOf())
      }
    }
    return carts
  }

  suspend fun organizeGroupOrder(): List<Cart>? {
    val groupOrderList = getGroupList()
    val carts = mutableListOf<Cart>()

    groupOrderList?.forEach { item ->
      var items = item.items ?: listOf()
      items = items.map {
        it.ownerId = item.user.deviceId
        it
      }
      carts.addAll(items)
    }

    return carts
  }

  suspend fun clearReward(restaurantId: String?, restIds: List<String>) {
    val orderGroup = getOrderGroup() ?: return
    val cartList = getSelfCartList(restaurantId)
    val remainCartList = if (restIds.isEmpty()) {
      cartList?.filter { it.reward != true }
    } else {
      val carts = mutableListOf<Cart>()

      val otherCarts = cartList?.filterNot { restIds.contains(it.bundleRestId) }
      if (otherCarts != null) {
        carts.addAll(otherCarts)
      }

      val checkCarts = cartList?.filter { restIds.contains(it.bundleRestId) }
      val remainCarts = checkCarts?.filter { it.reward != true }
      if (remainCarts != null) {
        carts.addAll(remainCarts)
      }

      carts
    }

    // don't delete order because of have others group
    orderGroup.list?.forEach {
      if (it.user.deviceId == BaseApplication.mDeviceId) {
        it.items = remainCartList
      }
    }
    saveOrder(orderGroup)
  }

  suspend fun getAllCartsSuspend(restCarts: List<Cart>?, restaurant: Restaurant?): List<Cart> {
    val carts = organizeGroupOrder()
    val groupInfo = getOrderSuspend()

    val isSameGroupedRestaurant = groupInfo?.restaurant?.id == restaurant?.id
    var combineCarts = restCarts?.toMutableList() ?: mutableListOf()
    if (carts != null && isSameGroupedRestaurant) {
      // add others carts
      combineCarts.addAll(carts.filter { it.ownerId != BaseApplication.mDeviceId })
    }
    combineCarts.sortBy { it.createdAt }
    return combineCarts
  }

  suspend fun removeCart(
    restaurant: Restaurant?,
    foodId: String?,
    optionId: String?
  ): OrderGroup? {
    val groupInfo = getOrderSuspend() ?: return null
    val customerCarts = getGroupList() ?: return null

    if (groupInfo?.restaurant?.id == restaurant?.id) else return null

    customerCarts.forEach { customerCart ->
      var carts = mutableListOf<Cart>()
      if (optionId != null && foodId != null) {
        // remove food option item
        carts.addAll(
          customerCart.items?.filterNot {
            it.id == foodId && it.opt.find {
              it.id == optionId
            } != null
          } ?: listOf()
        )
      } else if (foodId != null) {
        // remove food item
        carts.addAll(customerCart.items?.filter { it.id != foodId } ?: listOf())
      }
      customerCart.items = carts
    }
    groupInfo.list = customerCarts
    saveOrder(groupInfo)

    return groupInfo
  }
}
