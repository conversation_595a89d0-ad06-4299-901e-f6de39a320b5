package com.ricepo.app.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.sqlite.db.SupportSQLiteDatabase
import com.ricepo.app.data.converter.FormatLocationConverter
import com.ricepo.app.features.address.AddressDao
import com.ricepo.app.features.profile.datasource.db.RemoteKeys
import com.ricepo.app.features.profile.datasource.db.RemoteKeysDao
import com.ricepo.map.model.FormatUserAddress
import java.util.concurrent.Executors

//
// Created by <PERSON><PERSON> on 10/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Database(entities = [FormatUserAddress::class, RemoteKeys::class], version = 4, exportSchema = false)
@TypeConverters(FormatLocationConverter::class)
abstract class AppDatabase : RoomDatabase() {

  abstract fun addressDao(): AddressDao

  abstract fun remoteKeysDao(): RemoteKeysDao

  companion object {
    private const val DATABASE_NAME = "app.db"

    @Volatile private var INSTANCE: AppDatabase? = null

    fun getInstance(context: Context): AppDatabase = INSTANCE ?: synchronized(this) {
      INSTANCE ?: buildDatabase(context).also { INSTANCE = it }
    }

    private fun buildDatabase(context: Context) =
      Room.databaseBuilder(context.applicationContext, AppDatabase::class.java, DATABASE_NAME)
        .addMigrations(
          AppMigration.MIGRATION_1_2, AppMigration.MIGRATION_2_3,
          AppMigration.MIGRATION_3_4
        )
        .fallbackToDestructiveMigration()
        .addCallback(object : RoomDatabase.Callback() {
          override fun onCreate(db: SupportSQLiteDatabase) {
            super.onCreate(db)
            ioThread {
              // insert the data on the IO thread
            }
          }
        })
        .build()
  }
}

private val IO_EXECUTOR = Executors.newSingleThreadExecutor()

/**
 * Utility method to run blocks on a dedicated background thread, used for io/database work.
 */
fun ioThread(f: () -> Unit) {
}
