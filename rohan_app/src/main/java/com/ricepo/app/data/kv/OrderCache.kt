package com.ricepo.app.data.kv

import com.ricepo.app.model.Order
import com.ricepo.app.model.parser.ParserModelFacade
import com.ricepo.base.data.common.kv.KEY_ORDER
import com.ricepo.base.data.common.kv.KEY_REFRESH_ORDER
import com.ricepo.base.data.common.kv.KEY_REQUIRE_PERMISSION
import com.ricepo.base.data.common.kv.KeyValueHelper
import java.util.Calendar

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object OrderCache : KeyValueHelper() {

  suspend fun saveOrder(order: Order?) {
    if (order != null) {
      saveValue(KEY_ORDER, order)
    }
  }

  suspend fun deleteOrder(): Int {
    return deleteValue(KEY_ORDER)
  }

  fun getOrder(block: (Order?) -> Unit) {
    getValue(KEY_ORDER) { value ->
      var order: Order? = null
      if (value?.isNotEmpty() == true) {
        order = ParserModelFacade.fromJson(value, Order::class.java)
      }
      block(order)
    }
  }

  suspend fun saveRefreshOrder(order: Order?) {
    if (order != null) {
      saveValue(KEY_REFRESH_ORDER, order)
    }
  }

  suspend fun deleteRefreshOrder(): Int {
    return deleteValue(KEY_REFRESH_ORDER)
  }

  fun getRefreshOrder(block: (Order?) -> Unit) {
    getValue(KEY_ORDER) { value ->
      var order: Order? = null
      if (value?.isNotEmpty() == true) {
        order = ParserModelFacade.fromJson(value, Order::class.java)
      }
      block(order)
    }
  }

  suspend fun saveRequirePermTime() {
    val time = Calendar.getInstance().timeInMillis
    saveValue(KEY_REQUIRE_PERMISSION, time)
  }

  fun requirePermission(block: (Boolean) -> Unit) {
    getValue(KEY_REQUIRE_PERMISSION) { time ->
      var require = true
      val now = Calendar.getInstance()
      time?.toLong()?.let { t ->
        val diffInMillis = now.timeInMillis - t
        val monInMillis = 86400
        require = (diffInMillis >= monInMillis)
      }
      block(require)
    }
  }

}
