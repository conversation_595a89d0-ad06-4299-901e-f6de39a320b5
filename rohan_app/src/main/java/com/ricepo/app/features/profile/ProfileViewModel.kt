package com.ricepo.app.features.profile

import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.ricepo.base.model.Customer
import com.ricepo.base.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 2/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
@HiltViewModel
class ProfileViewModel @Inject constructor(
  private val profileUseCase: ProfileUseCase
) : BaseViewModel() {

  /**
   * pull down to refresh loading
   */
  private var _pullLoading: Boolean = false

  /**
   * loading indicator
   */
  private var _screenLoading: Boolean = false

  /**
   * the flag of screen loading indicator
   */
  val screenLoading: Boolean
    get() = _screenLoading

  /**
   * the flag of pull down refresh
   */
  val pullLoading: Boolean
    get() = _pullLoading

  private var currentProfileResult: Flow<PagingData<ProfileUiModel>>? = null

  fun loadData(
    customer: Customer?,
    isTab: Boolean = false
  ): Flow<PagingData<ProfileUiModel>?>? {

    val newResult = profileUseCase.getProfileData(
      customer?.id,
      viewModelScope,
      isTab = isTab
    ).cachedIn(viewModelScope)
    currentProfileResult = newResult
    return newResult
  }

  fun updatePullLoading(loading: Boolean) {
    _pullLoading = loading
  }

  fun updateScreenLoading(loading: Boolean) {
    _screenLoading = loading
  }
}
