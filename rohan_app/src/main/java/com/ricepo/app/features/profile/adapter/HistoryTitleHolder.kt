package com.ricepo.app.features.profile.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 2/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class HistoryTitleHolder(private val view: View) : RecyclerView.ViewHolder(view) {

  private val titleView = view.findViewById<TextView>(R.id.tv_history_title)
  private val groupEmptyView = view.findViewById<View>(R.id.group_history_empty)

  fun bind(title: String?, isEmpty: Boolean = false) {
    titleView.text = title ?: ""

    groupEmptyView.isVisible = isEmpty

    val params = view.layoutParams
    if (params is RecyclerView.LayoutParams) {
      params.topMargin = ResourcesUtil.getDimenPixelOffset(R.dimen.sw_section_space)
      params.leftMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.card_side_margin)
      params.rightMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.card_side_margin)
      view.layoutParams = params
    }
    if (isEmpty) {
      view.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card)
    } else {
      view.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_top)
    }
  }

  companion object {
    fun create(parent: ViewGroup): HistoryTitleHolder {
      val view = LayoutInflater.from(parent.context)
        .inflate(R.layout.profile_item_history_title, parent, false)
      return HistoryTitleHolder(view)
    }
  }
}
