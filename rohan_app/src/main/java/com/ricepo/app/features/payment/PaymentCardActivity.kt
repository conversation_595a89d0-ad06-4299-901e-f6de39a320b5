package com.ricepo.app.features.payment

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityPaymentCardBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.payment.data.ShowPaymentCase
import com.ricepo.app.model.Card
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.pattern.payment.PaymentRefer
import com.ricepo.base.BaseActivity
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.base.tools.SystemUtils
import com.ricepo.base.view.DialogFacade
import com.ricepo.tripartite.stripe.StripeRangeStore
import com.stripe.android.view.CardValidCallback
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

//
// Created by Thomsen on 8/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_PAYMENT_CARD)
class PaymentCardActivity : BaseActivity() {

  val paymentViewModel: PaymentViewModel by viewModels()

  @Inject
  lateinit var paymentUseCase: PaymentUseCase

  lateinit var showPaymentCase: ShowPaymentCase

  private lateinit var binding: ActivityPaymentCardBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityPaymentCardBinding.inflate(layoutInflater)
    setContentView(binding.root)

    showPaymentCase = intent.getParcelableExtra(FeaturePageConst.PARAM_PAGE_PAYMENT_SHOWCASE)!!

    setupView()
    setupListener()

    bindViewModel()
    clearStore(true)
  }

  override fun onResume() {
    super.onResume()

    try {
      KeyboardUtil.showKeyboard(
        this,
        binding.inputNewCard.findViewById(
          com.ricepo.tripartite.R.id.card_number_edit_text
        )
      )
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private fun bindViewModel() {
    paymentViewModel.observeCreateCard(this)
      .subscribe {
        val result = it
        result.fold(
          onSuccess = { data ->
            if (data is Card) {
              if (showPaymentCase == ShowPaymentCase.editCard) {
                addPaymentCard(data)
              } else {
                Loading.hideLoading()
                intent.putExtra(FeaturePageConst.PARAM_PAGE_PAYMENT_NEW_CARD, data)
                backResultEvent(intent)
              }
            }
          },
          onFailure = { error ->
            Loading.hideLoading()
            val message = error.message ?: ""
            if (!message.isNullOrEmpty()) {
              DialogFacade.showAlert(this, message)
            }
          }
        )
      }
  }

  private fun addPaymentCard(data: Card) {
    lifecycleScope.launch {
      paymentViewModel.addPaymentCard(this@PaymentCardActivity, data)
        .collectLatest { paymentObj ->
          val paymentMethod = PaymentOwnMethod(
            method = data.method ?: PaymentOwnMethod.CREDIT
          )
          paymentUseCase.handlePayment(
            this@PaymentCardActivity,
            paymentMethod,
            paymentObj
          ) { paymentError, _ ->
            Loading.hideLoading()
            if (paymentError.isNullOrEmpty()) {
              paymentAddCompleted()
            } else {
              paymentFailed(paymentError)
            }
          }
        }
    }
  }

  private fun paymentAddCompleted() {
    // payment added success
    backResultEvent(intent)
  }

  private fun paymentFailed(message: String?) {
    Loading.hideLoading()
    if (message != null) {
      DialogFacade.showAlert(this, message) {
      }
    }
  }

  private fun setupListener() {

    binding.btnAddCard.clickWithTrigger {
//            if (showPaymentCase == ShowPaymentCase.editCard) {
//                ToastUtil.showToast("add new card")
//            } else {
//            }
      paymentViewModel.triggerCreateCard(binding.inputNewCard?.paymentMethodCreateParams)
    }
  }

  private fun setupView() {
    lifecycleScope.launchWhenCreated {
      val isUS = withContext(Dispatchers.IO) {
        AddressCache.isUS()
      }
      // entry digits postal code of address is us
      binding.inputNewCard.postalCodeEnabled = true
      binding.inputNewCard.usZipCodeRequired = isUS

      binding.inputNewCard.setCardValidCallback { isValid, invalidFields ->
        if (invalidFields.contains(CardValidCallback.Fields.Number)) {
          clearStore(true)
        }
      }
    }
  }

  /**
   * clear the stripe store in memory card account range source
   * cache the key_account_ranges stripe(16.2.1) pan length has problem when quick input
   */
  private fun clearStore(isForceClean: Boolean) {
    try {
      StripeRangeStore(this).clearStore(SystemUtils.versionCode(), isForceClean)
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)

    PaymentRefer.onPaymentSetupIntentResult(
      this, requestCode, data,
      capture = {
        paymentAddCompleted()
      },
      completed = {
        paymentAddCompleted()
      },
      failed = {
        Loading.hideLoading()
//                paymentFailed(ResourcesUtil.getString(com.ricepo.style.R.string.error_card_declined))
      }
    )
  }
}
