package com.ricepo.app.features.refer

import androidx.core.app.ComponentActivity
import androidx.lifecycle.viewModelScope
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.channels.ConflatedBroadcastChannel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import java.lang.Exception
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 4/8/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
@HiltViewModel
class ReferViewModel @Inject constructor(
  val repository: CombineRestApi
) : BaseViewModel() {

  var referChannel = MutableSharedFlow<Result<ReferInfo>>()

//    var referErrorChannel = ConflatedBroadcastChannel<String>()

  fun refreshRefer(context: ComponentActivity, isLoading: Boolean) {
    viewModelScope.launch {
      if (isLoading) {
        flowLoading(
          context,
          { e ->
            // send() Back-end (JVM) Internal error: Failed to generate expression: KtCallExpression
            // run or (show kotlin bytecode)
//                    viewModelScope.launch {
//                        referChannel.send(Result.failure(e))
//                    }
            errorHandle(e)
          }
        ) {
          emit(repository.getRefer())
        }.collectLatest {
          referChannel.emit(Result.success(it))
        }
      } else {
        flow {
          try {
            emit(repository.getRefer())
          } catch (e: Exception) {
            e.printStackTrace()
          }
        }.collectLatest {
          referChannel.emit(Result.success(it))
        }
      }
    }
  }

  private fun errorHandle(e: Throwable) {
    viewModelScope.launch {
      referChannel.emit(Result.failure(e))
    }
  }
}
