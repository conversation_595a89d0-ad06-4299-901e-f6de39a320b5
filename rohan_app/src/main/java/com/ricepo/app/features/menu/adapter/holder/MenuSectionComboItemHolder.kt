package com.ricepo.app.features.menu.adapter.holder

import android.content.Context
import android.graphics.Paint
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.util.Log
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuSectionComboItemBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.adapter.listener.OnQuantityChocolateComboListener
import com.ricepo.app.features.menu.adapter.listener.QuantityDecorator
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.model.localize
import com.ricepo.app.restaurant.data.HotImage
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.span.CenteredImageSpan
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by Thomsen on 05/07/2021.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class MenuSectionComboItemHolder(
  private val binding: MenuSectionComboItemBinding,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty
) :
  SectionItemHolder(binding.root) {

  val mapper = MenuMapper()

  /**
   * [position] the ui model list index
   */
  fun bind(
    model: MenuUiModel.MenuComboItem,
    position: Int,
    size: Int,
    restPadding: (value: Int) -> Unit = {}
  ) {
    // completing the background
    setItemBackground(model, position, size, restPadding)

    binding.btnFoodOption.isVisible = (model.food != null)

    val food = model.food ?: return
    val restaurant = model.restaurant

    bindView(food, restaurant, position)
    bindQtyView(food)

    // qty operation listener
    OnQuantityChocolateComboListener(
      binding, food, position, restaurant,
      model.isRootClick, addFood, minusFood
    )
  }

  private fun setItemBackground(
    model: MenuUiModel.MenuComboItem,
    position: Int,
    size: Int,
    restPadding: (value: Int) -> Unit = {}
  ) {
    val params = binding.root.layoutParams
    binding.root.setBackgroundResource(0)
    binding.dividerFood.isVisible = true
    if (params is RecyclerView.LayoutParams) {
      // horizontal scroll aligned left margin

      val width = DisplayUtil.getScreenWidth(binding.root.context) -
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_44dp)

      val height = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_354dp)
      var leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_combo_card_margin)
      var rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_combo_card_margin)
      if (position == 0) {
        leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      } else if (position == (size - 1)) {
        rightMargin = ResourcesUtil.getDimenPixelOffset(binding.root, R.dimen.sw_card_margin)
      }
      params.width = width
      params.height = height
      params.leftMargin = leftMargin
      params.rightMargin = rightMargin
      binding.root.layoutParams = params

      binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card)
    }
  }

  fun bindQtyView(food: Food) {
    val selectedCount = food.selectedCount ?: 0
    binding.btnFoodOption.isPlus = food.isPlus
    binding.btnFoodOption.isMinus = food.isMinus
    QuantityDecorator.bindQtyChocolateCombo(binding, selectedCount)
    food.isPlus = null
    food.isMinus = null
  }

  private fun getTextLineContent(textView: TextView?, line: Int, src: String?): String {
    var result: String = ""
    if (textView == null || src.isNullOrEmpty()) {
      return result
    }
    Log.v("mark", "$line--line-->${textView.lineCount}")
    if (line > textView.lineCount) {
      return result
    }
    val layout = textView.layout
    val sb = StringBuilder(src)
    Log.v("mark", "--start-${layout.getLineStart(line)}----end---${layout.getLineEnd(line)}")
    return sb.subSequence(layout.getLineStart(line), layout.getLineEnd(line)).toString()
  }

  private fun bindView(food: Food, restaurant: Restaurant?, position: Int) {

    // set combo image
    setComboImage(food, binding.ivComboFood)

    binding.tvFoodName.text = bindName(food)

    binding.tvSeeMore.text = ResourcesUtil.getString(com.ricepo.style.R.string.menu_combo_see_more)

    binding.tvFoodInfoOne.isVisible = (food.description != null)
    binding.tvFoodInfoTwo.isVisible = (food.description != null)

    if (food.description != null) {
      val des = food.description?.localize()
      binding.tvFoodInfoOne.text = des
      binding.tvFoodInfoTwo.text = des

      binding.tvFoodInfoOne.post {
        // 获取第一个textview 显示的内容
        val lineContent = getTextLineContent(binding.tvFoodInfoOne, 0, des)
        if (TextUtils.equals(des, lineContent)) { // 可以显示完整
          binding.tvFoodInfoOne.visibility = View.GONE
          binding.tvFoodInfoTwo.text = des
        } else { // 显示不完整,需要分行
          binding.tvFoodInfoOne.visibility = View.VISIBLE
          val srcTwoContent = des?.substring(lineContent.length, des.length)
          binding.tvFoodInfoTwo.text = srcTwoContent
        }
      }
    }

    setFoodPrice(food, restaurant)

    if (food.available == false) {
      binding.laySectionItem.isEnabled = false
      binding.laySectionItem.alpha = 0.2f
      binding.btnFoodOption.isEnabled = false
      // recyclerview animations handle the itemView alpha animation
      // making it not work when show
    } else {
      binding.laySectionItem.isEnabled = true
      binding.laySectionItem.alpha = 1f
      binding.btnFoodOption.isEnabled = true
    }

    if (binding.laySectionItem.isEnabled) {
      binding.rlCombo.setOnClickListener {
        forwardComboDetailsRoute(binding.root.context, food, restaurant, position)
      }
      binding.tvFoodInfoOne.setOnClickListener {
        forwardComboDetailsRoute(binding.root.context, food, restaurant, position)
      }
      binding.tvFoodInfoTwo.setOnClickListener {
        forwardComboDetailsRoute(binding.root.context, food, restaurant, position)
      }
      binding.llComboDetails.setOnClickListener {
        forwardComboDetailsRoute(binding.root.context, food, restaurant, position)
      }
    }
  }

  private fun forwardComboDetailsRoute(
    context: Context,
    food: Food,
    restaurant: Restaurant?,
    position: Int
  ) {
    FeaturePageRouter.navigateCombo(context, food, restaurant, position, null, null)
  }

  private fun bindName(food: Food): SpannableStringBuilder {
    return if (food.featured == true) {
      val hotImage = ResourcesUtil.getDrawable(
        HotImage().localize(),
        binding.root.context
      ) ?: return SpannableStringBuilder().append(food.name.localize())
      hotImage.setBounds(
        0, 0,
        ResourcesUtil.getDimenPixelOffset(
          binding.root,
          com.ricepo.style.R.dimen.sw_36dp
        ),
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_26dp)
      )
      val imageSpan = CenteredImageSpan(hotImage)
      val spanText = SpannableStringBuilder(" ")
      spanText.append(food.name.localize())
      spanText.setSpan(imageSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
      spanText
    } else {
      SpannableStringBuilder().append(food.name.localize())
    }
  }

  private fun setFoodReward(food: Food) {
    binding.tvFoodPrice.let {
      it.text = "${mapper.calcCoinCount(food.point)}"
    }
  }

  private fun setFoodPrice(food: Food, restaurant: Restaurant?) {
    binding.tvFoodPrice.text = mapper.formatPriceByRestaurant(food.price, restaurant)
    binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))

    binding.tvFoodOriginalPrice.isVisible = (food.originalPrice != null)
    binding.tvFoodOriginalPrice.text = null
    food.originalPrice?.let {
      binding.tvFoodOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or
        Paint.ANTI_ALIAS_FLAG
      binding.tvFoodOriginalPrice.text = mapper.formatPriceByRestaurant(it, restaurant)
      binding.tvFoodOriginalPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)

      binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.green, binding.root))
    }
  }

  private fun setComboImage(
    food: Food?,
    ivComboView: ImageView,
  ) {
    val width = DisplayUtil.getScreenWidth(binding.root.context) -
      ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_22dp) -
      ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_44dp)
//        val layoutParams = RelativeLayout.LayoutParams(ivComboView.layoutParams)
//        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
//        layoutParams.height = (width / 1.5).toInt()
//        layoutParams.width = width
//        ivComboView.layoutParams = layoutParams
    if (food?.comboImage != null) {
      ImageLoader.load(ivComboView, food.comboImage?.localize(), com.ricepo.style.R.drawable.bg_combo)
    } else {
      ivComboView.setImageResource(com.ricepo.style.R.drawable.bg_combo)
    }
  }

  companion object {
    fun create(
      parent: ViewGroup,
      addFood: FunFoodQty,
      minusFood: FunFoodQty
    ): MenuSectionComboItemHolder {
      val binding = MenuSectionComboItemBinding.inflate(LayoutInflater.from(parent.context))
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      return MenuSectionComboItemHolder(binding, addFood, minusFood)
    }
  }
}
