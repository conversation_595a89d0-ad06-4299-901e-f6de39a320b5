package com.ricepo.app.features.coupon

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.add
import androidx.fragment.app.commit
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityCouponBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.coupon.data.ShowCouponCase
import com.ricepo.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

//
// Created by <PERSON><PERSON> on 4/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_COUPON)
class CouponActivity : BaseActivity() {

  val viewModel: CouponViewModel by viewModels()

  private lateinit var binding: ActivityCouponBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityCouponBinding.inflate(layoutInflater)
    setContentView(binding.root)
    supportFragmentManager.commit {
      add<CouponFragment>(R.id.coupon_container)
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_LOGIN &&
        viewModel.getShowCouponCase() == ShowCouponCase.all
      ) {
        backEvent()
      }
    }
  }
}
