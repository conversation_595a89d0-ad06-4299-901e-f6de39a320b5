package com.ricepo.app.features.order

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.view.View
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityOptionsCompat
import androidx.core.util.Pair
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.android.gms.maps.model.LatLng
import com.ricepo.app.R
import com.ricepo.app.consts.OptionMessage
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.OrderCache
import com.ricepo.app.databinding.ActivityOrderBinding
import com.ricepo.app.databinding.LayoutCardDividerBinding
import com.ricepo.app.databinding.OrderDeliveryProofItemBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.checkout.data.OrderCellInfo
import com.ricepo.app.features.menu.MenuGroupUseCase
import com.ricepo.app.features.order.data.OrderState
import com.ricepo.app.features.payment.data.PaymentHandleMode
import com.ricepo.app.features.preview.ImagePreviewActivity
import com.ricepo.app.message.NotificationService
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderStatus
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.utils.OSUtils
import com.ricepo.app.utils.checkRating
import com.ricepo.app.view.MenuFoodItemView
import com.ricepo.app.view.OrderItemView
import com.ricepo.base.BaseActivity
import com.ricepo.base.BaseApplication
import com.ricepo.base.adapter.BindListAdapter
import com.ricepo.base.adapter.OnBindViewListener
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.consts.TabMode
import com.ricepo.base.data.common.kv.KeyValueHelper
import com.ricepo.base.data.pref.CommonPref
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.uiSubscribe
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.DriverPoint
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.base.remoteconfig.RiceRemoteConfig
import com.ricepo.base.tools.IntentUtils
import com.ricepo.base.tools.StringUtils
import com.ricepo.base.tools.showErrorView
import com.ricepo.base.view.DialogFacade
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.monitor.firebase.FirebaseBaseEvent
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.time.TimeMonitorConfig
import com.ricepo.monitor.time.TimeMonitorManager
import com.ricepo.network.EnvNetwork.RECEIPT_URL
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

//
// Created by Thomsen on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_ORDER)
class OrderActivity : BaseActivity() {

  val orderViewModel: OrderViewModel by viewModels()

  @Inject
  lateinit var orderMapper: OrderMapper

  @Inject
  lateinit var menuGroupUseCase: MenuGroupUseCase

  private var position: Int = 0

  /**
   * the page first loaded
   */
  private var isFirst = true

  /**
   * the first show driver rating
   */
  private var isFirstShowRating = true

  private lateinit var binding: ActivityOrderBinding

  // cache the refer
  private var cachedReferInfo: ReferInfo? = null
  private var isOrderDataLoaded = false

  override fun onCreate(savedInstanceState: Bundle?) {
    TimeMonitorManager.instance.getTimeMonitor(TimeMonitorConfig.TIME_MONITOR_ID_APPLICATION_START)
      .recordingTimeTag("Order-onCreate")
    super.onCreate(savedInstanceState)
    binding = ActivityOrderBinding.inflate(layoutInflater)
    setContentView(binding.root)

    position = intent.getIntExtra(FeaturePageConst.PARAM_PROFILE_ORDER_POSITION, 0)
    val order = intent.getParcelableExtra<Order>(FeaturePageConst.PARAM_ORDER)
    entrance = intent.getStringExtra(FeaturePageConst.PARAM_PAGE_ENTRANCE)
    val paymentError = intent.getStringExtra(FeaturePageConst.PARAM_PAGE_ORDER_PAYMENT_ERROR)
    val orderId = intent.getStringExtra(FeaturePageConst.PARAM_ORDER_ID)
    if (!paymentError.isNullOrEmpty()) {
      DialogFacade.showAlert(this, paymentError)
    }

    setupListener()
    initViewModel()
    TimeMonitorManager.instance.getTimeMonitor(TimeMonitorConfig.TIME_MONITOR_ID_APPLICATION_START)
      .recordingTimeTag("Order-onCreate-noInitOrder")
    orderViewModel.initBaseOrder(order, orderId)

    orderViewModel?.restaurant = intent.getParcelableExtra<Restaurant?>(FeaturePageConst.PARAM_RESTAURANT)
    TimeMonitorManager.instance.getTimeMonitor(TimeMonitorConfig.TIME_MONITOR_ID_APPLICATION_START)
      .recordingTimeTag("Order-onCreate-Over")

  }

  private fun checkNotificationPermission() {
    if (!NotificationService.checkNotifyPermission(this)) {

      OrderCache.requirePermission { require ->
        if (require) {
          lifecycleScope.launch {
            OrderCache.saveRequirePermTime()
          }
          DialogFacade.showPrompt(this, com.ricepo.style.R.string.notification_permission) {
            NotificationService.toNotificationSettings(this)
          }
        }
      }
    }
  }

  override fun onStart() {
    super.onStart()
    bindViewModel()
    orderViewModel.startOrderUpdateTask(isFirst)
    isFirst = false
    TimeMonitorManager.instance.getTimeMonitor(TimeMonitorConfig.TIME_MONITOR_ID_APPLICATION_START)
      .end("Order-onStart", false)
  }

  override fun onStop() {
    super.onStop()
    stopOrderTask()
  }

  private fun stopOrderTask() {
    orderViewModel.clearOrderRefreshTimer()
    orderViewModel.clearPendingWaringTimer()
  }

  private fun setupListener() {
    binding.srlOrderContent?.setOnRefreshListener {
      orderViewModel.onRefreshLoading.onNext(false)
    }

    binding.inOrderStatus.tvOrderDelivery.viewTreeObserver
      .addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
        override fun onGlobalLayout() {
          val newText: CharSequence = binding.inOrderStatus.tvOrderDelivery.autoSplitText()
          if (!TextUtils.isEmpty(newText)) {
            binding.inOrderStatus.tvOrderDelivery.text = newText
            binding.inOrderStatus.tvOrderDelivery.viewTreeObserver.removeOnGlobalLayoutListener(this)
          }
        }
      })

    // hide the scshare
    binding.scShare.isVisible = false
  }

  private fun initViewModel() {
    orderViewModel.observerOrderError
      .uiSubscribe()
      .subscribe {
        val option = it
        if (option.errorInput != null) {
          binding.flOrderPage.showErrorView(option.errorInput)
        } else if (option.message?.isEmpty() == false) {
          if (option.message == OptionMessage.DATA_EMPTY) {
            backEvent()
          } else {
            DialogFacade.showAlert(this, option.message ?: "")
          }
        }
      }

    orderViewModel.observePendingWarningTimer()
      .uiSubscribe()
      .subscribe {
        if (it is String) {
          binding.inOrderStatus.tvOrderPendingWarning.visibility = View.VISIBLE
          binding.inOrderStatus.tvOrderPendingWarning.text = it
        } else {
          binding.inOrderStatus.tvOrderPendingWarning.visibility = View.GONE
        }
      }
  }

  private fun bindViewModel() {
    orderViewModel.observeOrderUpdate(this)
      .uiSubscribe()
      .subscribe {
        val option = it
        binding.srlOrderContent?.finishRefresh()
        if (it.order != null) {
          clearErrorView(binding.flOrderPage)
        }
        if (TextUtils.isEmpty(binding.inOrderStatus.tvOrderStatus.text)) {
          checkNotificationPermission()
        }
        setOrderView(option.order, option.orderBrand, option.orderLast4)
        setOrderMapView(option.order, option.driverLocation, option.driverPolyline)
        setOrderItemsView(option.order)
        setOrderFeesView(option.cellInfos)

        // order data success
        isOrderDataLoaded = true
        checkAndShowShareCard()
      }
    orderViewModel.driverPhone.observe(this) {
      DialogFacade.showPrompt(this@OrderActivity, it) {
        IntentUtils.intentPhone(this, lifecycleScope, it)
      }
    }

    lifecycleScope.launch {
      orderViewModel.referInfoFlow.collectLatest { referInfo ->
        // get the refer shre
        cachedReferInfo = referInfo
        checkAndShowShareCard()
      }
    }
  }

  private fun navigateDriverRating(order: Order) {
    FeaturePageRouter.navigateDriverRating(
      this@OrderActivity, order, 0
    )
  }

  private fun setOrderView(order: Order?, brand: String?, last4: String?) {
    if (order == null) return
    // status
    binding.inOrderStatus.tvOrderStatus.text = ResourcesUtil.getString(
      "status_${
      order.obtainState().replace(
        "-",
        "_"
      )
      }"
    )

    // message
    if (order.message == null) {
      binding.inOrderStatus.tvOrderMessage.visibility = View.GONE
    } else {
      binding.inOrderStatus.tvOrderMessage.visibility = View.VISIBLE
      binding.inOrderStatus.tvOrderMessage.text = order.message
    }

    // delivery method
    showDeliveryMethod(order)

    // rate driver button
    showRatingButton(order)

    // restaurant name
    binding.inOrderRestaurant.tvOrderRestaurantName.text = order.restaurant?.name?.localize()
    binding.inOrderRestaurant.tvOrderRestaurnatDivider.isVisible = true

    // group order flag
    binding.inOrderRestaurant.tvOrderGroupOrder.isVisible = (order.group != null)

    // created at
    val createdAt = order.createdAt
    if (createdAt != null) {
      binding.inOrderRestaurant.tvOrderCreatedAt.text = orderMapper.formatTime(createdAt, "hh:mm a")
    }

    // show earned points only confirmed or created and group owner
    val validateState = (order.status == OrderStatus.Confirmed || order.status == OrderStatus.Created) &&
      (order.group?.deviceId == null || BaseApplication.mDeviceId == order.group?.deviceId)
    val points = order.reward?.amount ?: 0
    if (points > 0 && validateState) {
      val earnPoint = ResourcesUtil.getString(
        com.ricepo.style.R.string.order_reward_amount,
        orderMapper.calcCoinCount(points)
      )
      binding.inOrderRestaurant.tvOrderEarnPoint.text = StringUtils.convertCoinPlainToImage(
        binding.root, earnPoint
      )
      binding.inOrderRestaurant.tvOrderEarnPoint.isVisible = true
    } else {
      binding.inOrderRestaurant.tvOrderEarnPoint.isVisible = false
    }

    // order payment
    setOrderPaymentView(order, brand, last4)

    // operator button
    setOrderOperatorView(order)

    // show app rating
    checkRating(order)

    // show update address
    showUpdateAddress(order)

    // show diary Image
//    showDiaryImage(order)
  }

  private fun showDiaryImage(order: Order) {
    val image = order.diary?.image
    binding.ivDiaryButton.isVisible = false
    binding.inRedLink.root.isVisible = !(image.isNullOrEmpty())

    binding.inRedLink.tvShareTopTitle.text = order.diary?.title?.localize()
    val btnText = order.diary?.button?.localize()
    binding.inRedLink.tvShareSubtitle.text = btnText
    binding.inRedLink.ivShareArrow.isVisible = !btnText.isNullOrEmpty()

    binding.inRedLink.root.clickWithTrigger {
      image?.let {
        FeaturePageRouter.navigateReferImage(this, order)
      }
    }
  }

  private fun showDeliveryMethod(order: Order) {
    val isPickupMode = (order.delivery == null)
    binding.layOrderPickup.isVisible = isPickupMode
    binding.tvDividerPickupTop.isVisible = isPickupMode
    binding.tvDividerPickup.isVisible = isPickupMode
    if (isPickupMode) {
      // pickup mode
      val address = order.restaurant?.address?.innerItemValue?.formatted
      binding.inCheckoutPickup.tvPickupAddress.text = address
      binding.inCheckoutPickup.groupPickupType.visibility = View.GONE
      binding.inCheckoutPickup.clPickupAddress.setOnClickListener {
        val location = order.restaurant?.address?.innerItemValue?.location?.coordinates
        val lat = location?.getOrNull(1) ?: 0
        val lon = location?.getOrNull(0) ?: 0
        IntentUtils.intentGoogleMap(this, "$lat", "$lon", address)
      }
    } else {
      val orderStatus = order.status
      // don't show delivery by cancelled declined and order delta price
      if (orderStatus != OrderStatus.Cancelled && order.original == null &&
        orderStatus != OrderStatus.Declined
      ) {
        // delivery provider
        if (order.delivery?.provider != null ||
          order.restaurant?.delivery?.provider != null
        ) {
          binding.inOrderStatus.tvOrderDelivery.visibility = View.GONE
          binding.inOrderStatus.tvOrderDeliveryRicepo.visibility = View.VISIBLE
          val provider = order.delivery?.provider ?: order.restaurant?.delivery?.provider
          binding.inOrderStatus.tvOrderDeliveryRicepo.text =
            if (provider != null && !"ricepo".equals(provider, true)) {
              ResourcesUtil.getString(com.ricepo.style.R.string.delivered_by_others, provider.capitalize())
            } else {
              ResourcesUtil.getString(com.ricepo.style.R.string.delivered_by_ricepo)
            }
        } else {
          showDeliveryByRestaurant(order)
        }
      } else {
        binding.inOrderStatus.tvOrderDeliveryRicepo.isVisible = false
        binding.inOrderStatus.tvOrderDelivery.isVisible = false
      }
    }

    // proof image
    showDeliveryProofImage(order)
  }

  /**
   * driver proof image adapter
   */
  private var imageAdapter: BindListAdapter<View, String>? = null

  private fun showDeliveryProofImage(order: Order) {
    val proof = order.delivery?.proof
    binding.inOrderStatus.tvDeliveryProof.isVisible = (proof != null)
    binding.inOrderStatus.rvDeliveryProof.isVisible = (proof != null)

    if (imageAdapter == null) {
      val count = 5
      val sideMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_40)
      val paddingRight = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_8)
      val size = (DisplayUtil.getScreenWidth() - (sideMargin * 2) - (paddingRight * 4)).div(count)

      val layoutManager = GridLayoutManager(this, count)
      binding.inOrderStatus.rvDeliveryProof.layoutManager = layoutManager
      imageAdapter = BindListAdapter<View, String>(
        proof?.photos, R.layout.order_delivery_proof_item,
        object : OnBindViewListener<View, String> {
          override fun onBindView(view: View, value: String?, position: Int) {
            val proofBinding = OrderDeliveryProofItemBinding.bind(view)
            val params = RecyclerView.LayoutParams(size, size)
            if (position in 1..3) {
              params.rightMargin = paddingRight
            }
            proofBinding.root.layoutParams = params
            proofBinding.ivImage.setImageDrawable(null)
            if (!value.isNullOrEmpty()) {
              ImageLoader.load(proofBinding.ivImage, value)

              proofBinding.root.clickWithTrigger {
                showPreviewImage(proofBinding, value)
              }
            }
          }
        }
      )
      binding.inOrderStatus.rvDeliveryProof.adapter = imageAdapter
    } else {
      imageAdapter?.dataSet = proof?.photos
      imageAdapter?.notifyDataSetChanged()
    }

    binding.inOrderStatus.tvDeliveryProof.text = ResourcesUtil.getString(
      com.ricepo.style.R.string.order_driver_message, proof?.message ?: ""
    )
  }

  private fun showPreviewImage(proofBinding: OrderDeliveryProofItemBinding, path: String) {
    val intent = Intent(this, ImagePreviewActivity::class.java)
    intent.putExtra(ImagePreviewActivity.BUNDLE_PREVIEW_IMAGE, path)
    val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
      this,
      Pair(proofBinding.ivImage, ImagePreviewActivity.SHARE_PREVIEW_IMAGE)
    )
    ActivityCompat.startActivity(this, intent, activityOptions.toBundle())
  }

  private fun showDeliveryByRestaurant(order: Order) {
    binding.inOrderStatus.tvOrderDeliveryRicepo.visibility = View.GONE
    binding.inOrderStatus.tvOrderDelivery.visibility = View.VISIBLE

    // already init delivery text
    if (binding.inOrderStatus.tvOrderDelivery.text.isNotEmpty()) return

    val spanBuilder = SpannableStringBuilder()
    val restaurantText = ResourcesUtil.getString(
      com.ricepo.style.R.string.delivered_by,
      order.restaurant?.name?.localize()?.toUpperCase() ?: ""
    )
    spanBuilder.append(restaurantText)

    val text = ResourcesUtil.getString(com.ricepo.style.R.string.delivered_by_phone)
    spanBuilder.append(text)

    val phone = order.restaurant?.phone ?: ""

    if (phone.trim().isEmpty()) {
      binding.inOrderStatus.tvOrderDelivery.text = spanBuilder
      return
    }

    val start = restaurantText.length
    val end = start + text.length

    val underlineSpan = UnderlineSpan()
    spanBuilder.setSpan(underlineSpan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

    val clickableSpan = object : ClickableSpan() {
      override fun onClick(widget: View) {
        IntentUtils.intentPhone(this@OrderActivity, lifecycleScope, phone)
      }
    }
    spanBuilder.setSpan(clickableSpan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

    // for clickable
    binding.inOrderStatus.tvOrderDelivery.movementMethod = LinkMovementMethod.getInstance()

    // for clickable color
    val colorSpan = ForegroundColorSpan(ResourcesUtil.getColor(com.ricepo.style.R.color.mainText, binding.root))
    spanBuilder.setSpan(colorSpan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

    binding.inOrderStatus.tvOrderDelivery.text = spanBuilder
  }

  private fun showRatingButton(order: Order) {
    val showRatingButton = order.allowRating ?: false
    binding.inOrderStatus.btnOrderRating.isVisible = showRatingButton
    if (showRatingButton && isFirstShowRating && !binding.scShare.isCardShowing) {
      isFirstShowRating = false
      navigateDriverRating(order)
    }
    binding.inOrderStatus.btnOrderRating.clickWithTrigger {
      navigateDriverRating(order)
    }
  }

  private fun setOrderPaymentView(order: Order, brand: String?, last4: String?) {

    binding.inOrderPayment.root.isVisible = true

    // address
    val address = orderMapper.getOrderAddressFormatted(order)
    binding.inOrderPayment.tvOrderAddress.text = address
    binding.inOrderPayment.tvOrderAddress.isVisible = address.isNotEmpty()

    // phone
    val phone = order.customer?.phone ?: ""
    binding.inOrderPayment.tvOrderPhone.text = phone
    binding.inOrderPayment.tvOrderPhone.isVisible = phone.isNotEmpty()

    if (OrderState.PENDING == order.status) {
      // show payment button if order is pending
      binding.inOrderStatus.btnOrderPay.visibility = View.VISIBLE
      binding.inOrderStatus.btnOrderPay.clickWithTrigger {
        // show refresh loading from payment page
        isFirst = true
        stopOrderTask()
        // auto payment
        FeaturePageRouter.navigatePayment(
          this,
          paymentHandle = PaymentHandleMode.autoPayment, order = order
        )
      }

      binding.inOrderPayment.tvOrderPaymentMethod.visibility = View.GONE
    } else {
      binding.inOrderStatus.btnOrderPay.visibility = View.GONE
      binding.inOrderPayment.tvOrderPaymentMethod.visibility = View.GONE

      // show image when wechat and alipay payment
      when (order.stripe?.source?.stringValue?.toLowerCase()) {
        PaymentOwnMethod.ALIPAY.toLowerCase() -> {
          binding.inOrderPayment.tvOrderPaymentMethod.visibility = View.GONE
          binding.inOrderPayment.ivOrderPaymentMethod.visibility = View.VISIBLE
          binding.inOrderPayment.ivOrderPaymentMethod.setImageResource(com.ricepo.style.R.drawable.ic_alipay)
        }
        PaymentOwnMethod.WECHAT.toLowerCase() -> {
          binding.inOrderPayment.tvOrderPaymentMethod.visibility = View.GONE
          binding.inOrderPayment.ivOrderPaymentMethod.visibility = View.VISIBLE
          binding.inOrderPayment.ivOrderPaymentMethod.setImageResource(com.ricepo.style.R.drawable.ic_wechatpay)
        }
        PaymentOwnMethod.UNION_PAY.toLowerCase() -> {
          binding.inOrderPayment.tvOrderPaymentMethod.visibility = View.GONE
          binding.inOrderPayment.ivOrderPaymentMethod.visibility = View.VISIBLE
          binding.inOrderPayment.ivOrderPaymentMethod.setImageResource(com.ricepo.style.R.drawable.ic_unionpay)
        }
        else -> {}
      }

      // condition for stripe intent
      if (brand != null && last4 != null) {
        binding.inOrderPayment.tvOrderPaymentMethod.visibility = View.VISIBLE
        binding.inOrderPayment.ivOrderPaymentMethod.visibility = View.GONE
        binding.inOrderPayment.tvOrderPaymentMethod.text = "${brand.capitalize()} - $last4"
      }
    }
  }

  private fun setOrderOperatorView(order: Order) {
    val status = order.status

    // hidden help button if order is canceled or declined
    // and group member not show
    binding.tvOrderHelp.isVisible = !(
      OrderState.CANCELLED == status ||
        OrderState.CANCELED == status || OrderState.DECLINED == status
      ) &&
      (order.group?.deviceId == null || BaseApplication.mDeviceId == order.group.deviceId)
    binding.tvOrderHelp.clickWithTrigger {
      orderViewModel.clickOrderConfigHelp(this)
    }

    binding.inOrderOperator.root.isVisible = true

    // call driver
//    val driverPhone = order.delivery?.courier?.phone
//    if (!driverPhone.isNullOrEmpty()) {
//      // show call driver button if phone is not empty
//      binding.inOrderOperator.layOrderCallDriver.visibility = View.VISIBLE
//      binding.inOrderOperator.layOrderCallDriver.clickWithTrigger {
//        orderViewModel.callPhoneMasking(order)
//      }
//    } else {
    binding.inOrderOperator.layOrderCallDriver.visibility = View.GONE
//    }

    // renew order
    binding.inOrderOperator.layOrderReorder.isVisible = (order.original == null)
    binding.inOrderOperator.layOrderReorder.clickWithTrigger {
      checkRenewOrder()
      AnalyticsFacade.logEvent(FirebaseBaseEvent(), FirebaseEventName.rReorder)
    }

    // show order receipt if order confirmed
    binding.inOrderOperator.layOrderReceipt.isVisible = (OrderState.CONFIRMED == status)
    binding.inOrderOperator.layOrderReceipt.clickWithTrigger {
      val url = "$RECEIPT_URL${order.id ?: ""}"
      IntentUtils.intentBrowser(this, url)
      AnalyticsFacade.logEvent(FirebaseBaseEvent(), FirebaseEventName.rGetReceipt)
    }

    // chat
    binding.inOrderOperator.layOrderChat.isVisible = (order.allowChat == true)
    if (order.allowChat == true) {
      binding.inOrderOperator.layOrderChat.clickWithTrigger {
        FeaturePageRouter.navigateOrderSupportChat(this)
      }
    }

    // divider
    binding.inOrderOperator.dividerCallDriver.isVisible = (order.original == null) ||
      (OrderState.CONFIRMED == status) || (order.allowChat == true)
    binding.inOrderOperator.dividerOrderReorder.isVisible = (OrderState.CONFIRMED == status) || (order.allowChat == true)
    binding.inOrderOperator.dividerReceipt.isVisible = (order.allowChat == true)
  }

  private fun checkRenewOrder() {
    lifecycleScope.launch {
      val groupInfo = GroupOrderCache.getOrderSuspend()
      // can not reorder if user not join any menu group
      if (groupInfo == null) {
        // renew order
        orderViewModel.renewOrder(this@OrderActivity)
      } else {
        // show alert with have group info
        // REFACTOR: extract to method
        val restaurantName = groupInfo?.restaurant?.name?.localize() ?: ""
        DialogFacade.showPrompt(
          this@OrderActivity,
          message = ResourcesUtil.getString(
            com.ricepo.style.R.string.group_please_exit_subtitle, restaurantName
          ),
          title = ResourcesUtil.getString(com.ricepo.style.R.string.group_please_exit_title),
          positiveId = com.ricepo.style.R.string.group_please_exit_button
        ) {
          val groupId = groupInfo?.groupId
          val restId = groupInfo?.restaurant?.id
          FeaturePageRouter.navigateMenuAfterSubscription(
            this@OrderActivity,
            Restaurant(restId), groupId = groupId
          )
        }
      }
    }
  }

  private var isMapAdded = false
  private var fragmentMap: OrderMapFragment? = null
  private var fragmentMapbox: OrderMapboxFragment? = null

  private fun setOrderMapView(order: Order?, driverLocation: DriverPoint?, driverPolyline: List<LatLng>?) {
    if (order == null) return
    val status = order.obtainState()
    val hideMapStatus = listOf(
      OrderState.PENDING, OrderState.CANCELED,
      OrderState.CANCELLED, OrderState.DECLINED
    )
    val (restaurantCoordinate, customerCoordinate) = orderMapper.mapCoordinate(order)
    // hide map if order is not confirmed or no restaurant address / delivery address
    if (hideMapStatus.contains(status) ||
      restaurantCoordinate == null ||
      customerCoordinate == null
    ) {
      // hide order map
      binding.inOrderMap.llOrderMap.visibility = View.GONE
    } else {
      binding.inOrderMap.llOrderMap.visibility = View.VISIBLE
      val transaction = supportFragmentManager.beginTransaction()

      if (OSUtils.isGMSInstalledAndEnabled()) {
        if (!isMapAdded) {
          fragmentMap = OrderMapFragment()
          fragmentMap?.let {
            transaction.add(R.id.frame_order_map, it)
          }
          isMapAdded = true
          transaction.commitNowAllowingStateLoss()
          // execute async transaction commit
          supportFragmentManager.executePendingTransactions()
        }
        fragmentMap?.renderMap(order, driverLocation, driverPolyline)
      } else {
        val boxDriverPolyline = driverPolyline?.map {
          com.mapbox.mapboxsdk.geometry.LatLng(it.latitude, it.longitude)
        }
        if (!isMapAdded) {
          fragmentMapbox = OrderMapboxFragment()
          fragmentMapbox?.let {
            transaction.add(R.id.frame_order_map, it)
          }
          isMapAdded = true
          transaction.commitNowAllowingStateLoss()
          supportFragmentManager.executePendingTransactions()
        }
        fragmentMapbox?.renderMap(order, driverLocation, boxDriverPolyline)
      }

      // show delivery note
      binding.inOrderMap.cardDeliveryNote.isVisible = (order.delivery?.note != null)
      binding.inOrderMap.tvDeliveryNote.text = order.delivery?.note?.localize()
      binding.inOrderMap.tvDeliveryNote.isSelected = true
    }
  }

  private fun setOrderItemsView(order: Order?) {
    // show order info layout
    binding.llOrderInfo.isVisible = true

    binding.llOrderItems.removeAllViews()
    val lastIndex = (order?.items?.size ?: 0) - 1

    order?.items?.forEachIndexed { index, it ->
      val view = MenuFoodItemView(this)
      view.initiate(it, order.restaurant)
      binding.llOrderItems.addView(view)
      if (index != lastIndex) {
        val dividerBinding = LayoutCardDividerBinding.inflate(layoutInflater)
        binding.llOrderItems.addView(dividerBinding.root)
      }
    }
  }

  private fun setOrderFeesView(cellInfos: List<OrderCellInfo>) {
    binding.llOrderFees.removeAllViews()
    binding.llOrderFees.isVisible = !cellInfos.isNullOrEmpty()
    val lastIndex = cellInfos.size - 1
    cellInfos.forEachIndexed { index, it ->
      if (index == lastIndex) {
        val dividerBinding = LayoutCardDividerBinding.inflate(layoutInflater)
        val params = LinearLayout.LayoutParams(
          LinearLayout.LayoutParams.MATCH_PARENT,
          LinearLayout.LayoutParams.WRAP_CONTENT
        )
        params.leftMargin = ResourcesUtil.getDimenPixelOffset(
          binding.root, com.ricepo.style.R.dimen.card_side_margin
        )
        params.rightMargin = ResourcesUtil.getDimenPixelOffset(
          binding.root, com.ricepo.style.R.dimen.card_side_margin
        )
        dividerBinding.root.layoutParams = params
        binding.llOrderFees.addView(dividerBinding.root)
      }
      val view = OrderItemView(this)
      view.initiate(it)

      if (it.rightText.isNullOrEmpty()) {
        // comments
        val dividerBinding = LayoutCardDividerBinding.inflate(layoutInflater)
        view.binding.guidelineItemLeft.setGuidelineBegin(0)
        view.binding.tvLeftText.isSingleLine = false
        binding.llOrderItems.addView(dividerBinding.root)
        binding.llOrderItems.addView(view)
      } else {
        // order fees
        binding.llOrderFees.addView(view)
      }
    }
  }

  /**
   * smooth scroll view to top
   */
  private fun smoothScrollTop() {
    binding.nslCheckoutContent.smoothScrollTo(0, 0)
  }

  override fun onBackPressed() {
    if (entrance == FeaturePageConst.PAGE_CHECKOUT) {
      val deliveryMode = if (orderViewModel.order?.delivery == null) {
        TabMode.MODE_PICKUP
      } else {
        null
      }
      FeaturePageRouter.navigateProfileAfterCheckout(this, deliveryMode)
    }
    intent.putExtra(FeaturePageConst.PARAM_PROFILE_HISTORY_ORDER, orderViewModel.order)
    intent.putExtra(FeaturePageConst.PARAM_PROFILE_ORDER_POSITION, position)
    backResultEvent(intent)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_DRIVER_RATING ||
        requestCode == FeaturePageConst.REQUEST_CODE_HISTORY_ORDER
      ) {
        val order = data?.getParcelableExtra<Order>(FeaturePageConst.PARAM_PROFILE_HISTORY_ORDER)
        if (order != null) {
          showRatingButton(order)
        }
      }
    }
  }

  private fun showUpdateAddress(order: Order) {
    if (order.delivery?.addressUpdated == true) else return
    if (CommonPref.getOrderUpdateAddress(this, order.id) == null) else return
    lifecycleScope.launch {
      val localAddress = withContext(Dispatchers.IO) {
        AddressCache.getAddressSuspend()
      }
      val remoteAddress = order.delivery?.address?.innerItemValue
      if (localAddress?.location?.coordinates != remoteAddress?.location?.coordinates ||
        localAddress?.formatted != remoteAddress?.formatted
      ) {
        val message = ResourcesUtil.getString(
          com.ricepo.style.R.string.address_update,
          remoteAddress?.formatted ?: ""
        )
        DialogFacade.showPrompt(
          this@OrderActivity, message,
          negative = {
            CommonPref.saveOrderUpdateAddress(this@OrderActivity, order.id)
          },
          positive = {
            CommonPref.saveOrderUpdateAddress(this@OrderActivity, order.id)
            saveUpdateAddress(remoteAddress)
          }
        )
      }
    }
  }

  private fun saveUpdateAddress(remoteAddress: AddressObj?) {
    val address = remoteAddress ?: return
    lifecycleScope.launch {
      val formatUserAddress = FormatUserAddress(address)
      AddressCache.saveAddress(formatUserAddress)
    }
  }

  /**
   * 检查是否可以显示ShareCard
   * 只有在订单数据加载完成且有refer信息时才显示
   */
  private fun checkAndShowShareCard() {
    if (isOrderDataLoaded && cachedReferInfo != null) {
      updateShareCardView(cachedReferInfo)
    } else if (isOrderDataLoaded && cachedReferInfo == null) {
      // waiting for order
      binding.scShare.isVisible = false
    }
  }

  /**
   * shor share card by refer info share
   */
  private fun updateShareCardView(referInfo: ReferInfo?) {
    if (referInfo != null) {
      binding.scShare.initTitle(referInfo)

      val shouldShowShare = referInfo.share != null
      binding.scShare.isVisible = shouldShowShare
    } else {
      binding.scShare.isVisible = false
    }
  }
}
