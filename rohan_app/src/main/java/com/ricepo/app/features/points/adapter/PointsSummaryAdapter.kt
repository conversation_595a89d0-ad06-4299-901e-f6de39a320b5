package com.ricepo.app.features.points.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.PointsSummaryItemBinding
import com.ricepo.app.model.PointsSummaryModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.localize
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 6/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class PointsSummaryAdapter constructor(
  private val datas: List<PointsSummaryModel>
) : RecyclerView.Adapter<PointsSummaryAdapter.ViewHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
    val binding = PointsSummaryItemBinding.inflate(
      LayoutInflater.from(parent.context)
    )
    binding.root.layoutParams = RecyclerView.LayoutParams(
      RecyclerView.LayoutParams.MATCH_PARENT,
      RecyclerView.LayoutParams.WRAP_CONTENT
    )
    return ViewHolder(binding)
  }

  override fun getItemCount() = datas?.size ?: 0

  override fun onBindViewHolder(holder: ViewHolder, position: Int) {
    val data = datas[position]
    holder.bind(data, position, itemCount)
  }

  class ViewHolder(private val binding: PointsSummaryItemBinding) :
    RecyclerView.ViewHolder(binding.root) {

    private val mapper = BaseMapper()

    init {
      binding.root.clickWithTrigger { view ->
      }
    }

    fun bind(data: PointsSummaryModel, position: Int, size: Int) {
      binding.root.tag = data

      binding.tvPointsTitle.isVisible = (position == 0)
      binding.tvDivider.isVisible = (position == 0)

      if (position == 0 && data.description == null && data.balance == null) {
        binding.tvPointsName.text = ResourcesUtil.getString(com.ricepo.style.R.string.refer_no_record)
        binding.tvPointsName.setTextColor(
          ResourcesUtil.getColor(
            com.ricepo.style.R.color.subText, binding.root.context
          )
        )
      } else {

        binding.tvPointsName.text = data.description?.localize()
        binding.tvPointsName.setTextColor(
          ResourcesUtil.getColor(
            com.ricepo.style.R.color.mainText, binding.root.context
          )
        )
        binding.tvPointsDate.text = "${mapper.formatTime(data.createdAt, "M.dd.yyyy")}"

        val diff = data.balance?.available?.diff ?: 0
        if (diff > 0) {
          binding.tvPoint.text =
            "+$diff ${ResourcesUtil.getString(com.ricepo.style.R.string.redeem_points)}"
        } else {
          binding.tvPoint.text =
            "$diff ${ResourcesUtil.getString(com.ricepo.style.R.string.redeem_points)}"
        }
      }
    }
  }
}
