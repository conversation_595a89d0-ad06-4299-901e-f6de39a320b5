package com.ricepo.app.features.login.repository

import com.ricepo.app.model.UserInformation
import com.ricepo.app.restapi.AuthRestApi
import com.ricepo.base.model.Customer
import com.ricepo.base.model.GlobalConfigModel
import io.reactivex.rxjava3.core.Single
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 7/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class AuthRemoteImpl @Inject constructor(
  private val restApi: AuthRestApi
) : AuthRepository {

  override fun getVcode(phone: String, method: String): Single<Any> {
    val params = HashMap<String, String>()
    params.put("phone", phone)
    params.put("method", method)
    return restApi.getVcode(params)
  }

  override fun login(phone: String, vcode: String): Single<UserInformation> {
    val params = HashMap<String, String>()
    params.put("phone", phone)
    params.put("vcode", vcode)
    return restApi.login(params)
  }

  override fun getCustomer(customerId: String): Single<Customer> {
    return restApi.getCustomer(customerId)
  }

  override fun resetToken(): Single<UserInformation> {
    return restApi.resetToken()
  }

  override suspend fun getCountry(): Map<String, GlobalConfigModel> {
    return restApi.getCountry()
  }
}
