package com.ricepo.app.features.menu.adapter.holder

import android.graphics.Paint
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuGalleryItemBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.adapter.listener.OnGalleryChocolateListener
import com.ricepo.app.features.menu.adapter.listener.QuantityDecorator
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.model.localize
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 28/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class MenuGalleryItemHolder(
  private val binding: MenuGalleryItemBinding,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty
) :
  BaseFoodLargeImageHolder(binding.root) {

  val mapper = MenuMapper()

  fun bind(uiModel: MenuUiModel.MenuGalleryItem, position: Int, size: Int) {
    val food = uiModel.food ?: return
    val restaurant = uiModel.restaurant
    bindView(food, restaurant)
    bindQtyView(food)

    setItemParams(position, size)

    // qty operation listener for gallery use gallery in uiModels position
    val pos = uiModel.galleryPosition ?: position
    val listener = OnGalleryChocolateListener(
      binding, food, pos, addFood, minusFood,
      restaurant = restaurant
    )
    if (uiModel.galleryPosition != null) {
      listener.sectionFoodIndex = position
    }
  }

  private fun setItemParams(position: Int, size: Int) {
    val params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {

      var leftMargin = 0
      var topMargin = 0

      var width = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_270dp)
      var height = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_184dp)

      if (position % 2 == 0) {
        // 155 + 35
        height += ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_6dp)
        // the last column
        when {
          position == 0 -> {
            // the first of top
          }
          position >= (size - 2) -> {
          }
          else -> {
          }
        }
      } else {
        when {
          position == 1 -> {
            // the first of bottom
            leftMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_26dp)
            width += leftMargin
          }
          position >= (size - 2) -> {
          }
          else -> {
          }
        }
      }

      params.width = width
      params.height = height
      params.leftMargin = leftMargin
      params.topMargin = topMargin

      binding.root.layoutParams = params
    }

    val itemParams = binding.clGalleryItem.layoutParams
    if (itemParams is ConstraintLayout.LayoutParams) {

      var topMargin = 0

      if (position % 2 == 0) {
        // cascade item top will squeeze mTop = 35dp and it layout not squeeze
        topMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_35dp)
        // the last column
      } else {
        // top height
        topMargin = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_16dp)
      }

      itemParams.topMargin = topMargin

      binding.clGalleryItem.layoutParams = itemParams
    }

    // item bg params
    val itemBgParams = binding.ivItemBg.layoutParams
    if (itemBgParams is ConstraintLayout.LayoutParams) {
      when (position % 2) {
        0 -> {
          itemBgParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
          itemBgParams.bottomToBottom = -1
        }
        else -> {
          itemBgParams.topToTop = -1
          itemBgParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
        }
      }
      binding.ivItemBg.layoutParams = itemBgParams
    }
  }

  fun bindQtyView(food: Food) {
    val selectedCount = food.selectedCount ?: 0
    binding.btnFoodOption.isPlus = food.isPlus
    binding.btnFoodOption.isMinus = food.isMinus
    QuantityDecorator.bindGalleryQtyChocolate(binding, selectedCount)
    food.isPlus = null
    food.isMinus = null
  }

  fun stopMarquee() {
    binding.tvFoodRecommend.isSelected = false
  }

  private fun setRatingOrDesc(food: Food) {
    with(binding.tvFoodRecommend) {
      isVisible = (food.description != null || food.hasRating())
      text = food.humanizeRating() ?: food.description?.localize()
      isSelected = (food.description != null)
      setCompoundDrawablesWithIntrinsicBounds(
        if (food.hasRating()) {
          ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_thumb)
        } else {
          null
        },
        null,
        null,
        null
      )
    }
  }

  private fun bindView(food: Food, restaurant: Restaurant?) {
    binding.tvFoodName.text = food.name.localize()

    setRatingOrDesc(food)

    binding.tvFoodPrice.text = mapper.formatPriceByRestaurant(food.price, restaurant)
    binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))

    binding.tvFoodOriginalPrice.isVisible = (food.originalPrice != null)
    binding.tvFoodOriginalPrice.text = null
    food.originalPrice?.let {
      binding.tvFoodOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or
        Paint.ANTI_ALIAS_FLAG
      binding.tvFoodOriginalPrice.text = mapper.formatPriceByRestaurant(it, restaurant)
      binding.tvFoodOriginalPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)

      binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.green, binding.root))
    }

    setFoodImage(food, restaurant, binding.ivFood, binding.ivFoodBg)

    if (food.background?.localize() != null) {
      ImageLoader.load(binding.ivItemBg, food.background?.localize())
    } else {
      binding.ivItemBg.setBackgroundResource(0)
    }

    if (food.available == false) {
      binding.root.isEnabled = false
      binding.root.alpha = 0.2f
      binding.btnFoodOption.isEnabled = false
    } else {
      binding.root.isEnabled = true
      binding.root.alpha = 1f
      binding.btnFoodOption.isEnabled = true
    }
  }

  companion object {
    fun create(
      parent: ViewGroup,
      addFood: FunFoodQty,
      minusFood: FunFoodQty
    ): MenuGalleryItemHolder {
      val binding = MenuGalleryItemBinding.inflate(LayoutInflater.from(parent.context))

      val width = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_270dp)
      val height = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_184dp)
      val params = RecyclerView.LayoutParams(width, height)

      binding.root.layoutParams = params

      return MenuGalleryItemHolder(binding, addFood, minusFood)
    }
  }
}
