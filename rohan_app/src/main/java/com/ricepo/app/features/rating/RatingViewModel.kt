package com.ricepo.app.features.rating

import androidx.lifecycle.viewModelScope
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.Order
import com.ricepo.app.model.RatingItem
import com.ricepo.app.utils.SingleLiveEvent
import com.ricepo.base.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed class RatingState {
  data class Failed(val error: String) : RatingState()
  data class Success(val order: Order?, val toPayment: Boolean) : RatingState()
}

@HiltViewModel
class RatingViewModel @Inject constructor(
  private val ratingUseCase: RatingUseCase
) : BaseViewModel() {

  val ratingState = SingleLiveEvent<RatingState>()

  fun mapOrderCreatedAt(order: Order) = ratingUseCase.mapOrderCreatedAt(order)

  fun ratingDriver(
    orderId: String,
    stars: Int?,
    ratingSelectedItems: List<String>,
    feeTip: Int?,
    dishes: List<RatingItem>,
    onStart: () -> Unit,
    onEnd: () -> Unit
  ) {
    viewModelScope.launch {
      onStart.invoke()
      try {
        val respOrder = ratingUseCase.ratingDriver(orderId, stars, ratingSelectedItems, feeTip, dishes)
        ratingState.postValue(RatingState.Success(order = respOrder, toPayment = respOrder.rechargeOrder?.id != null))
      } catch (e: Exception) {
        e.printStackTrace()
        val message = e.parseByBuzNetwork().message ?: ""
        ratingState.postValue(RatingState.Failed(message))
      } finally {
        onEnd.invoke()
      }
    }
  }
}
