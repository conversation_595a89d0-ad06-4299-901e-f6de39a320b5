package com.ricepo.app.features.menu

import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.menu.data.MenuGroupStatus
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseApplication
import com.ricepo.base.BaseUseCase
import com.ricepo.base.model.Cart
import com.ricepo.base.model.CreateOrderGroupReq
import com.ricepo.base.model.OrderGroup
import com.ricepo.base.model.OrderGroupRest
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.model.UpdateOrderGroupReq
import com.ricepo.network.executor.PostExecutionThread
import retrofit2.Response
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 18/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuGroupUseCase @Inject constructor(
  private val repository: CombineRestApi,
  private val menuMapper: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  private val postExecutionThread: PostExecutionThread
) : BaseUseCase() {

  /**
   * Description: Create group order
   * [customerId]: customer id
   * [deviceId]: UUID of the device
   * [items]: Food items user selected in the correspond grouped restaurant
   * [restaurant]: Restaurant info with restaurant id and name
   * [isOwner]: Show if user is the group creater or a member
   * [groupId]: Group id string for join a created groupid
   * [nickName]: The nick name of group member
   * Returns with created group info
   */
  suspend fun createOrderGroup(
    customerId: String,
    deviceId: String,
    items: List<Cart>,
    restaurant: OrderGroupRest,
    isOwner: Boolean,
    groupId: String? = null,
    nickname: String
  ): OrderGroup {

    val req = CreateOrderGroupReq(
      customerId,
      deviceId,
      items,
      restaurant,
      isOwner,
      nickname,
      groupId
    )

    return repository.createGroupOrder(req)
  }

  /**
   * clear the reward when group invited
   */
  suspend fun clearReward(restaurant: Restaurant?) {
    val restaurant = restaurant ?: return
    val restaurantCart = RestaurantCartCache.getRestaurantCartSuspend(restaurant)

    if (restaurantCart?.restaurant?.id != restaurant.id) return

    val noRewardList = restaurantCart?.cartList?.filter { it.reward != true }
    if (noRewardList.isNullOrEmpty()) {
      RestaurantCartCache.deleteRestaurantCart(restaurant)
    } else {
      // update restaurant cart
      val restCart = RestaurantCart(noRewardList, restaurant)
      RestaurantCartCache.saveRestaurantCart(restCart)
    }
  }

  /**
   * prevent user create new group or add cart for menu if has joined a group
   *
   */
  suspend fun checkGroupStatus(
    restaurant: Restaurant?,
    groupId: String?
  ): MenuGroupStatus {
    // return true no group order cache
    val groupInfo = GroupOrderCache.getOrderSuspend()

    // restaurant == null only other group menu
    val isSameGroupedRestaurant = restaurant == null ||
      (groupInfo?.restaurant?.id == restaurant?.id)
    val isInGroup = groupInfo?.groupId != null
    val isShareGroup = groupId != null && groupId != groupInfo?.groupId

    return if (isInGroup) {
      if (isShareGroup) {
        // join group from share link
        if (isSameGroupedRestaurant) {
          MenuGroupStatus.SameShareGroup(groupInfo)
        } else {
          MenuGroupStatus.DiffShareGroup(groupInfo)
        }
      } else {
        if (isSameGroupedRestaurant) {
          // show alert
          MenuGroupStatus.SameGroup(groupInfo)
        } else {
          MenuGroupStatus.DiffGroup(groupInfo)
        }
      }
    } else {
      MenuGroupStatus.None(restaurant)
    }
  }

  suspend fun isInGroup(restaurant: Restaurant?): Boolean {
    val groupInfo = GroupOrderCache.getOrderSuspend() ?: return false

    val isSameGroupedRestaurant = groupInfo.restaurant?.id == restaurant?.id
    val isInGroup = GroupOrderCache.isGroupExist()

    return (isInGroup && isSameGroupedRestaurant)
  }

  suspend fun updateGroupCartQuality(groupId: String, carts: List<Cart>): OrderGroup {

    val updateOrderGroupReq = UpdateOrderGroupReq(
      items = carts,
      deviceId = BaseApplication.mDeviceId,
      groupId = groupId
    )

    return repository.updateGroupOrder(groupId, updateOrderGroupReq)
  }

  suspend fun getOrderGroup(groupId: String): OrderGroup {
    val deviceId = BaseApplication.mDeviceId
    return repository.getGroupOrder(groupId, deviceId)
  }

  suspend fun deleteOrderGroup(): Response<Unit>? {
    val groupId = GroupOrderCache.getGroupId() ?: return null
    val deviceId = BaseApplication.mDeviceId
    return repository.deleteGroupOrder(groupId, deviceId)
  }

  fun getGroupId(restaurant: Restaurant?): String? {
    val restaurantId = restaurant?.id ?: return null

    val order = GroupOrderCache.getOrderGroup()
    return if (order == null || order.restaurant?.id != restaurantId) {
      null
    } else {
      order.groupId
    }
  }

  fun memberInGroup(restaurant: Restaurant?): Boolean {
    val restaurantId = restaurant?.id ?: return false

    val order = GroupOrderCache.getOrderGroup()
    val isGroupExist = order?.groupId?.isNullOrEmpty() == false
    val isOwner = if (order == null || order.restaurant?.id != restaurantId) {
      true
    } else {
      order.owner
    }

    return isGroupExist && !isOwner
  }

  /**
   * get self cards from group order
   */
  fun getOwnerCards(restaurant: Restaurant?, restCarts: List<Cart>?): List<Cart>? {
    val groupInfo = GroupOrderCache.getOrderGroup()

    val isSameGroupedRestaurant = groupInfo?.restaurant?.id == restaurant?.id
    val isGroupExist = groupInfo?.groupId?.isNullOrEmpty() == false

    return if (isGroupExist && isSameGroupedRestaurant) {
      var carts = mutableListOf<Cart>()
      groupInfo?.list?.forEach { item ->
        var items = item.items ?: listOf()
        items = items.map {
          it.ownerId = item.user.deviceId
          it
        }
        carts.addAll(items)
      }
      carts?.filter {
        it.ownerId == BaseApplication.mDeviceId
      }
    } else {
      restCarts
    }
  }

  /**
   * show cart bar with all carts by suspend
   */
  suspend fun getAllCartsSuspend(restCarts: List<Cart>?, restaurant: Restaurant?): List<Cart> {
//        val carts = GroupOrderCache.organizeGroupOrder()
//        val groupInfo = GroupOrderCache.getOrderSuspend()
//
//        val isSameGroupedRestaurant = groupInfo?.restaurant?.id == restaurant?.id
//        var combineCarts = restCarts?.toMutableList() ?: mutableListOf()
//        if (carts != null && isSameGroupedRestaurant) {
//            // add others carts
//            combineCarts.addAll(carts.filter { it.ownerId != BaseApplication.deviceId })
//        }
//        combineCarts.sortBy { it.createdAt }
//        return combineCarts
    return GroupOrderCache.getAllCartsSuspend(restCarts, restaurant)
  }

  /**
   * show cart bar with all carts
   * return restaurant carts if not group carts
   */
  fun getAllCartsGroup(restCarts: List<Cart>?, restaurant: Restaurant?, isMapper: Boolean): List<Cart> {
    val groupInfo = GroupOrderCache.getOrderGroup()

    val groupOrderList = groupInfo?.list ?: return restCarts ?: listOf()
    val carts = mutableListOf<Cart>()

    groupOrderList?.forEach { item ->
      var items = item.items ?: listOf()
      val singleCarts = if (isMapper) menuMapper.mapCartsAndOptions(items) else items
      items = singleCarts?.map {
        it.nickName = item.user.nickName
        it.ownerId = item.user.deviceId
        it
      } ?: listOf()
      carts.addAll(items)
    }

    return carts
  }

  suspend fun updateGroupCartQuality(groupCart: OrderGroup?) {
    val groupId = groupCart?.groupId ?: return
    groupCart?.list?.forEach { customerCart ->

      val updateOrderGroupReq = UpdateOrderGroupReq(
        items = customerCart.items ?: listOf(),
        deviceId = customerCart.user.deviceId,
        groupId = groupId
      )

      repository.updateGroupOrder(groupId, updateOrderGroupReq)
    }
  }
}
