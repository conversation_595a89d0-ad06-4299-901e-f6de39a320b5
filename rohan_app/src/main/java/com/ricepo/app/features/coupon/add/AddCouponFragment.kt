package com.ricepo.app.features.coupon.add

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.ricepo.app.R
import com.ricepo.app.databinding.FragmentAddCouponBinding
import com.ricepo.app.utils.activityContext
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.sheet.RoundedBottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddCouponFragment(
  val onSuccess: (() -> Unit)? = null,
  val onError: (() -> Unit)? = null,
) : RoundedBottomSheetDialogFragment() {

  val viewModel: AddCouponViewModel by viewModels()
  lateinit var binding: FragmentAddCouponBinding

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View {
    binding = FragmentAddCouponBinding.inflate(inflater)
    return binding.root.apply {
      post {
        expendDialog("") {}
        KeyboardUtil.showKeyboard(activityContext(), binding.etAddCoupon)
      }
    }
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    binding.apply {
      btnAddCoupon.setOnClickListener {
        viewModel.addCoupon(
          etAddCoupon.text.toString().trim(),
          onSuccess = {
            onSuccess?.invoke()
            dismiss()
          },
          onError = {
            onError?.invoke()
          }
        )
      }
      ivClose.setOnClickListener {
        dismiss()
      }
    }
    observeError()
  }

  private fun observeError() {
    viewModel.errorData.observe(viewLifecycleOwner) {
      it?.let {
        DialogFacade.showPrompt(
          context = activityContext(),
          title = getString(com.ricepo.style.R.string.ricepo),
          message = getString(com.ricepo.style.R.string.error_not_found)
        )
      } ?: DialogFacade.showPrompt(
        context = activityContext(),
        title = getString(com.ricepo.style.R.string.ricepo),
        message = getString(com.ricepo.style.R.string.error_load_failed)
      )
    }
  }
}
