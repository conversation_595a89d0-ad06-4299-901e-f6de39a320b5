package com.ricepo.app.features.preview

import android.os.Bundle
import android.transition.Transition
import android.view.MotionEvent
import androidx.core.view.ViewCompat
import com.ricepo.app.databinding.ActivityImagePreviewBinding
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.image.ImageLoader

//
// Created by <PERSON><PERSON> on 3/9/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class ImagePreviewActivity : BaseActivity() {

  companion object {
    const val SHARE_PREVIEW_IMAGE = "preview:image"
    const val SHARE_PREVIEW_BACKGROUND = "preview:background"

    const val BUNDLE_PREVIEW_IMAGE = "preview_image"
    const val BUNDLE_PREVIEW_BACKGROUND = "preview_background"
  }

  private lateinit var binding: ActivityImagePreviewBinding

  private var imageRes: String? = null
  private var backgroundRes: String? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityImagePreviewBinding.inflate(layoutInflater)
    setContentView(binding.root)

    imageRes = intent.getStringExtra(BUNDLE_PREVIEW_IMAGE)
    backgroundRes = intent.getStringExtra(BUNDLE_PREVIEW_BACKGROUND)

    ViewCompat.setTransitionName(binding.ivImage, SHARE_PREVIEW_IMAGE)
    ViewCompat.setTransitionName(binding.ivImageBg, SHARE_PREVIEW_BACKGROUND)

    setupListener()
  }

  private fun setupListener() {

    addTransitionListener()
    // important for transition start
    loadFullSizeImage()

    binding.root.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        onBackPressed()
      }
    }
  }

  private fun loadFullSizeImage() {
    loadNormalImage()
  }

  private fun loadNormalImage() {
    imageRes?.let {
      ImageLoader.load(binding.ivImage, it)
    }
  }

  private fun addTransitionListener(): Boolean {
    val transition = window.sharedElementEnterTransition
    if (transition != null) {
      // There is an entering shared element transition so add a listener to it
      transition.addListener(object : Transition.TransitionListener {
        override fun onTransitionEnd(transition: Transition) {
          // As the transition has ended, we can now load the full-size image
          loadFullSizeImage()

          // Make sure we remove ourselves as a listener
          transition.removeListener(this)
        }

        override fun onTransitionStart(transition: Transition) {
          // No-op
        }

        override fun onTransitionCancel(transition: Transition) {
          // Make sure we remove ourselves as a listener
          transition.removeListener(this)
        }

        override fun onTransitionPause(transition: Transition) {
          // No-op
        }

        override fun onTransitionResume(transition: Transition) {
          // No-op
        }
      })
      return true
    }

    // If we reach here then we have not added a listener
    return false
  }
}
