package com.ricepo.app.features.points

import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityPointsSummaryBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.points.adapter.PointsSummaryAdapter
import com.ricepo.app.model.PointsSummaryModel
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Customer
import com.ricepo.base.model.localize
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

//
// Created by <PERSON><PERSON> on 5/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_POINTS_SUMMARY)
class PointsSummaryActivity : BaseActivity() {

  val viewModel: PointsViewModel by viewModels()

  lateinit var binding: ActivityPointsSummaryBinding

  private var isLoaded = false

  private var customer: Customer? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityPointsSummaryBinding.inflate(layoutInflater)
    setContentView(binding.root)
    setTitle(com.ricepo.style.R.string.my_coins)

    customer = intent.getParcelableExtra(FeaturePageConst.PARAM_CUSTOMER)

    setupListener()
  }

  override fun onResume() {
    super.onResume()
    viewModel.checkLogin().observe(
      this,
      Observer {
        binding.btnLogin.isVisible = it.isNullOrEmpty()
        binding.nslCoins.isVisible = !it.isNullOrEmpty()

        // get coins
        reloadCoins()
      }
    )
  }

  private fun setupListener() {
    binding.btnLogin.clickWithTrigger {
      FeaturePageRouter.navigateLogin()
    }
  }

  private fun reloadCoins() {
    if (isLoaded) return
    lifecycleScope.launch {
      viewModel.getCoins(this@PointsSummaryActivity) {
        showErrorNetworkView(it)
      }.collectLatest { points ->
        setPointsView(points)
      }
    }
  }

  private fun setPointsView(points: List<PointsSummaryModel>?) {

    binding.rvCoinsList.isNestedScrollingEnabled = false

    val amount = customer?.point?.balance?.available ?: 0
    binding.tvPointsTitle.setText("$amount")

    val desc = customer?.point?.balance?.description?.localize() ?: ""
    binding.tvPointsDesc.setText(desc)

    if (points?.isNullOrEmpty() == false) {
      val adapter = PointsSummaryAdapter(points)
      binding.rvCoinsList.adapter = adapter
      isLoaded = true
    } else {
      val adapter = PointsSummaryAdapter(listOf(PointsSummaryModel()))
      binding.rvCoinsList.adapter = adapter
      isLoaded = true
    }
  }

  private fun showErrorNetworkView(message: String?) {
    showErrorView(
      binding.flMenuPage, com.ricepo.style.R.drawable.ic_error_no_network,
      com.ricepo.style.R.string.error_title_load_failed, message, com.ricepo.style.R.string.retry,
      isCleanWhenClick = true,
      View.OnClickListener {
        reloadCoins()
      }
    )
  }
}
