package com.ricepo.app.features.rating

import android.app.Activity
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.view.forEach
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.android.material.chip.Chip
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityDriverRatingBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.base.BroadcastConst
import com.ricepo.app.features.payment.data.PaymentHandleMode
import com.ricepo.app.model.Order
import com.ricepo.app.model.RatingItem
import com.ricepo.app.pattern.payment.PaymentRefer
import com.ricepo.base.BaseActivity
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_DRIVER_RATING)
class RatingActivity : BaseActivity() {

  private lateinit var binding: ActivityDriverRatingBinding

  val viewModel: RatingViewModel by viewModels()

  private lateinit var order: Order

  private var isGoodDriver: Boolean? = null

  /**
   * the position of profile list
   * 'lateinit' modifier is not allowed on properties of primitive types
   */
  private var position: Int = -1

  private var ratingAdapter: FoodItemsAdapter? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    order = intent.getParcelableExtra(FeaturePageConst.PARAM_PROFILE_HISTORY_ORDER)!!
    position = intent.getIntExtra(FeaturePageConst.PARAM_PROFILE_ORDER_POSITION, -1)

    binding = ActivityDriverRatingBinding.inflate(layoutInflater)
    setContentView(binding.root)

    render()

    event()

    val filter = IntentFilter()
    filter.addAction(BroadcastConst.ACTION_DRIVER_RATING_PAYMENT)
    registerReceiver(mPaymentReceiver, filter)
  }

  private fun render() {
    setTitle(com.ricepo.style.R.string.rate_driver)
  }

  override fun onDestroy() {
    super.onDestroy()
    unregisterReceiver(mPaymentReceiver)
  }

  private fun checkSubmit() = (isGoodDriver != null || getDishesRating().isNotEmpty()).apply {
    binding.btnRateSubmit.let {
      it.isEnabled = this
      if (this) {
        it.setCardBackgroundColor(ResourcesUtil.getColor(com.ricepo.style.R.color.mr))
      } else {
        it.setCardBackgroundColor(ResourcesUtil.getColor(com.ricepo.style.R.color.disable_mr))
      }
    }
  }

  private fun event() {

    with(binding.btnRateLike) {
      setOnCheckedChangeListener { _, isChecked ->
        isChipIconVisible = !isChecked
        setDishes()
      }
    }

    with(binding.btnRateDislike) {
      setOnCheckedChangeListener { _, isChecked ->
        isChipIconVisible = !isChecked
        setDishes()
      }
    }

    binding.btnRateSubmit.run {
      clickWithTrigger {
        submitRating()
      }
    }

    checkSubmit()

    ratingAdapter = FoodItemsAdapter {
      checkSubmit()
    }

    binding.itemsRecyclerView.apply {
      layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
      adapter = ratingAdapter
    }

    order.items?.let {
      ratingAdapter?.setData(it)
    }

    viewModel.ratingState.observe(this) {
      when (it) {
        is RatingState.Failed -> {
          DialogFacade.showAlert(
            this@RatingActivity, it.error
          )
        }
        is RatingState.Success -> {
          if (it.toPayment) {
            FeaturePageRouter.navigatePayment(
              this@RatingActivity,
              paymentHandle = PaymentHandleMode.autoPayment, order = it.order
            )
          } else {
            showRatingSuccess()
          }
        }
      }
    }
  }

  private fun setDishes() {
    with(binding.llRateContainer) {
      removeAllViews()
      val chips = if (binding.btnRateLike.isChecked) {
        isGoodDriver = true
        RatingUiUtil.getLikes(this@RatingActivity)
      } else if (binding.btnRateDislike.isChecked) {
        isGoodDriver = false
        RatingUiUtil.getDisLikes(this@RatingActivity)
      } else {
        isGoodDriver = null
        listOf()
      }
      chips.forEach {
        addView(
          Chip(context).apply {
            text = it
            isCheckable = true
            isCheckedIconVisible = false
            chipBackgroundColor = getColorStateList(com.ricepo.style.R.color.sub_chip_color)
            this.setTextColor(getColorStateList(com.ricepo.style.R.color.sub_chip_text_color))
          }
        )
      }
    }
    checkSubmit()
  }

  private fun getRatings(): List<String> {
    val list = mutableListOf<String>()
    binding.llRateContainer.forEach {
      (it as? Chip)?.takeIf { chip -> chip.isChecked }?.let { chip ->
        list.add(chip.text.toString())
      }
    }
    return list
  }

  private fun getDishesRating(): List<RatingItem> = ratingAdapter?.getResult() ?: listOf()

  private fun submitRating() {

    viewModel.ratingDriver(
      order.id,
      getDriverRating(),
      getRatings(),
      null,
      getDishesRating(),
      onStart = {
        Loading.showLoading(this)
      },
      onEnd = {
        Loading.hideLoading()
      }
    )
  }

  private fun getDriverRating() = when (isGoodDriver) {
    null -> {
      null
    }
    true -> {
      5
    }
    else -> {
      1
    }
  }

  private fun backRefreshEvent() {
    intent.putExtra(FeaturePageConst.PARAM_PROFILE_ORDER_POSITION, position)
    // todo check the result if here should set the changed result
    intent.putExtra(FeaturePageConst.PARAM_PROFILE_HISTORY_ORDER, order)
    backResultEvent(intent)
  }

  private fun showRatingSuccess() {
    DialogFacade.showAlert(
      this, com.ricepo.style.R.string.thank_rating,
      canCancel = false
    ) {
      backRefreshEvent()
    }
  }

  private fun showPaymentCancel() {
    DialogFacade.showAlert(
      this, com.ricepo.style.R.string.driver_rating_incomplete,
      canCancel = false
    ) {
      backRefreshEvent()
    }
  }

  private val mPaymentReceiver = PaymentRefer.onPaymentWechatResult(
    completed = {
      showRatingSuccess()
    },
    failed = {
      showPaymentCancel()
    }
  )

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_PAYMENT) {
        showRatingSuccess()
      }
    } else if (resultCode == Activity.RESULT_CANCELED) {
      showPaymentCancel()
    }
  }
}
