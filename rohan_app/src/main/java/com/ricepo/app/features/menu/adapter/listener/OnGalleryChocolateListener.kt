package com.ricepo.app.features.menu.adapter.listener

import android.view.View
import com.ricepo.app.databinding.MenuGalleryItemBinding
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant

//
// Created by <PERSON><PERSON> on 4/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OnGalleryChocolateListener(
  private val binding: MenuGalleryItemBinding,
  private val food: Food,
  private val position: Int,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty,
  private val restaurant: Restaurant? = null
) :
  OnChocolateListener(food, position, restaurant, null, null, addFood, minusFood) {

  init {

    // add listener to resolved of conflict with item view click
    var menuTouchView = binding.ivFoodBg
    if (menuTouchView?.visibility != View.VISIBLE) {
      menuTouchView = binding.ivFood
    }

    init(binding.root, menuTouchView, binding.btnFoodOption)
  }
}
