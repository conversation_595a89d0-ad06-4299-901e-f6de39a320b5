package com.ricepo.app.features.profile.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.databinding.ProfileItemHeaderBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.profile.ProfileMapper
import com.ricepo.base.extension.clickAllWithTrigger
import com.ricepo.base.model.Customer
import com.ricepo.base.model.CustomerSubscription
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

//
// Created by <PERSON><PERSON> on 2/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ProfileHeaderHolder(private val binding: ProfileItemHeaderBinding) :
  RecyclerView.ViewHolder(binding.root) {

  private val subscriptionGroup: Group = binding.groupSubscriptionVip
  private val daysOnView: TextView = binding.tvDaysOn
  private val orderCountView: TextView = binding.tvOrderCount
  private val savingView: TextView = binding.tvSaving
  private val savingGroup: Group = binding.groupSaving

  init {
    subscriptionGroup.clickAllWithTrigger { view ->
      val subscription = subscriptionGroup.tag
      if (subscription is CustomerSubscription) {
        // update subscription
        FeaturePageRouter.navigateSubscriptionUpdate(view.context, subscription)
      }
    }
  }

  fun bind(customer: Customer?) {
    if (customer != null) {
      // show header view
      binding.root.visibility = View.VISIBLE

      // bind info
      bindInfo(customer)
    } else {
      binding.root.visibility = View.GONE
    }
  }

  private fun bindInfo(customer: Customer) {
    // render days label
    daysOnView.text = "${customer.days ?: "0"}"
    // render order count label
    orderCountView.text = "${customer.orderCount ?: "0"}"

    // render saving label
    val saving = customer.saving ?: 0
    if (saving > 0) {
      GlobalScope.launch(Dispatchers.IO) {
        val country = AddressCache.getCountrySuspend()
        CoroutineScope(Dispatchers.Main + Job()).launch {
          try {
            val savingPrice = ProfileMapper.formatPrice(saving, country, 0)
            savingView.text = savingPrice
            savingGroup.visibility = View.VISIBLE
            val params = daysOnView.layoutParams
            if (params is ConstraintLayout.LayoutParams) {
              params.horizontalChainStyle = 1
              daysOnView.layoutParams = params
            }
          } catch (e: Exception) {
            e.printStackTrace()
          }
        }
      }
    } else {
      // hide saving
      savingGroup.visibility = View.GONE
      val params = daysOnView.layoutParams
      if (params is ConstraintLayout.LayoutParams) {
        params.horizontalChainStyle = 0
        daysOnView.layoutParams = params
      }
    }
  }

  companion object {
    fun create(parent: ViewGroup): ProfileHeaderHolder {
      val binding = ProfileItemHeaderBinding.inflate(LayoutInflater.from(parent.context))
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      return ProfileHeaderHolder(binding)
    }
  }
}
