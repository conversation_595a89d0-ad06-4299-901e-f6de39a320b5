package com.ricepo.app.features.order

import android.graphics.PointF
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import com.google.android.gms.maps.CameraUpdate
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.Polyline
import com.google.android.gms.maps.model.PolylineOptions
import com.google.android.gms.maps.model.RoundCap
import com.ricepo.app.R
import com.ricepo.app.model.Order
import com.ricepo.base.model.DriverPoint
import com.ricepo.base.model.localize
import com.ricepo.map.CompassPoint
import com.ricepo.map.extension.direction
import com.ricepo.map.extension.getFullBounds
import com.ricepo.map.extension.midPoint
import com.ricepo.map.fragment.MapFragment
import com.ricepo.style.ResourcesUtil
import java.lang.Math.min

//
// Created by <PERSON><PERSON> on 29/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OrderMapFragment : MapFragment() {

  private var mapView: GoogleMap? = null

  lateinit var orderMapper: OrderMapper

  private var mRestaurantPoint: android.graphics.Point? = null
  private var mCustomerPoint: android.graphics.Point? = null

  private var mCustomerFinishAt: String? = null

  private var mRestaurantPreparedAt: String? = null

  private var mLastRestState: String? = null

  private var mDriverPolyline: Polyline? = null
  private var mDriverPolylineShadow: Polyline? = null
  private var mDriverPolylineShadow2: Polyline? = null

  private lateinit var render: OrderMapRender

  private var mOrder: Order? = null
  private var mDriverPoint: DriverPoint? = null
  private var mDriverLatLng: List<LatLng>? = null

  override fun onCreate(bundle: Bundle?) {
    super.onCreate(bundle)
    orderMapper = OrderMapper()
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    activity?.let {
      render = OrderMapRender(it, view)
    }
  }

  fun renderMap(order: Order?, driverLocation: DriverPoint?, driverLagLng: List<LatLng>?) {
    mOrder = order ?: return
    mDriverPoint = driverLocation
    mDriverLatLng = mDriverLatLng
    // stop if order is null
    val mapView = mapView ?: return
    val order = order

    // render coordinate and marker within map loaded
    mapView?.setOnMapLoadedCallback {
      renderCoordinate(order, driverLocation, driverLagLng)
    }
  }

  private fun renderCoordinate(
    order: Order,
    driverLocation: DriverPoint?,
    driverPolyline: List<LatLng>?
  ) {

    val (restaurantCoordinate, customerCoordinate) = orderMapper.mapCoordinate(order)

    var initLatLng = LatLng(customerCoordinate?.lat ?: 0.0, customerCoordinate?.lon ?: 0.0)

    var cameraUpdate: CameraUpdate? = null

    var restaurantLatLng: LatLng? = null
    var customerLatLng: LatLng? = null
    var driverLatLng: LatLng? = null
    var zoom = 6f

    if (restaurantCoordinate != null && customerCoordinate != null) {
      restaurantLatLng = LatLng(restaurantCoordinate.lat, restaurantCoordinate.lon)
      customerLatLng = LatLng(customerCoordinate.lat, customerCoordinate.lon)
      val midLatLng = restaurantLatLng.midPoint(customerLatLng)

      // save as init map view target
      initLatLng = midLatLng

      // starting and ending path
      if (driverLocation != null) {
        driverLatLng = LatLng(
          driverLocation.coordinates.getOrNull(1) ?: 0.0,
          driverLocation.coordinates.getOrNull(0) ?: 0.0
        )
      }
      // update the map to auto fit the zoom and bounds
      val bounds = restaurantLatLng.getFullBounds(customerLatLng, driverLatLng)

      if (context != null) {
        // construct camera update
        val width = resources.displayMetrics.widthPixels
        val height = resources.displayMetrics.heightPixels
        val minMetric = min(width, height)
        val padding = minMetric.times(0.20).toInt()
        cameraUpdate = CameraUpdateFactory.newLatLngBounds(bounds, padding)
      }
    }

    // let the map show the correct zoom size and margin
    if (cameraUpdate != null) {
      mapView?.moveCamera(cameraUpdate)
    } else {
      // init the position
      mapView?.moveCamera(CameraUpdateFactory.newLatLngZoom(initLatLng, zoom))
    }

    // draw the map marker
    drawMapMarker(order, restaurantLatLng, customerLatLng, driverLatLng)

    // draw the driver polyline
    drawMapPolyline(driverPolyline)

    // set alpha to show map
    view?.alpha = 1f
  }

  private fun drawMapPolyline(driverPolyline: List<LatLng>?) {
    mDriverPolyline?.remove()
    mDriverPolyline = null
    mDriverPolylineShadow?.remove()
    mDriverPolylineShadow = null
    mDriverPolylineShadow2?.remove()
    mDriverPolylineShadow2 = null
    val driverPolyline = driverPolyline ?: return
    val mapView = mapView ?: return
    val context = context ?: return

    var polylineOptionsShadow = PolylineOptions()
      .addAll(driverPolyline)
      .color(ResourcesUtil.getColor(com.ricepo.style.R.color.driver_polyline_shadow, context))
      .width(ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_driver_trace_shadow_width).toFloat())
      .geodesic(true)
    mDriverPolylineShadow = mapView.addPolyline(polylineOptionsShadow)
    mDriverPolylineShadow?.startCap = RoundCap()
    mDriverPolylineShadow?.endCap = RoundCap()

    var polylineOptionsShadow2 = PolylineOptions()
      .addAll(driverPolyline)
      .color(ResourcesUtil.getColor(com.ricepo.style.R.color.driver_polyline_shadow2, context))
      .width(ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_driver_trace_shadow_width2).toFloat())
      .geodesic(true)
    mDriverPolylineShadow2 = mapView.addPolyline(polylineOptionsShadow2)
    mDriverPolylineShadow2?.startCap = RoundCap()
    mDriverPolylineShadow2?.endCap = RoundCap()

    var polylineOptions = PolylineOptions()
      .addAll(driverPolyline)
      .color(ResourcesUtil.getColor(com.ricepo.style.R.color.driver_polyline, context))
      .width(ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_driver_trace_width).toFloat())
      .geodesic(true)
    mDriverPolyline = mapView.addPolyline(polylineOptions)
    mDriverPolyline?.startCap = RoundCap()
    mDriverPolyline?.endCap = RoundCap()
  }

  private fun drawMapMarker(
    order: Order?,
    restaurantLatLng: LatLng?,
    customerLatLng: LatLng?,
    driverLatLng: LatLng?
  ) {
    // return if map view is not ready
    val mapView = mapView ?: return
    // return if order is null
    val order = order ?: return
    // return if restaurant location is not found
    val restaurantLatLng = restaurantLatLng ?: return
    // return if customer location is not found
    val customerLatLng = customerLatLng ?: return
    val context = context ?: return

    // calculate the relative direction from restaurant to customer
    val direction = restaurantLatLng.direction(customerLatLng)

    // calculate the screen point from latlng
    val restaurantPoint = mapView.projection.toScreenLocation(restaurantLatLng)

    // restaurant change
    if (mRestaurantPoint != restaurantPoint || mLastRestState != order.state ||
      mRestaurantPreparedAt != order.delivery?.preparedAt
    ) {
      val restaurantName = " ${order.restaurant?.name?.localize() ?: ""} "
      val preparedAt = orderMapper.getRestaurantPreparedAt(order)
      val restaurantLabel = render.initMarkerLabel(
        restaurantName,
        ResourcesUtil.getColor(com.ricepo.style.R.color.rest_address_text, context),
        direction, order, preparedAt = preparedAt
      )
//            restaurantLabel.setBackgroundColor(ResourcesUtil.getColor(
//                com.ricepo.style.R.color.rest_address_fill, context))
      restaurantLabel.setBackgroundResource(com.ricepo.style.R.drawable.button_plate_floating_red_3d)
      restaurantLabel.setTag(com.ricepo.map.R.id.map_rest_label)

      render.addRestOverlayAnimView(
        PointF(restaurantPoint.x.toFloat(), restaurantPoint.y.toFloat()),
        render.getRestAnimDatas(order), restaurantLabel, direction
      )
      // no need to add views repeatedly
      mRestaurantPoint = restaurantPoint
      mLastRestState = order.state
      mRestaurantPreparedAt = order.delivery?.preparedAt
    }

    // calculate the home point in the screen
    val customerPoint = mapView.projection.toScreenLocation(customerLatLng)

    // customer change
    if (mCustomerPoint != customerPoint || mCustomerFinishAt != order.delivery?.finishAt) {
      val customerAddress = " ${orderMapper.getOrderAddress(order) ?: ""} "
      val finishAt = orderMapper.getDeliveryFinishAt(order)
      val customerLabel = render.initMarkerLabel(
        customerAddress,
        ResourcesUtil.getColor(com.ricepo.style.R.color.home_address_text, context),
        CompassPoint.init(direction.reverse()), order, finishAt = finishAt
      )
//            customerLabel.setBackgroundColor(ResourcesUtil.getColor(
//                com.ricepo.style.R.color.home_address_fill, context))
      customerLabel.setBackgroundResource(com.ricepo.style.R.drawable.button_plate_floating_3d)
      customerLabel.setTag(com.ricepo.map.R.id.map_home_label)

      render.addHomeOverlayView(
        PointF(customerPoint.x.toFloat(), customerPoint.y.toFloat()),
        com.ricepo.map.R.drawable.customer_home, customerLabel, CompassPoint.init(direction.reverse())
      )

      mCustomerPoint = customerPoint
      mCustomerFinishAt = order.delivery?.finishAt
    }

    // driver position change
    if (driverLatLng != null) {
      // add marker repeatedly
      val driverPoint = mapView.projection.toScreenLocation(driverLatLng)
      render.addDriverOverlayView(PointF(driverPoint.x.toFloat(), driverPoint.y.toFloat()))
    } else {
      if (view?.parent is FrameLayout) {
        val layout = view?.parent as FrameLayout
        render.clearDriverOverlay(layout)
      }
    }
  }

  override fun onMapReady(map: GoogleMap?) {
    super.onMapReady(map)

    // init map view
    mapView = map

    // map settings:
    mapSettings()

    renderMap(mOrder, mDriverPoint, mDriverLatLng)
  }

  private fun mapSettings() {
    val mapView = mapView ?: return
    // disable gesture of map
    with(mapView.uiSettings) {
      isScrollGesturesEnabled = false
      isTiltGesturesEnabled = false
      isRotateGesturesEnabled = false
      setAllGesturesEnabled(false)
      isZoomGesturesEnabled = false
    }

    // ignore user event for map view for scroll
    mapView.setOnMarkerClickListener(null)
    mapView.setOnInfoWindowClickListener(null)

    // reset padding
    mapView?.setPadding(30, 100, 30, 100)

    // set alpha to hide init map
    view?.alpha = 0f
  }

  override fun onResume() {
    super.onResume()
    render?.resumeAnimOverlay(true)
  }

  override fun onStop() {
    super.onStop()
    render?.resumeAnimOverlay(false)
  }

  override fun onLowMemory() {
    super.onLowMemory()
    mapView?.clear()
  }

  override fun onDestroyView() {
    super.onDestroyView()
    val layout = view?.parent
    if (layout != null && layout is FrameLayout) {
      render?.quitOverlay(layout)
    }
    mapView?.clear()
    mapView = null
  }
}
