package com.ricepo.app.features.reward.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.databinding.RewardSummaryItemBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.model.RestaurantName
import com.ricepo.app.model.RewardSummaryModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.base.model.mapper.BaseMapper

//
// Created by <PERSON><PERSON> on 6/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RewardSummaryAdapter constructor(
  private val datas: List<RewardSummaryModel>
) : RecyclerView.Adapter<RewardSummaryAdapter.ViewHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
    val binding = RewardSummaryItemBinding.inflate(
      LayoutInflater.from(parent.context)
    )
    binding.root.layoutParams = RecyclerView.LayoutParams(
      RecyclerView.LayoutParams.MATCH_PARENT,
      RecyclerView.LayoutParams.WRAP_CONTENT
    )
    return ViewHolder(binding)
  }

  override fun getItemCount() = datas?.size ?: 0

  override fun onBindViewHolder(holder: ViewHolder, position: Int) {
    val data = datas[position]
    holder.bind(data, position, itemCount)
  }

  class ViewHolder(private val binding: RewardSummaryItemBinding) :
    RecyclerView.ViewHolder(binding.root) {

    private val mapper = BaseMapper()

    init {
      binding.root.clickWithTrigger { view ->
        val data = view.tag as RestaurantName
        val restaurant = Restaurant(data.id)
        FeaturePageRouter.navigateMenu(restaurant)
      }
    }

    fun bind(data: RewardSummaryModel, position: Int, size: Int) {
      binding.root.tag = data.restaurant
      binding.tvRestaurantName.text = data.restaurant?.name?.localize()
      binding.tvPoint.text = "${mapper.calcCoinCount(data.balance)}"

      binding.tvDivider.isVisible = (position !== (size - 1))
    }
  }
}
