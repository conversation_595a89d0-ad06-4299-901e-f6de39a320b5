package com.ricepo.app.features.points

import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.model.PointsSummaryModel
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseUseCase
import javax.inject.Inject

//
// Created by Thomsen on 6/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class PointsUseCase @Inject constructor(
  private val repository: CombineRestApi
) : BaseUseCase() {

  /**
   * get customer reward coins
   */
  suspend fun getMyPoints(): List<PointsSummaryModel>? {

    val customer = CustomerCache.getCustomerSuspend()
    val customerId = customer?.id ?: return null

    return repository.getPointsByCustomer(customer.id)
  }
}
