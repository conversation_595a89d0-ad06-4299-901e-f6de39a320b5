package com.ricepo.app.features.menu.options

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.consts.FoodSize
import com.ricepo.app.databinding.MenuOptionSectionTitleBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Food
import com.ricepo.base.model.FoodImage
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by <PERSON><PERSON> on 7/7/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class MenuOptionsSectionTitleHolder(private val binding: MenuOptionSectionTitleBinding) :
  SectionItemHolder(binding.root) {

  private val mapper = MenuMapper()

  fun bind(uiModel: MenuUiModel.MenuOptionsTitle) {

    val food = uiModel.food
    val restaurant = uiModel.restaurant

    setFoodImage(food, null, binding.ivFood, binding.ivFoodBg)

    binding.tvFoodName.text = food?.name?.localize()

    binding.tvFoodInfo.isVisible = true
    binding.tvFoodInfo.text = ResourcesUtil.getString(com.ricepo.style.R.string.menu_option_select_info)

    val minimum = food?.minimum ?: 0
    val titleInfo = mapper.toMinimumInfo(minimum, restaurant)

    binding.tvFoodSubinfo.isVisible = (food?.minimum != null)
    binding.tvFoodSubinfo.text = titleInfo
  }

  private fun setFoodImage(
    food: Food?,
    item: Restaurant?,
    menuView: ImageView,
    menuBgView: ImageView
  ) {
    val isHiddenImage = (food == null || food.image?.url == null)
    menuBgView.isVisible = !isHiddenImage
    menuView.isVisible = !isHiddenImage

    if (!isHiddenImage) {
      RestViewUtils.setMenuBackground(menuBgView, item, food?.image)
      ImageLoader.load(menuView, food?.image?.url)
    } else {
      menuBgView.setImageResource(0)
      if (food?.image?.placeholder == FoodImage.MARKET) {
        menuView.setImageResource(com.ricepo.style.R.drawable.ic_placeholder_market)
      } else {
        menuView.setImageResource(com.ricepo.style.R.drawable.ic_placeholder_bag)
      }
    }
  }

  companion object {
    fun create(parent: ViewGroup): MenuOptionsSectionTitleHolder {
      val binding = MenuOptionSectionTitleBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      params.topMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_10)
      binding.root.layoutParams = params
      RestViewUtils.setFoodViewSize(
        FoodSize.BIG_WIDTH, FoodSize.BIG_HEIGHT,
        binding.ivFood, binding.ivFoodBg
      )
      return MenuOptionsSectionTitleHolder(binding)
    }
  }
}
