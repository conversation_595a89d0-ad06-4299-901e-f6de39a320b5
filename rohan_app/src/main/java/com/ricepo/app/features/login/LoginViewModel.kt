package com.ricepo.app.features.login

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.address.AddressDao
import com.ricepo.app.features.login.repository.AuthUseCase
import com.ricepo.app.model.UserInformation
import com.ricepo.base.model.Customer
import com.ricepo.base.model.GlobalConfigModel
import com.ricepo.base.tools.AssetUtils
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.ErrorMessage
import com.ricepo.network.resource.NetworkError
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import javax.inject.Inject

//
// Created by Thomsen on 2/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class LoginViewModel @Inject constructor(
  val addressDao: AddressDao,
  val useCase: AuthUseCase
) : BaseViewModel() {

  private var globalConfig: GlobalConfigModel = GlobalConfigModel()

  private var listRegions: List<GlobalConfigModel> = listOf()

  // 添加数据加载状态管理
  private var isRegionDataLoaded = false
  private var isRegionDataLoading = false
  private var isNetworkDataLoaded = false  // 区分网络数据和本地数据

  private var phoneText: String? = null

  private var intervalUnsubscribe: Disposable? = null
  val liveCountdown = MutableLiveData<LoginOption>()

  val liveGetVcode = MutableLiveData<LoginOption>()

  // 专门用于region数据加载的Loading状态
  val liveRegionLoading = MutableLiveData<Boolean>()

  data class LoginOption(
    val message: String?,
    var phone: String? = null,
    var tooMany: Boolean = false,
    var countdown: String? = null
  ) {
    companion object {
      val MSG_IS_LOADING = "loading"
    }
  }

  private fun validatePhoneNumber(phone: String): Boolean {
    try {
      val numberProto = PhoneNumberUtil.getInstance()
        .parse("+${globalConfig.area}$phone", globalConfig.area)
      return PhoneNumberUtil.getInstance().isValidNumber(numberProto)
    } catch (e: Exception) {
      return false
    }
  }

  fun checkLogin(phone: String?, type: String) {
    if (phone == null) return
    // the minimum of country digit is 9
    val isDigit = if ("33".equals(globalConfig.area)) {
      if (phone.startsWith("0")) {
        phone.count() >= 10
      } else {
        phone.count() >= 9
      }
    } else {
      phone.count() >= globalConfig.digit
    }
    if (isDigit) {
      val phoneValidate = validatePhoneNumber(phone)
      if (phoneValidate) {
        liveGetVcode.postValue(LoginOption(LoginOption.MSG_IS_LOADING))
        this.phoneText = phone
        val standPhone = "+${globalConfig.area}$phone"
        getVcode(standPhone, type)
      } else {
        if (phone.count() == globalConfig.digit) {
          liveGetVcode.postValue(LoginOption(ResourcesUtil.getString(com.ricepo.style.R.string.error_invalid_phone_number)))
        }
      }
    }
  }

  /**
   * [type] is sms or call
   */
  fun getVcode(phone: String?, type: String) {
    val lPhone = phone ?: return
    useCase.getVcode(
      object : DisposableSingleObserver<Any>() {
        override fun onSuccess(t: Any) {
          liveGetVcode.postValue(LoginOption(null, phone = "+${globalConfig.area} $phoneText"))
          countdownVcode(30)
        }

        override fun onError(e: Throwable) {
          e?.printStackTrace()
          if (e is NetworkError) {
            handleNetworkError(e, liveGetVcode, lPhone)
          } else {
            liveGetVcode.postValue(LoginOption(ErrorMessage.NETWORK_ERROR))
//            MonitorFacade.captureException(
//              Exception(ErrorMessage.NETWORK_ERROR),
//              mapOf("phone" to phone, "type" to type)
//            )
          }
        }
      },
      AuthUseCase.Params(lPhone, type, "")
    )
  }

  private fun login(phone: String?, vcode: String, liveData: MutableLiveData<LoginOption>) {
    val lPhone = phone ?: return
    useCase.login(
      object : DisposableSingleObserver<UserInformation>() {
        override fun onSuccess(t: UserInformation) {
          // save token
          saveToken(t, liveData)
        }

        override fun onError(e: Throwable) {
          e?.printStackTrace()
          if (e is NetworkError) {
            handleNetworkError(e, liveData, lPhone)
          } else {
            liveData.postValue(LoginOption(ErrorMessage.NETWORK_ERROR))
//            MonitorFacade.captureException(
//              Exception(ErrorMessage.NETWORK_ERROR),
//              mapOf("phone" to phone, "vcode" to vcode)
//            )
          }
        }
      },
      AuthUseCase.Params(lPhone, "", vcode)
    )
  }

  private fun saveToken(
    t: UserInformation?,
    liveData: MutableLiveData<LoginOption>
  ) {
    viewModelScope.launch {
//            if (t is Map<*, *> && t.get("token") != null) {
//                val token = t.get("token") as String
//                KeyCacheFacade.saveToken(token)
//                val customerId = t.get("_id") as String
//                getCustomer(customerId, liveData)
//            }
      if (t != null) {
        CustomerCache.saveToken(t.token)
        getCustomer(t.id, liveData)
      }
    }
  }

  private fun getCustomer(customerId: String, liveData: MutableLiveData<LoginOption>) {
    if (customerId != null) {
      useCase.getCustomer(
        object : DisposableSingleObserver<Customer>() {
          override fun onSuccess(t: Customer) {
            viewModelScope.launch {
              withContext(Dispatchers.IO) {
                // save customer info
                CustomerCache.saveCustomer(t)
              }

              // login success
              liveData.postValue(LoginOption(null))
            }
          }

          override fun onError(e: Throwable) {
            liveData.postValue(LoginOption(ErrorMessage.NETWORK_ERROR))
//            MonitorFacade.captureException(
//              Exception(ErrorMessage.NETWORK_ERROR),
//              mapOf("customerId" to customerId)
//            )
          }
        },
        customerId
      )
    }
  }

  private fun handleNetworkError(
    t: NetworkError,
    liveData: MutableLiveData<LoginOption>,
    phone: String
  ) {
    if (t.code == ErrorCode.TWO_MANY_REQUESTS && t.details != null) {
      val map = t.details
      val remaining = map?.get("remaining")
      var remain = 0
      if (remaining != null) {
        try {
          remain = (remaining as Double).toInt()
        } catch (e: Exception) {
          e.printStackTrace()
        }
      }
      countdownVcode(remain)
      liveData.postValue(
        LoginOption(
          ResourcesUtil.getString(com.ricepo.style.R.string.error_too_many_requests, remain),
          tooMany = true, phone = "+${globalConfig.area} $phoneText"
        )
      )
    } else if (t.code == ErrorCode.AUTH_FAILED) {
      liveData.postValue(LoginOption(ResourcesUtil.getString(com.ricepo.style.R.string.error_auth_failed)))
    } else {
      liveData.postValue(LoginOption(t.message ?: ErrorMessage.NETWORK_ERROR))
      if (t.message == null) {
//        MonitorFacade.captureException(
//          Exception(ErrorMessage.NETWORK_ERROR),
//          mapOf("phone" to phone)
//        )
      }
    }
  }

  private fun countdownVcode(remain: Int) {
    if (intervalUnsubscribe != null) {
      intervalUnsubscribe?.dispose()
    }
    intervalUnsubscribe = Observable.interval(0, 1, TimeUnit.SECONDS)
      .map { it + 1 }
      .map { remain - it }
      .distinct()
      .subscribe {
        val resend = ResourcesUtil.getString(com.ricepo.style.R.string.resend)
        val text = if (it > 0) "${ResourcesUtil.getString(com.ricepo.style.R.string.wait_seconds, it)} $resend" else {
          intervalUnsubscribe?.dispose()
          resend
        }
        liveCountdown.postValue(LoginOption(null, countdown = text))
      }
  }

  fun initCountryArea(lifecycleOwner: LifecycleOwner):
    LiveData<Pair<GlobalConfigModel, List<GlobalConfigModel>>> {
    val liveData = MutableLiveData<Pair<GlobalConfigModel, List<GlobalConfigModel>>>()

    preloadRegionData()

    viewModelScope.launch {
      val countryMap = useCase.getCountry()
      liveCountry(lifecycleOwner, liveData, countryMap)
    }
    return liveData
  }

  private fun preloadRegionData() {
    if (isRegionDataLoaded || isRegionDataLoading) {
      return
    }

    isRegionDataLoading = true
    viewModelScope.launch {
      try {
        val countryMap = useCase.getCountry()
        if (countryMap?.values != null) {
          listRegions = countryMap.values.toList()
          isRegionDataLoaded = true
          isNetworkDataLoaded = true  // 网络数据加载成功
        } else {
          // 如果网络数据为空，使用本地备用数据
          val backupCountryMap = AssetUtils.getCountryModel()
          if (backupCountryMap?.values != null) {
            listRegions = backupCountryMap.values.toList()
            isRegionDataLoaded = true
            // 注意：这里不设置isNetworkDataLoaded，因为这是本地数据
          }
        }
      } catch (e: Exception) {
        // 网络请求失败，使用本地备用数据
        val backupCountryMap = AssetUtils.getCountryModel()
        if (backupCountryMap?.values != null) {
          listRegions = backupCountryMap.values.toList()
          isRegionDataLoaded = true
          // 注意：这里不设置isNetworkDataLoaded，因为网络请求失败了
        }
      } finally {
        isRegionDataLoading = false
      }
    }
  }

  private fun liveCountry(
    lifecycleOwner: LifecycleOwner,
    liveData: MutableLiveData<Pair<GlobalConfigModel, List<GlobalConfigModel>>>,
    cm: Map<String, GlobalConfigModel>?
  ) {
    addressDao.liveAddressLatest().observe(
      lifecycleOwner,
      Observer {
        val address = it
        val countryMap = cm ?: AssetUtils.getCountryModel()
        val country = address?.country ?: "ES"

        // default ES if not in regions
        var dic = countryMap?.get(country)
        if (dic == null) {
          dic = countryMap?.get("ES")
        }

        dic?.let { config ->
          globalConfig = config
        }

        if (countryMap?.values != null) {
          listRegions = countryMap.values.toList().fold(mutableListOf()) { result, item ->
            result.add(item)
            result
          }
          isRegionDataLoaded = true
        } else {
          // 如果countryMap为空，尝试从AssetUtils获取备用数据
          val backupCountryMap = AssetUtils.getCountryModel()
          if (backupCountryMap?.values != null) {
            listRegions = backupCountryMap.values.toList()
            isRegionDataLoaded = true
          }
        }

        liveData.apply {
          value = Pair(globalConfig, listRegions)
        }
      }
      )
  }

  fun showAreaBottomSheet(): LiveData<Pair<List<String>, List<GlobalConfigModel>>> {
    val liveData = MutableLiveData<Pair<List<String>, List<GlobalConfigModel>>>()

    // 优先使用网络数据，如果没有网络数据则显示Loading获取
    if (isNetworkDataLoaded && listRegions.isNotEmpty()) {
      val sortedItems = listRegions.sortedBy { it.order }
      val strItems = sortedItems
        .fold(mutableListOf<String>()) { result, it ->
          result.add("${it.name} +${it.area}")
          result
        }

      liveData.apply {
        value = Pair(strItems, sortedItems)
      }
    } else {
      requestRegionDataWithLoading(liveData)
    }

    return liveData
  }

  /**
   * 请求region数据并显示Loading
   */
  private fun requestRegionDataWithLoading(liveData: MutableLiveData<Pair<List<String>, List<GlobalConfigModel>>>) {
    if (isRegionDataLoading) {
      liveRegionLoading.postValue(true)

      viewModelScope.launch {
        while (isRegionDataLoading) {
          kotlinx.coroutines.delay(100)
        }

        liveRegionLoading.postValue(false)

        if (listRegions.isNotEmpty()) {
          val sortedItems = listRegions.sortedBy { it.order }
          val strItems = sortedItems
            .fold(mutableListOf<String>()) { result, it ->
              result.add("${it.name} +${it.area}")
              result
            }

          liveData.postValue(Pair(strItems, sortedItems))
        } else {
          // 加载失败，使用备用数据
          loadBackupRegionData(liveData)
        }
      }
    } else {
      // 开始新的加载请求
      isRegionDataLoading = true
      liveRegionLoading.postValue(true)

      viewModelScope.launch {
        try {

          val countryMap = useCase.getCountry()
          if (countryMap?.values != null) {
            listRegions = countryMap.values.toList()
            isRegionDataLoaded = true
            isNetworkDataLoaded = true  // 网络数据加载成功

            val sortedItems = listRegions.sortedBy { it.order }
            val strItems = sortedItems
              .fold(mutableListOf<String>()) { result, it ->
                result.add("${it.name} +${it.area}")
                result
              }

            liveRegionLoading.postValue(false)
            liveData.postValue(Pair(strItems, sortedItems))
          } else {
            // 网络数据为空，使用备用数据
            liveRegionLoading.postValue(false)
            loadBackupRegionData(liveData)
            isRegionDataLoaded = true
          }
        } catch (e: Exception) {
          liveRegionLoading.postValue(false)
          loadBackupRegionData(liveData)
          isRegionDataLoaded = true
        } finally {
          isRegionDataLoading = false
        }
      }
    }
  }

  /**
   * 加载备用region数据
   */
  private fun loadBackupRegionData(liveData: MutableLiveData<Pair<List<String>, List<GlobalConfigModel>>>) {
    val countryMap = AssetUtils.getCountryModel()
    if (countryMap?.values != null) {
      val regions = countryMap.values.toList()
      val sortedItems = regions.sortedBy { it.order }
      val strItems = sortedItems
        .fold(mutableListOf<String>()) { result, it ->
          result.add("${it.name} +${it.area}")
          result
        }

      liveData.apply {
        value = Pair(strItems, sortedItems)
      }

      if (!isRegionDataLoaded) {
        listRegions = regions
      }
    } else {
      liveData.apply {
        value = Pair(listOf(), listOf())
      }
    }
  }

  fun setGlobalConfigModel(data: GlobalConfigModel?) {
    globalConfig = data ?: GlobalConfigModel()
  }

  fun validateCode(code: String?): LiveData<LoginOption> {
    val liveData = MutableLiveData<LoginOption>()
    // the length of captcha code is four
    if (code != null && code.length == 4) {
      liveData.postValue(LoginOption(LoginOption.MSG_IS_LOADING))
      val standPhone = "+${globalConfig.area}$phoneText"
      login(standPhone, code, liveData)
    }
    return liveData
  }

  fun resendVcode(text: String) {
    if (text == ResourcesUtil.getString(com.ricepo.style.R.string.resend_by_sms)) {
      checkLogin(phoneText, "sms")
    }
    if (text == ResourcesUtil.getString(com.ricepo.style.R.string.resend_by_voice)) {
      checkLogin(phoneText, "call")
    }
  }

  override fun onCleared() {
    super.onCleared()
    intervalUnsubscribe?.dispose()
  }
}
