package com.ricepo.app.features.menu.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.consts.CategoryConst
import com.ricepo.app.databinding.MenuSectionHorizontalBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.adapter.FunShowMore
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.base.model.Food
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionItemHolder
import com.ricepo.style.view.rv.ScrollStatePersist

//
// Created by <PERSON><PERSON> on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuSectionHorizontalHolder(
  private val binding: MenuSectionHorizontalBinding,
  private val persist: ScrollStatePersist?,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty,
  private val showMore: FunShowMore
) :
  SectionItemHolder(binding.root), ScrollStatePersist.ScrollStateKeyProvider {

  val mapper = MenuMapper()

  var menuAdapter: MenuSectionAdapter? = null

  var foodItems: List<Food>? = null

  private var model: MenuUiModel.MenuHorizontalSection? = null

  private var rows = 1
  private var amount = 0

  fun bind(uiModel: MenuUiModel.MenuHorizontalSection, sectionPosition: Int) {
    model = uiModel
    rows = uiModel.category?.rows ?: 1

    var uiModels = uiModel.models.map {
      if (it is MenuUiModel.MenuNormalItem) {
        it.type = CategoryConst.HORIZONTAL
        it.rows = rows
      }
      it
    }

    // completed rows last column
    val size = uiModels.size
    val mod = (size % rows)
    if (mod > 0) {
      val complete = rows - mod
      for (i in 0 until complete) {
        val mut = uiModels.toMutableList()
        mut.add(
          MenuUiModel.MenuNormalItem(
            null, null, type = CategoryConst.HORIZONTAL, rows = rows
          )
        )
        uiModels = mut
      }
    }
    amount = uiModels.size

    val layoutManager = binding.rvMenuContainer.layoutManager
    if (layoutManager is GridLayoutManager) {
      layoutManager.spanCount = rows
    }
    snapHelper.attachToRecyclerView(binding.rvMenuContainer)

//        binding.rvMenuContainer.addItemDecoration(
//            LinearEdgeDecoration(
//                startPadding = binding.rvMenuContainer.resources.getDimensionPixelOffset(
//                    com.ricepo.style.R.dimen.sw_margin
//                ),
//                endPadding = 0,
//                orientation = RecyclerView.HORIZONTAL
//            )
//        )

    val foodPosition = uiModel.foodIndex ?: -1
    if (menuAdapter == null) {
      menuAdapter = MenuSectionAdapter(
        uiModels, addFood = addFood,
        minusFood = minusFood, showMore = showMore, navMenu = {}
      )
      binding.rvMenuContainer.adapter = menuAdapter
      binding.rvMenuContainer.addOnScrollListener(onScrollListener)
      // init padding
      setRightPadding(false)
    } else {
      menuAdapter?.models = uiModels
      if (foodPosition == -1) {
        menuAdapter?.notifyDataSetChanged()
      } else {
        menuAdapter?.notifyItemChanged(foodPosition)
      }
    }

    persist?.restoreScrollState(binding.rvMenuContainer, this)
  }

  fun bindQtyView(food: Food) {
    val foodPosition = foodItems?.indexOfFirst { it.id == food.id } ?: -1

    if (foodPosition == -1) {
      menuAdapter?.notifyDataSetChanged()
    } else {
      menuAdapter?.notifyItemChanged(foodPosition, food)
    }
  }

  fun onRecycled() {
    persist?.saveScrollState(binding.rvMenuContainer, this)
    model = null
  }

  fun onDetachedFromWindow() {
//        if (binding.rvMenuContainer.scrollState != RecyclerView.SCROLL_STATE_IDLE) {
//            val lm = binding.rvMenuContainer.layoutManager ?: return
//            // Find the view that needs to be snapped
//            gravitySnapHelper.findSnapView(lm)?.let {
//                // Get the distance to the final position
//                val out = gravitySnapHelper.calculateDistanceToFinalSnap(lm, it)
//                // Scroll the recyclerview to the final position immediately
//                binding.rvMenuContainer.scrollBy(out[0], out[1])
//            }
//        }
  }

  private val onScrollListener = object : RecyclerView.OnScrollListener() {
    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
      super.onScrollStateChanged(recyclerView, newState)

      if (newState == RecyclerView.SCROLL_STATE_IDLE) {
        val layoutManager = recyclerView.layoutManager
        if (layoutManager is GridLayoutManager) {
          val position = snapHelper.findTargetSnapPosition(layoutManager, 0, 0)
          if (position != -1) {
            val isLast = (position == (amount - rows))
            if (isLast) {
              setRightPadding(true)
            }
          }
        }
      }
    }

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
      super.onScrolled(recyclerView, dx, dy)
      if (recyclerView.scrollState == RecyclerView.SCROLL_STATE_DRAGGING) {
        // scroll to left
        if (dx < 0) {
          setRightPadding(false)
        }
      }
    }
  }

  private val snapHelper = PagerSnapHelper()

  private fun setRightPadding(isLast: Boolean) {
    if (isLast) {
      binding.rvMenuContainer.setPadding(
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_22dp), 0,
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_22dp), 0
      )
    } else {
      binding.rvMenuContainer.setPadding(
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_22dp), 0,
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_44dp), 0
      )
    }
  }

  companion object {
    fun create(
      parent: ViewGroup,
      persist: ScrollStatePersist?,
      addFood: FunFoodQty,
      minusFood: FunFoodQty,
      showMore: FunShowMore
    ): MenuSectionHorizontalHolder {
      val binding = MenuSectionHorizontalBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      binding.rvMenuContainer.layoutManager = GridLayoutManager(
        binding.root.context, 1, LinearLayoutManager.HORIZONTAL, false
      )

      binding.rvMenuContainer.isNestedScrollingEnabled = false
      binding.rvMenuContainer.itemAnimator = null

      val holder = MenuSectionHorizontalHolder(binding, persist, addFood, minusFood, showMore)
      persist?.setupRecyclerView(binding.rvMenuContainer, holder)

      // don't used the item view (complete rows last column)
      binding.rvMenuContainer.recycledViewPool.setMaxRecycledViews(
        R.layout.menu_section_normal_item, 0
      )

      return holder
    }
  }

  override fun getScrollStateKey(): String? {
    return model?.category?.id + model?.category?.groupId
  }
}
