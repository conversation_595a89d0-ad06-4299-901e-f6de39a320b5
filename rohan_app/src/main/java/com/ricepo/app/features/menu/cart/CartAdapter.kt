package com.ricepo.app.features.menu.cart

import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.CartMenuItemBinding
import com.ricepo.app.databinding.CartRestaurantItemBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.adapter.EmptyViewHolder
import com.ricepo.base.model.FoodImage

//
// Created by <PERSON><PERSON> on 4/6/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class CartAdapter(var models: List<CartUiModel>) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {
  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return when (viewType) {
      R.layout.cart_restaurant_item -> CartRestaurantHolder.create(parent)
      R.layout.cart_menu_item -> CartMenuHolder.create(parent)
      else -> EmptyViewHolder.create(parent)
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val model = models.getOrNull(position)
    model?.let {
      when (holder) {
        is CartRestaurantHolder -> holder.bind(it as CartUiModel.CartMenuInfoUiModel)
        is CartMenuHolder -> holder.bind(it as CartUiModel.CartMenuUiModel, position)
      }
    }
  }

  override fun getItemViewType(position: Int): Int {
    val model = models.getOrNull(position) ?: return super.getItemViewType(position)
    return when (model) {
      is CartUiModel.CartMenuUiModel -> R.layout.cart_menu_item
      is CartUiModel.CartMenuInfoUiModel -> R.layout.cart_restaurant_item
    }
  }

  override fun getItemCount() = models.size
}

class CartRestaurantHolder(private val binding: CartRestaurantItemBinding) :
  RecyclerView.ViewHolder(binding.root) {

  private val menuMapper = MenuMapper()

  fun bind(uiModel: CartUiModel.CartMenuInfoUiModel) {
    val menuInfo = SpannableStringBuilder()
    menuInfo.append(uiModel.menuInfo)
//        if (uiModel.showVert) {
//            menuInfo.append("  ")
//            menuMapper.appendVertLine(binding.root.context, menuInfo)
//            menuInfo.append(" ")
//        }
    binding.tvMenuCartRestaurantName.text = menuInfo
  }

  companion object {
    fun create(parent: ViewGroup): CartRestaurantHolder {
      val binding = CartRestaurantItemBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.WRAP_CONTENT,
        RecyclerView.LayoutParams.MATCH_PARENT
      )
      binding.root.layoutParams = params
      return CartRestaurantHolder(binding)
    }
  }
}

class CartMenuHolder(private val binding: CartMenuItemBinding) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(uiModel: CartUiModel.CartMenuUiModel, position: Int) {
    binding.tvMenuCart.text = uiModel.textBuilder
    // show food image
    val foodImage = uiModel.foodImage
    if (uiModel.isOption == true) {
      binding.ivRestaurantMenuBg.isVisible = false
      binding.ivRestaurantMenu.isVisible = false
    } else {
      binding.ivRestaurantMenuBg.isVisible = true
      binding.ivRestaurantMenu.isVisible = true
      RestViewUtils.setFoodImage(foodImage, null, binding.ivRestaurantMenu, binding.ivRestaurantMenuBg)
    }

//        val layoutParams = binding.ivRestaurantMenuBg.layoutParams as ConstraintLayout.LayoutParams
//        layoutParams.leftMargin = if (position < 2) {
//            // because before that has info label
// //            ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_8)
//            0
//        } else {
//            ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_8)
//        }
//        binding.ivRestaurantMenuBg.layoutParams = layoutParams
  }

  companion object {
    fun create(parent: ViewGroup): CartMenuHolder {
      val binding = CartMenuItemBinding.inflate(LayoutInflater.from(parent.context))
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.WRAP_CONTENT,
        RecyclerView.LayoutParams.MATCH_PARENT
      )
      return CartMenuHolder(binding)
    }
  }
}

sealed class CartUiModel {

  class CartMenuInfoUiModel(
    val menuInfo: SpannableStringBuilder,
    val showVert: Boolean = true
  ) : CartUiModel()

  class CartMenuUiModel(
    val textBuilder: SpannableStringBuilder,
    val foodImage: FoodImage?,
    val isOption: Boolean? = null
  ) : CartUiModel()
}
