package com.ricepo.app.features.address

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Query
import androidx.room.Transaction
import com.ricepo.base.data.AbstractBaseDao
import com.ricepo.map.model.FormatUserAddress

//
// Created by <PERSON><PERSON> on 10/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Dao
abstract class AddressDao : AbstractBaseDao<FormatUserAddress>() {

  // the room use rxjava2 not rxjava3

  /**
   * get all history address data
   */
  @Query("SELECT * FROM T_ADDRESS ORDER BY UPDATED_AT DESC")
  abstract fun getAllAddress(): LiveData<List<FormatUserAddress>>

  @Query("select count(id) from t_address")
  abstract fun getCount(): Int

  @Query("select count(id) from t_address where placeId=(:placeId)")
  abstract fun getCount(placeId: String): Int

  @Query("select count(id) from t_address where formatted=(:formatted)")
  abstract fun getCountByFormatted(formatted: String): Int

  /**
   * delete first row when count is 5
   */
  @Query("delete from t_address where id=(select id from t_address order by updated_at limit 1)")
  abstract fun deleteFirstAddress(): Int

  @Query("delete from t_address where placeId=(:placeId)")
  abstract fun deleteAddressByPlaceId(placeId: String): Int

  @Query("delete from t_address where formatted=(:formatted)")
  abstract fun deleteAddressByFormatted(formatted: String): Int

  /**
   * Live the latest address
   * @return the address from the table.
   */
  @Query("SELECT * FROM T_ADDRESS WHERE ID = (SELECT id FROM T_ADDRESS order by updated_at desc limit 1)")
  abstract fun liveAddressLatest(): LiveData<FormatUserAddress>

  /**
   * Get the latest address
   * @return the address from the table.
   */
  @Query("SELECT * FROM T_ADDRESS WHERE ID = (SELECT id FROM T_ADDRESS order by updated_at desc limit 1)")
  abstract fun getAddressLatest(): FormatUserAddress?

  /**
   * Get the latest address by suspend
   */
  @Query("SELECT * FROM T_ADDRESS WHERE ID = (SELECT id FROM T_ADDRESS order by updated_at desc limit 1)")
  abstract suspend fun getAddress(): FormatUserAddress?

  /**
   * the max size of address
   */
  private val ADDRESS_FIFO_SIZE = 5

  /**
   * save [address] to database
   * explore json no place id
   */
  @Transaction
  open fun saveAddress(address: FormatUserAddress) {
    val placeId = address.placeId ?: ""
    val formatted = address.formatted ?: ""
    if (placeId.isNullOrEmpty() && formatted.isNullOrEmpty()) {
      return
    } else if (getCountByFormatted(formatted) > 0) {
      // update the history data
      if (deleteAddressByFormatted(formatted) > 0) {
        insert(address)
      }
    } else if (getCount(placeId) > 0) {
      // update the history data
      if (deleteAddressByPlaceId(placeId) > 0) {
        insert(address)
      }
    } else if (getCount() >= ADDRESS_FIFO_SIZE) {
      // first delete and insert new data
      if (deleteFirstAddress() > 0) {
        insert(address)
      }
    } else {
      // insert new address
      insert(address)
    }
  }
}
