package com.ricepo.app.features.profile.datasource

import androidx.paging.ExperimentalPagingApi
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.profile.ProfileUiModel
import com.ricepo.app.model.Order
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restapi.loadResultError
import com.ricepo.app.restaurant.CustomerOrderType
import com.ricepo.base.model.Customer
import com.ricepo.base.remoteconfig.RiceRemoteConfig
import com.ricepo.style.ResourcesUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import java.lang.Thread.sleep

//
// Created by <PERSON><PERSON> on 3/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ProfileDataSource constructor(
  private val repository: CombineRestApi,
  private val customerId: String?,
  private val coroutineScope: CoroutineScope,
  private val isTab: Boolean = false
) : PagingSource<Int, ProfileUiModel>() {

  private val PAGE_INDEX_STARTING = 1

  private var caches: MutableList<ProfileUiModel> = mutableListOf()

  // cache key to createdAt (1, null) (2, start)
  private var cacheCreatedAt: MutableMap<Int, String?> = mutableMapOf()

  override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ProfileUiModel> {

    val page = params.key ?: PAGE_INDEX_STARTING

    val pageSize = params.loadSize
    val customerId = customerId ?: ""

    var customerDef: Deferred<Customer>? = null
    var recentOrders: Deferred<List<Order>>? = null
    var referInfoDef: Deferred<ReferInfo?>? = null

    if (page == PAGE_INDEX_STARTING) {
      // get customer info
      customerDef = coroutineScope.async {
        repository.getCustomer(customerId)
      }

      val recentParams = mapOf<String, Any>(
        "type" to CustomerOrderType.recent
      )
      // get recent orders
      recentOrders = coroutineScope.async {
        repository.getOrders(customerId, recentParams)
      }

      // get refer info
      referInfoDef = coroutineScope.async {
        try {
          repository.getRefer()
        } catch (e: Exception) {
          null
        }
      }
    }

    if (page == PAGE_INDEX_STARTING && params is LoadParams.Refresh) {
      cacheCreatedAt.clear()
    }

    val historyParams = mutableMapOf<String, Any>(
      "limit" to pageSize,
      "type" to CustomerOrderType.history
    )
    // get the history paging params
    val lastOrderCreatedAt = cacheCreatedAt.get(page)
    if (lastOrderCreatedAt != null) {
      historyParams["isBefore"] = lastOrderCreatedAt
    }
    // get history orders
    val historyOrdersDef = coroutineScope.async {
      repository.getOrders(customerId, historyParams)
    }

    try {
      val historyOrders = historyOrdersDef.await()

      val endOfPaginationReached = historyOrders.isNullOrEmpty() ||
        historyOrders.size < pageSize
      val prevKey = if (page == PAGE_INDEX_STARTING) null else page - 1
      val nextKey = if (endOfPaginationReached) null else page + 1

      val customer = customerDef?.await()
      coroutineScope.launch {
        // save customer
        CustomerCache.saveCustomer(customer, false)
      }
      val uiModels = constructUiModels(
        customer, recentOrders?.await(),
        historyOrders, params, lastOrderCreatedAt, referInfoDef?.await()
      )

      uiModels.map { model ->
        if (caches.find { it.id == model.id } != null) {
          caches.set(caches.indexOfFirst { it.id == model.id }, model)
        } else {
          caches.add(model)
        }
      }

      var models = uiModels
      if (params is LoadParams.Refresh && page > PAGE_INDEX_STARTING) {
        models = caches
      }

      return LoadResult.Page(
        data = models,
        prevKey = prevKey,
        nextKey = nextKey
      )
    } catch (e: Exception) {
      return if (page > PAGE_INDEX_STARTING) {
        sleep(200)
        LoadResult.Page(
          data = listOf(ProfileUiModel.LoadMoreError(page)),
          prevKey = page - 1,
          nextKey = cacheCreatedAt.size + 1
        )
      } else {
        loadResultError<Int, ProfileUiModel>(e)
      }
    }
  }

  private fun constructUiModels(
    customer: Customer?,
    recentOrders: List<Order>?,
    historyOrders: List<Order>,
    params: LoadParams<Int>,
    lastCreatedAt: String?,
    referInfo: ReferInfo?,
  ): List<ProfileUiModel> {
    val uiModels = mutableListOf<ProfileUiModel>()
    // customer model
    if (customer != null && !isTab) {
      uiModels.add(ProfileUiModel.CustomerHeader(customer, id = customer.id))
    }
    // recent orders model
    recentOrders?.forEach { order ->
      uiModels.add(ProfileUiModel.RecentOrders(order, id = order.id))
    }
    if (customer != null) {
      // share ricepo model
//      if (customer.isReferEnable(RiceRemoteConfig.referralConfig()) && !isTab) {
      if (referInfo?.share != null && !isTab) {
        uiModels.add(ProfileUiModel.ShareRicepo(referInfo, id = customer.refer?.reward?.toString()))
      }
      // customer settings ui model
      if (!isTab) {
        uiModels.add(ProfileUiModel.CustomerSetting(customer, id = customer.id))
      }
      // history title model
      val title = ResourcesUtil.getString(com.ricepo.style.R.string.past_order)
      val isEmpty = (params.key == null) && historyOrders.isNullOrEmpty()
      uiModels.add(ProfileUiModel.HistoryTitle(title, isEmpty, id = title))
    }

    // history orders model
    historyOrders.forEach { order ->
      uiModels.add(ProfileUiModel.HistoryOrders(order, params.key, lastCreatedAt, id = order.id))
    }

    // get the last history order
    val createdAt = historyOrders.lastOrNull()?.createdAt
    val nextPage = (params.key ?: PAGE_INDEX_STARTING) + 1
    val vCreatedAt = cacheCreatedAt.get(nextPage)
    if (vCreatedAt == null) {
      cacheCreatedAt.put(nextPage, createdAt)
    }

    return uiModels
  }

  /**
   * The same value, 2, was passed as the nextKey in two
   * sequential Pages loaded from a PagingSource. Re-using load keys in
   * PagingSource is often an error, and must be explicitly enabled by
   * overriding PagingSource.keyReuseSupported.
   */
  override val keyReuseSupported: Boolean
    get() = true

  @OptIn(ExperimentalPagingApi::class)
  override fun getRefreshKey(state: PagingState<Int, ProfileUiModel>): Int? {

    val key = state.anchorPosition?.let { position ->
      val model = state.closestItemToPosition(position)
      if (model is ProfileUiModel.HistoryOrders) {
        model.page
      } else {
        super.getRefreshKey(state)
      }
    }

    return key
  }
}
