package com.ricepo.app.features.menu

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Activity
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.text.SpannableStringBuilder
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.widget.LinearLayout
import androidx.activity.viewModels
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.android.material.appbar.AppBarLayout
import com.ricepo.app.R
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.databinding.ActivityMenuNewBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.jump.CategoryJumpFragment
import com.ricepo.app.features.menu.adapter.MenuBundleAdapter
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.adapter.holder.CategoryModel
import com.ricepo.app.features.menu.adapter.holder.MenuNavigationAdapter
import com.ricepo.app.features.menu.base.MenuBaseActivity
import com.ricepo.app.features.menu.bundle.RestaurantBundleFragment
import com.ricepo.app.features.menu.cart.CartAdapter
import com.ricepo.app.features.menu.cart.CartUiModel
import com.ricepo.app.features.menu.data.MenuGroupStatus
import com.ricepo.app.features.menu.data.MenuPageData
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.utils.FoodLargeImageUtil
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.analytics.ScrollDepthFacade
import com.ricepo.base.animation.Loading
import com.ricepo.base.consts.TabMode
import com.ricepo.base.extension.bindLoading
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.isVisibleBottom
import com.ricepo.base.inputmanager.KeyboardVisibilityEvent
import com.ricepo.base.inputmanager.KeyboardVisibilityEventListener
import com.ricepo.base.model.CategoryJumpData
import com.ricepo.base.model.CategoryJumpItem
import com.ricepo.base.model.Food
import com.ricepo.base.model.MenuBundle
import com.ricepo.base.model.OrderGroup
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseMenuSearchEvent
import com.ricepo.monitor.firebase.FirebaseMenuSelectEvent
import com.ricepo.monitor.firebase.FirebaseMonitor
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.ThemeUtil
import com.ricepo.style.view.VerticalTextView
import com.ricepo.style.view.rv.BottomSmoothScroller
import com.ricepo.style.view.rv.LinearHeightLayoutManager
import com.ricepo.style.view.rv.ScrollStatePersist
import com.ricepo.style.view.rv.TopSmoothScroller
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.kotlin.addTo
import io.reactivex.rxjava3.observers.DisposableObserver
import io.reactivex.rxjava3.subjects.BehaviorSubject
import io.reactivex.rxjava3.subjects.PublishSubject
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlin.math.min

//
// Created by Thomsen on 6/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_MENU)
class MenuActivity : MenuBaseActivity() {

  companion object {
    val ACTIVITY_NAME = "com.ricepo.app.features.menu.MenuActivity"
  }

  val menuViewModel: MenuViewModel by viewModels()

  val menuGroupViewModel: MenuGroupViewModel by viewModels()

  lateinit var viewModelOutput: MenuViewModel.Output
  lateinit var viewModelInput: MenuViewModel.Input

  private val compositeDisposable = CompositeDisposable()

  lateinit var aRefreshMenu: PublishSubject<Boolean>

  lateinit var aSearchBarEditing: BehaviorSubject<String>

  lateinit var aGroupCreating: PublishSubject<Boolean>

  lateinit var mapper: MenuMapper

  var menuAdapter: MenuSectionAdapter? = null

  private lateinit var menuSectionPersist: ScrollStatePersist

  var bundleAdapter: MenuBundleAdapter? = null

  private var groupId: String? = null

  lateinit var binding: ActivityMenuNewBinding

  private var menuPageData: MenuPageData? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    menuSectionPersist = ScrollStatePersist(savedInstanceState)

    binding = ActivityMenuNewBinding.inflate(layoutInflater)
    setContentView(binding.root)
    initSearchBar()

    mapper = MenuMapper()
    menuRestaurant = intent.getParcelableExtra(FeaturePageConst.PARAM_MENU_RESTAURANT)
    FoodLargeImageUtil.calculateBigImageIds(menuRestaurant, menuPageData)
    menuViewModel.searches = intent.getStringArrayListExtra(FeaturePageConst.PARAM_MENU_RESTAURANT_SEARCH_KEYWORD)
    groupId = intent.getStringExtra(FeaturePageConst.PARAM_MENU_GROUP_ID)
    menuViewModel.deliveryMode = intent.getStringExtra(FeaturePageConst.PARAM_DELIVERY_MODE)
    menuViewModel.location = intent.getStringExtra(FeaturePageConst.PARAM_RESTAURANT_LOCATION)

    menuGroupViewModel.initGroupId(groupId)

    setupListener()
    bindEvent(menuRestaurant)
    initMenuRecommendCollect(binding.inMenuCart, menuViewModel)

    ScrollDepthFacade.computeScrollDepth(binding.rvMenu)

    observeLoginLoad()
  }

  override fun onSaveInstanceState(outState: Bundle) {
    super.onSaveInstanceState(outState)
    menuSectionPersist.onSaveInstanceState(outState)
  }

  override fun onConfigurationChanged(newConfig: Configuration) {
    super.onConfigurationChanged(newConfig)
    if (ThemeUtil.changeThemeDark(newConfig)) {
      // fix unlimited loading when change uiMode
      // view model afterGetMenu Observable exception
      menuRestaurant?.let {
        FeaturePageRouter.navigateMenu(it)
        finish()
      }
    }
  }

  private fun observeLoginLoad() {
    // reward and delivery fee after login
    menuViewModel.loginState.observe(this) {
      if (it.isLoginAndVipChange && !it.isFirstCreate) {
        // force refresh restaurant when false
        aRefreshMenu.onNext(true)
      } else if (it.isFirstCreate) {
        // first check
        aRefreshMenu.onNext(true)
      }
    }
  }

  override fun onStart() {
    super.onStart()
    setupBackListener()
    menuViewModel.checkLoginChange()
    ScrollDepthFacade.resumeScrollDepth(binding.rvMenu)
  }

  override fun onDestroy() {
    super.onDestroy()
    compositeDisposable.dispose()
    binding.rtvPool.onDestroy()
  }

  private fun setupBackListener() {
    binding.ivBack.clickWithTrigger { view ->
      if (view.tag == groupTag) {
        // exit group
        closeGroupOrder()
      } else {
        // com.ricepo.style.R.drawable.ic_back
        onBackPressed()
      }
    }
  }

  override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
    return if (binding.ivBack.tag == groupTag &&
      event?.keyCode == KeyEvent.KEYCODE_BACK
    ) {
      // forbidden the back keyboard if group menu
      true
    } else {
      super.dispatchKeyEvent(event)
    }
  }

  private fun setupListener() {
    // listener of search bar collapse
    binding.appbarMenu.addOnOffsetChangedListener(AppBarLayout.OnOffsetChangedListener { _, _ -> })

    // listener of search editing
    binding.etMenuSearch.doAfterTextChanged { charSequence ->
      aSearchBarEditing.onNext(charSequence.toString())
      // clear the recyclerview persist
      menuSectionPersist.clearScrollState()
    }

    // listener of menu group
    binding.ivGroupOrder.clickWithTrigger { view ->
      if (view.tag == com.ricepo.style.R.drawable.ic_invite) {
        showShareInvite()
      } else {
        // com.ricepo.style.R.drawable.ic_group_order
        checkCreateGroup()
      }
    }

    binding.inMenuCart.clMenuCart.clickWithTrigger {
      menuViewModel.checkToCheckout(this, entrance)
    }
    binding.inMenuCart.rcvMenuCart.setOnTouchListener(
      CartTouchListener { isDown ->
        if (isDown && binding.inMenuCart.clMenuRecommend.isVisible) {
          resetHideRecommend(binding.inMenuCart)
        } else {
          menuViewModel.checkToCheckout(this, entrance)
        }
      }
    )

    binding.rvMenu.addOnScrollListener(onMenuMarqueeListener)

    binding.ivSearch.clickWithTrigger {
      FeaturePageRouter.navigateMenuSearch(
        this, menuViewModel.restaurantInfo,
        menuViewModel.promotionFoods, menuViewModel.mapCategoryMaxItem,
        menuViewModel.bundles, entrance
      )
    }
  }

  private var cartAdapter: CartAdapter? = null

  private fun observeCartUpdated() {
    // observer the food update
    viewModelOutput.menuCartObserver.observe(
      this,
      Observer {

        val cartData = it

        // update group cart
        if (cartData.update) {
          lifecycleScope.launch {
            menuGroupViewModel.updateGroupCartQuantity(
              this@MenuActivity,
              cartData.carts, menuViewModel.restaurantInfo
            )?.collectLatest { orderGroup ->
              menuGroupViewModel.updateLocalGroupInfo(menuRestaurant, orderGroup)
            }
          }
        }

        // combine group carts
        lifecycleScope.launch {
          val allCarts = menuGroupViewModel.getAllCarts(
            cartData.carts,
            menuViewModel.restaurantInfo
          )

          if (allCarts.isNullOrEmpty()) {
            showCart(false, binding.inMenuCart)
            binding.inMenuCart.tvDelivery.text = ""
            menuViewModel.menuRestaurantCart?.cartList = null
          } else {
            showCart(true, binding.inMenuCart)
            val triple = mapper.mapCartUiModel(allCarts, menuViewModel.menuRestaurantCart)
//                    binding.inMenuCart.tvMenuCart.text = triple.first

            val price = mapper.toTotalPrice(
              triple.second,
              menuViewModel.restaurantInfo
            )
            val models = mutableListOf<CartUiModel>()
            models.add(CartUiModel.CartMenuInfoUiModel(SpannableStringBuilder(price)))
            models.addAll(triple.first)

            if (cartAdapter == null) {
              cartAdapter = CartAdapter(models)
              val cartLayoutManager = LinearLayoutManager(this@MenuActivity)
              cartLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
              binding.inMenuCart.rcvMenuCart.apply {
                layoutManager = cartLayoutManager
                adapter = cartAdapter
              }
            } else {
              cartAdapter?.models = models
              val recyclerViewState = binding.inMenuCart.rcvMenuCart.layoutManager?.onSaveInstanceState()
              cartAdapter?.notifyDataSetChanged()
              binding.inMenuCart.rcvMenuCart.layoutManager?.onRestoreInstanceState(recyclerViewState)
            }

            menuViewModel.menuRestaurantCart = triple.third

            // pickup menu don't show delivery
            if (menuViewModel.deliveryMode != TabMode.MODE_PICKUP) {
              binding.inMenuCart.tvDelivery.text = mapper.toDeliveryMessage(
                triple.second,
                menuViewModel.restaurantInfo
              )
            }
          }
        }
      }
    )
  }

  private fun bindEvent(restaurant: Restaurant?) {
    // init restaurant search keyword
    bindViewModelInput(restaurant)
    bindViewModelOutput()
  }

  private fun bindViewModelInput(restaurant: Restaurant?) {
//        aRefreshMenu = BehaviorSubject.createDefault(false)
    aRefreshMenu = PublishSubject.create<Boolean>()
    aSearchBarEditing = BehaviorSubject.createDefault("")
    aGroupCreating = PublishSubject.create()

    viewModelInput = MenuViewModel.Input(
      restaurant, aRefreshMenu,
      aSearchBarEditing
    )
    viewModelOutput = menuViewModel.transform(viewModelInput)
  }

  private fun bindViewModelOutput() {
    viewModelOutput.menuTableUpdated
      .bindLoading(this)
      .subscribeWith(object : DisposableObserver<MenuPageData>() {
        override fun onComplete() {
        }

        override fun onNext(pageData: MenuPageData) {
          // back check the restaurant by id
          if (menuViewModel.restaurantInfo?.name == null) {
            onBackPressed()
          }
          // re init menu restaurant
          menuRestaurant = menuViewModel.restaurantInfo
          checkRestaurantClosed()

          showContentView(pageData)

          menuPageData = pageData

          if (!pageData.isTableRefresh && pageData.sectionPosition == -1) {
            showBundleView(pageData)
          }

          if (!isAlreadyCheckMenuStatus) {
            // set the menu title bar tag
            changeMenuStatus(true)
//                        isAlreadyCheckMenuStatus = true
          }
        }

        override fun onError(e: Throwable) {
          // set the menu title bar when request error
          changeMenuStatus(false)
          showErrorNetworkView(e?.localizedMessage)

          // back check the restaurant by id
          if (menuViewModel.restaurantInfo?.name == null) {
            onBackPressed()
          }
        }
      }).addTo(compositeDisposable)

    viewModelOutput.menuTableNoFood
      .doOnNext { Loading.hideLoading() }
      .subscribe {
        if (it.sections?.filter { it != null }?.isNotEmpty() == false) {
          showTryOtherView()
          changeMenuStatus(false)
        }
      }

    viewModelOutput.menuCheckOption
      .observe(
        this,
        Observer {
          val checkOption = it
          checkAddCartOption(checkOption)
        }
      )

    menuViewModel.observeError
      .subscribe {
        // set the menu title bar when request error
        changeMenuStatus(false)
        showErrorNetworkView(it?.localizedMessage)

        // back check the restaurant by id
        if (menuViewModel.restaurantInfo?.name == null) {
          onBackPressed()
        }
      }

    observeCartUpdated()
  }

  private fun checkAddCartOption(checkOption: MenuViewModel.CheckOption) {
    when (val pageStatus = checkOption.pageStatus ?: MenuGroupStatus.None(null, null)) {
      is MenuGroupStatus.None,
      is MenuGroupStatus.SameGroup,
      is MenuGroupStatus.SameShareGroup -> {

        when {
          FeaturePageConst.PAGE_MENU == checkOption.message -> {
            FeaturePageRouter.navigateOptions(
              this@MenuActivity,
              checkOption.food, checkOption.restaurant,
              checkOption.selectedPosition, checkOption.galleryFoodIndex
            )
            resetHideRecommend(binding.inMenuCart)
          }
          MenuViewModel.CheckOption.MESSAGE_REFRESH == checkOption.message -> {
            val food = checkOption.food
            menuAdapter?.models?.filter {
              it.category?.items?.filter { it.id == food.id }?.isNotEmpty() == true
            }?.forEachIndexed { index, model ->
              if (model is MenuUiModel.MenuGallerySection) {
                model.foodIndex = checkOption.galleryFoodIndex
                // set gallery food selected count
                model.category?.items?.filter { it.id == food.id }?.forEach {
                  it.selectedCount = food.selectedCount
                }
              }
              when (model) {
                is MenuUiModel.MenuHorizontalSection -> {
                  model.models.filter { it.food?.id == food.id }.forEach {
                    it.food?.selectedCount = food.selectedCount
                  }
                }
                is MenuUiModel.MenuVerticalSection -> {
                  model.models.filter { it.food?.id == food.id }.forEach {
                    it.food?.selectedCount = food.selectedCount
                  }
                }
                is MenuUiModel.MenuComboHorizontalSection -> {
                  model.models.filter { it.food?.id == food.id }.forEach {
                    it.food?.selectedCount = food.selectedCount
                  }
                }
                else -> {
                  model.food?.selectedCount = food.selectedCount
                }
              }
              // refresh food and related food
              val position = model.itemPosition ?: checkOption.selectedPosition ?: index
              menuAdapter?.notifyItemChanged(position, model.food)
            }
          }
          checkOption.isAlert -> {
            DialogFacade.showAlert(
              this@MenuActivity,
              checkOption.message
            )
          }
          checkOption.message.isNotEmpty() -> {
            DialogFacade.showPrompt(
              this@MenuActivity,
              checkOption.message
            ) {
              if (checkOption.deleteCacheCart) {
                // clear the other cache cart
                menuViewModel.deleteRestaurantCart(checkOption)
              }
            }
          }
        }
      }
      is MenuGroupStatus.DiffShareGroup,
      is MenuGroupStatus.DiffGroup -> {
        // REFACTOR: extract to method
        val restaurantName =
          pageStatus?.groupInfo?.restaurant?.name?.localize() ?: ""
        DialogFacade.showPrompt(
          this@MenuActivity,
          message = ResourcesUtil.getString(
            com.ricepo.style.R.string.group_please_exit_subtitle, restaurantName
          ),
          title = ResourcesUtil.getString(com.ricepo.style.R.string.group_please_exit_title),
          positiveId = com.ricepo.style.R.string.group_please_exit_button
        ) {
          val groupId = pageStatus?.groupInfo?.groupId
          val restId = pageStatus?.groupInfo?.restaurant?.id
          FeaturePageRouter.navigateMenuAfterSubscription(
            this@MenuActivity,
            Restaurant(restId), groupId
          )
        }
      }
    }
  }

  private fun changeMenuStatus(isCheckJoin: Boolean, isClearId: Boolean = false) {
    // init menu status get the restaurant
    lifecycleScope.launch {
      menuGroupViewModel.checkGroupStatus(menuViewModel.restaurantInfo)
        .collectLatest { pageStatus ->
          menuViewModel.mGroupStatus = pageStatus
          // clear groupId when close group from checkout
          if (pageStatus is MenuGroupStatus.None && isClearId) {
            groupId = null
          }
          showTitleBar(pageStatus)
          // show bundle opt
          changeBundleView(menuPageData)

          // bundles show restaurant to select
          if (menuPageData?.listBundle.isNullOrEmpty() && isCheckJoin) {
            showRestaurantView(true)
          }
          isAlreadyCheckMenuStatus = true

          if (isCheckJoin) {
            checkJoinGroupOrder(pageStatus)
          }
        }
    }
  }

  private var refreshHandler = object : Handler() {
    override fun handleMessage(msg: Message) {
      super.handleMessage(msg)
//            ToastUtil.showToast(LocalDateTime.now().toString())
      reloadMenu()
    }
  }

  private fun checkCreateGroup() {
    lifecycleScope.launch {
      menuGroupViewModel.checkGroupStatus(menuViewModel.restaurantInfo)
        .collect { groupStatus ->
          when (groupStatus) {
            is MenuGroupStatus.None -> {
              DialogFacade.showInput(
                this@MenuActivity,
                com.ricepo.style.R.string.group_create_group_order_title,
                com.ricepo.style.R.string.group_create_group_order_desc,
                com.ricepo.style.R.string.group_create_group_order_placeholder,
                confirm = { content ->
                  createGroup(content, true)
                }
              )
            }
            is MenuGroupStatus.DiffShareGroup,
            is MenuGroupStatus.DiffGroup -> {
              val restaurantName =
                groupStatus.groupInfo?.restaurant?.name?.localize() ?: ""
              DialogFacade.showPrompt(
                this@MenuActivity,
                message = ResourcesUtil.getString(
                  com.ricepo.style.R.string.group_please_exit_subtitle, restaurantName
                ),
                title = ResourcesUtil.getString(com.ricepo.style.R.string.group_please_exit_title),
                positiveId = com.ricepo.style.R.string.group_please_exit_button
              ) {
                val groupId = groupStatus.groupInfo?.groupId
                val restId = groupStatus.groupInfo?.restaurant?.id
                FeaturePageRouter.navigateMenuAfterSubscription(
                  this@MenuActivity,
                  Restaurant(restId), groupId = groupId
                )
              }
            }
            else -> {}
          }
        }
    }
  }

  private fun createGroup(nickname: String, isGroupOwner: Boolean) {

    lifecycleScope.launch {
      menuGroupViewModel.createMenuGroup(
        this@MenuActivity, nickname, groupId,
        restaurantCart = menuViewModel.menuRestaurantCart,
        restaurantInfo = menuViewModel.restaurantInfo, isGroupOwner = isGroupOwner
      )
        .collect { result ->
          result.fold(
            onSuccess = {
              handleGroupSuccess(it, isGroupOwner)
            },
            onFailure = { error ->
              menuGroupViewModel.handleGroupError(
                this@MenuActivity,
                menuRestaurant, error, isShowAlert = true
              ) {
                groupId = null
                changeMenuStatus(false)
                reloadMenu()
              }
            }
          )
        }
    }
  }

  private fun handleGroupSuccess(group: OrderGroup?, isGroupOwner: Boolean) {
    if (group != null) {
      // change title bar
      changeMenuStatus(false)
      // refresh menu reward by group and start timer
      reloadMenu()
      startGroupOrderCartTimer()
      if (isGroupOwner) {
        // group owner show alert to invite
        DialogFacade.showAlert(
          this, titleId = com.ricepo.style.R.string.group_create_success_title,
          messageId = com.ricepo.style.R.string.group_create_success_subtitle,
          positiveId = com.ricepo.style.R.string.ok
        ) {
          showShareInvite()
        }
      }
    }
  }

  private fun showShareInvite() {
    // get dynamic link and show sheet bottom
    menuGroupViewModel.showShareInvite(this, menuViewModel.restaurantInfo)
  }

  override fun refreshGroupOrderCart() {
    super.refreshGroupOrderCart()

    lifecycleScope.launch {
      // refresh group order quantity
      menuGroupViewModel.updateGroupCartQuantity(menuRestaurant)?.collectLatest { result ->
//                ToastUtil.showToast(LocalDateTime.now().toString())
        if (menuGroupViewModel.groupId == null) {
          cancel()
        } else {
          result.fold(
            onSuccess = {
              reloadMenu()
            },
            onFailure = { error ->
              menuGroupViewModel.handleGroupError(
                this@MenuActivity,
                menuRestaurant, error
              ) {
                // re create group order when last group order closed
                groupId = null
                changeMenuStatus(false)
                reloadMenu()
              }
            }
          )
        }
      }
    }
  }

  private fun checkJoinGroupOrder(groupStatus: MenuGroupStatus) {
    // return if not enter from group share link or close group order
    if (groupId == null) return

    // listRestaurant empty is header nav
    if (!menuPageData?.listBundle.isNullOrEmpty() && !menuPageData?.listRestaurant.isNullOrEmpty()) {
      groupId = null
      changeBundleView(menuPageData, true)
      // bundle menu don't use group order
      DialogFacade.showAlert(this@MenuActivity, com.ricepo.style.R.string.bundle_select_alert)
      return
    }

    if (groupStatus is MenuGroupStatus.DiffShareGroup) {
      val restaurantName = groupStatus.groupInfo?.restaurant?.name?.localize() ?: ""
      DialogFacade.showPrompt(
        this@MenuActivity,
        message = ResourcesUtil.getString(
          com.ricepo.style.R.string.group_please_exit_subtitle, restaurantName
        ),
        title = ResourcesUtil.getString(com.ricepo.style.R.string.group_please_exit_title),
        positiveId = com.ricepo.style.R.string.group_please_exit_button
      ) {
        val groupId = groupStatus.groupInfo?.groupId
        val restId = groupStatus.groupInfo?.restaurant?.id
        FeaturePageRouter.navigateMenuAfterSubscription(
          this@MenuActivity,
          Restaurant(restId), groupId = groupId
        )
      }
    } else if (groupStatus is MenuGroupStatus.SameShareGroup) {
      val restaurantName = groupStatus.groupInfo?.restaurant?.name?.localize() ?: ""
      DialogFacade.showAlert(
        this@MenuActivity,
        message = ResourcesUtil.getString(
          com.ricepo.style.R.string.group_please_exit_subtitle, restaurantName
        ),
        title = ResourcesUtil.getString(com.ricepo.style.R.string.group_please_exit_title)
      )
    } else if (groupStatus is MenuGroupStatus.None) {
      // alert nickname to create group
      DialogFacade.showInput(
        this@MenuActivity, com.ricepo.style.R.string.group_join_title,
        hintId = com.ricepo.style.R.string.group_join_nickname,
        cancel = {
          DialogFacade.showPrompt(
            this@MenuActivity,
            titleId = com.ricepo.style.R.string.group_cancel_join_title,
            messageId = com.ricepo.style.R.string.group_cancel_join_subtitle,
            negative = {
              checkJoinGroupOrder(groupStatus)
            },
            positive = {
              // cancel join group order
              groupId = null
              changeBundleView(menuPageData)
            }
          )
        },
        confirm = { content ->
          createGroup(content, false)
        }
      )
    }
  }

  private fun closeGroupOrder() {
    lifecycleScope.launch {
      val isOwner = GroupOrderCache.isOwner(menuRestaurant?.id ?: "")
      val messageId = if (isOwner) {
        com.ricepo.style.R.string.group_close_subtitle_host
      } else {
        com.ricepo.style.R.string.group_close_subtitle_member
      }
      DialogFacade.showPrompt(this@MenuActivity, messageId, com.ricepo.style.R.string.group_close_title) {
        lifecycleScope.launch {
          menuGroupViewModel.deleteMenuGroup(this@MenuActivity)
            .collectLatest {
              stopGroupOrderCartTimer()
              groupId = null
              // change menu title bar
              changeMenuStatus(false)
              // change menu cart bar
              reloadMenu()
            }
        }
      }
    }
  }

  private fun reloadMenu() {
    menuViewModel.reloadTable(true)
  }

  /**
   * show title bar with normal or group
   */
  private val groupTag = "group"
  private fun showTitleBar(groupStatus: MenuGroupStatus) {
    when (groupStatus) {
      is MenuGroupStatus.DiffShareGroup,
      is MenuGroupStatus.DiffGroup,
      is MenuGroupStatus.None -> {
        binding.ivBack.setImageResource(com.ricepo.style.R.drawable.ic_back)
        binding.ivBack.tag = com.ricepo.style.R.drawable.ic_back
        binding.ivGroupOrder.setImageResource(com.ricepo.style.R.drawable.ic_group_order)
        binding.ivGroupOrder.tag = com.ricepo.style.R.drawable.ic_group_order
        binding.tvTitle.text = ""
      }
      is MenuGroupStatus.SameShareGroup,
      is MenuGroupStatus.SameGroup -> {
        binding.ivBack.tag = groupTag
        binding.ivGroupOrder.setImageResource(com.ricepo.style.R.drawable.ic_invite)
        binding.ivGroupOrder.tag = com.ricepo.style.R.drawable.ic_invite
        binding.tvTitle.text = ResourcesUtil.getString(com.ricepo.style.R.string.group_order)
      }
    }

    if (!menuViewModel.isMemberInGroup) {
      // rice pool
      bindPool(menuRestaurant?.pool)
    }
  }

  private fun showContentView(menuPageData: MenuPageData) {
    val position = menuPageData.sectionPosition
    val uiModels = menuPageData.uiModels
    val isTableRefresh = menuPageData.isTableRefresh
    FoodLargeImageUtil.calculateBigImageIds(menuRestaurant, menuPageData)
    when {
      isTableRefresh -> {
        // group refresh menu qty
        menuPageData.uiModels.mapIndexed { index, model ->
          val food = model.food
          if (food?.selectedCount != null || food == null) {
            // food is null by horizontal and gallery
            menuAdapter?.notifyItemChanged(index, food)
          } else if (model.category?.items?.any { it.id == food.id } == true) {

            if (model is MenuUiModel.MenuHorizontalSection) {
              model.models.filter { it.food?.id == food.id }.forEach {
                it.food?.selectedCount = food.selectedCount
              }
            } else if (model is MenuUiModel.MenuVerticalSection) {
              model.models.filter { it.food?.id == food.id }.forEach {
                it.food?.selectedCount = food.selectedCount
              }
            }
            model.category?.items?.filter { it.selectedCount != null }?.let {
              it.mapIndexed { _, galleryFood ->
                menuAdapter?.notifyItemChanged(index, galleryFood)
              }
            }
          } else {
            // pass
          }
        }
      }
      position == -1 -> {
        menuAdapter = MenuSectionAdapter(
          uiModels, menuSectionPersist, isMenuPage = true,
          addFood = { foodQty ->
            resetHideRecommend(binding.inMenuCart, false)
            lifecycleScope.launch {
              menuViewModel.addFood(foodQty.food, foodQty.position, foodQty.foodIndex)
            }
          },
          minusFood = { foodQty ->
            menuViewModel.minusFood(foodQty.food, foodQty.position, foodQty.foodIndex)
          },
          showMore = { category, position, showSelectedCount ->
            binding.rvMenu.itemAnimator = null
            menuViewModel.limitChange(category, position, showSelectedCount)
          },
          navMenu = {
            // hidden the search bar
            binding.appbarMenu.setExpanded(false)
            // nav menu section
            smoothScrollNavMenu(menuPageData?.listBundle?.getOrNull(it), true)
          },
          navigate = { model ->
            if (model.isJump) {
              // category groups categoryIndex
              showJumpView(model)
            } else {
              FeaturePageRouter.navigateMenuSubMore(
                this, menuViewModel.restaurantInfo, model.category,
                menuViewModel.promotionFoods, entrance
              )
              resetHideRecommend(binding.inMenuCart)
            }
          }
        )
        menuAdapter?.entrance = entrance
        binding.rvMenu.apply {
          adapter = menuAdapter
          // itemView visibility and alpha invalid
          itemAnimator = null
        }

        // set the menu listener to nav selected
        val listNav = menuPageData.listBundle
        if (!listNav.isNullOrEmpty()) {
          binding.rvMenu.tag = listNav
          binding.rvMenu.addOnScrollListener(onMenuNavScrollListener)
        }

        // operator for header navigation
        binding.shlMenuLayout.attachTo(binding.rvMenu)
      }
      else -> {
        // bundle needn't scroll
        bundleAdapter?.isBundleScroll = true
        menuAdapter?.isHeaderNavScroll = true
        menuAdapter?.models = uiModels
        menuAdapter?.notifyDataSetChanged()
      }
    }
  }

  private var jumpSheetFragment: CategoryJumpFragment? = null

  private fun showJumpView(model: CategoryModel?) {
    val jumps = model?.jumps ?: return
    val groups = model?.jumps.firstOrNull()?.groups
    val models = jumps.map {
      val items = it.category?.items?.take(2)?.map {
        CategoryJumpItem(it.name.localize(), it.image?.url, it.available)
      }
      CategoryJumpData(
        groupId = it.category?.groupId,
        itemPosition = it.itemPosition,
        title = it.category?.name?.localize(),
        items = items
      )
    }
    // reset fragment to avoid memory leak
    jumpSheetFragment = CategoryJumpFragment.newInstance(groups, models)
    jumpSheetFragment?.jump = { index ->
      smoothScrollMenu(true, index, isNotScroll = false)
    }
    jumpSheetFragment?.show(supportFragmentManager, CategoryJumpFragment.TAG)
  }

  private var isAlreadyCheckMenuStatus = false

  private var bundleSheetFragment: RestaurantBundleFragment? = null

  private var firstEnter = true
  private fun showRestaurantView(isHalf: Boolean) {
    // didn't show bundle when when rice pool
    if (menuRestaurant?.pool != null) return
    // didn't auto show when entrance is not null and first enter
    if (!isAlreadyCheckMenuStatus && (
      entrance != null &&
        entrance != FeaturePageConst.PAGE_COUPON
      )
    ) return

    val restaurants = menuPageData?.listRestaurant ?: return
    // don't auto popup when restaurant all closed
    if (menuViewModel.checkAllClosed(restaurants) && isHalf) return
    // bundle restaurants
    if (!restaurants.isNullOrEmpty() && isNotGroupOrder()) {
      // reset fragment to avoid ml
      bundleSheetFragment = RestaurantBundleFragment(
        menuRestaurant, restaurants,
        menuViewModel.bundles, isHalf,
        confirm = {
          menuViewModel.bundles = it
          // getRestaurant to reset flag
          menuViewModel.sectionPosition = -1
          menuViewModel.isTableRefresh = false
          aRefreshMenu.onNext(false)
          Loading.showLoading(this@MenuActivity)
          resetHideRecommend(binding.inMenuCart)
        },
        refreshMenu = {
          reloadMenu()
        }
      )
      try {
        bundleSheetFragment?.setBundles(menuViewModel.bundles)
        if (
          supportFragmentManager.findFragmentByTag(RestaurantBundleFragment.TAG) == null &&
          bundleSheetFragment?.isAdded == false &&
          !firstEnter
        ) {
          bundleSheetFragment?.show(supportFragmentManager, RestaurantBundleFragment.TAG)
        }
        firstEnter = false
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
  }

  private fun isNotGroupOrder(): Boolean {
    // groupId != null is can join group
    return groupId == null && (
      menuViewModel.mGroupStatus == null ||
        menuViewModel.mGroupStatus is MenuGroupStatus.None ||
        menuViewModel.mGroupStatus is MenuGroupStatus.DiffGroup ||
        menuViewModel.mGroupStatus is MenuGroupStatus.DiffShareGroup
      )
  }

  private fun initBundleAdapter(listBundle: List<MenuBundle>): MenuBundleAdapter {
    val adapter = bundleAdapter
    return if (adapter != null) {
      adapter.bundles = listBundle
      adapter
    } else {
      MenuBundleAdapter(listBundle) { lastPos, targetPos, isScrollMenu, isScrollBundle ->
        smoothScrollBundle(targetPos)
        translateBundleView(lastPos, targetPos)
        if (isScrollMenu) {
          val isScrollToTop = (targetPos > lastPos)
          // modify the bundle restaurant out of bounds
          smoothScrollBundleMenu(bundleAdapter?.bundles?.getOrNull(targetPos), isScrollToTop)
        }
        // firebase event
        FirebaseMonitor.logEvent(
          FirebaseEventName.rMenuSelectTab,
          FirebaseMenuSelectEvent(FirebaseMenuSelectEvent.TYPE_SIDE, targetPos)
        )
      }
    }
  }

  private fun translateBundleView(lastPosition: Int, targetPosition: Int) {
    val layoutManager = binding.rvSidebar.layoutManager as LinearLayoutManager
    val view = layoutManager.findViewByPosition(targetPosition) ?: return
    if (lastPosition == -1) {
      view.setBackgroundColor(ResourcesUtil.getColor(com.ricepo.style.R.color.primary_satisfied_button_fill, this))
      view.findViewById<VerticalTextView>(R.id.tv_bundle_name)?.setTextColor(
        ResourcesUtil.getColor(com.ricepo.style.R.color.bundle_bar_selected_text, this)
      )
      binding.rvSidebar.scrollToPosition(0)
    } else {
      val lastView = layoutManager.findViewByPosition(lastPosition)
      binding.tvBundleBg.isVisible = true
      binding.tvBundleBg.y = lastView?.y ?: 0f
      binding.tvBundleBg.x = lastView?.x ?: 0f
      val params = binding.tvBundleBg.layoutParams
      params.width = view.width
      params.height = min(lastView?.height ?: Int.MAX_VALUE, view.height)
      binding.tvBundleBg.layoutParams = params

      val startY = lastView?.y
        ?: if (targetPosition > lastPosition) 0f else binding.clSidebar.height.toFloat()
      val endY = view.y

      val animatorY = ObjectAnimator.ofFloat(
        binding.tvBundleBg, "translationY",
        startY, endY
      ).setDuration(200)
      animatorY.addUpdateListener {
      }
      animatorY.doOnStart {
        lastView?.setBackgroundColor(ResourcesUtil.getColor(com.ricepo.style.R.color.bundle_bar_background, this))
      }
      animatorY.doOnEnd {
        binding.tvBundleBg.isVisible = false
        view.setBackgroundColor(ResourcesUtil.getColor(com.ricepo.style.R.color.primary_satisfied_button_fill, this))

        view.findViewById<VerticalTextView>(R.id.tv_bundle_name)?.setTextColor(
          ResourcesUtil.getColor(com.ricepo.style.R.color.bundle_bar_selected_text, this)
        )
        lastView?.findViewById<VerticalTextView>(R.id.tv_bundle_name)?.setTextColor(
          ResourcesUtil.getColor(com.ricepo.style.R.color.bundle_bar_text, this)
        )
      }

      val animatorSet = AnimatorSet()
      animatorSet.playTogether(
        animatorY
      )
      animatorSet.start()
    }
  }

  private fun showBundleView(menuPageData: MenuPageData) {
    // didn't show bundle when when rice pool
    if (menuRestaurant?.pool != null) return

    changeBundleView(menuPageData)

    val listBundle = menuPageData.listBundle
    if (listBundle.isNullOrEmpty() || menuPageData.listRestaurant.isNullOrEmpty()) return

    // visible to measure layout
    binding.clSidebar.isVisible = true
    var layoutManager: LinearLayoutManager = LinearHeightLayoutManager(this)
    binding.rvSidebar.layoutManager = layoutManager

    bundleAdapter = initBundleAdapter(listBundle)
    binding.rvSidebar.adapter = bundleAdapter
    binding.rvSidebar.recycledViewPool.setMaxRecycledViews(0, 0)

    binding.rvSidebar.post {
      val totalHeight = (layoutManager as LinearHeightLayoutManager).totalHeight
//            val halfHeight = DisplayUtil.getScreenHeight() / 2
      val halfHeight = DisplayUtil.getScreenHeight().times(0.45).toInt()
      val height = min(totalHeight, halfHeight)

      val params = binding.clSidebar.layoutParams as LinearLayout.LayoutParams
      params.height = height
      binding.clSidebar.layoutParams = params
      layoutManager = LinearLayoutManager(this@MenuActivity)
      binding.rvSidebar.layoutManager = layoutManager

      // show down arrow
      if (totalHeight > halfHeight) {
        binding.ivBundleDown.isVisible = true
      }

      // init bundle scroll to top
//            smoothScrollBundle(0)
      bundleAdapter?.selectPosition(0)
    }

    binding.ivBundleUp.clickWithTrigger {
      val pos = bundleAdapter?.scrollLastPosition() ?: -1
//            smoothScrollBundle(pos)
      bundleAdapter?.selectPosition(pos)
    }

    binding.ivBundleDown.clickWithTrigger {
      val pos = bundleAdapter?.scrollNextPosition() ?: -1
//            smoothScrollBundle(pos)
      bundleAdapter?.selectPosition(pos)
    }

    binding.rvSidebar.addOnScrollListener(onBundleScrollListener)

    binding.rvMenu.tag = listBundle
    binding.rvMenu.addOnScrollListener(onMenuScrollListener)
  }

  private val onBundleScrollListener = object : RecyclerView.OnScrollListener() {
    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
      super.onScrollStateChanged(recyclerView, newState)
      if (RecyclerView.SCROLL_STATE_IDLE == newState) {
        checkBundleUpDown(recyclerView)
//                bundleAdapter?.isBundleScroll = false
      }
    }

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
      super.onScrolled(recyclerView, dx, dy)
    }
  }

  private val onMenuScrollListener = object : RecyclerView.OnScrollListener() {

    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
      super.onScrollStateChanged(recyclerView, newState)
      if (RecyclerView.SCROLL_STATE_IDLE == newState) {
        bundleAdapter?.isBundleScroll = false
      }
    }

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
      super.onScrolled(recyclerView, dx, dy)
      if (bundleAdapter?.isBundleScroll == true) return
      val listBundle = recyclerView.tag
      if (listBundle is List<*>) {
        listBundle as List<MenuBundle>
      } else return
      val layoutManager = recyclerView.layoutManager as LinearLayoutManager
      val firstVisiblePosition = layoutManager.findLastCompletelyVisibleItemPosition()
      val groupId = menuAdapter?.models?.getOrNull(firstVisiblePosition)?.groupId
      var position = -1
      listBundle.forEachIndexed { index, bundle ->
        if (groupId == bundle.id && position == -1) {
          position = index
        }
      }
      if (position == -1) return
//            smoothScrollBundle(position, false)
      bundleAdapter?.selectPosition(position, isScrollMenu = false)
    }
  }

  private val onMenuNavScrollListener = object : RecyclerView.OnScrollListener() {

    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
      super.onScrollStateChanged(recyclerView, newState)
      if (RecyclerView.SCROLL_STATE_IDLE == newState) {
        menuAdapter?.isHeaderNavScroll = false
      }
    }

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
      super.onScrolled(recyclerView, dx, dy)
      if (menuAdapter?.isHeaderNavScroll == true) return
      val listBundle = recyclerView.tag
      if (listBundle is List<*>) {
        listBundle as List<MenuBundle>
      } else return
      val layoutManager = recyclerView.layoutManager as LinearLayoutManager
      val firstVisiblePosition = if (dy > 0) {
        layoutManager.findLastCompletelyVisibleItemPosition()
      } else {
        layoutManager.findFirstCompletelyVisibleItemPosition()
      }
      // category id for navigation
      var navigationId = menuAdapter?.models?.getOrNull(firstVisiblePosition)?.navigationId
      var position = -1
      listBundle.forEachIndexed { index, bundle ->
        if (navigationId == bundle.id && position == -1) {
          position = index
        }
      }
      // header navigation layout handle
      selectNavLayoutPosition(position)
    }
  }

  private fun selectNavLayoutPosition(position: Int) {
    if (position == -1) return

    if (binding.shlMenuLayout.childCount > 1) {
      val layout = binding.shlMenuLayout.getChildAt(1)
      val rcvView = layout.findViewById<RecyclerView?>(R.id.rcv_menu_navigation)
      if (rcvView != null) {
        val adapter = rcvView.adapter
        if (adapter is MenuNavigationAdapter) {
          adapter.selectSelection(position)
        }
      }
    }
    // invoke recycler view item to select position
    menuAdapter?.selectPosition(position)
  }

  private val onMenuMarqueeListener = object : RecyclerView.OnScrollListener() {

    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
      super.onScrollStateChanged(recyclerView, newState)
      if (newState == RecyclerView.SCROLL_STATE_IDLE) {
        stopDescriptionMarquee(false)
      }
    }
    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
      super.onScrolled(recyclerView, dx, dy)
//            Log.d("thom", "marquee dy $dy")
      stopDescriptionMarquee(dy != 0)
    }
  }

  private fun stopDescriptionMarquee(isStop: Boolean) {
    val childCount = binding.rvMenu.childCount
    for (i in 0 until childCount) {
      try {
        binding.rvMenu.get(i).findViewById<View?>(R.id.tv_food_recommend)?.let {
          it.isSelected = !isStop
        }
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
  }

  private fun smoothScrollNavMenu(
    bundle: MenuBundle?,
    isScrollToTop: Boolean = false
  ) {
    val position = menuAdapter?.models?.indexOfFirst {
      it is MenuUiModel.MenuNavigationSection
    } ?: return
    smoothScrollMenu(true, position)
    Handler().postDelayed(
      {
        smoothScrollBundleMenu(bundle, isScrollToTop = isScrollToTop, isNav = true)
      },
      120
    )
  }

  private fun smoothScrollBundleMenu(
    bundle: MenuBundle?,
    isScrollToTop: Boolean = false,
    isNav: Boolean = false
  ) {
    val bundle = bundle ?: return
    val position = menuAdapter?.models?.indexOfFirst {
      if (isNav) { it.navigationId == bundle.id } else { it.groupId == bundle.id }
    } ?: return

    smoothScrollMenu(isScrollToTop, position)
  }

  private fun smoothScrollMenu(isScrollToTop: Boolean, position: Int, isNotScroll: Boolean = true) {
    try {
      bundleAdapter?.isBundleScroll = isNotScroll
      menuAdapter?.isHeaderNavScroll = isNotScroll
      val layoutManager = binding.rvMenu.layoutManager as LinearLayoutManager
      val firstVisiblePos = layoutManager.findFirstVisibleItemPosition()
      val lastVisiblePos = layoutManager.findLastVisibleItemPosition()
      // the item height is small
      val isScrollToTop =
        isScrollToTop || (position in (firstVisiblePos + 1) until lastVisiblePos)
      if (isScrollToTop) {
        val topSmoothScroller = TopSmoothScroller(this)
        topSmoothScroller.targetPosition = position
        layoutManager.startSmoothScroll(topSmoothScroller)
      } else {
        // show more can smooth menu notifyItemRange
        binding.rvMenu.smoothScrollToPosition(position)
      }
    } catch (e: Exception) {
      e.printStackTrace()
      bundleAdapter?.isBundleScroll = false
      menuAdapter?.isHeaderNavScroll = false
    }
  }

  private fun smoothScrollBundle(pos: Int) {
    if (pos == -1) return

    val layoutManager = binding.rvSidebar.layoutManager as LinearLayoutManager
    val firstVisiblePos = layoutManager.findFirstVisibleItemPosition()
    val lastVisiblePos = layoutManager.findLastVisibleItemPosition()
    Log.i(
      "thom",
      "targetPos = $pos firstVisiblePos = $firstVisiblePos" +
        " lastVisiblePos = $lastVisiblePos"
    )

    when {
      pos >= lastVisiblePos -> {
        val bottomScroller = BottomSmoothScroller(this)
        bottomScroller.targetPosition = pos
        layoutManager.startSmoothScroll(bottomScroller)
      }
      pos <= firstVisiblePos -> {
        val topScroller = TopSmoothScroller(this)
        topScroller.targetPosition = pos
        layoutManager.startSmoothScroll(topScroller)
      }
      else -> {
        if (bundleAdapter?.selectedPosition == pos) return
        binding.rvSidebar.smoothScrollToPosition(pos)
      }
    }

    if (pos == 0) {
      binding.ivBundleUp.isVisible = false
    }
    if (pos == ((bundleAdapter?.itemCount ?: 1) - 1)) {
      binding.ivBundleDown.isVisible = false
    }
  }

  private fun changeBundleView(menuPageData: MenuPageData?, isAlreadyBundle: Boolean = false) {
    // didn't show bundle when when rice pool
    if (menuRestaurant?.pool != null) return

    val pageData = menuPageData ?: return
    val restaurantSize = pageData.listRestaurant?.size ?: 0
    val size = pageData.listBundle?.size ?: 0

    // hide the side bar and show header nav when modify data of backend
    binding.clSidebar.isVisible = size > 0 && restaurantSize > 0
    binding.clSidebarOpt.isVisible = restaurantSize > 0

    if (restaurantSize == 0) return

    // show bundle opt with not group order
    binding.clSidebar.isVisible = (size > 0) && (isNotGroupOrder() || isAlreadyBundle)
    // don't show bundle opt when the host restaurant maybe closed
    binding.clSidebarOpt.isVisible = (restaurantSize > 0) && (isNotGroupOrder() || isAlreadyBundle)
    binding.clSidebarOpt.clickWithTrigger {
      showRestaurantView(false)
    }

    // group order visibility ignore
    binding.collapseMenu.getConstraintSet(R.id.menu_start)
      .getConstraint(binding.ivGroupOrder.id)
      .propertySet.mVisibilityMode = 1

    val params = binding.clSidebarOpt.layoutParams as LinearLayout.LayoutParams
    if (size > 0) {
      params.topMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_10)
      binding.tvBundleOpt.setText(ResourcesUtil.getString(com.ricepo.style.R.string.bundle_change))
      binding.clSidebarOpt.setBackgroundColor(ResourcesUtil.getColor(com.ricepo.style.R.color.bundle_bar_background, this))
      binding.tvBundleOpt.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.bundle_bar_text, this))
      // not show group invite
      binding.ivGroupOrder.isVisible = false
      // not show delivery prompt
      binding.inMenuCart.tvDelivery.isVisible = false
    } else {
      params.topMargin = 0
      binding.tvBundleOpt.setText(ResourcesUtil.getString(com.ricepo.style.R.string.bundle_title))
      binding.clSidebarOpt.setBackgroundColor(ResourcesUtil.getColor(com.ricepo.style.R.color.primary_satisfied_button_fill, this))
      binding.tvBundleOpt.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.bundle_bar_selected_text, this))
      //
      binding.ivGroupOrder.isVisible = true
      // show delivery prompt
      binding.inMenuCart.tvDelivery.isVisible = true
    }
    binding.clSidebarOpt.layoutParams = params
  }

  private fun checkBundleUpDown(recyclerView: RecyclerView) {
    val isTopScroll = recyclerView.canScrollVertically(-1)
    val isBottomScroll = recyclerView.canScrollVertically(1)

    // bottom down visible
    binding.ivBundleDown.isVisible = isBottomScroll

    // top up visible
    binding.ivBundleUp.isVisible = isTopScroll
  }

  private fun initSearchBar() {

    // set visibility mode (1 - ignore, 0 - normal)
    binding.collapseMenu.getConstraintSet(R.id.menu_start)
      .getConstraint(binding.etMenuSearch.id)
      .propertySet.mVisibilityMode = 1
    binding.etMenuSearch.isVisible = false

    // rice pool visibility mode
    binding.collapseMenu.getConstraintSet(R.id.menu_start)
      .getConstraint(binding.rtvPool.id)
      .propertySet.mVisibilityMode = 1

    KeyboardVisibilityEvent.setEventListener(
      this,
      object : KeyboardVisibilityEventListener {
        override fun onVisibilityChanged(isOpen: Boolean) {
          val keyboard = binding.etMenuSearch.text.toString()
          if (!isOpen && keyboard.isNotEmpty()) {
            // keyboard hidden
            AnalyticsFacade.logEvent(
              FirebaseMenuSearchEvent(keyboard),
              FirebaseEventName.rSearchMenu
            )
          }
        }
      }
    )
  }

  /**
   * forbidden change search bar when refresh group order
   */
  private var isShownSearchBar = false

  /**
   * appbar layout with motion layout
   */
  private fun systolicSearchBar() {
    if (isShownSearchBar) return

    binding.etMenuSearch.isVisible = true
    if (binding.rvMenu.isVisibleBottom()) {
      binding.etMenuSearch.setHint(com.ricepo.style.R.string.search_menu)
      binding.appbarMenu.setExpanded(true)
    } else {
      val params = binding.collapseMenu.layoutParams as AppBarLayout.LayoutParams
      params.scrollFlags = AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL or
        AppBarLayout.LayoutParams.SCROLL_FLAG_EXIT_UNTIL_COLLAPSED
      binding.collapseMenu.layoutParams = params
      val listener = AppBarLayout.OnOffsetChangedListener { appBar, verticalOffset ->
        val seekPosition = -verticalOffset / appBar.totalScrollRange.toFloat()
        binding.collapseMenu.progress = seekPosition
      }
      binding.appbarMenu.addOnOffsetChangedListener(listener)
      Handler().postDelayed(
        {
          binding.appbarMenu.setExpanded(false)
        },
        200
      )
    }
    isShownSearchBar = true
  }

  /**
   * appbarlayout with collapsingtoolbarlayout
   */
  private fun showSearchBar() {
    if (isShownSearchBar) return
    var params = binding.collapseMenu.layoutParams as AppBarLayout.LayoutParams
    binding.appbarMenu.visibility = View.VISIBLE
    if (binding.rvMenu.isVisibleBottom()) {
      params.scrollFlags = AppBarLayout.LayoutParams.SCROLL_FLAG_EXIT_UNTIL_COLLAPSED
//                et_menu_search.setCollapse
      binding.appbarMenu.setExpanded(true)
      binding.etMenuSearch.setHint(com.ricepo.style.R.string.search_menu)
    } else {
      Handler().postDelayed(
        {
          binding.appbarMenu.setExpanded(false)
        },
        100
      )
      params.scrollFlags = AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL.or(
        AppBarLayout.LayoutParams.SCROLL_FLAG_EXIT_UNTIL_COLLAPSED
      )
    }
    binding.collapseMenu.layoutParams = params
    isShownSearchBar = true
  }

  private fun showTryOtherView() {
    val message = ResourcesUtil.getString(com.ricepo.style.R.string.error_menu_not_found)
    showErrorView(
      binding.flMenuPage, com.ricepo.style.R.drawable.ic_rice_1,
      com.ricepo.style.R.string.error_title_menu_not_found, message, com.ricepo.style.R.string.try_other_restaurant,
      isCleanWhenClick = true,
      View.OnClickListener {
        onBackPressed()
      }
    )
  }

  private fun showErrorNetworkView(message: String?) {
    showErrorView(
      binding.flMenuPage, com.ricepo.style.R.drawable.ic_error_no_network,
      com.ricepo.style.R.string.error_title_load_failed, message, com.ricepo.style.R.string.retry,
      isCleanWhenClick = true,
      View.OnClickListener {
        bindEvent(menuRestaurant)
        aRefreshMenu.onNext(true)
      }
    )
  }

  private fun bindPool(pool: RestaurantPool?) {
    if (pool == null) {
      return
    }
    binding.rtvPool.isVisible = true
    binding.rtvPool.setMessage(pool.message?.localize())
    binding.rtvPool.setExpireAt(pool.expiresAt)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_MENU_OPTIONS) {
        val food = data?.getParcelableExtra<Food>(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD)
        lifecycleScope.launch {
          // options select position
          menuViewModel.addFood(food, -1, null, false)
        }
      }
      // image feedback success
      if (requestCode == FeaturePageConst.REQUEST_CODE_MENU_FEEDBACK) {
        DialogFacade.showAlert(this, ResourcesUtil.getString(com.ricepo.style.R.string.thank_rating))
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_CHECKOUT ||
        requestCode == FeaturePageConst.REQUEST_CODE_MENU_SUBMORE ||
        requestCode == FeaturePageConst.REQUEST_CODE_MENU_SEARCH
      ) {
        // hide recommend menu
        resetHideRecommend(binding.inMenuCart)
        reloadMenu()
        changeMenuStatus(false, true)
      }
    }
  }

  override fun onBackPressed() {
    if (entrance == FeaturePageConst.PAGE_SUBSCRIPTION) {
      FeaturePageRouter.navigateHomeWithReset(this)
      backEvent()
    } else {
      super.onBackPressed()
    }
  }
}
