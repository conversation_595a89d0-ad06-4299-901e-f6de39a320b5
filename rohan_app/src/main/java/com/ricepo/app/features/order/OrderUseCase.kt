package com.ricepo.app.features.order

import android.content.Context
import com.google.android.gms.maps.model.LatLng
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.checkout.data.OrderCellInfo
import com.ricepo.app.features.order.data.OrderState
import com.ricepo.app.model.Fees
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderItem
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.model.SupportRuleGroup
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restaurant.RestaurantMapper
import com.ricepo.app.restaurant.RestaurantRemote
import com.ricepo.base.BaseUseCase
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.deepCopy
import com.ricepo.base.model.Cart
import com.ricepo.base.model.DriverPoint
import com.ricepo.base.model.DriverTrace
import com.ricepo.base.model.Restaurant
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.style.ResourcesUtil
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import java.util.Calendar
import javax.inject.Inject
import kotlin.math.roundToInt

//
// Created by Thomsen on 22/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OrderUseCase @Inject constructor(
  private val repository: RestaurantRemote,
  private val combineRepo: CombineRestApi,
  private val postExecutionThread: PostExecutionThread,
  private val restaurantMapper: RestaurantMapper,
  private val orderMapper: OrderMapper
) : BaseUseCase() {

  /**
   * construct the fees array
   */
  private fun updateFeesInfo(context: Context, order: Order): List<OrderCellInfo> {
    var cellInfos = mutableListOf<OrderCellInfo>()

    val country = order.restaurant?.address?.innerItemValue?.country
    val diff = order.fees?.delta ?: 0
    val subtotal = order.subtotal
    val tax = order.fees?.tax ?: 0
    val delivery = order.fees?.delivery ?: 0
    val tip = order.fees?.tip?.amount ?: 0
    var total = order.total
    val serviceFee = order.fees?.service ?: 0

    val comments = order.comments
    if (!comments.isNullOrEmpty()) {
      cellInfos.add(
        OrderCellInfo(
          leftText = comments,
          rightText = ""
        )
      )
    }

    if (!isPickMode(order) && diff > 0) {
      cellInfos.add(
        OrderCellInfo(
          leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_delta),
          rightText = orderMapper.formatPrice(diff, country)
        )
      )
    }

    // subtotal
    cellInfos.add(
      OrderCellInfo(
        leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_subtotal),
        rightText = orderMapper.formatPrice(subtotal, country)
      )
    )

    // service fee and tax
    if (serviceFee > 0) {
      cellInfos.add(
        OrderCellInfo(
          leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_tax_and_service),
          rightText = orderMapper.formatPrice(tax + serviceFee, country),
          leftTips = serviceFeesInfo(
            order.restaurant, subtotal = order.subtotal,
            Fees(tax = tax, service = serviceFee)
          )
        )
      )
    } else if (tax > 0) {
      cellInfos.add(
        OrderCellInfo(
          leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_tax),
          rightText = orderMapper.formatPrice(tax, country)
        )
      )
    }

    // delivery and tips
    if (!isPickMode(order)) {
      cellInfos.add(
        OrderCellInfo(
          leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_delivery),
          rightText = orderMapper.formatPrice(delivery, country)
        )
      )

      // tip
      if (tip > 0) {
        cellInfos.add(
          OrderCellInfo(
            leftText = ResourcesUtil.getString(com.ricepo.style.R.string.fees_tip),
            rightText = orderMapper.formatPrice(tip, country)
          )
        )
      }
    }

    // adjustments using multiple lines
    val adj = order.adj
    if (adj != null) {
      val adjLines = adj.filter { it.customer != null && it.customer != 0 }
        .map {
          val cuPrice = it.customer ?: 0
          val priceStr = if (cuPrice > 0) "-${orderMapper.formatPrice(cuPrice, country)}" else
            "${orderMapper.formatPrice(-cuPrice, country)}"
          OrderCellInfo(
            leftText = it.reason ?: "",
            rightText = priceStr,
            color = ResourcesUtil.getColor(com.ricepo.style.R.color.green, context)

          )
        }
      cellInfos.addAll(adjLines)
    }

    // adjust on total
    val adjust = order.adjustments?.customer
    if (adjust != null) {
      total = if (total > adjust) total - adjust else 0
    }

    // total
    cellInfos.add(
      OrderCellInfo(
        leftText = ResourcesUtil.getString(com.ricepo.style.R.string.total),
        rightText = orderMapper.formatPrice(total, country),
        large = true
      )
    )

    return cellInfos
  }

  /**
   * link CheckoutUseCase
   */
  private fun serviceFeesInfo(restaurant: Restaurant?, subtotal: Int, fees: Fees?): String? {
    if (restaurant == null) return null
    val tax = fees?.tax ?: 0
    val service = fees?.service ?: 0
    if (tax == 0 && service == 0) return null
    val restaurantTax = restaurant?.tax ?: 0.0
    val factor = restaurant?.serviceFee?.factor ?: 0.0
    val flat = restaurant?.serviceFee?.flat ?: 0

    var alertInfo = StringBuffer(ResourcesUtil.getString(com.ricepo.style.R.string.fees_tax_service_include))
    if (tax > 0) {
      alertInfo.append(" ")
      alertInfo.append(
        orderMapper.formatPriceByRestaurant(
          restaurantTax
            .times(subtotal).roundToInt(),
          restaurant
        )
      )
      alertInfo.append(" ")
      alertInfo.append(ResourcesUtil.getString(com.ricepo.style.R.string.fees_tax_service_tax))

      if (service > 0) {
        alertInfo.append(ResourcesUtil.getString(com.ricepo.style.R.string.fees_tax_service_and))
      }
    }
    if (service > 0 && (factor > 0 || flat > 0)) {
      val amount = (factor.times(subtotal) + flat).roundToInt()
      alertInfo.append(" ")
      alertInfo.append(orderMapper.formatPriceByRestaurant(amount, restaurant))
      alertInfo.append(" ")
    }
//        if (service > 0 && flat > 0) {
//            alertInfo.append("+ ")
//            alertInfo.append(orderMapper.formatPriceByRestaurant(flat, restaurant))
//            alertInfo.append(" ")
//        }
    if (service > 0) {
      alertInfo.append(ResourcesUtil.getString(com.ricepo.style.R.string.fees_tax_service_fee))
    }

    return alertInfo.toString()
  }

  /**
   * return pick mode status
   */
  private fun isPickMode(order: Order?): Boolean {
    return order?.delivery == null
  }

  /**
   * return new order from parse the order
   */
  fun parse(context: Context, o: Order?, driverLocation: DriverPoint?, driverPolyline: List<LatLng>?):
    Observable<OrderViewModel.OrderOption> {
    val order = o?.deepCopy() ?: return Observable.just(OrderViewModel.OrderOption())

    return Observable.create<OrderViewModel.OrderOption> { emitter ->
      // check fees.delta is greater than 0
//            val delta = order.fees?.delta
//            if (delta != null && delta > 0) {
//                val restaurantTax = order.restaurant?.tax ?: 0.0
//                val diff = delta.toDouble().div(1 + restaurantTax)
//                // get the different between minimum price and subtotal from delta
//                order.fees?.diff = diff.toInt()
//
//                // add this part to total tax
//                val tax = (order?.fees?.tax ?: 0).plus(diff.times(restaurantTax).toInt())
//                order.fees?.tax = tax
//            }

      // prompt for permission when finish the payment (android not need)
      if (listOf(
          OrderState.CONFIRMED, OrderState.CREATED,
          OrderState.SENT
        ).contains(order.status)
      ) {
        // notification

        // upload user property to firebase
        try {
          val cus = CustomerCache.getCustomer()
          AnalyticsFacade.updateUserProp(cus)
        } catch (e: Exception) {}
      }

      // combine same items
      order.items = orderMapper.groupOrderItems(o.items ?: listOf())

      // format both old and new stripe
      var orderLast4: String? = null
      var orderBrand: String? = null
      // old
      if (order.stripe?.source?.innerItemValue?.last4?.isNullOrEmpty() == false) {
        orderLast4 = order?.stripe?.source?.innerItemValue?.last4
        orderBrand = order?.stripe?.source?.innerItemValue?.brand
      }
      // new
      if (order?.stripe?.source?.stringValue == PaymentOwnMethod.INTENT) {
        val card = order.stripe?.intent?.charges?.data?.get(0)?.paymentMethodDetails?.card
        orderLast4 = card?.last4
        orderBrand = card?.brand
      }

      // construct the fees array
      val cellInfos = updateFeesInfo(context, order)

      emitter.onNext(
        OrderViewModel.OrderOption(
          order, cellInfos,
          orderLast4 = orderLast4, orderBrand = orderBrand,
          driverLocation = driverLocation, driverPolyline = driverPolyline
        )
      )
    }
  }

  /**
   * return order by [orderId]
   */
  fun getOrderById(orderId: String): Observable<Result<Order>> {
    return Observable.create<Result<Order>> { emitter ->
      val single = repository.getOrderById(orderId)
      addDisposable(
        single.subscribeWith(object : DisposableSingleObserver<Order>() {
          override fun onSuccess(t: Order) {
            if (t != null) {
              emitter.onNext(Result.success(t))
            }
          }

          override fun onError(e: Throwable) {
            if (e != null) {
              emitter.onNext(Result.failure(e))
            }
          }
        })
      )
    }.subscribeOn(postExecutionThread.ioScheduler).share()
  }

  /**
   * return point of driver
   */
  fun getDriverLocation(orderId: String): Observable<Result<DriverTrace>> {
    return repository.getDriverTrace(orderId)
      .flatMapObservable {
        Observable.just(Result.success(it))
      }
      .onErrorReturn { Result.failure(it) }
  }

  fun validCreatedAt(createdAt: String): Calendar? {
    return orderMapper.toDate(createdAt)
  }

  fun toCartList(orderItems: List<OrderItem>): List<Cart>? {
    return orderItems?.fold(mutableListOf<Cart>()) { result, item ->
      result.addAll(orderMapper.mapCart(item))
      result
    }
  }

  fun updateRestaurant(restaurant: Restaurant): Restaurant {

    // get closed value from selected restaurant
    val closedString = restaurantMapper.mapClosed(restaurant)

    val isClosed = closedString != null
    val isRestaurantClosed = restaurant.isClosed != null

    // if changed refresh restaurant
    val isRefresh = (isClosed != isRestaurantClosed)

    if (isRefresh) {
      restaurant.isClosed = closedString
    }

    return restaurant
  }

  suspend fun getOrderSupportConfig(orderId: String): List<SupportRuleGroup> {
    return combineRepo.getOrderSupportGroupConfig(orderId)
  }
  
  suspend fun getReferInfo(): ReferInfo? {
    return try {
      combineRepo.getRefer()
    } catch (e: Exception) {
       null
    }
  }
}
