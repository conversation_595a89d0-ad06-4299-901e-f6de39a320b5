package com.ricepo.app.features.menu

import android.content.Context
import android.graphics.BitmapFactory
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.viewModelScope
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.data.MenuGroupStatus
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.base.BaseApplication
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.model.Cart
import com.ricepo.base.model.OrderGroup
import com.ricepo.base.model.OrderGroupRest
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.tools.IntentUtils
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.NetworkError
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.sheet.BaseBottomSheetFragment
import com.ricepo.tripartite.google.DynamicLinks
import com.ricepo.tripartite.wechat.WeChatShare
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.ReceiveChannel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import java.net.URLEncoder
import java.util.Calendar
import java.util.Timer
import javax.inject.Inject

//
// Created by Thomsen on 18/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class MenuGroupViewModel @Inject constructor(
  private val menuGroupUseCase: MenuGroupUseCase
) : BaseViewModel() {

  private var shareUrl: String? = null

  var groupId: String? = null

  fun initGroupId(groupId: String?) {
    this.groupId = groupId
  }

  fun checkGroupStatus(restaurant: Restaurant?): Flow<MenuGroupStatus> {
    return flow {
      // check the group order
      val groupStatus = menuGroupUseCase.checkGroupStatus(restaurant, groupId)
      if (groupStatus is MenuGroupStatus.SameGroup ||
        groupStatus is MenuGroupStatus.SameShareGroup
      ) {
        // init groupId from local cache
        groupId = GroupOrderCache.getGroupId()
      } else {
        groupId = null
      }
      emit(groupStatus)
    }
  }

  fun createMenuGroup(
    context: ComponentActivity,
    nickname: String?,
    groupId: String? = null,
    restaurantCart: RestaurantCart?,
    restaurantInfo: Restaurant?,
    isGroupOwner: Boolean
  ): Flow<Result<OrderGroup>> {

    return flowLoading<Result<OrderGroup>>(context) {

      if (nickname == null || nickname.trim().isEmpty()) {
        val message = ResourcesUtil.getString(com.ricepo.style.R.string.group_create_group_order_placeholder)
        emit(Result.failure(Exception(message)))
      } else {

        val customer = CustomerCache.getCustomerSuspend()
        val customerId = customer?.id ?: ""
        val deviceId = BaseApplication.mDeviceId
        var items = RestaurantCartCache.getRestaurantCartSuspend(restaurantInfo)?.cartList ?: listOf<Cart>()
        val restaurant = OrderGroupRest(
          restaurantCart?.restaurant?.id ?: "",
          restaurantCart?.restaurant?.name
        )

        val groupId = groupId
        val nickname = nickname

        try {

          // prepare to clear items reward if not group host
          if (!isGroupOwner) {
            items = items.filter { it.reward != true }
          }

          val groupOrder = menuGroupUseCase.createOrderGroup(
            customerId, deviceId, items,
            restaurant, isGroupOwner, groupId, nickname
          )

          if (groupOrder != null) {

            updateLocalGroupInfo(restaurantInfo, groupOrder)

            // clear reward if not group host after create group success
            if (!isGroupOwner) {
              menuGroupUseCase.clearReward(restaurantInfo)
            }

            emit(Result.success(groupOrder))
          }
        } catch (e: Exception) {
          emit(Result.failure(e.parseByBuzNetwork()))
        }
      }
    }
  }

  fun showShareInvite(context: ComponentActivity, restaurant: Restaurant?) {
    viewModelScope.launch {
      flowLoading<String?>(context) {
        val groupId = GroupOrderCache.getGroupId()
        if (groupId != null) {
          val shortLink = DynamicLinks.getGroupOrderLink(restaurant?.id ?: "", groupId)
          // direct use shortLink
          if (shortLink != null) {
            shareUrl = shortLink.toString()

            val restaurantName = GroupOrderCache.getRestaurantName()
            emit(restaurantName)
          } else {
            DialogFacade.showAlert(context, com.ricepo.style.R.string.error_load_failed)
          }
        }
      }.collect { restaurantName ->
        showInviteSheet(context, restaurantName)
      }
    }
  }

  /**
   * show invite bottom sheet
   */
  private fun showInviteSheet(context: Context, restaurantName: String?) {
    val shareLink = shareUrl ?: return
    Log.i("thom", "group share url = $shareUrl")
    val wechat = ResourcesUtil.getString(com.ricepo.style.R.string.group_share_wechat)
    val others = ResourcesUtil.getString(com.ricepo.style.R.string.group_share_other)
    val sharedTexts = listOf(wechat, others)
    val title = ResourcesUtil.getString(com.ricepo.style.R.string.group_share_title)
    val bottomSheet = BaseBottomSheetFragment.newInstance<String>(sharedTexts)
    val supportFragmentManager = (context as AppCompatActivity).supportFragmentManager
    bottomSheet.show(supportFragmentManager, "sheet_group_shared")
    bottomSheet.onItemTextClickListener = object : BaseBottomSheetFragment.OnItemTextClickListener {
      override fun onItemClick(text: String) {
        if (wechat == text) {
          val shareLink = "https://riceeu-static.s3-eu-west-1.amazonaws.com/restaurants/index.html?restaurant=${
          URLEncoder.encode(restaurantName, "UTF-8")
          }&dl=$shareLink"
          val wxTitle = ResourcesUtil.getString(com.ricepo.style.R.string.group_share_wechat_title)
          val content = ResourcesUtil.getString(com.ricepo.style.R.string.group_share_wechat_subtitle, (restaurantName ?: ""))
          val shareImage = BitmapFactory.decodeResource(context.resources, com.ricepo.style.R.drawable.ic_logo)

          WeChatShare.shareStringToWx(context, shareLink, wxTitle, content, shareImage)
        }
        if (others == text) {
          IntentUtils.shareToOthers(context, shareLink)
        }
      }
    }
  }

  /**
   * the group update time
   */
  private var groupUpdateAt: Calendar? = null

  /**
   * the group sync time
   */
  private var groupSyncAt: Calendar? = null

  /**
   * update group cart quantity of server
   */
  fun updateGroupCartQuantity(
    context: ComponentActivity,
    carts: List<Cart>?,
    restaurant: Restaurant?
  ): Flow<OrderGroup>? {
    val carts = carts ?: return null
    val groupId = groupId ?: return null

    return flowLoading<OrderGroup>(context) {
      groupUpdateAt = Calendar.getInstance()
      emit(menuGroupUseCase.updateGroupCartQuality(groupId, carts))
      groupUpdateAt = null
    }
  }

  fun updateGroupCartQuantity(restaurant: Restaurant?): Flow<Result<Pair<OrderGroup?, Boolean>>>? {
    val groupId = groupId ?: return null
    return flow<Result<Pair<OrderGroup?, Boolean>>> {
      val restaurant = RestaurantCartCache.getRestaurantSuspend(restaurant)
      // refresh in common restaurant or not local restaurant cart
      if (GroupOrderCache.getGroupRestaurantId() == restaurant?.id || (
        GroupOrderCache.isGroupExist() && restaurant == null
        )
      ) {

        val orderGroup = try {
          menuGroupUseCase.getOrderGroup(groupId)
        } catch (e: Exception) {
          // network 502
          e.printStackTrace()
          emit(Result.failure(e))
          null
        }
        groupSyncAt = Calendar.getInstance()

        if (orderGroup != null && groupUpdateAt == null) {

          val localOrderGroup = GroupOrderCache.getOrderGroup()
          val isChanged = localOrderGroup?.list != orderGroup?.list
//                    Log.d("thom", "is $isChanged")

          updateLocalGroupInfo(restaurant, orderGroup)

          emit(Result.success(Pair(orderGroup, isChanged)))
        }
      }
      // Please refer to 'flow' documentation or use 'flowOn' instead
    }.flowOn(Dispatchers.Default)
  }

  suspend fun updateLocalGroupInfo(restaurant: Restaurant?, orderGroup: OrderGroup) {

    // update group order info
    GroupOrderCache.saveOrder(orderGroup)

    // clear old restaurant cart if the restaurant of the cart is not the grouped restaurant
    val restCart = RestaurantCartCache.getRestaurantCartSuspend(restaurant)
    if (restCart != null) {
      if (restCart?.restaurant?.id != orderGroup.restaurant?.id) {
        RestaurantCartCache.deleteRestaurantCart(restCart.restaurant)
      }

      // update restaurant cart info list
      val restaurantCart = RestaurantCart(
        cartList = GroupOrderCache.getSelfCartList(restCart?.restaurant?.id),
        restaurant = restCart?.restaurant
      )
      RestaurantCartCache.saveRestaurantCart(restaurantCart)
    }
  }

  suspend fun getAllCarts(foods: List<Cart>?, restaurant: Restaurant?): List<Cart> {
    return menuGroupUseCase.getAllCartsSuspend(foods, restaurant)
  }

  /**
   * handle 'the group is closed' error for create/get group info
   */
  fun handleGroupError(
    context: ComponentActivity,
    restaurant: Restaurant?,
    error: Throwable,
    isShowAlert: Boolean = false,
    block: () -> Unit
  ) {
    if (error is NetworkError && (
      ErrorCode.GROUP_IS_CLOSED == error.code ||
        ErrorCode.GROUP_IS_CLOSED_M == error.code
      )
    ) {
      val orderId = error.details?.get("orderId")
      if (orderId is String && orderId.isNotEmpty()) {
        DialogFacade.showAlert(context, com.ricepo.style.R.string.group_order_placed, canCancel = false) {
          FeaturePageRouter.navigateOrder(context, orderId)
        }
        // clear cart not add food
        viewModelScope.launch {
          RestaurantCartCache.deleteRestaurantCart(restaurant)
        }
      } else {
        DialogFacade.showAlert(
          context, com.ricepo.style.R.string.group_closed_subtitle,
          com.ricepo.style.R.string.group_closed_title, canCancel = false
        ) {
          // menu and checkout different interaction
//                    context.finish()
          block()
        }
      }
      // clear group order info
      viewModelScope.launch {
        GroupOrderCache.deleteOrder()
      }
    } else if (ErrorCode.BAD_GATEWAY_MESSAGE != error.message) {
      Log.e("thom", "502 ${error.message}")
      // don't display other error (network timeout etc.)
      // but create order failed need to show error
      if (isShowAlert) {
        DialogFacade.showAlert(context, error.message ?: "")
      }
    }
  }

  suspend fun deleteMenuGroup(context: ComponentActivity): Flow<Boolean> {
    return flowLoading<Boolean>(context) {
      try {
        menuGroupUseCase.deleteOrderGroup()
      } catch (e: Exception) {
        if (e is NetworkError && e.code == ErrorCode.RESPONSE_BODY_EMPTY) {
          // pass
        } else {
          DialogFacade.showAlert(context, e.parseByBuzNetwork().message ?: "")
          return@flowLoading
        }
      }
      // clear group order
      GroupOrderCache.deleteOrder()
      emit(true)
    }
  }

  private var tickerChannel: ReceiveChannel<Unit>? = null

  private var refreshTimer: Timer? = null

  private fun stopRefreshTimer() {
    refreshTimer?.cancel()
    refreshTimer = null
  }

  override fun onCleared() {
    super.onCleared()
    tickerChannel?.cancel()
    stopRefreshTimer()
  }
}
