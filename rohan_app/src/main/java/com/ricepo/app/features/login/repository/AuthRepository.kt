package com.ricepo.app.features.login.repository

import com.ricepo.app.model.UserInformation
import com.ricepo.base.model.Customer
import com.ricepo.base.model.GlobalConfigModel
import io.reactivex.rxjava3.core.Single

//
// Created by <PERSON><PERSON> on 7/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

interface AuthRepository {

  fun getVcode(phone: String, method: String): Single<Any>

  fun login(phone: String, vcode: String): Single<UserInformation>

  fun getCustomer(customerId: String): Single<Customer>

  fun resetToken(): Single<UserInformation>

  suspend fun getCountry(): Map<String, GlobalConfigModel>
}
