package com.ricepo.app.features.menu.preview

import android.os.Bundle
import android.transition.Transition
import android.view.MotionEvent
import androidx.core.view.ViewCompat
import com.ricepo.app.databinding.ActivityMenuPreviewMarketBinding
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant

//
// Created by <PERSON><PERSON> on 3/9/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class MenuMarketPreviewActivity : BaseActivity() {

  companion object {
    const val VIEW_MENU_IMAGE = "menu:image"

    const val BUNDLE_MENU_PREVIEW_FOOD = "menu_preview_food"
    const val BUNDLE_MENU_PREVIEW_RESTAURANT = "menu_preview_restaurant"
  }

  private lateinit var binding: ActivityMenuPreviewMarketBinding

  private var food: Food? = null
  private var restaurant: Restaurant? = null

  private var isMarket: Boolean = false

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityMenuPreviewMarketBinding.inflate(layoutInflater)
    setContentView(binding.root)

    food = intent.getParcelableExtra(BUNDLE_MENU_PREVIEW_FOOD)
    restaurant = intent.getParcelableExtra(BUNDLE_MENU_PREVIEW_RESTAURANT)

    ViewCompat.setTransitionName(binding.ivFoodImg, VIEW_MENU_IMAGE)

    setupListener()
  }

  private fun setupListener() {

    addTransitionListener()
    loadFullSizeImage()

    binding.root.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        onBackPressed()
      }
    }
  }

  private fun loadFullSizeImage() {
    loadMarketImage()
  }

  private fun loadMarketImage() {
    food?.image?.url?.let {
      ImageLoader.load(binding.ivFoodImg, it)
    }
  }

  private fun addTransitionListener(): Boolean {
    val transition = window.sharedElementEnterTransition
    if (transition != null) {
      // There is an entering shared element transition so add a listener to it
      transition.addListener(object : Transition.TransitionListener {
        override fun onTransitionEnd(transition: Transition) {
          // As the transition has ended, we can now load the full-size image
          loadFullSizeImage()

          // Make sure we remove ourselves as a listener
          transition.removeListener(this)
        }

        override fun onTransitionStart(transition: Transition) {
          // No-op
        }

        override fun onTransitionCancel(transition: Transition) {
          // Make sure we remove ourselves as a listener
          transition.removeListener(this)
        }

        override fun onTransitionPause(transition: Transition) {
          // No-op
        }

        override fun onTransitionResume(transition: Transition) {
          // No-op
        }
      })
      return true
    }

    // If we reach here then we have not added a listener
    return false
  }
}
