package com.ricepo.app.features.menu.adapter.listener

import com.ricepo.app.databinding.MenuSectionComboItemBinding
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant

//
// Created by <PERSON><PERSON> on 4/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OnQuantityChocolateComboListener(
  private val binding: MenuSectionComboItemBinding,
  private val food: Food,
  private val position: Int,
  private val restaurant: Restaurant? = null,
  private val isRootClick: Boolean = true,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty,
) :
  OnChocolateListener(food, position, restaurant, null, null, addFood, minusFood) {

  init {

    // add listener to resolved of conflict with item view click
//        var menuTouchView = binding.ivComboBg
//        if (menuTouchView?.visibility != View.VISIBLE) {
//            menuTouchView = binding.ivComboFood
//        }

    init(binding.root, null, binding.btnFoodOption, isRootClick)
  }
}
