package com.ricepo.app.features.profile

import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.ricepo.app.features.profile.datasource.ProfileDataSource
import com.ricepo.app.model.Order
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseUseCase
import com.ricepo.base.model.Customer
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 2/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ProfileUseCase @Inject constructor(
  private val dataSource: CombineRestApi
) : BaseUseCase() {

  private val PAGE_SIZE = 30
  /**
   * get profile data by [customerId]
   * return the pager flow
   */
  fun getProfileData(
    customerId: String?,
    coroutineScope: CoroutineScope,
    isTab: Boolean = false
  ): Flow<PagingData<ProfileUiModel>> {
    // need create pager because of customer can switch change
    val profileDataSource = ProfileDataSource(
      dataSource,
      customerId,
      coroutineScope,
      isTab = isTab
    )
    return Pager(
      config = PagingConfig(
        PAGE_SIZE,
        // restore position
        enablePlaceholders = false,
        prefetchDistance = 1,
        initialLoadSize = PAGE_SIZE
      ),
      pagingSourceFactory = { profileDataSource }
    ).flow
  }
}

sealed class ProfileUiModel {

  abstract val id: String?

  /**
   * the customer subscription and saving price info
   */
  data class CustomerHeader(val customer: Customer?, override val id: String? = null) : ProfileUiModel()

  /**
   * the customer profile setting ui model
   */
  data class CustomerSetting(val customer: Customer?, override val id: String? = null) : ProfileUiModel()

  /**
   * the recent orders
   */
  data class RecentOrders(val order: Order, override val id: String? = null) : ProfileUiModel()

  /**
   * share ricepo app to others
   */
  data class ShareRicepo(val referInfo: ReferInfo?, override val id: String? = null) : ProfileUiModel()

  /**
   * the history title
   */
  data class HistoryTitle(
    val title: String?,
    val isEmpty: Boolean = false,
    override val id: String? = null
  ) : ProfileUiModel()

  /**
   * the history orders
   */
  data class HistoryOrders(
    var order: Order,
    val page: Int? = null,
    val lastCreatedAt: String? = null,
    override val id: String? = null
  ) : ProfileUiModel()

  /**
   * load more error ui model
   */
  data class LoadMoreError(val key: Int, override val id: String? = null) : ProfileUiModel()
}
