package com.ricepo.app.features.menu.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuRecommendItemBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.adapter.listener.OnRecommendChocolateListener
import com.ricepo.app.features.menu.adapter.listener.QuantityDecorator
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by <PERSON><PERSON> on 05/07/2021.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class MenuRecommendItemHolder(
  private val binding: MenuRecommendItemBinding,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty
) :
  SectionItemHolder(binding.root) {

  val mapper = MenuMapper()

  /**
   * [position] the ui model list index
   */
  fun bind(model: MenuUiModel.MenuRecommendItem, position: Int, size: Int) {

    val params = binding.root.layoutParams
    val leftMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.card_side_margin)
    var rightMargin = 0
    if (position == (size - 1)) {
      rightMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.card_side_margin)
    }
    if (params is RecyclerView.LayoutParams) {
      params.leftMargin = leftMargin
      params.rightMargin = rightMargin
      binding.root.layoutParams = params
    }

    binding.btnFoodOption.isVisible = (model.food != null)

    val food = model.food ?: return
    val restaurant = model.restaurant

    bindView(food, restaurant, position)
    bindQtyView(food)

    // qty operation listener
    OnRecommendChocolateListener(
      binding, food, position, restaurant,
      model.isRootClick, addFood, minusFood
    )
  }

  fun bindQtyView(food: Food) {
    val selectedCount = food.selectedCount ?: 0
    binding.btnFoodOption.isPlus = food.isPlus
    binding.btnFoodOption.isMinus = food.isMinus
    QuantityDecorator.bindRecommendQtyChocolate(binding, selectedCount)
    food.isPlus = null
    food.isMinus = null
  }

  private fun bindView(food: Food, restaurant: Restaurant?, position: Int) {

    // set food image
    setFoodImage(food, restaurant, binding.ivFood, binding.ivFoodBg)

    binding.tvFoodName.text = food.name.localize()

    binding.btnFoodOption.setPlusSize(
      ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_22dp),
      ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_23dp)
    )
    setFoodPrice(food, restaurant)

    if (food.available == false) {
      binding.laySectionItem.isEnabled = false
      binding.laySectionItem.alpha = 0.2f
      binding.btnFoodOption.isEnabled = false
      // recyclerview animations handle the itemView alpha animation
      // making it not work when show
    } else {
      binding.laySectionItem.isEnabled = true
      binding.laySectionItem.alpha = 1f
      binding.btnFoodOption.isEnabled = true
    }
  }

  private fun setFoodReward(food: Food) {
    binding.tvFoodPrice.let {
      it.text = "${mapper.calcCoinCount(food.point)}"
    }
  }

  private fun setFoodPrice(food: Food, restaurant: Restaurant?) {
    binding.tvFoodPrice.text = mapper.formatPriceByRestaurant(food.price, restaurant)
    binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))
  }

  private fun setFoodImage(
    food: Food?,
    item: Restaurant?,
    menuView: ImageView,
    menuBgView: ImageView
  ) {
    if (food?.image != null && !food.image?.url.isNullOrEmpty()) {
      RestViewUtils.setMenuBackground(menuBgView, item, food?.image)
      ImageLoader.load(menuView, food.image?.url)
    } else {
      menuBgView.setImageResource(0)
      menuView.setImageResource(com.ricepo.style.R.drawable.dish_placeholder)
    }
  }

  companion object {
    fun create(
      parent: ViewGroup,
      addFood: FunFoodQty,
      minusFood: FunFoodQty
    ): MenuRecommendItemHolder {
      val binding = MenuRecommendItemBinding.inflate(LayoutInflater.from(parent.context))
//            val width = DisplayUtil.getScreenWidth(binding.root.context).div(2.2).toInt()
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.WRAP_CONTENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      return MenuRecommendItemHolder(binding, addFood, minusFood)
    }
  }
}
