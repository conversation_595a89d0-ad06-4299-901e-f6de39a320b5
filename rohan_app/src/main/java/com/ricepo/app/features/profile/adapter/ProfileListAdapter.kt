package com.ricepo.app.features.profile.adapter

import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.features.profile.ProfileUiModel
import com.ricepo.base.adapter.EmptyViewHolder

//
// Created by <PERSON><PERSON> on 2/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ProfileListAdapter : PagingDataAdapter<ProfileUiModel, RecyclerView.ViewHolder>(PROFILE_UIMODEL) {

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val uiModel = getItemModel(position)
    uiModel?.let {
      when (uiModel) {
        is ProfileUiModel.CustomerHeader -> (holder as ProfileHeaderHolder).bind(uiModel.customer)
        is ProfileUiModel.RecentOrders -> (holder as RecentOrderHolder).bind(uiModel.order, position)
        is ProfileUiModel.ShareRicepo -> (holder as ShareRicepoHolder).bind(uiModel.referInfo)
        is ProfileUiModel.HistoryTitle -> (holder as HistoryTitleHolder).bind(uiModel.title, uiModel.isEmpty)
        is ProfileUiModel.HistoryOrders -> (holder as HistoryOrderHolder).bind(uiModel.order, position, itemCount)
        is ProfileUiModel.CustomerSetting -> (holder as ProfileSettingHolder).bind(uiModel.customer)
        else -> {}
      }
    }
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return when (viewType) {
      R.layout.profile_item_header -> ProfileHeaderHolder.create(parent)
      R.layout.profile_item_recent_order -> RecentOrderHolder.create(parent)
      R.layout.profile_item_share_ricepo -> ShareRicepoHolder.create(parent)
      R.layout.profile_item_history_title -> HistoryTitleHolder.create(parent)
      R.layout.profile_item_history_order -> HistoryOrderHolder.create(parent)
      R.layout.profile_item_setting -> ProfileSettingHolder.create(parent)
      else -> EmptyViewHolder.create(parent)
    }
  }

  override fun getItemViewType(position: Int): Int {
    if (position >= itemCount) return 0
    val mode = getItem(position)
    return when (mode) {
      is ProfileUiModel.CustomerHeader -> R.layout.profile_item_header
      is ProfileUiModel.RecentOrders -> R.layout.profile_item_recent_order
      is ProfileUiModel.ShareRicepo -> R.layout.profile_item_share_ricepo
      is ProfileUiModel.HistoryTitle -> R.layout.profile_item_history_title
      is ProfileUiModel.HistoryOrders -> R.layout.profile_item_history_order
      is ProfileUiModel.CustomerSetting -> R.layout.profile_item_setting
      else -> 0
    }
  }

  fun getItemModel(position: Int): ProfileUiModel? {
    return try { getItem(position) } catch (e: Exception) { null }
  }

  companion object {
    private val PROFILE_UIMODEL = object : DiffUtil.ItemCallback<ProfileUiModel>() {

      override fun areItemsTheSame(oldItem: ProfileUiModel, newItem: ProfileUiModel): Boolean {

        return if (oldItem is ProfileUiModel.CustomerHeader && newItem is ProfileUiModel.CustomerHeader) {
          oldItem.customer?.id == newItem.customer?.id &&
            oldItem.customer?.subscription == newItem.customer?.subscription
        } else if (oldItem is ProfileUiModel.RecentOrders && newItem is ProfileUiModel.RecentOrders) {
          oldItem.order.id == newItem.order.id &&
            oldItem.order.state == newItem.order.state &&
            oldItem.order.status == newItem.order.status
        } else if (oldItem is ProfileUiModel.ShareRicepo && newItem is ProfileUiModel.ShareRicepo) {
          oldItem.referInfo?.title == newItem.referInfo?.title
                  && oldItem.referInfo?.share?.url == newItem.referInfo?.share?.url
        } else if (oldItem is ProfileUiModel.HistoryTitle && newItem is ProfileUiModel.HistoryTitle) {
          oldItem.title == newItem.title
        } else if (oldItem is ProfileUiModel.HistoryOrders && newItem is ProfileUiModel.HistoryOrders) {
          oldItem.order.id == newItem.order.id &&
            oldItem.order.state == newItem.order.state &&
            oldItem.order.status == newItem.order.status &&
            oldItem.order.rating == newItem.order.rating
        } else {
          oldItem == newItem
        }
      }

      override fun areContentsTheSame(oldItem: ProfileUiModel, newItem: ProfileUiModel): Boolean =
        oldItem == newItem
    }
  }
}
