package com.ricepo.app.features.refer.diary

import android.Manifest
import android.app.Activity
import android.content.ContentValues
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.webkit.JsPromptResult
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.lifecycleScope
import androidx.webkit.WebViewClientCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.eazypermissions.common.model.PermissionResult
import com.eazypermissions.coroutinespermission.PermissionManager
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityBannerWebBinding
import com.ricepo.app.features.BaseWebActivity
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.Order
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.animation.Loading
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.view.DialogFacade
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.NetworkError
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.util.Objects
import javax.inject.Inject

//
// Created by Thomsen on 3/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_WEB_REFER)
class ReferWebActivity : BaseWebActivity() {

  lateinit var binding: ActivityBannerWebBinding

  private var imageUrl: String? = null
  private var fileName: String? = null
  private var file: File? = null
  private var order: Order? = null

  @Inject
  lateinit var combineRestApi: CombineRestApi

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityBannerWebBinding.inflate(layoutInflater)
    setContentView(binding.root)

    order = intent.getParcelableExtra<Order?>(FeaturePageConst.PARAM_WEB_ORDER)
    imageUrl = order?.diary?.image
    fileName = imageUrl?.split("/")?.lastOrNull() ?: "default.png"

    binding.wvBanner.webViewClient = webviewClient
    binding.wvBanner.webChromeClient = webchromeClient

    binding.wvBanner.settings.cacheMode = WebSettings.LOAD_NO_CACHE
    // js prompt
    binding.wvBanner.settings.let {
      it.javaScriptEnabled = true
      it.javaScriptCanOpenWindowsAutomatically = true
    }

    binding.wvBanner.setBackgroundColor(Color.TRANSPARENT)

    intent.getStringExtra(FeaturePageConst.PARAM_BANNER_URL)?.let {
      binding.wvBanner.loadUrl(it)
    }
  }

  // activity:1.2.0-alpha04 prepareCall renamed to registerForActivityResult
  private val loginLauncher = registerForActivityResult(
    ActivityResultContracts.StartActivityForResult()
  ) { activityResult ->
    if (activityResult.resultCode == Activity.RESULT_OK) {
      finish()
    }
  }

  private val webviewClient = object : WebViewClientCompat() {
    override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
      super.onPageStarted(view, url, favicon)
    }

    override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
      dispatchUrlLoading(view, request.url)
      return true
    }

    override fun onPageFinished(view: WebView?, url: String?) {
      super.onPageFinished(view, url)
      binding.wpBanner.hide()
    }

    private fun dispatchUrlLoading(view: WebView, uri: Uri) {
      val title = uri.getQueryParameter("title")
      if (!title.isNullOrEmpty()) {
        binding.tvTitle.text = title
      }

      val type = uri.getQueryParameter("type")
      if (type != null) {
        handleUrlWithType(type, uri, view)
        return
      }
    }

    private fun handleUrlWithType(type: String, uri: Uri, view: WebView) {
      if (type == "download") {
        downloadBitmap()
      }
    }
  }

  private val webchromeClient = object : WebChromeClient() {
    override fun onProgressChanged(view: WebView?, newProgress: Int) {
      super.onProgressChanged(view, newProgress)
      binding.wpBanner.setWebProgress(newProgress)
    }

    override fun onJsPrompt(
      view: WebView?,
      url: String?,
      message: String?,
      defaultValue: String?,
      result: JsPromptResult?
    ): Boolean {
      Log.i("thom", "msg $message")
      DialogFacade.showInput(binding.root.context, message ?: "") {
        logRedLink(order?.id, it)
      }
      result?.confirm()
      return true
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
  }

  /**
   * log xiaohongshu link
   */
  fun logRedLink(orderId: String?, link: String?) {
    val link = link ?: return
    val orderId = orderId ?: return
    lifecycleScope.launch {
      try {
        Loading.showLoading(this@ReferWebActivity)
        withContext(Dispatchers.IO) {
          val params = mapOf("link" to link)
          combineRestApi.logRedLink(orderId, params)
        }
        Loading.hideLoading()
        DialogFacade.showAlert(this@ReferWebActivity, com.ricepo.style.R.string.diary_red_link_success)
      } catch (e: Exception) {
        Loading.hideLoading()
        val error = e.parseByBuzNetwork()
        if (error is NetworkError && error.code == ErrorCode.RESPONSE_BODY_EMPTY) {
          DialogFacade.showAlert(this@ReferWebActivity, com.ricepo.style.R.string.diary_red_link_success)
        } else {
          DialogFacade.showAlert(
            this@ReferWebActivity,
            error.message ?: ""
          )
        }
      }
    }
  }

  private fun downloadBitmap() {
    if (imageUrl == null) {
      DialogFacade.showAlert(
        this,
        ResourcesUtil.getString(
          com.ricepo.style.R.string.diary_image_failed
        )
      )
    }
    val url = imageUrl ?: return
    ImageLoader.downloadBitmap(this, url) {
      if (it == null) {
        DialogFacade.showAlert(
          this,
          ResourcesUtil.getString(
            com.ricepo.style.R.string.diary_image_failed
          )
        )
      } else {
        saveBitmapToGallery(it)
      }
    }
  }

  private fun saveBitmapToGallery(bitmap: Bitmap) {
    val fos: OutputStream?
    try {
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        val resolver = contentResolver
        val contentValues = ContentValues()
        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
        contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "image/jpg")
        contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + File.separator + "Ricepo")
        val imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
        imageUri?.let {
          fos = resolver.openOutputStream(Objects.requireNonNull(it)) ?: return
          bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
          Objects.requireNonNull<OutputStream?>(fos)
          DialogFacade.showAlert(
            this,
            ResourcesUtil.getString(
              com.ricepo.style.R.string.diary_image_downloaded
            )
          )
        }
      } else {
//                MediaStore.Images.Media.insertImage(contentResolver, bitmap, fileName, "")
        saveToGalleryByPermission(bitmap)
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private fun saveToGalleryBeforeQ(bitmap: Bitmap) {
    lifecycleScope.launch {
      file = withContext(Dispatchers.IO) {
        saveImage(bitmap)
      }
      file?.absolutePath?.let {
        DialogFacade.showAlert(
          this@ReferWebActivity,
          ResourcesUtil.getString(
            com.ricepo.style.R.string.diary_image_downloaded
          )
        )
        MediaScannerConnection.scanFile(
          applicationContext, arrayOf(it), arrayOf("image/jpeg")
        ) { path: String?, uri: Uri? -> }
      }
    }
  }

  private fun saveImage(image: Bitmap?): File? {
    if (image == null) return null
    var imageFile: File? = null
    val imageFileName = fileName
    val storageDir = File(
      Environment.getExternalStoragePublicDirectory(
        Environment.DIRECTORY_PICTURES
      ).toString() + "/Rice"
    )
    var success = true
    if (!storageDir.exists()) {
      success = storageDir.mkdirs()
    }
    if (success) {
      imageFile = File(storageDir, imageFileName)
      var fos: OutputStream? = null
      try {
        fos = FileOutputStream(imageFile)
        image.compress(Bitmap.CompressFormat.JPEG, 100, fos)
        Objects.requireNonNull<OutputStream?>(fos)
      } catch (e: Exception) {
        e.printStackTrace()
      } finally {
        fos?.close()
      }
    }
    return imageFile
  }

  private val REQUEST_ID = 222

  private fun saveToGalleryByPermission(bitmap: Bitmap) {
    lifecycleScope.launch {
      // CoroutineScope suspends the coroutine
      var permissionResult = PermissionManager.requestPermissions(
        this@ReferWebActivity,
        REQUEST_ID,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
      )

      // Resume coroutine once result is ready
      when (permissionResult) {
        is PermissionResult.PermissionGranted -> {
          // Add your logic here after user grants permission(s)
          saveToGalleryBeforeQ(bitmap)
        }
        is PermissionResult.PermissionDenied -> {
          // Add your logic to handle permission denial
        }
        is PermissionResult.PermissionDeniedPermanently -> {
          // Add your logic here if user denied permission(s) permanently.
          // Ideally you should ask user to manually go to settings and enable permission(s)
          DialogFacade.showAlert(this@ReferWebActivity, ResourcesUtil.getString(com.ricepo.style.R.string.diary_permission)) {
            getAppDetailSettingIntent()
          }
        }
        is PermissionResult.ShowRational -> {
          // If user denied permission frequently then she/he is not clear about why you are asking this permission.
          // This is your chance to explain them why you need permission.
        }
      }
    }
  }

  private fun getAppDetailSettingIntent() {
    val intent = Intent()
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    intent.action = "android.settings.APPLICATION_DETAILS_SETTINGS"
    intent.data = Uri.fromParts("package", packageName, null)
    startActivity(intent)
  }
}
