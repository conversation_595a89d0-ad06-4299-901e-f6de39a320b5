package com.ricepo.app.features.profile.adapter

import android.animation.ObjectAnimator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.animation.addListener
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.profile.ProfileMapper
import com.ricepo.app.model.Order
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.localize
import com.ricepo.map.MapFacade
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.ThemeUtil
import com.ricepo.style.round.RoundLayout

//
// Created by <PERSON><PERSON> on 2/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RecentOrderHolder(private val view: View) : RecyclerView.ViewHolder(view) {

  private val addressView = view.findViewById<ImageView>(R.id.iv_order_address)
  private val statusView = view.findViewById<TextView>(R.id.tv_order_status)
  private val restaurantView = view.findViewById<TextView>(R.id.tv_restaurant_label)
  private val tvOderNumber = view.findViewById<TextView>(R.id.tv_order_number)
  private val tvOrderDate = view.findViewById<TextView>(R.id.tv_order_date)

  init {
    view.clickWithTrigger { v ->
      val order = v.getTag()
      if (order is Order) {
        // navigate to order page
        FeaturePageRouter.navigateRecentOrder(
          view.context, order,
          FeaturePageConst.REQUEST_CODE_RECENT_ORDER
        )
      }
    }
  }

  fun bind(order: Order, position: Int) {
    // cache order with with
    view.tag = order
    statusView.text = ResourcesUtil.getString("status_${order.obtainState().replace("-", "_")}")
    restaurantView.text = order.restaurant?.name?.localize() ?: ""
    tvOderNumber.text = ResourcesUtil.getString(com.ricepo.style.R.string.support_order_number, order.passcode)
    tvOrderDate.text = ProfileMapper.mapOrderCreatedAt(order)

    // show address map image
    var formattedAddress: String = ""
    val deliveryAddress = order.delivery?.address?.innerItemValue?.formatted
    val restaurantAddress = order.restaurant?.address?.innerItemValue?.formatted
    if (deliveryAddress != null) {
      formattedAddress = deliveryAddress
    } else if (restaurantAddress != null) {
      formattedAddress = restaurantAddress
    }

    val mapUrl = MapFacade.generateMapUrl(formattedAddress, ThemeUtil.isDarkMode())
    ImageLoader.load(addressView, mapUrl)
  }

  companion object {
    fun create(parent: ViewGroup): RecentOrderHolder {
      val view = LayoutInflater.from(parent.context)
        .inflate(R.layout.profile_item_recent_order, parent, false)
      alphaAddressView(view)
      return RecentOrderHolder(view)
    }

    private fun alphaAddressView(view: View) {
      val addressRoundLayout = view.findViewById<RoundLayout>(R.id.round_order_address)
      val roundAlphaAnimator = ObjectAnimator.ofFloat(
        addressRoundLayout,
        "alpha", 0f, 1f
      )
      roundAlphaAnimator.duration = 2000
      roundAlphaAnimator.start()

      val addressView = view.findViewById<ImageView>(R.id.iv_order_address) ?: return
      val alphaAnimator = ObjectAnimator.ofFloat(
        addressView,
        "alpha", 0f, 1f
      )
      alphaAnimator.duration = 2000
      alphaAnimator.addListener(
        onEnd = {
          addressView.alpha = 1f
        }
      )
      alphaAnimator.start()
    }
  }
}
