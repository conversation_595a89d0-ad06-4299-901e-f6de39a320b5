package com.ricepo.app.features.login.repository

import androidx.lifecycle.LifecycleOwner
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.model.UserInformation
import com.ricepo.base.BaseUseCase
import com.ricepo.base.model.Customer
import com.ricepo.base.model.GlobalConfigModel
import com.ricepo.network.executor.PostExecutionThread
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import io.reactivex.rxjava3.schedulers.Schedulers
import javax.inject.Inject
import kotlin.Exception

//
// Created by <PERSON><PERSON> on 7/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class AuthUseCase @Inject constructor(
  val repository: AuthRepository,
  private val postExecutionThread: PostExecutionThread
) : BaseUseCase() {

  data class Params(
    val phone: String,
    var method: String,
    val vscode: String
  )

  /**
   * get the captcha code
   */
  open fun getVcode(singleObserver: DisposableSingleObserver<Any>, params: Params) {
    val single = repository.getVcode(params.phone, params.method)
      .subscribeOn(Schedulers.io())
      .observeOn(postExecutionThread.mainScheduler) as Single<Any>
    addDisposable(single.subscribeWith(singleObserver))
  }

  /**
   * get token with login
   */
  open fun login(singleObserver: DisposableSingleObserver<UserInformation>, params: Params) {
    val single = repository.login(params.phone, params.vscode)
      .subscribeOn(Schedulers.io())
      .observeOn(postExecutionThread.mainScheduler) as Single<UserInformation>
    addDisposable(single.subscribeWith(singleObserver))
  }

  /**
   * get the customer info
   */
  fun getCustomer(singleObserver: DisposableSingleObserver<Customer>, customerId: String) {
    val single = repository.getCustomer(customerId)
      .subscribeOn(Schedulers.io())
      .doOnSuccess { CustomerCache.saveCustomer(it) {} }
      .observeOn(postExecutionThread.mainScheduler) as Single<Customer>
    addDisposable(single.subscribeWith(singleObserver))
  }

  /**
   * renew token with app start
   * return boolean if not exist
   */
  fun renewToken(lifecycleOwner: LifecycleOwner, singleObserver: DisposableSingleObserver<UserInformation>) {
    CustomerCache.liveCustomer { customer ->
      if (customer != null) {
        val single = repository.resetToken()
          .subscribeOn(Schedulers.io())
          .observeOn(postExecutionThread.mainScheduler) as Single<UserInformation>
        addDisposable(single.subscribeWith(singleObserver))
      } else {
        singleObserver.onError(Exception("token is empty"))
      }
    }
  }

  suspend fun getCountry(): Map<String, GlobalConfigModel>? {
    return  try {
      repository.getCountry()
    } catch (e: Exception) {
      null
    }
  }
}
