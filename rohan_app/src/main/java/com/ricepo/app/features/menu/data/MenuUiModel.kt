package com.ricepo.app.features.menu.data

import com.ricepo.base.model.Category
import com.ricepo.base.model.Food
import com.ricepo.base.model.Item
import com.ricepo.base.model.MenuBundle
import com.ricepo.base.model.Option
import com.ricepo.base.model.Restaurant

//
// Created by <PERSON><PERSON> on 28/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

sealed class MenuUiModel {

  abstract val food: Food?

  /**
   * section items origin index
   */
  abstract var foodIndex: Int?

  /**
   * adapter item origin position
   */
  abstract var itemPosition: Int?

  /**
   * groupId for bundle menu (restaurant id)
   */
  abstract val groupId: String?

  /**
   * id for header navigation
   */
  abstract val navigationId: String?

  /**
   * section category
   */
  abstract var category: Category?

  /**
   * menu restaurant info
   */
  data class RestaurantSection(
    val restaurant: Restaurant,
    override val food: Food? = null,
    override var category: Category? = null,
    override var foodIndex: Int? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  /**
   * menu header navigation
   */
  data class MenuNavigationSection(
    val items: List<MenuBundle>? = null,
    var lastSelectedPosition: Int = -1,
    var selectedPosition: Int = 0,
    override val food: Food? = null,
    override var category: Category? = null,
    override var foodIndex: Int? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  /**
   * menu category info
   */
  data class MenuCategoryItem(
    val restaurant: Restaurant? = null,
    val balance: Int? = null,
    val jumps: List<MenuCategoryItem>? = null,
    val groups: List<MenuBundle>? = null,
    var categoryIndex: Int? = null,
    val isOptionsCateogry: Boolean? = null,
    override val food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  /**
   * index is the category index
   * food is null
   */
  data class MenuGallerySection(
    val restaurant: Restaurant? = null,
    override val food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  data class MenuNormalItem(
    override val food: Food?,
    override var foodIndex: Int?,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
    // the type of horizontal
    var type: String? = null,
    var rows: Int? = null,
    val restaurant: Restaurant? = null,
    val isRootClick: Boolean = true,
  ) : MenuUiModel()

  data class MenuComboItem(
    override val food: Food?,
    override var foodIndex: Int?,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
    // the type of horizontal
    var type: String? = null,
    var rows: Int? = null,
    val restaurant: Restaurant? = null,
    val isRootClick: Boolean = true,
  ) : MenuUiModel()

  data class MenuShowMoreItem(
    var categoryIndex: Int?,
    val showSelectedCount: Int = 0,
    override val food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  /**
   * layout margin bottom item
   */
  data class MenuBottomItem(
    val width: Int? = null,
    val height: Int? = null,
    override val food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  // / ========== new menu section ============== ///

  data class MenuVerticalSection(
    val restaurant: Restaurant?,
    val models: List<MenuUiModel>,
    var isMarginTop: Boolean = true,
    override var food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  /**
   * consider item refresh
   */
  data class MenuHorizontalSection(
    val restaurant: Restaurant?,
    val models: List<MenuUiModel>,
//        val models: List<MenuVerticalSection>,
    override var food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  data class MenuComboHorizontalSection(
    val restaurant: Restaurant?,
    val models: List<MenuUiModel>,
//        val models: List<MenuVerticalSection>,
    override var food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  data class MenuGalleryItem(
    val restaurant: Restaurant?,
    val galleryPosition: Int?,
    override val food: Food?,
    override var foodIndex: Int?,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  /**
   * the menu item in options page
   */
  data class MenuOptionsTitle(
    val restaurant: Restaurant? = null,
    var categoryIndex: Int? = null,
    override val food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  /**
   * the option item
   */
  data class MenuOptionsItem(
    val item: Item?,
    val option: Option?,
    var categoryIndex: Int? = null,
    override val food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  /**
   * menu search prompt label
   */
  data class MenuSearchPrompt(
    var label: String?,
    override val food: Food? = null,
    override var foodIndex: Int? = null,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
  ) : MenuUiModel()

  /**
   * menu recommend item
   */
  data class MenuRecommendItem(
    override val food: Food?,
    override var foodIndex: Int?,
    override var category: Category? = null,
    override val groupId: String? = null,
    override val navigationId: String? = null,
    override var itemPosition: Int? = null,
    // the type of horizontal
    var type: String? = null,
    var rows: Int? = null,
    val restaurant: Restaurant? = null,
    val isRootClick: Boolean = true,
  ) : MenuUiModel()
}
