package com.ricepo.app.features.refer.diary

import android.Manifest
import android.content.ContentValues
import android.content.Intent
import android.graphics.Bitmap
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import androidx.core.view.drawToBitmap
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.eazypermissions.common.model.PermissionResult
import com.eazypermissions.coroutinespermission.PermissionManager
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityDiaryImageBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.model.Order
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.network.EnvNetwork
import com.ricepo.style.ResourcesUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.util.Objects

//
// Created by Thomsen on 18/8/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

@Route(path = FeaturePageConst.PAGE_REFER_DIARY)
class DiaryImageActivity : BaseActivity() {

  private lateinit var binding: ActivityDiaryImageBinding

  private var imageUrl: String? = null
  private var fileName: String? = null
  private var file: File? = null

  private var order: Order? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityDiaryImageBinding.inflate(layoutInflater)
    setContentView(binding.root)

//        setTitleSubText(ResourcesUtil.getString(com.ricepo.style.R.string.diary_image_title))

    order = intent.getParcelableExtra(FeaturePageConst.PARAM_DIARY_ORDER)
    imageUrl = order?.diary?.image
    fileName = imageUrl?.split("/")?.lastOrNull() ?: "default.png"

    binding.tvImageInfo.text = order?.diary?.title?.localize()
    binding.tvImageMessage.text = order?.diary?.message?.localize()
    ImageLoader.load(binding.ivDiaryImage, imageUrl)

    setupListener()
  }

  private fun setupListener() {

    binding.btnDownloadImage.clickWithTrigger {
      try {
        // if no image exception width and height must be > 0
        val bitmap = binding.ivDiaryImage.drawToBitmap()
        saveBitmapToGallery(bitmap)
      } catch (e: Exception) {
        DialogFacade.showAlert(
          this,
          ResourcesUtil.getString(
            com.ricepo.style.R.string.diary_image_failed
          )
        )
      }
    }

    binding.tvImageDetail.clickWithTrigger {
      order?.let {
        FeaturePageRouter.navigateReferWeb(EnvNetwork.REFER_URL, it)
      }
    }
    binding.ivImageDetail.clickWithTrigger {
      order?.let {
        FeaturePageRouter.navigateReferWeb(EnvNetwork.REFER_URL, it)
      }
    }
  }

  private fun saveBitmapToGallery(bitmap: Bitmap) {
    val fos: OutputStream?
    try {
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        val resolver = contentResolver
        val contentValues = ContentValues()
        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
        contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "image/jpg")
        contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + File.separator + "Ricepo")
        val imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
        imageUri?.let {
          fos = resolver.openOutputStream(Objects.requireNonNull(it)) ?: return
          bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
          Objects.requireNonNull<OutputStream?>(fos)
          DialogFacade.showAlert(
            this,
            ResourcesUtil.getString(
              com.ricepo.style.R.string.diary_image_downloaded
            )
          )
        }
      } else {
//                MediaStore.Images.Media.insertImage(contentResolver, bitmap, fileName, "")
        saveToGalleryByPermission(bitmap)
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private fun saveToGalleryBeforeQ(bitmap: Bitmap) {
    lifecycleScope.launch {
      file = withContext(Dispatchers.IO) {
//                saveImage(ImageLoader.download(binding.ivDiaryImage, imageUrl))
        saveImage(bitmap)
      }
      file?.absolutePath?.let {
        DialogFacade.showAlert(
          this@DiaryImageActivity,
          ResourcesUtil.getString(
            com.ricepo.style.R.string.diary_image_downloaded
          )
        )
        MediaScannerConnection.scanFile(
          applicationContext, arrayOf(it), arrayOf("image/jpeg")
        ) { path: String?, uri: Uri? -> }
      }
    }
  }

  private fun saveImage(image: Bitmap?): File? {
    if (image == null) return null
    var imageFile: File? = null
    val imageFileName = fileName
    val storageDir = File(
      Environment.getExternalStoragePublicDirectory(
        Environment.DIRECTORY_PICTURES
      ).toString() + "/Rice"
    )
    var success = true
    if (!storageDir.exists()) {
      success = storageDir.mkdirs()
    }
    if (success) {
      imageFile = File(storageDir, imageFileName)
      var fos: OutputStream? = null
      try {
        fos = FileOutputStream(imageFile)
        image.compress(Bitmap.CompressFormat.JPEG, 100, fos)
        Objects.requireNonNull<OutputStream?>(fos)
      } catch (e: Exception) {
        e.printStackTrace()
      } finally {
        fos?.close()
      }
    }
    return imageFile
  }

  private val REQUEST_ID = 222

  private fun saveToGalleryByPermission(bitmap: Bitmap) {
    lifecycleScope.launch {
      // CoroutineScope suspends the coroutine
      var permissionResult = PermissionManager.requestPermissions(
        this@DiaryImageActivity,
        REQUEST_ID,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
      )

      // Resume coroutine once result is ready
      when (permissionResult) {
        is PermissionResult.PermissionGranted -> {
          // Add your logic here after user grants permission(s)
          saveToGalleryBeforeQ(bitmap)
        }
        is PermissionResult.PermissionDenied -> {
          // Add your logic to handle permission denial
        }
        is PermissionResult.PermissionDeniedPermanently -> {
          // Add your logic here if user denied permission(s) permanently.
          // Ideally you should ask user to manually go to settings and enable permission(s)
          DialogFacade.showAlert(this@DiaryImageActivity, ResourcesUtil.getString(com.ricepo.style.R.string.diary_permission)) {
            getAppDetailSettingIntent()
          }
        }
        is PermissionResult.ShowRational -> {
          // If user denied permission frequently then she/he is not clear about why you are asking this permission.
          // This is your chance to explain them why you need permission.
        }
      }
    }
  }

  private fun getAppDetailSettingIntent() {
    val intent = Intent()
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    intent.action = "android.settings.APPLICATION_DETAILS_SETTINGS"
    intent.data = Uri.fromParts("package", packageName, null)
    startActivity(intent)
  }
}
