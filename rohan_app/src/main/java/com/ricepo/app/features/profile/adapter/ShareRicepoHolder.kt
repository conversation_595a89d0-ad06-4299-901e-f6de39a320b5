package com.ricepo.app.features.profile.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.view.ShareCardView
import com.ricepo.base.model.Customer

//
// Created by <PERSON><PERSON> on 2/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ShareRicepoHolder(private val view: ShareCardView) : RecyclerView.ViewHolder(view) {

  fun bind(referInfo: ReferInfo?) {
    view.initTitle(referInfo)
  }

  companion object {
    fun create(parent: ViewGroup): ShareRicepoHolder {
      val view = LayoutInflater.from(parent.context)
        .inflate(R.layout.profile_item_share_ricepo, parent, false)
      return ShareRicepoHolder(view.findViewById(R.id.sc_share))
    }
  }
}
