package com.ricepo.app.features.menu.adapter.listener

import com.ricepo.app.databinding.MenuOptionsSectionItemBinding
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.base.model.Food
import com.ricepo.base.model.Item
import com.ricepo.base.model.Option
import com.ricepo.base.model.Restaurant

//
// Created by <PERSON><PERSON> on 4/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OnOptionsChocolateListener(
  private val binding: MenuOptionsSectionItemBinding,
  private val food: Food,
  private val option: Option?,
  private val item: Item?,
  private val position: Int,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty,
  private val restaurant: Restaurant? = null
) :
  OnChocolateListener(food, position, restaurant, option, item, addFood, minusFood) {

  init {

    // add listener to resolved of conflict with item view click
    init(binding.root, binding.ivFood, binding.btnFoodOption)
  }
}
