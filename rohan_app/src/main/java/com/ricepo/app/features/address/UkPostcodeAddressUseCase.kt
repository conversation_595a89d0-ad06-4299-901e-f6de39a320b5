package com.ricepo.app.features.address

import com.ricepo.app.R
import com.ricepo.app.restapi.GoogleMapApi
import com.ricepo.app.utils.log
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.style.IResourcesUtil
import com.skydoves.sandwich.message
import com.skydoves.sandwich.onError
import com.skydoves.sandwich.onException
import com.skydoves.sandwich.onSuccess
import com.skydoves.whatif.whatIfNotNullOrEmpty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class UkPostcodeAddressUseCase @Inject constructor(
  private val mapApi: GoogleMapApi,
  private val addressDao: AddressDao,
  private val resourceUtils: IResourcesUtil
) {

  fun updateAddressByPostcode(
    scope: CoroutineScope,
    address: FormatUserAddress,
    networkPostcode: String?,
    onSuccess: (List<FormatUserAddress>?) -> Unit,
    onError: (String?) -> Unit,
    onLoading: () -> Unit
  ) {
    scope.launch {
      val oldAddress: FormatUserAddress? = withContext(Dispatchers.IO) {
        addressDao.getAddress()
      }
      /**
       * if zip code not change
       * or not uk
       * or zipcode is null
       * then not call this api
       */
      if (
        oldAddress?.zipcode == address.zipcode ||
        !address.isUk() ||
        address.zipcode.isNullOrEmpty() ||
        address.zipcode == networkPostcode
      ) {
        onSuccess.invoke(null)
        return@launch
      }
      onLoading.invoke()
      mapApi.fetchAddressByPostalCode(
        components = address.generateComponents(),
      ).onSuccess {
        data.results.whatIfNotNullOrEmpty(
          whatIf = {
            it.map { place ->
              FormatUserAddress(place).apply {
                unit = address.unit
                note = address.note
              }
            }.let { resultList ->
              onSuccess(resultList)
            }
          },
          whatIfNot = {
            onError.invoke(resourceUtils.getString(com.ricepo.style.R.string.address_not_found))
          }
        )
      }.onError {
        message().log("fetch address by postcode error")
        onError.invoke(message())
      }.onException {
        message().log("fetch address by postcode exception")
        onError.invoke(message)
      }
    }
  }

  private fun FormatUserAddress.generateComponents() = "postal_code:$zipcode" +
    "|country:$country" +
    "|administrative_area_level_1:$state" +
    "|locality:$city" +
    "|route:$street" +
    "|street_number:$number"
}
