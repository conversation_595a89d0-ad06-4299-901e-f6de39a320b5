package com.ricepo.app.features.menu.preview

import android.os.Bundle
import android.transition.Transition
import android.view.MotionEvent
import android.view.View
import androidx.core.view.ViewCompat
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityMenuPreviewNormalBinding
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Combo
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant

//
// Created by <PERSON><PERSON> on 3/9/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class MenuNormalPreviewActivity : BaseActivity() {

  companion object {
    const val VIEW_MENU_IMAGE = "menu:image"
    const val VIEW_MENU_BACKGROUND = "menu:background"

    const val BUNDLE_MENU_PREVIEW_FOOD = "menu_preview_food"
    const val BUNDLE_MENU_PREVIEW_RESTAURANT = "menu_preview_restaurant"
    const val BUNDLE_MENU_PREVIEW_COMBO = "menu_preview_combo"
    const val BUNDLE_MENU_PREVIEW_COMBO_FLAG = "menu_preview_food_combo"
    const val BUNDLE_MENU_PREVIEW_RECOMMEND = "menu_preview_food_recommend"
  }

  private lateinit var binding: ActivityMenuPreviewNormalBinding

  private var food: Food? = null
  private var restaurant: Restaurant? = null
  private var combo: Combo? = null

  private var isRecommend: Boolean = false
  private var isCombo: Boolean = false

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityMenuPreviewNormalBinding.inflate(layoutInflater)
    setContentView(binding.root)

    food = intent.getParcelableExtra(BUNDLE_MENU_PREVIEW_FOOD)
    restaurant = intent.getParcelableExtra(BUNDLE_MENU_PREVIEW_RESTAURANT)
    combo = intent.getParcelableExtra(BUNDLE_MENU_PREVIEW_COMBO)
    isCombo = intent.getBooleanExtra(BUNDLE_MENU_PREVIEW_COMBO_FLAG, false)
    isRecommend = intent.getBooleanExtra(BUNDLE_MENU_PREVIEW_RECOMMEND, false)

    ViewCompat.setTransitionName(binding.ivRestaurantMenu, VIEW_MENU_IMAGE)
    ViewCompat.setTransitionName(binding.ivRestaurantMenuBg, VIEW_MENU_BACKGROUND)

    setupListener()
  }

  private fun setupListener() {

    addTransitionListener()
    // important for transition start
    loadFullSizeImage()

    binding.root.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        onBackPressed()
      }
    }
  }

  private fun loadFullSizeImage() {
    if (isCombo) {
      loadComboImage()
    } else {
      loadNormalImage()
    }
  }

  private fun loadComboImage() {
    combo?.image?.url?.let {
      ImageLoader.load(binding.ivRestaurantMenu, it)
    }

    val resId = if (restaurant?.vip == true) com.ricepo.style.R.drawable.bg_plate_membership else com.ricepo.style.R.drawable.bg_plate
    binding.ivRestaurantMenuBg.setImageResource(resId)

    if (combo?.image?.noPlate == true) {
      binding.ivRestaurantMenuBg.visibility = View.INVISIBLE
    } else {
      binding.ivRestaurantMenuBg.visibility = View.VISIBLE
    }
  }

  private fun loadNormalImage() {
    food?.image?.url?.let {
      ImageLoader.load(binding.ivRestaurantMenu, it)
    }

    if (isRecommend) {
      binding.ivRestaurantMenuBg.setImageResource(com.ricepo.style.R.drawable.leaderboard_dish)
    } else {
      val resId = if (restaurant?.vip == true)
        com.ricepo.style.R.drawable.bg_plate_membership else com.ricepo.style.R.drawable.bg_plate
      binding.ivRestaurantMenuBg.setImageResource(resId)
    }

    if (food?.image?.noPlate == true) {
      binding.ivRestaurantMenuBg.visibility = View.INVISIBLE
    } else {
      binding.ivRestaurantMenuBg.visibility = View.VISIBLE
    }

//        val width = DisplayUtil.getScreenWidth(this).times(0.95).toInt()
//        val height = width * 1.3.toInt()
//
//        val bgParams = binding.ivRestaurantMenuBg.layoutParams
//        bgParams.width = width
//        bgParams.height = height
//        binding.ivRestaurantMenuBg.layoutParams = bgParams
//
//        val foodParams = binding.ivRestaurantMenu.layoutParams
//        foodParams.width = width
//        foodParams.height = width
//        binding.ivRestaurantMenu.layoutParams = foodParams
  }

  private fun addTransitionListener(): Boolean {
    val transition = window.sharedElementEnterTransition
    if (transition != null) {
      // There is an entering shared element transition so add a listener to it
      transition.addListener(object : Transition.TransitionListener {
        override fun onTransitionEnd(transition: Transition) {
          // As the transition has ended, we can now load the full-size image
          loadFullSizeImage()

          // Make sure we remove ourselves as a listener
          transition.removeListener(this)
        }

        override fun onTransitionStart(transition: Transition) {
          // No-op
        }

        override fun onTransitionCancel(transition: Transition) {
          // Make sure we remove ourselves as a listener
          transition.removeListener(this)
        }

        override fun onTransitionPause(transition: Transition) {
          // No-op
        }

        override fun onTransitionResume(transition: Transition) {
          // No-op
        }
      })
      return true
    }

    // If we reach here then we have not added a listener
    return false
  }
}
