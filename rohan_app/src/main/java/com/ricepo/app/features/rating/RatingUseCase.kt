package com.ricepo.app.features.rating

import com.ricepo.app.model.Order
import com.ricepo.app.model.RatingItem
import com.ricepo.app.model.RatingReq
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseUseCase
import com.ricepo.base.model.mapper.BaseMapper
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 8/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RatingUseCase @Inject constructor(
  private val repository: CombineRestApi
) : BaseUseCase() {

  suspend fun ratingDriver(
    orderId: String,
    stars: Int?,
    ratingSelectedItems: List<String>,
    feeTip: Int?,
    dishes: List<RatingItem>
  ): Order {
    val ratingReq = RatingReq(stars, ratingSelectedItems, "", feeTip, dishes)
    return repository.driverRating(orderId, ratingReq)
  }

  fun mapOrderCreatedAt(order: Order): String? {
    val mapper = BaseMapper()
    val createdAt = order.createdAt
    return mapper.formatTime(createdAt, "M/dd")
  }
}
