package com.ricepo.app.features.menu.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuSectionVerticalBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.adapter.FunShowMore
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.base.model.Food
import com.ricepo.base.model.Item
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by <PERSON><PERSON> on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuSectionVerticalHolder(
  private val binding: MenuSectionVerticalBinding,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty,
  private val showMore: FunShowMore
) :
  SectionItemHolder(binding.root) {

  val mapper = MenuMapper()

  var menuAdapter: MenuSectionAdapter? = null

  var foodItems: MutableList<Food> = mutableListOf()

  var items: MutableList<Item> = mutableListOf()

  fun bind(uiModel: MenuUiModel.MenuVerticalSection, sectionPosition: Int) {
    var uiModels = uiModel.models

    val params = binding.rvMenuContainer.layoutParams

    if (params is ConstraintLayout.LayoutParams) {
      // local food search result top Margin
      val topMargin = if (sectionPosition == 1 && uiModel.isMarginTop) {
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_40)
      } else {
        0
      }
      params.topMargin = topMargin
      binding.rvMenuContainer.layoutParams = params
    }

    foodItems.clear()
    items.clear()
    uiModels.forEach { model ->
      if (model is MenuUiModel.MenuOptionsItem) {
        model.item?.let {
          items.add(it)
        }
      } else if (model is MenuUiModel.MenuNormalItem) {
        model.food?.let {
          foodItems.add(it)
        }
      }
    }

    val foodPosition = uiModel.foodIndex ?: -1
    if (foodPosition == -1) {
      menuAdapter = MenuSectionAdapter(
        uiModels, addFood = addFood,
        minusFood = minusFood, showMore = showMore, navMenu = {}
      )
      binding.rvMenuContainer.adapter = menuAdapter

//            setGalleryBackground(uiModel.category, restaurant)
    } else {
      menuAdapter?.models = uiModels
      menuAdapter?.notifyItemChanged(foodPosition)
    }
  }

  fun bindQtyView(food: Food) {
    val foodPosition = foodItems?.indexOfFirst { it?.id == food.id } ?: -1

    if (foodPosition == -1) {
      menuAdapter?.notifyDataSetChanged()
    } else {
      menuAdapter?.notifyItemChanged(foodPosition, food)
    }
  }

  fun bindItemQtyView(item: Item) {
    val foodPosition = items?.indexOfFirst { it?.id == item.id } ?: -1

//        if (foodPosition == -1) {
//            menuAdapter?.notifyDataSetChanged()
//        } else {
//            menuAdapter?.notifyItemChanged(foodPosition, item)
//        }
    menuAdapter?.notifyDataSetChanged()
  }

  companion object {
    fun create(
      parent: ViewGroup,
      addFood: FunFoodQty,
      minusFood: FunFoodQty,
      showMore: FunShowMore
    ): MenuSectionVerticalHolder {
      val binding = MenuSectionVerticalBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      binding.rvMenuContainer.layoutManager = LinearLayoutManager(parent.context)
      binding.rvMenuContainer.isNestedScrollingEnabled = false
      binding.rvMenuContainer.itemAnimator = null
      return MenuSectionVerticalHolder(binding, addFood, minusFood, showMore)
    }
  }
}
