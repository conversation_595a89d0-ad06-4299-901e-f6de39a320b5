package com.ricepo.app.features.menu.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuItemShowMoreBinding
import com.ricepo.app.features.menu.adapter.FunShowMore
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Category
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseMenuEvent
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by <PERSON><PERSON> on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuShowMoreHolder(
  private val binding: MenuItemShowMoreBinding,
  private val showMore: FunShowMore
) :
  SectionItemHolder(binding.root) {

  private var category: Category? = null
  private var itemPosition: Int = -1
  private var showSelectedCount: Int = 0

  init {
    binding.root.clickWithTrigger { view ->
      category?.let {
        showMore(it, itemPosition, showSelectedCount)
        // firebase event show more
        AnalyticsFacade.logEvent(view, FirebaseEventName.rShowMore)
      }
    }
  }

  fun bind(uiModel: MenuUiModel.MenuShowMoreItem, position: Int) {
    category = uiModel.category
    showSelectedCount = uiModel.showSelectedCount
    itemPosition = uiModel.categoryIndex ?: position

    binding.root.setTag(
      com.ricepo.base.R.id.tag_firebase_event,
      FirebaseMenuEvent(
        rCategoryType = category?.type,
        rCategoryId = category?.id,
        rCategoryIndex = uiModel.categoryIndex?.toLong(),
      )
    )
  }

  companion object {
    fun create(parent: ViewGroup, showMore: FunShowMore): MenuShowMoreHolder {
      val binding = MenuItemShowMoreBinding.inflate(LayoutInflater.from(parent.context))
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      return MenuShowMoreHolder(binding, showMore)
    }
  }
}
