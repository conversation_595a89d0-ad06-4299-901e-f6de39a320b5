package com.ricepo.app.features.coupon.data

import com.ricepo.app.model.Coupon
import com.ricepo.base.model.Customer

//
// Created by <PERSON><PERSON> on 4/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/*
 * Coupon Conditions
 * - minimum
 * - customer (no need)
 * - region (no need)
 * - restaurant (no need)
 * - hours (skip)
 * - wechat (skip)
 * - restaurant firstTime (skip)
 * - limitPerCustomer (skip)
 */
class CouponValidator {
  // Coupon
  var coupon: Coupon
  // Order subtotal
  var orderSubtotal: Int
  // Customer info, current is from quote.customer
  var customer: Customer? = null

  constructor(coupon: Coupon, orderSubtotal: Int, customer: Customer?) {
    this.coupon = coupon
    this.orderSubtotal = orderSubtotal
    this.customer = customer
  }

    /*
     * Run validator
     * Return true if all conditions are pass
     * - firstTime
     * - minimum
     *
     * skiped when:
     * - hours
     * - wechat
     * - restaurant firstTime
     * - limitPerCustomer
     */
  fun run(): Boolean =
    minimum() && firstTime()

    /*
     * Minimum condition
     */
  fun minimum(): Boolean {
    // Skip if minimum is empty
    val minimum = this.coupon.condition?.minimum ?: return true
    return minimum <= this.orderSubtotal
  }

    /*
     * First time coupon condition
     */
  fun firstTime(): Boolean {
    // Skip if firstTime condition is empty
    if (this.coupon.condition?.firstTime != true) {
      return true
    }
    // Default order count is 0
    val orderCount = this.customer?.orderCount ?: 0
    // max condition count
    val conditionCount = this.coupon.condition?.orderCount ?: 1
    return orderCount < conditionCount
  }
}
