package com.ricepo.app.features.menu.adapter.holder

import android.view.View
import android.widget.ImageView
import com.google.android.material.imageview.ShapeableImageView
import com.google.android.material.shape.ShapeAppearanceModel
import com.ricepo.app.R
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.app.utils.FoodLargeImageUtil
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionItemHolder

private val shapeAppearanceModel: ShapeAppearanceModel by lazy {
  ShapeAppearanceModel.builder().setAllCornerSizes(
    ResourcesUtil.getDimenSize(com.ricepo.style.R.dimen.card_radius)
  ).build()
}

open class BaseFoodLargeImageHolder(view: View) : SectionItemHolder(view) {

  open fun setFoodImage(
    food: Food?,
    item: Restaurant?,
    menuView: ShapeableImageView,
    menuBgView: ImageView
  ) {
    if (FoodLargeImageUtil.shouldUseBigImage(food?.restaurant?.id)) {
      menuView.shapeAppearanceModel = shapeAppearanceModel
      ImageLoader.load(menuView, food?.largeImage?.url, placeholderId = com.ricepo.style.R.drawable.food_large_holder)
    } else {
      RestViewUtils.setMenuBackground(
        menuBgView,
        item,
        food?.image,
        visible = food?.image?.url != null
      )
      ImageLoader.load(
        menuView, food?.image?.url,
        placeholderId = com.ricepo.style.R.drawable.ic_placeholder_bag
      )
    }
  }
}
