package com.ricepo.app.features.profile.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.Group
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.ProfileItemSettingBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.coupon.data.ShowCouponCase
import com.ricepo.app.features.profile.EmailEditFragment
import com.ricepo.app.utils.findFragmentActivity
import com.ricepo.base.data.pref.CommonPref
import com.ricepo.base.extension.clickAllWithTrigger
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Customer
import com.ricepo.base.model.CustomerSubscription
import com.ricepo.base.model.localize
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.base.remoteconfig.RiceRemoteConfig
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 12/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ProfileSettingHolder(private val binding: ProfileItemSettingBinding) :
  RecyclerView.ViewHolder(binding.root) {

  private val subscriptionGroup: Group = binding.groupSubscriptionVip

  init {
    subscriptionGroup.clickAllWithTrigger { view ->
      val subscription = subscriptionGroup.tag
      if (subscription is CustomerSubscription) {
        // update subscription
        FeaturePageRouter.navigateSubscriptionUpdate(view.context, subscription)
      }
    }

    binding.tvPersonCoupon.clickWithTrigger {
      // navigate coupon
      FeaturePageRouter.navigateCoupon(it.context, ShowCouponCase.customer, restaurant = null)
    }

    binding.tvPersonCoin.clickWithTrigger {
      // navigate coin
      FeaturePageRouter.navigateRewardSummary()
    }

    binding.tvRicepoPoint.clickWithTrigger {
      val tag = binding.root.tag
      if (tag is Customer) {
//        FeaturePageRouter.navigatePointsSummary(tag)
      }
    }
  }

  fun bind(customer: Customer?) {
    if (customer != null) {
      // show header view
      binding.root.visibility = View.VISIBLE

      // bind add subscription
      bindMembership(customer)

      // bind listener
      bindSubscription(customer)

      // bind info
      bindInfo(customer)
    } else {
      binding.root.visibility = View.GONE
    }
  }

  private fun bindInfo(customer: Customer) {
    customer.infoCount?.coupons?.let {
      if (it > 0) {
        binding.tvPersonCoupon.text = ResourcesUtil.getString(com.ricepo.style.R.string.n_coupon, it)
      }
    }
    customer.infoCount?.reward?.let {
      if (it > 0) {
        binding.tvPersonCoin.text = ResourcesUtil.getString(com.ricepo.style.R.string.n_restaurant_reward, it)
      }
    }
    customer.infoCount?.points?.let {
      if (it > 0) {
        binding.tvRicepoPoint.text = ResourcesUtil.getString(com.ricepo.style.R.string.n_coins, it)
      }
    }

    // hide the email
    binding.layEmail.isVisible = false
    if (customer.email?.address == null) {
      binding.tvEmailReward.run {
        isVisible = true
        text = ResourcesUtil.getString(
          com.ricepo.style.R.string.email_reward,
          BaseMapper().formatPrice(
            RiceRemoteConfig.completeProfileCouponAmount,
            countryCode = CommonPref.getCountryCode(),
            null
          )
        )
      }
    }

    binding.tvEmailCoin.clickWithTrigger {
      it.findFragmentActivity()?.let {
        EmailEditFragment(
          onSuccess = {
          },
          onError = {
          },
          customer = customer
        ).show(it.supportFragmentManager, null)
      }
    }
    binding.root.tag = customer
  }

  private fun bindMembership(customer: Customer?) {
    binding.layMembership.isVisible = false
    val plan = customer?.plan ?: return

    binding.inMembership.tvDividerTop.isVisible = false
    binding.inMembership.tvMembershipTitle.text = plan.title?.localize()
    binding.inMembership.tvMembershipSubtitle.text = plan.subtitle?.localize()
    binding.inMembership.tvMembershipApply.text = plan.button?.localize()
    binding.layMembership.isVisible = true

    binding.inMembership.root.clickWithTrigger {
      FeaturePageRouter.navigateSubscription(
        binding.root.context, plan,
        isOnlySubscribe = false, true
      )
    }
  }

  private fun bindSubscription(customer: Customer) {
    val subscription = customer.subscription
    if (subscription?.plan != null && subscription?.currentPeriodEndAt != null) {
      subscriptionGroup.visibility = View.VISIBLE
      subscriptionGroup.tag = subscription
      // hide the vip add subscription
      binding.layMembership.isVisible = false
    } else {
      subscriptionGroup.visibility = View.GONE
    }
  }

  companion object {
    fun create(parent: ViewGroup): ProfileSettingHolder {
      val binding = ProfileItemSettingBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      return ProfileSettingHolder(binding)
    }
  }
}
