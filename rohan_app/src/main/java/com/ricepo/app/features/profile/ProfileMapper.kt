package com.ricepo.app.features.profile

import com.ricepo.app.model.Order
import com.ricepo.base.model.mapper.BaseMapper

//
// Created by <PERSON><PERSON> on 2/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

// todo use di on this type
object ProfileMapper : BaseMapper() {

  fun mapOrderPrice(order: Order): String? {
    val total = order.total
    val customerAdjustment = order.adjustments?.customer ?: 0
    val num = if (total > customerAdjustment) total - customerAdjustment else 0
    return formatPriceByRestaurant(num, order.restaurant)
  }

  fun mapOrderCreatedAt(order: Order): String? {
    val createdAt = order.createdAt
    return formatTime(createdAt, "M.dd.yyyy")
  }
}
