package com.ricepo.app.features.menu.data

import com.ricepo.base.model.Category
import com.ricepo.base.model.Food
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.style.button.HamburgerButton

//
// Created by <PERSON><PERSON> on 7/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

data class MenuSection(
  var category: Category,
  var items: MutableList<MenuSectionItem>
) {
  val name: InternationalizationContent?
    get() {
      return category.name
    }
  val description: InternationalizationContent?
    get() {
      return category.description
    }
}

data class MenuSectionItem(
  val food: Food,
  val sectionType: Int? = null,
  val processedCategoryId: String? = null,
  // select hamburger button status
  var tag: HamburgerButton.RotateType? = null
)

object MenuSectionType {
  const val NORMAL = 200
  const val MARKET = 201
  const val FEATURED = 202
  const val REWARD = 203
}
