package com.ricepo.app.features.menu

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ImageSpan
import com.ricepo.app.R
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.menu.cart.CartUiModel
import com.ricepo.app.restaurant.data.OptionsModify
import com.ricepo.base.extension.deepCopy
import com.ricepo.base.model.AddressObj
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Food
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.Item
import com.ricepo.base.model.Option
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.model.localize
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.span.CenteredImageSpan
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 30/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class MenuMapper @Inject constructor() : BaseMapper() {

  /**
   * sort options and items
   */
  fun mapFood(food: Food): Food {
    var f = food

    // order the items of the food option by popularity
    for (i in 0 until (f.options?.count() ?: 0)) {
      f.options?.get(i)?.items = f.options?.get(i)?.items?.sortedWith(
        Comparator { o1, o2 ->
          val popularity1 = if (o1?.available == true) - (o1?.analytics?.popularity ?: 0) else 1
          val popularity2 = if (o2?.available == true) - (o2?.analytics?.popularity ?: 0) else 1
          popularity1.compareTo(popularity2)
        }
      ) ?: listOf()
    }

    // order the food options by min accend
    f.options = f.options?.sortedWith(
      Comparator { o1, o2 ->
        var min1 = o1?.min ?: 0
        var min2 = o2?.min ?: 0
        min1 = if (min1 > 0) min1 else 9999
        min2 = if (min2 > 0) min2 else 9999
        min1.compareTo(min2)
      }
    )?.toMutableList()

    return f
  }

  /**
   * range of [option] min to max
   */
  fun mapRange(option: Option?): String {
    val min = option?.min ?: 0
    val max = option?.max ?: 0

    var textRange = ""
    if (min == 0 && max > 0) {
      textRange = ResourcesUtil.getString(com.ricepo.style.R.string.option_max, max)
    } else if (min > 0 && max == 0) {
      textRange = ResourcesUtil.getString(com.ricepo.style.R.string.option_min, min)
    } else if (min > 0 && max == min) {
      textRange = ResourcesUtil.getString(com.ricepo.style.R.string.option_exact, min)
    } else if (min in 1 until max) {
      textRange = ResourcesUtil.getString(com.ricepo.style.R.string.option_range, min, max)
    }

    return textRange
  }

  /**
   * map cart from food
   */
  fun mapCart(food: Food, restaurantName: InternationalizationContent?): Cart {
    val maxItem = food.category?.maxItem
    val categoryId = food.category?.id
    return Cart(
      id = food.id, price = food.price, originalPrice = food.originalPrice,
      point = food.point, reward = food.reward,
      name = food.name, opt = food.opt ?: listOf(),
      qty = null, limit = food.limit, createdAt = System.currentTimeMillis().toDouble(),
      categoryMaxItem = maxItem, categoryId = categoryId,
      bundleRestBalance = food.category?.restBalance,
      bundleRestId = food.restaurant?.id,
      bundleRestName = food.restaurant?.name ?: restaurantName,
      minimum = food.condition?.minimum,
      foodImage = food.image,
    )
  }

  /**
   * group by id and return the list cart with qty count
   */
  fun mapCarts(carts: List<Cart>?): List<Cart>? {
    return carts?.groupBy {
      it.id
    }?.mapNotNull {
      var cart = it.value
      cart[0].qty = cart.count()
      cart[0]
    }
  }

  /**
   * group by id and options
   */
  fun mapCartsAndOptions(carts: List<Cart>?): List<Cart>? {
    return carts?.groupBy {
      val optStr = it.opt.fold(StringBuilder()) { result, item ->
        result.append(item.id)
      }
      it.id + optStr
    }?.mapNotNull {
      var cart = it.value
      cart[0].qty = cart.count()
      cart[0]
    }?.sortedBy { it.createdAt }
  }

  suspend fun mapCartCount(restaurant: Restaurant?): Int {
    // group by id and opt str
    var restCart = RestaurantCartCache.getRestaurantCartSuspend(restaurant)
    val cartList = groupByCarts(restCart?.cartList)

    var totalCount = 0

    cartList?.forEachIndexed { index, cart ->
      // compute the cart price
      var count = cart.qty ?: 0
      totalCount += count
    }

    return totalCount
  }

  fun mapCartCount(foods: List<Cart>?, restaurantCart: RestaurantCart?): Pair<String?, Int> {
    // group by id and opt str
    val cartList = groupByCarts(foods)
    var restCart = restaurantCart ?: RestaurantCart(cartList = listOf())
    restCart.cartList = cartList?.sortedBy { it.createdAt }

    var totalCount = 0

    cartList?.forEachIndexed { index, cart ->
      // compute the cart price
      var count = cart.qty ?: 0
      totalCount += count
    }

    return Pair(restCart.restaurant?.name?.localize(), totalCount)
  }

  fun mapCartUiModel(foods: List<Cart>?, restaurantCart: RestaurantCart?):
    Triple<List<CartUiModel>, Int, RestaurantCart> {
    // group by id and opt str
    val cartList = groupByCarts(foods)
    var restCart = restaurantCart ?: RestaurantCart(cartList = listOf())
    restCart.cartList = cartList?.sortedBy { it.createdAt }

    val uiModels = mutableListOf<CartUiModel>()
    var totalPrice = 0

    cartList?.reversed()?.forEachIndexed { index, cart ->
      // compute the cart price
      var count = cart.qty ?: 0
      totalPrice += (cart.price.times(count))

      // builder cart food
      val subBuilder = StringBuilder()
      if (count > 0) {
        subBuilder.append("${cart.name.localize()}")
        if (count > 1) {
          subBuilder.append(" x $count")
        }
      }

      val textBuilder = SpannableStringBuilder()
      textBuilder.append(subBuilder)

      uiModels.add(CartUiModel.CartMenuUiModel(textBuilder, cart.foodImage))
    }

    return Triple(uiModels, totalPrice, restCart)
  }

  /**
   * return the item str and total price of list cart
   */
  fun mapCarts(foods: List<Cart>?, restaurantCart: RestaurantCart?, context: Context? = null):
    Triple<SpannableStringBuilder, Int, RestaurantCart> {
    // group by id and opt str
    val cartList = groupByCarts(foods)
    var restCart = restaurantCart ?: RestaurantCart(cartList = listOf())
    restCart.cartList = cartList?.sortedBy { it.createdAt }
    return cartList?.fold(Triple(SpannableStringBuilder(), 0, restCart)) { triple, cart ->

      var totalPrice = triple.second
      val subBuilder = StringBuilder()

      var count = cart.qty ?: 0

      // compute the cart price
      totalPrice += (cart.price.times(count))

      if (count > 0) {
        subBuilder.append("${cart.name.localize()}")
        if (count > 1) {
          subBuilder.append(" x $count")
        }
      }

      triple.first.append(subBuilder)

      Triple(triple.first, totalPrice, triple.third)
    } ?: Triple(
      SpannableStringBuilder(), 0,
      restaurantCart ?: RestaurantCart(
        listOf()
      )
    )
  }

  fun groupByCarts(cartList: List<Cart>?): List<Cart>? {
    return cartList?.groupBy {
      val optStr = it.opt.fold(StringBuilder()) { result, item ->
        result.append(item.id)
      }
      it.id + optStr
    }?.mapNotNull {
      var carts = it.value
      val cart = carts.last()
      val count = carts.count()
      cart.qty = count
      cart
    }
  }

  fun mapOptionsUiModel(context: Context, food: Food): Pair<List<CartUiModel>, Int> {
    val options = food?.options
    if (options == null || options.isEmpty()) return Pair(listOf(), 0)

    // init of food price
    return options.fold(Pair(mutableListOf<CartUiModel>(), food.price)) { result, option ->
      val uniqueSelected = option.selected?.fold(mutableListOf<Item>()) { items, item ->
        if (items.find { it.id == item.id } == null) {
          items.add(item)
        }
        items
      }

      val itemPair = uniqueSelected?.fold(Pair(SpannableStringBuilder(), 0)) { pair, item ->
//                val count = option.items.first { it.id == item.id }?.count ?: 0
        val count = item.count ?: 0

        var subBuilder = StringBuilder()
        if (count > 0) {
          subBuilder.append("${item.name.localize()}")
          if (count > 1) {
            subBuilder.append(" x $count")
          }
        }

        pair.first.append(subBuilder)
        result.first.add(CartUiModel.CartMenuUiModel(SpannableStringBuilder(subBuilder), null, true))
        var totalPrice = pair.second
        totalPrice += (item.price?.times(count) ?: 0)

        Pair(pair.first, totalPrice)
      } ?: Pair(SpannableStringBuilder(), 0)

      var totalPrice = result.second
      totalPrice += itemPair.second

      Pair(result.first, totalPrice)
    }
  }

  /**
   * [options] of selected to price and string
   */
  fun mapOptions(context: Context, food: Food): Pair<List<CartUiModel>, Int> {
    val options = food?.options
    if (options == null || options.isEmpty()) return Pair(listOf(), 0)

    // init of food price
    return options.fold(Pair(mutableListOf(), food.price)) { result, option ->
      val uniqueSelected = option.selected?.fold(mutableListOf<Item>()) { items, item ->
        if (items.find { it.id == item.id } == null) {
          items.add(item)
        }
        items
      }

      val itemPair = uniqueSelected?.fold(Pair(SpannableStringBuilder(), 0)) { pair, item ->
//                val count = option.items.first { it.id == item.id }?.count ?: 0
        val count = item.count ?: 0

        var subBuilder = StringBuilder()
        if (count > 0) {
          subBuilder.append("${item.name.localize()}")
          if (count > 1) {
            subBuilder.append(" x $count")
          }
        }

        pair.first.append(subBuilder)

        var totalPrice = pair.second
        totalPrice += (item.price?.times(count) ?: 0)

        Pair(pair.first, totalPrice)
      } ?: Pair(SpannableStringBuilder(), 0)

      result.first.toMutableList().add(
        CartUiModel.CartMenuUiModel(
          itemPair.first, null, true
        )
      )

      var totalPrice = result.second
      totalPrice += itemPair.second

      Pair(result.first, totalPrice)
    }
  }

  /**
   * [options] of selected to string
   */
  fun mapOptionsComplete(food: Food, restaurant: Restaurant?): Pair<StringBuilder, Food> {
    val food = food.deepCopy()
    val options = food.options
    if (options == null || options.isEmpty()) return Pair(StringBuilder(), food)

    // invalid if selected options count not reach the minimum count
    val invalidOptions = options.filter {
      var count = 0
      it.selected?.forEach {
        count += it.count ?: 0
      }
      (count < (it.min ?: 0))
    }

    // iterate the invalid options to construct warning text
    var message = invalidOptions.fold(StringBuilder()) { result, option ->
      val name = option.name?.localize() ?: ""
      val min = option.min ?: ""
      val text = ResourcesUtil.getString(com.ricepo.style.R.string.error_options_not_reach_min, name, min)
      result.append(text)
      result.append("\n")
      result
    }

    // sort & update the food opt list as selected option list
    val opt = options.fold(mutableListOf<Item>()) { result, option ->
      // distinct the options
      option.selected?.sortedBy { it.id }?.distinctBy { it.id }?.forEach {
        for (i in 1..(it.count ?: 0)) {
          result.add(it)
        }
      }
      result
    }
    // set selected options
    food.opt = opt

    // init food price and iterate selected option to calculate total price
    val totalPrice = opt.fold(food.price) { result, item ->
      var price = result
      price += (item.price ?: 0)
      price
    }

    // update food price, add selected options price
    food.price = totalPrice

    // original price
    food.originalPrice?.let {
      val totalOriginPrice = opt.fold(it) { result, item ->
        var price = result
        price += (item.price ?: 0)
        price
      }
      food.originalPrice = totalOriginPrice
    }

    val minimum = food.minimum ?: 0

    if (totalPrice < minimum) {
        message = StringBuilder(toMinimumInfo(minimum, restaurant))
      }

    return Pair(message, food)
  }

  fun mapOptionsLucky(food: Food): Food {
    val opt = food.options?.fold(mutableListOf<Item>()) { result, option ->
      option.name?.let {
//                Log.d(LuckyRecommendViewModel.TAG, "reFood option price-${option.price}")
        val item = Item(option.id, null, null, it, 0, 1)
        result.add(item)
      }
      result
    }
    food.opt = opt
    return food
  }

  // append the header vertical line
  fun appendHeadVertLine(context: Context?, builder: SpannableStringBuilder) {
    val drawable = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_vert_line, context)
    val right = DisplayUtil.dp2PxOffset(3f)
    val bottom = DisplayUtil.dp2PxOffset(15f)
    drawable.setBounds(0, 0, right, bottom)
    val imageSpan = ImageSpan(drawable)
    builder.insert(0, " ")
    builder.setSpan(
      imageSpan, 0, 1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE
    )
  }

  // append vertical line
  fun appendVertLine(context: Context?, builder: SpannableStringBuilder) {
    val drawable = ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_vert_line, context)
    val right = DisplayUtil.dp2PxOffset(3f)
    val bottom = DisplayUtil.dp2PxOffset(15f)
    drawable.setBounds(0, 0, right, bottom)
    val imageSpan = ImageSpan(drawable)
    builder.setSpan(
      imageSpan, builder.length - 1, builder.length,
      Spannable
        .SPAN_INCLUSIVE_EXCLUSIVE
    )
  }

  fun toMinimumInfo(minimum: Int, restaurant: Restaurant?): String {
    val address = restaurant?.address
    if (address is AddressObj) {
      val countryCode = address.country
      val minimumPrice = formatPrice(minimum, countryCode, 0)
      return ResourcesUtil.getString(com.ricepo.style.R.string.menu_option_food_minimum, minimumPrice)
    }
    return ""
  }

  fun toDeliveryMessage(totalPrice: Int = 0, restaurant: Restaurant?): String {
    val fees = restaurant?.delivery?.fees ?: return ""

    val firstFreeDelivery = getFreeDelivery(fees) ?: return ""

    // condition1: not reach first free delivery, show `再加$xx享$xx送费`
    if (firstFreeDelivery.minimum > totalPrice) {
      val diff = firstFreeDelivery.minimum - totalPrice
      val fee = firstFreeDelivery.delivery - (firstFreeDelivery.adjustments?.customer ?: 0)

      val diffText = toTotalPrice(diff.toInt(), restaurant, null)
      val feeText = toTotalPrice(fee, restaurant, null)

      return ResourcesUtil.getString(com.ricepo.style.R.string.free_delivery_diff, diffText, feeText)
    }

    // condition2: do reach the first free delivery, show current level `享$x送费`
    val currentFreeDelivery = getFreeDelivery(fees, totalPrice) ?: return ""

    val fee = currentFreeDelivery.delivery - (currentFreeDelivery.adjustments?.customer ?: 0)
    val feeText = toTotalPrice(fee, restaurant, null)

    return ResourcesUtil.getString(com.ricepo.style.R.string.free_delivery_ok, feeText)
  }

  fun toTotalPrice(price: Int, restaurant: Restaurant?, fixed: Int? = 2): String {
    val address = restaurant?.address
    if (address is AddressObj) {
      val countryCode = address.country
      return "${formatPrice(price, countryCode, fixed)}"
    }
    return "${formatPrice(price, null, toFixed = fixed)}"
  }

  fun getModifySpan(context: Context, showModify: Boolean): ImageSpan? {
    if (!showModify) return null
    val optionsModify = ResourcesUtil.getDrawable(OptionsModify().localize(), context)
    return if (optionsModify != null) {
      val right = DisplayUtil.density.times(28).toInt()
      val bottom = DisplayUtil.density.times(18).toInt()
      optionsModify.setBounds(
        0, 0, right,
        bottom
      )
      CenteredImageSpan(optionsModify, ImageSpan.ALIGN_BOTTOM)
    } else {
      null
    }
  }
}
