package com.ricepo.app.features.menu.adapter.listener

import android.view.Gravity
import com.ricepo.app.databinding.MenuGalleryItemBinding
import com.ricepo.app.databinding.MenuOptionsSectionItemBinding
import com.ricepo.app.databinding.MenuRecommendItemBinding
import com.ricepo.app.databinding.MenuSectionComboItemBinding
import com.ricepo.app.databinding.MenuSectionNormalItemBinding

//
// Created by <PERSON><PERSON> on 5/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object QuantityDecorator {

  fun bindRecommendQtyChocolate(binding: MenuRecommendItemBinding, selectedCount: Int) {
    binding.btnFoodOption.setSelectedCount(selectedCount)
    binding.btnFoodOption.bringToFront()
  }

  fun bindOptionsQtyChocolate(binding: MenuOptionsSectionItemBinding, selectedCount: Int) {
    binding.btnFoodOption.setSelectedCount(selectedCount)
    binding.btnFoodOption.bringToFront()
  }

  fun bindGalleryQtyChocolate(binding: MenuGalleryItemBinding, selectedCount: Int) {
    binding.btnFoodOption.setQtyAddGravity(Gravity.LEFT)
    binding.btnFoodOption.setSelectedCount(selectedCount)
    binding.btnFoodOption.bringToFront()
  }

  /**
   * qty operation
   */
  fun bindQtyChocolate(binding: MenuSectionNormalItemBinding, selectedCount: Int) {
    binding.btnFoodOption.setSelectedCount(selectedCount)
    binding.btnFoodOption.bringToFront()
  }

  /**
   * qty operation
   */
  fun bindQtyChocolateCombo(binding: MenuSectionComboItemBinding, selectedCount: Int) {
    binding.btnFoodOption.setSelectedCount(selectedCount)
    binding.btnFoodOption.bringToFront()
  }
}
