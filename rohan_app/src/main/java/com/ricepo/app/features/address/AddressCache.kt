package com.ricepo.app.features.address

import com.ricepo.app.consts.GlobalValueHolder
import com.ricepo.app.data.AppDatabase
import com.ricepo.base.BaseApplication
import com.ricepo.base.BuildConfig
import com.ricepo.base.data.pref.CommonPref
import com.ricepo.map.model.FormatLocation
import com.ricepo.map.model.FormatUserAddress
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

//
// Created by <PERSON><PERSON> on 15/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
object AddressCache {

  /**
   * save address
   */
  @OptIn(DelicateCoroutinesApi::class)
  fun saveAddress(address: FormatUserAddress, completed2: () -> Unit = {}) {
    GlobalScope.launch {
      dataSource().saveAddress(address)
      GlobalValueHolder.address.value = address
      // save the country code
      CommonPref.saveCountryCode(address.country)
      completed2()
    }
  }

  fun getAddress(): FormatUserAddress? {
    return dataSource().getAddressLatest()
  }

  /**
   * need with context io dispatcher
   */
  suspend fun getAddressSuspend(): FormatUserAddress? {
    return suspendCoroutine { continuation ->
      val address = dataSource().getAddressLatest()
      continuation.resume(address)
    }
  }

  suspend fun getLocationSuspend(): String? {
    val address = withContext(Dispatchers.IO) {
      getAddressSuspend()
    }
    return address?.location?.coordinates?.joinToString(",")
  }

  suspend fun getCountrySuspend(): String? {
    return suspendCoroutine { continuation ->
      val address = dataSource().getAddressLatest()
      val country = address?.country
      continuation.resume(country)
    }
  }

  /**
   * the us address
   */
  fun isCountryUS(): Boolean {
    return getAddress()?.country == "US"
  }

  /**
   * the us address
   */
  suspend fun isUS(): Boolean {
    return getCountrySuspend() == "US"
  }

  /**
   * provide key value dao
   */
  private fun dataSource(): AddressDao {
    val database = AppDatabase.getInstance(BaseApplication.context)
    return database.addressDao()
  }

  fun defaultAddress() = if (BuildConfig.DEBUG) {
    FormatUserAddress(
      name = "Madrid",
      formatted = "Spain",
      placeId = "ChIJgTwKgJcpQg0RaSKMYcHeNsQ",
      location = FormatLocation(
        type = "Point",
        coordinates = listOf(-3.7037902, 40.4167754),
      ),
      unit = "1234",
      note = "456",
      city = "Madrid",
      state = "MD",
      country = "ES"
    )
  } else {
    null
  }
}
