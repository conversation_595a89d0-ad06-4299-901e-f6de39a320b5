package com.ricepo.app.features.menu.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.consts.CategoryConst
import com.ricepo.app.databinding.MenuSectionComboHorizontalBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.adapter.FunShowMore
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.base.model.Food
import com.ricepo.style.view.headerlayout.SectionItemHolder
import com.ricepo.style.view.rv.ScrollStatePersist

//
// Created by <PERSON><PERSON> on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuSectionComboHorizontalHolder(
  private val binding: MenuSectionComboHorizontalBinding,
  private val persist: ScrollStatePersist?,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty,
  private val showMore: FunShowMore
) :
  SectionItemHolder(binding.root), ScrollStatePersist.ScrollStateKeyProvider {

  val mapper = MenuMapper()

  var menuAdapter: MenuSectionAdapter? = null

  var foodItems: List<Food>? = null

  private var model: MenuUiModel.MenuComboHorizontalSection? = null

  private var rows = 1
  private var amount = 0

  fun bind(uiModel: MenuUiModel.MenuComboHorizontalSection, sectionPosition: Int) {
    model = uiModel
    rows = uiModel.category?.rows ?: 1

    var uiModels = uiModel.models.map {
      if (it is MenuUiModel.MenuComboItem) {
        it.type = CategoryConst.COMBO
        it.rows = rows
      }
      it
    }

    // completed rows last column
    val size = uiModels.size
    val mod = (size % rows)
    if (mod > 0) {
      val complete = rows - mod
      for (i in 0 until complete) {
        val mut = uiModels.toMutableList()
        mut.add(
          MenuUiModel.MenuComboItem(
            null, null, type = CategoryConst.COMBO, rows = rows
          )
        )
        uiModels = mut
      }
    }
    amount = uiModels.size

    gravitySnapHelper.attachToRecyclerView(binding.rvMenuContainer)

    val foodPosition = uiModel.foodIndex ?: -1
    if (menuAdapter == null) {
      menuAdapter = MenuSectionAdapter(
        uiModels, addFood = addFood,
        minusFood = minusFood, showMore = showMore, navMenu = {}
      )
      binding.rvMenuContainer.adapter = menuAdapter
    } else {
      menuAdapter?.models = uiModels
      if (foodPosition == -1) {
        menuAdapter?.notifyDataSetChanged()
      } else {
        menuAdapter?.notifyItemChanged(foodPosition)
      }
    }

    persist?.restoreScrollState(binding.rvMenuContainer, this)

    val isLast = binding.rvMenuContainer.tag
    if (isLast is Boolean && isLast == true) {
//            setRightPadding(isLast)
    }
  }

  fun bindQtyView(food: Food) {
    val foodPosition = foodItems?.indexOfFirst { it.id == food.id } ?: -1

    if (foodPosition == -1) {
      menuAdapter?.notifyDataSetChanged()
    } else {
      menuAdapter?.notifyItemChanged(foodPosition, food)
    }
  }

  private val gravitySnapHelper = PagerSnapHelper()

  companion object {
    fun create(
      parent: ViewGroup,
      persist: ScrollStatePersist?,
      addFood: FunFoodQty,
      minusFood: FunFoodQty,
      showMore: FunShowMore
    ): MenuSectionComboHorizontalHolder {
      val binding = MenuSectionComboHorizontalBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      binding.rvMenuContainer.layoutManager = GridLayoutManager(
        binding.root.context, 1, LinearLayoutManager.HORIZONTAL, false
      )

      binding.rvMenuContainer.isNestedScrollingEnabled = false
      binding.rvMenuContainer.itemAnimator = null

      val holder = MenuSectionComboHorizontalHolder(binding, persist, addFood, minusFood, showMore)
      persist?.setupRecyclerView(binding.rvMenuContainer, holder)

      // don't used the item view (complete rows last column)
      binding.rvMenuContainer.recycledViewPool.setMaxRecycledViews(
        R.layout.menu_section_combo_item, 0
      )

      return holder
    }
  }

  override fun getScrollStateKey(): String? {
    return model?.category?.id + model?.category?.groupId
  }
}
