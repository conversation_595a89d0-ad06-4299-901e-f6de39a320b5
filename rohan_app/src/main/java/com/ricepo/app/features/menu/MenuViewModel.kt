package com.ricepo.app.features.menu

import androidx.activity.ComponentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ricepo.app.R
import com.ricepo.app.consts.CategoryConst
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.menu.data.MenuGroupStatus
import com.ricepo.app.features.menu.data.MenuPageData
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.base.BaseApplication
import com.ricepo.base.analytics.AnalyticsFacade
import com.ricepo.base.extension.deepCopy
import com.ricepo.base.extension.update
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Category
import com.ricepo.base.model.Food
import com.ricepo.base.model.MenuBean
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.base.viewmodel.RxBiFunction
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.subjects.BehaviorSubject
import io.reactivex.rxjava3.subjects.PublishSubject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.channels.ConflatedBroadcastChannel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.min

//
// Created by Thomsen on 30/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
open class MenuViewModel @Inject constructor(
  private val menuUseCase: MenuUseCase,
  private val menuGroupUseCase: MenuGroupUseCase,
  private val menuMapper: MenuMapper,
  private val postExecutionThread: PostExecutionThread
) : BaseViewModel() {

  init {
    viewModelScope.launch {
      // coroutines that will be canceled when the ViewModel is cleared.
      menuCartObserver = MutableLiveData<CartData>()
    }
  }

  data class Input(
    val restaurant: Restaurant?,
    val refreshMenu: PublishSubject<Boolean>,
    val searchBarEditing: BehaviorSubject<String>
  )

  data class Output(
    var menuTableUpdated: Observable<MenuPageData>,
    var menuTableNoFood: Observable<MenuBean>,
    var menuCheckOption: LiveData<CheckOption>,
    var menuCartObserver: MutableLiveData<CartData>
  )

  data class CartData(
    val update: Boolean,
    val carts: List<Cart>?
  )

  data class CheckOption(
    val message: String,
    val food: Food,
    val restaurant: Restaurant,
    val deleteCacheCart: Boolean = false,
    val isAlert: Boolean = false,
    val pageStatus: MenuGroupStatus? = null,
    val selectedPosition: Int? = null,
    val galleryFoodIndex: Int? = null
  ) {
    companion object {
      const val MESSAGE_REFRESH = "refresh"
    }
  }

  var restaurantInfo: Restaurant? = null

  var restaurantMenu: MenuBean? = null

  var menuRestaurantCart: RestaurantCart? = null
  private var restaurantCartCache: RestaurantCart? = null

  private var foodWithCategory: List<Category>? = null

  private var categoryLimitDicChange: BehaviorSubject<Category> = BehaviorSubject.create()

  private val reloadTableForQtyChanging = BehaviorSubject.create<Boolean>()

  val menuSearchObserver = BehaviorSubject.create<Boolean>()
  var menuSearchFilter: String? = null

  var mapCategoryMaxItem: HashMap<String, Int?>? = null

  var menuCheckOption: MutableLiveData<CheckOption> = MutableLiveData()

  lateinit var menuCartObserver: MutableLiveData<CartData>

  /**
   * the search keyword of restaurant search and menu food name
   */
  var searches: List<String>? = null

  private var localSearchKeyword: String? = null

  var mGroupStatus: MenuGroupStatus? = null

  var isTableRefresh = false

  var isMemberInGroup = false

  private var categoryExpand: MutableMap<String, Int?> = mutableMapOf()

  var bundles: ArrayList<String>? = null

  var promotionFoods: ArrayList<Food>? = null

  var uiModels: List<MenuUiModel>? = null

  var deliveryMode: String? = null

  // current location
  var location: String? = null

  val observeError = PublishSubject.create<Throwable>()

  val menuRecommendChannel = MutableSharedFlow<List<Food>>()

  fun transform(input: Input, isSearch: Boolean = false): Output {

    // get the restaurant
    val restaurant = getRestaurant(input)

    val isLoadingMenu = restaurant.flatMap {
      Observable.just(it != null && restaurantInfo != null)
    }

    val afterGetMenu = observeGetMenu(input, isLoadingMenu, isSearch)

    // execute twice if afterGetMenu isLoadingMenu dependencies restaurant
    // publish share distinct replay(1) and compose(ReplayingShare.instance()) convert cold to hot
    val menuWithFood = afterGetMenu.flatMap {
      val result = it ?: MenuBean()
      restaurantMenu = result
      Observable.just(result)
    }.share()

    // get menu and check reward validation
    val menuUpdated = Observable.merge(
      menuWithFood,
      reloadTableForQtyChanging
    )
      .flatMap { checkRewardValidation(input, restaurantMenu) }

    // observe the search text
    val searchUpdated = observeSearch(input)

    // observe menu info by food search text and menu request response
    // menuUpdated don't use distinct because of show search result
    // after qty change (checkout back)
    val menuResult =
      Observable.combineLatest(
        menuUpdated,
        searchUpdated.distinctUntilChanged(),
        RxBiFunction<MenuBean, String, List<Category>> { menuBean, searchText ->
          localSearchKeyword = searchText
          // set the foodWithCategory initial
          foodWithCategory = menuBean.sections?.filterNotNull() ?: listOf()
          var categories = foodWithCategory ?: listOf()
          if (searchText.isNullOrEmpty()) {
            // directly return categories info as menu info if not in local search mode
            // reset the food search result hidden image
            categories
          } else {
            // reset show more change select position to notify data
            sectionPosition = -1
            isTableRefresh = false
            // construct local search category info if search text is not empty
            menuUseCase.filterLocalSearchCategory(categories, searchText)
          }
        }
      )

    // calculate food quantity when
    // 1. menu result update: load / search food success
    // 2. view appear
    val categoriesResult = Observable.merge(
      menuResult,
      input.refreshMenu
        .filter { it == true }
        .flatMap {
          // don't use the ?: in just
          val result = foodWithCategory ?: listOf()
          Observable.just(result)
        }
    )
      .observeOn(postExecutionThread.ioScheduler)
      .filter { !it.isNullOrEmpty() }
      .switchMap {
        // the local search results
        foodWithCategory = it
        val result = foodWithCategory ?: listOf()
        Observable.just(result)
      }

    val menuTableUpdated = observerTableUpdated(
      menuResult,
      reloadTableForQtyChanging, categoryLimitDicChange
    )

    return Output(menuTableUpdated, menuWithFood, menuCheckOption, menuCartObserver)
  }

  fun updateFoodQuantity(categories: List<Category>?): List<Category>? {

    // restaurant carts
    val restaurantCart = RestaurantCartCache
      .getRestaurantCart(restaurantInfo) ?: return categories?.map {
      var cts = it
      cts.items = it.items?.map { food ->
        food.selectedCount = 0
        food
      }
      cts
    }

    var carts: List<Cart>? = restaurantCart?.cartList

    // update cart value
    carts?.forEach { cart ->
      val category = categories?.filter { it.type == CategoryConst.REWARD }
        ?.firstOrNull { it.groupId == cart.bundleRestId }
      if (category != null) {
        cart.bundleRestBalance = category.balance
      }
      // image from server by reorder and feeling lucky
//            // update image by reorder
//            if (cart.foodImage == null) {
//                categories?.filter { it.groupId == cart.bundleRestId }?.map {
//                    it.items?.firstOrNull { it.id == cart.id }?.let {
//                        cart.foodImage = it.image
//                    }
//                }
//            }
    }
    restaurantCart?.cartList = carts
    viewModelScope.launch {
      if (restaurantCart.restaurant?.tags == null &&
        restaurantInfo?.tags != restaurantCart.restaurant?.tags
      ) {
        // delete the market flag of old reorder tags null
        RestaurantCartCache.deleteRestaurantCart(restaurantCart.restaurant)
        // update reorder no restaurant tags
        restaurantCart.restaurant = restaurantInfo
      }
      RestaurantCartCache.saveRestaurantCart(restaurantCart, isUpdate = false)
    }

    // owner carts in group and count the qty
    val cartList = menuMapper.mapCarts(menuGroupUseCase.getOwnerCards(restaurantInfo, carts))

    return categories?.map {
      var cts = it
      cts.items = it.items?.map { food ->
        val cl = cartList?.filter { cart -> cart.id == food.id }
        food.selectedCount = if (cl?.isNotEmpty() == true) cl[0].qty else 0
        food
      }
      cts
    }
  }

  /**
   * observe search bar editing event
   */
  private fun observeSearch(input: Input): Observable<String> {
    return input.searchBarEditing
      .startWith(Observable.just(""))
      .distinctUntilChanged()
      .throttleLast(30, TimeUnit.MILLISECONDS)
      .flatMap {
        Observable.just(it.toLowerCase())
      }
  }

  private fun observerTableUpdated(
    menuUpdated: Observable<List<Category>>,
    reloadQtyChanged: Observable<Boolean>,
    categoryLimitDicChange: Observable<Category>
  ): Observable<MenuPageData> {
    return Observable.merge(menuUpdated, reloadQtyChanged, categoryLimitDicChange)
      .observeOn(postExecutionThread.ioScheduler)
      .map {
        // should remove reward section if join a group
        val memberInGroup = menuGroupUseCase.memberInGroup(restaurantInfo)
        if (isMemberInGroup != memberInGroup) {
          // switch member in group to refresh list
          isTableRefresh = false
          sectionPosition = -1
          isMemberInGroup = memberInGroup
        }
        (if (memberInGroup) {
          val categories = foodWithCategory?.filter {
            it.type != CategoryConst.REWARD
          }
          categories
        } else {
          foodWithCategory
        } ?: listOf())
      }
      .switchMap {

        // update food quantity with foodWithCategory
        val categories = updateFoodQuantity(it)
        if (categories.isNullOrEmpty()) {
          // need explicit
          val pageData = MenuPageData(
            0, listOf(), listOf(), listOf(),
            null, shouldShowCount, false
          )
          Observable.just(pageData)
        } else {
          val mapMaxItem = mapCategoryMaxItem ?: menuUseCase.associateCategoryMaxItem(categories)
          uiModels = menuUseCase.constructUiModels(
            restaurantInfo, categories, mapMaxItem,
            categoryExpand, restaurantMenu, !menuSearchFilter.isNullOrEmpty()
          )

          // construct promotion food (bundle menu need clear)
          if (menuSearchFilter.isNullOrEmpty()) {
            promotionFoods = null
            promotionFoods = menuUseCase.constructPromotionFood(categories)
            // max category item to sub page
            mapMaxItem.let { maxItems ->
              mapCategoryMaxItem = hashMapOf()
              maxItems.forEach {
                mapCategoryMaxItem?.put(it.key, it.value)
              }
            }
          }

          val models = uiModels ?: listOf()
          val pageData = MenuPageData(
            sectionPosition, null,
            models, restaurantMenu?.groups, restaurantMenu?.bundles,
            shouldShowCount, isTableRefresh
          )

          Observable.just(pageData)
        }
      }
  }

  var sectionPosition: Int = -1

  private var shouldShowCount: Int = 1
  private var galleryFoodIndex: Int? = null

  private fun checkRewardValidation(input: Input, bean: MenuBean?): Observable<MenuBean> {
    return Observable.create { emitter ->
      // clean the reward
      viewModelScope.launch {
        val m = bean ?: return@launch
        val menu = m.deepCopy()
        withContext(Dispatchers.IO) {
          val restIds = menuUseCase.checkCartReward(restaurantInfo, menu)
          if (restIds != null) {
            RestaurantCartCache.cleanReward(restaurantInfo, restIds)
            // clear selected the categories reward in group
            GroupOrderCache.clearReward(restaurantInfo?.id, restIds)
          }
        }
        // init menu cart
        initMenuCart(input.restaurant)
        withContext(Dispatchers.Main) {
          emitter.onNext(menu)
        }
      }
    }
  }

  private fun observeGetMenu(input: Input, isLoadingMenu: Observable<out Any>, isSearch: Boolean):
    Observable<MenuBean> {

    val observer = if (isSearch && menuSearchFilter.isNullOrEmpty()) {
      menuSearchObserver
    } else {
      isLoadingMenu
    }

    return observer.observeOn(postExecutionThread.ioScheduler)
      .flatMap {
        val restaurantId = input.restaurant?.id ?: ""
        val loc = location ?: AddressCache.getAddress()?.location?.loc() ?: ""
        if (bundles == null) {
          RestaurantCartCache.getRestaurantCart(input.restaurant)?.cartList?.filter {
            it.bundleRestId != null && it.bundleRestId != restaurantId
          }?.distinctBy {
            it.bundleRestId
          }?.map { it.bundleRestId ?: "" }?.let { restIds ->
            bundles = arrayListOf()
            restIds.forEach {
              bundles?.add(it)
            }
          }
        }
        menuUseCase.getMenuBean(
          restaurantId, loc, null,
          bundles, searches, menuSearchFilter
        ).toObservable()
          .flatMap {
            it.fold(
              onSuccess = {
                Observable.just(it)
              },
              onFailure = {
                observeError.onNext(it)
                Observable.just(MenuBean())
              }
            )
          }
      }
  }

  private fun getRestaurant(input: Input): Observable<Restaurant> {
    return input.refreshMenu
      .flatMapSingle {
        getRestaurantById(input, it)
      }
  }

  private fun getRestaurantById(input: Input, refreshRestaurant: Boolean): Single<Restaurant> {
    val restaurantId = input.restaurant?.id
    // if the name equals null with from url id is not null
    if (input.restaurant?.name != null && restaurantInfo == null) {
      restaurantInfo = input.restaurant
    }
    return if (restaurantInfo != null && !refreshRestaurant) {
      //  If rest id not found, return rest info from user default
      menuRestaurantCart = RestaurantCart(listOf(), restaurantInfo)
      Single.just(restaurantInfo!!)
    } else if (restaurantId != null) {
      // refresh reward and delivery fee when login at checkout page
      sectionPosition = -1
      isTableRefresh = false
      menuUseCase
        .getRestaurantById(restaurantId, location)
        .flatMap {
          // Update restaurant info after get rest success
          // refresh restaurant when login changed
          if (it.name != null) {
            if (restaurantInfo?.name != null && restaurantInfo?.pool == null) {
              restaurantInfo = it
            } else {
              // swap pool from dynamic link and cart bar
              val pool = restaurantInfo?.pool ?: RestaurantCartCache
                .getRestaurantCart(input.restaurant)?.restaurant?.pool
              restaurantInfo = it
              restaurantInfo?.pool = pool
            }
          }
          menuRestaurantCart = RestaurantCart(listOf(), restaurantInfo)
          Single.just(it)
        }
    } else {
      Single.just(Restaurant())
    }
  }

  fun initMenuCart(restaurant: Restaurant?) {
    viewModelScope.launch(Dispatchers.IO) {
      restaurantCartCache = RestaurantCartCache.getRestaurantCart(restaurantInfo)
        ?: RestaurantCart(listOf(), restaurant = null)
      if (restaurantCartCache?.restaurant?.id == restaurant?.id) {
        menuCartObserver.postValue(CartData(false, restaurantCartCache?.cartList))
      } else {
        menuCartObserver.postValue(CartData(false, null))
      }
    }
  }

  fun reloadTable(reload: Boolean) {
    // refresh qty by payload
    isTableRefresh = true
    // quantity dependency food when update group menu
    if (foodWithCategory != null) {
      // update quantity or changed group
      reloadTableForQtyChanging.onNext(reload)
    }
  }

  fun limitChange(category: Category, position: Int, selectedCount: Int) {
    // notify range start position
    isTableRefresh = false
    sectionPosition = position - selectedCount
    if (sectionPosition < 0) {
      sectionPosition = 0
    }
    // add name when id maybe empty multiple in bundle restaurant with search
    foodWithCategory?.filter {
      it.id == category.id &&
        it.groupId == category.groupId && it.name == category.name
    }
      ?.map {
        val count = it.items?.count() ?: 0
        val expand = it.expand ?: 10
        // count limit before expand
        val limitBeforeExpand = it.limit ?: 0
        shouldShowCount = min(count - limitBeforeExpand, expand)
        it.limit = (it.limit ?: 0) + shouldShowCount
        // itemCount with selectedCount
        shouldShowCount += selectedCount
        // id maybe "" multiple filter, add others
        categoryExpand.put(it.id + it.groupId + it.name?.localize(), it.limit)
        categoryLimitDicChange.onNext(category)
      }
  }

  /**
   * [isAdded] is add and return of menu option
   */
  suspend fun addFood(food: Food?, position: Int, foodIndex: Int?, isAdded: Boolean = true) {
    sectionPosition = position
    galleryFoodIndex = foodIndex
    // first check the group status
    if (mGroupStatus is MenuGroupStatus.None ||
      mGroupStatus is MenuGroupStatus.SameGroup ||
      mGroupStatus is MenuGroupStatus.SameShareGroup
    ) {

      val otherCart = RestaurantCartCache.getRestaurantCartSuspend(restaurantInfo, true)
      if (otherCart != null && otherCart?.restaurant?.id != restaurantInfo?.id &&
        otherCart?.cartList?.size ?: 0 > 0
      ) {
        // alert the cart will be cleared
        val message = ResourcesUtil.getString(
          com.ricepo.style.R.string.hint_cart_will_be_cleared,
          otherCart?.restaurant?.name?.localize() ?: ""
        )
        val checkOption = CheckOption(
          message, food ?: Food(), restaurantInfo ?: Restaurant(),
          deleteCacheCart = true, pageStatus = mGroupStatus
        )
        menuCheckOption.postValue(checkOption)
      } else {
        // reward check
        if (food?.reward == true) {
          // only allow select one reward food.
          val rewardCart = menuUseCase.validReward(
            menuRestaurantCart,
            restaurantInfo, food.restaurant?.id
          )
          if (rewardCart != null) {
            val restaurantName = rewardCart.bundleRestName?.localize()
            val checkOption = CheckOption(
              message = ResourcesUtil.getString(
                com.ricepo.style.R.string.reward_limit_alert,
                restaurantName ?: ""
              ),
              food = food,
              restaurant = restaurantInfo ?: Restaurant(),
              isAlert = true,
              pageStatus = mGroupStatus
            )
            menuCheckOption.postValue(checkOption)
            return
          }

          // check cart and owner point is not enough if need
          if (menuUseCase.checkCardPoint(menuRestaurantCart, restaurantInfo, food) != null) {
            val checkOption = CheckOption(
              message = ResourcesUtil.getString(com.ricepo.style.R.string.balance_alert),
              food = food,
              restaurant = restaurantInfo ?: Restaurant(),
              isAlert = true,
              pageStatus = mGroupStatus
            )
            menuCheckOption.postValue(checkOption)
            return
          }
        }

        // check the limit of food
        food?.limit?.let {
          if (food?.reward != true && (food.selectedCount ?: 0) >= it) {
            val checkOption = CheckOption(
              message = ResourcesUtil.getString(
                com.ricepo.style.R.string.alert_food_limit,
                food.name.localize(), it
              ),
              food = food,
              restaurant = restaurantInfo ?: Restaurant(),
              isAlert = true,
              pageStatus = mGroupStatus
            )
            menuCheckOption.postValue(checkOption)
            return
          }
        }

        // check the total section if over the limit of category
        food?.category?.maxItem?.let {
          val count = menuUseCase.getCategoryCount(
            food.category?.id,
            menuRestaurantCart, restaurantInfo
          )
          if (count >= it) {
            val message = if (it > 1) {
              ResourcesUtil.getString(com.ricepo.style.R.string.alert_category_limits, it)
            } else {
              ResourcesUtil.getString(com.ricepo.style.R.string.alert_category_limit, it)
            }
            val checkOption = CheckOption(
              message = message,
              food = food,
              restaurant = restaurantInfo ?: Restaurant(),
              isAlert = true,
              pageStatus = mGroupStatus
            )
            menuCheckOption.postValue(checkOption)
            return
          }
        }

        if (food?.options?.isNotEmpty() == true && isAdded) {
          // set default food options selected
          val cartList = menuRestaurantCart?.cartList
          // clear the options item count and selected
          food.options?.forEach { option ->
            option.items?.forEach { item ->
              item.count = null
            }
            option.selected = null
          }
          // filter the owner food otherwise group member
          cartList?.filter {
            it.ownerId == null || (it.ownerId == BaseApplication.mDeviceId)
          }?.lastOrNull {
            it.id == food.id && (
              it.bundleRestId == null ||
                it.bundleRestId == restaurantInfo?.id
              )
          }?.let {
            menuUseCase.setFoodOptionsSelected(it, food)
          }
          // intent to menu options page
          val checkOption = CheckOption(
            FeaturePageConst.PAGE_MENU, food, restaurantInfo ?: Restaurant(),
            pageStatus = mGroupStatus
          )
          menuCheckOption.postValue(checkOption)
        } else {
          addFoodInner(food)
        }
      }
    } else {
      val food = food ?: return
      val checkOption = CheckOption(
        "", food,
        restaurantInfo ?: Restaurant(),
        pageStatus = mGroupStatus
      )
      menuCheckOption.postValue(checkOption)
    }
  }

  /**
   * add the [food] for observer
   * food and options to cart
   */
  private fun addFoodInner(food: Food?) {
    if (food == null) return
    isTableRefresh = false

    val count = food.selectedCount ?: 0
    food.selectedCount = count + 1

    // refresh menu section
    val checkOption = CheckOption(
      CheckOption.MESSAGE_REFRESH, food,
      restaurantInfo ?: Restaurant(), pageStatus = mGroupStatus,
      selectedPosition = sectionPosition, galleryFoodIndex = galleryFoodIndex
    )
    menuCheckOption.postValue(checkOption)
    galleryFoodIndex = null

    // update restaurant cart
    val foods = menuCartObserver.value?.carts?.toMutableList() ?: mutableListOf()

    val restName = restaurantMenu?.groups?.firstOrNull {
      it.id == food.restaurant?.id
    }?.name ?: restaurantInfo?.name
    foods.add(menuMapper.mapCart(food, restName))

    // get the recommend menu
    getMenuRecommend(foods)

    menuCartObserver.update(CartData(true, foods))

    // update restaurant cart
    updateRestaurantCart(foods, restaurantCartCache?.comments)

    // firebase event add menu item
    AnalyticsFacade.logEvent(food, FirebaseEventName.rAddItem)
    // log add food
    LifecycleNetworkListener.logAddFood(food)
  }

  /**
   * minus the [food] for observer
   * remove from cart
   */
  fun minusFood(food: Food, position: Int, foodIndex: Int?) {
    sectionPosition = position
    galleryFoodIndex = foodIndex
    if (food == null || food.selectedCount == 0) return
    isTableRefresh = false
    // added count
    val count = food.selectedCount ?: 0
    food.selectedCount = count - 1

    // refresh menu section
    val checkOption = CheckOption(
      CheckOption.MESSAGE_REFRESH, food,
      restaurantInfo ?: Restaurant(), pageStatus = mGroupStatus,
      selectedPosition = sectionPosition, galleryFoodIndex = galleryFoodIndex
    )
    menuCheckOption.postValue(checkOption)
    galleryFoodIndex = null

    // update restaurant cart
    var foods = menuCartObserver.value?.carts?.toMutableList()
    val index = foods?.indexOfLast { it.id == food.id } ?: -1
    if (index > -1) {
      foods?.removeAt(index)
    }

    // remove the recommend menu
    if (foods.isNullOrEmpty()) {
      sendRecommendEmpty()
    }

    menuCartObserver.update(CartData(true, foods?.toList()))

    // update restaurant cart
    updateRestaurantCart(foods, restaurantCartCache?.comments)

    // firebase event remove menu item
    AnalyticsFacade.logEvent(food, FirebaseEventName.rRemoveItem)
  }

  private fun getMenuRecommend(foods: List<Cart>?) {
    val foodIds = foods?.map {
      it.id
    }
    viewModelScope.launch {
      val restaurantId = restaurantInfo?.id
      if (foodIds.isNullOrEmpty() || restaurantId == null) {
        sendRecommendEmpty()
      } else {
        try {
          val recommends = menuUseCase.getMenuRecommend(restaurantId, foodIds, bundles)
          menuRecommendChannel.emit(recommends)
        } catch (e: Exception) {
          e.printStackTrace()
//                    sendRecommendEmpty()
        }
      }
    }
  }

  private fun sendRecommendEmpty() {
    viewModelScope.launch {
      menuRecommendChannel.emit(listOf())
    }
  }

  private fun updateRestaurantCart(carts: List<Cart>?, comments: String?) {
    GlobalScope.launch {
      val restaurantCart = RestaurantCart(carts, restaurantInfo, comments)
      RestaurantCartCache.saveRestaurantCart(restaurantCart)
    }
  }

  fun deleteRestaurantCart(checkOption: CheckOption?) {
    viewModelScope.launch {
      val p = RestaurantCartCache.deleteRestaurantCart(checkOption?.restaurant)
      if (p > 0 && checkOption != null) {
        // clear restaurant cart cache
        restaurantCartCache = RestaurantCart(
          restaurant = checkOption.restaurant,
          cartList = listOf()
        )
        addFood(checkOption.food, -1, null)
      }
    }
  }

  fun checkToCheckout(context: ComponentActivity, entrance: String?) {
    viewModelScope.launch {
      // only show a alert only for the owner
      val restaurantCart = withContext(Dispatchers.IO) {
        RestaurantCartCache.getRestaurantCartSuspend(restaurantInfo)
      }
      val restaurantId = restaurantInfo?.id ?: restaurantCart?.restaurant?.id
      if (restaurantId != null) {
        val food = withContext(Dispatchers.IO) {
          val cartList = menuRestaurantCart?.cartList
          menuUseCase.checkNeedSelectPromotionFood(promotionFoods, cartList)
        }
        if (GroupOrderCache.isOwner(restaurantId) && food != null) {
          val msg = ResourcesUtil.getString(com.ricepo.style.R.string.menu_promotion_add, food.name.localize())
          // from entrance coupon to menu back
          val entrance = if (entrance == FeaturePageConst.PAGE_COUPON) {
            entrance
          } else null
          DialogFacade.showPrompt(
            context, message = msg,
            negative = {
              FeaturePageRouter.navigateCheckoutByEntrance(
                context,
                restaurantInfo, entrance, deliveryMode
              )
            },
            positive = {
              viewModelScope.launch {
                val model = uiModels?.find { it.food?.id == food.id }
                val position = model?.itemPosition ?: 0
                val foodIndex = model?.foodIndex
                addFood(food, position, foodIndex)
                FeaturePageRouter.navigateCheckoutByEntrance(
                  context,
                  restaurantInfo,
                  entrance,
                  deliveryMode
                )
              }
            }
          )
        } else {
          FeaturePageRouter.navigateCheckoutByEntrance(
            context,
            restaurantInfo, entrance, deliveryMode
          )
        }
      }
    }
  }

  fun checkAllClosed(restaurants: List<Restaurant>?): Boolean {
    return menuUseCase.checkAllClosed(restaurants)
  }

  override fun onCleared() {
    super.onCleared()
    restaurantInfo = null
    menuRestaurantCart = null
    restaurantCartCache = null
    foodWithCategory = null
  }
}
