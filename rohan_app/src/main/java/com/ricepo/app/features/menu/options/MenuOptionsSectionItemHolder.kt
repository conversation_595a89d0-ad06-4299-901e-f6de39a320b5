package com.ricepo.app.features.menu.options

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuOptionsSectionItemBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.adapter.listener.OnOptionsChocolateListener
import com.ricepo.app.features.menu.adapter.listener.QuantityDecorator
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.restaurant.data.HotImage
import com.ricepo.base.model.Food
import com.ricepo.base.model.Item
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.span.CenteredImageSpan
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by <PERSON><PERSON> on 05/07/2021.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class MenuOptionsSectionItemHolder(
  private val binding: MenuOptionsSectionItemBinding,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty
) :
  SectionItemHolder(binding.root) {

  val mapper = MenuMapper()

  /**
   * [position] the ui model list index
   */
  fun bind(model: MenuUiModel.MenuOptionsItem, position: Int, size: Int) {
    val item = model.item ?: return
    val food = model.food ?: return
    val option = model.option

    var params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {
      var width = params.width
      var height = params.height
      var leftMargin = 0
      var rightMargin = 0

      params.width = width
      params.height = height
      params.leftMargin = leftMargin
      params.rightMargin = rightMargin
      binding.root.layoutParams = params
    }

    // top divider visible
    binding.dividerFood.isVisible = !(position == 0)

    val isSingle = (option?.max == 1)
    // || (option?.min == 0 || option?.min == 1)
    if (isSingle) {
      binding.btnFoodOption.maxCount = 1
    } else {
      binding.btnFoodOption.maxCount = 0
    }

    bindView(food, item, null, position)
    bindQtyView(item)

    // the section position in the recyclerview
    val sectionPosition = model.itemPosition ?: -1

    // qty operation listener
    OnOptionsChocolateListener(binding, food, option, item, sectionPosition, addFood, minusFood)
  }

  fun bindQtyView(item: Item) {
    val selectedCount = item.count ?: 0
    binding.btnFoodOption.isPlus = item?.isPlus
    binding.btnFoodOption.isMinus = item?.isMinus
    QuantityDecorator.bindOptionsQtyChocolate(binding, selectedCount)
    item?.isPlus = null
    item?.isMinus = null
  }

  private fun bindView(food: Food, item: Item, restaurant: Restaurant?, position: Int) {

    // set food image
    setFoodImage(item, restaurant, binding.ivFood)

    binding.tvFoodName.text = bindName(item)

    binding.tvFoodInfo.isVisible = false

    val isReward = (food.reward == true)
    binding.ivRewardIcon.isVisible = isReward
    if (isReward) {
      setFoodReward(item)
    } else {
      setFoodPrice(item)
    }

    if (item.available == false) {
      binding.root.isEnabled = false
      binding.root.alpha = 0.2f
      binding.btnFoodOption.isEnabled = false
      // recyclerview animations handle the itemView alpha animation
      // making it not work when show
    } else {
      binding.root.isEnabled = true
      binding.root.alpha = 1f
      binding.btnFoodOption.isEnabled = true
    }
  }

  private fun bindName(item: Item): SpannableStringBuilder {
    return if (item.featured == true) {
      val hotImage = ResourcesUtil.getDrawable(
        HotImage().localize(),
        binding.root.context
      ) ?: return SpannableStringBuilder().append(item.name.localize())
      hotImage.setBounds(
        0, 0, ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_36dp),
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_26dp)
      )
      val imageSpan = CenteredImageSpan(hotImage)
      val spanText = SpannableStringBuilder(" ")
      spanText.append(item.name.localize())
      spanText.setSpan(imageSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
      spanText
    } else {
      SpannableStringBuilder().append(item.name.localize())
    }
  }

  private fun setFoodReward(item: Item) {
    binding.tvFoodPrice.text = "${mapper.calcCoinCount(0)}"
  }

  private fun setFoodPrice(item: Item) {
    binding.tvFoodPrice.text = mapper.formatPrice(item.price ?: 0, "")
    binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))

    binding.tvFoodOriginalPrice.isVisible = false
    binding.tvFoodOriginalPrice.text = null
  }

  private fun setFoodImage(
    food: Item?,
    item: Restaurant?,
    menuView: ImageView,
  ) {
    menuView.setImageResource(com.ricepo.style.R.drawable.ic_option_placeholder)
  }

  companion object {
    fun create(
      parent: ViewGroup,
      addFood: FunFoodQty,
      minusFood: FunFoodQty
    ): MenuOptionsSectionItemHolder {
      val binding = MenuOptionsSectionItemBinding.inflate(LayoutInflater.from(parent.context))
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      return MenuOptionsSectionItemHolder(binding, addFood, minusFood)
    }
  }
}
