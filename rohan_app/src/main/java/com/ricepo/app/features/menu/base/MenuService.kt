package com.ricepo.app.features.menu.base

import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.restaurant.RestaurantMapper
import com.ricepo.base.model.Restaurant
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import kotlin.coroutines.EmptyCoroutineContext

//
// Created by <PERSON><PERSON> on 23/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuService : Service() {

  // binder given to clients
  private val binder = LocalBinder()

  private lateinit var restaurantMapper: RestaurantMapper

  private val serviceScope = CoroutineScope(EmptyCoroutineContext)

  /**
   * class used for the client Binder
   */
  inner class LocalBinder : Binder() {
    // return this instance of MenuService so clients can call public methods
    fun getService(): MenuService = this@MenuService
  }

  override fun onBind(intent: Intent?): IBinder? {
    restaurantMapper = RestaurantMapper()
    return binder
  }

  override fun onRebind(intent: Intent?) {
    super.onRebind(intent)
  }

  override fun onUnbind(intent: Intent?): Boolean {
    return super.onUnbind(intent)
  }

  override fun onDestroy() {
    super.onDestroy()
    stopRestaurantTimer()
    stopGroupCartTimer()
    serviceScope.cancel()
  }

  private var refreshRestaurantDisposable: Disposable? = null

  /**
   * restaurant will reload with by restaurant id
   */
  private var restaurant: Restaurant? = null

  fun refreshRestaurantTimer(restaurant: Restaurant?) {
    this.restaurant = restaurant
    if (refreshRestaurantDisposable != null) return
    refreshRestaurantDisposable = Observable.interval(10, TimeUnit.SECONDS)
      .flatMap {
        Observable.just(refreshRestaurant())
      }
      .subscribeOn(Schedulers.io())
      .observeOn(AndroidSchedulers.mainThread())
      .subscribe { pair ->
        // send broadcast to refresh
        val isRefresh = pair.first
        val isClosed = pair.second
        if (isRefresh) {
          val intent = Intent()
          intent.action = BroadcastConst.ACTION_REFRESH_RESTAURANT_CLOSED
          intent.putExtra(BroadcastConst.PARAM_RESTAURANT_CLOSED, isClosed)
          sendBroadcast(intent)
        }
      }
  }

  private fun stopRestaurantTimer() {
    refreshRestaurantDisposable?.dispose()
    refreshRestaurantDisposable = null
  }

  /**
   * update if restaurant close changed
   */
  private fun refreshRestaurant(): Pair<Boolean, Boolean> {
    val restaurant = restaurant ?: return Pair(false, false)
    // return if restaurant no data
    restaurant.name ?: return Pair(false, false)

    // get closed value from selected restaurant
    val closedString = restaurantMapper.mapClosed(restaurant)

    val isClosed = closedString != null
    val isRestaurantClosed = restaurant.isClosed != null

    // if changed refresh restaurant
    val isRefresh = (isClosed != isRestaurantClosed)

    if (isRefresh) {
      // update the variable closed
      this.restaurant?.isClosed = closedString
      // update the local restaurant closed
      restaurant.isClosed = closedString
      val restCart = RestaurantCartCache.getRestaurantCart(restaurant)
      // update restaurant cache if restaurant is same and closing changed
      if (restCart != null && restCart.restaurant?.id == restaurant.id) {
        restCart.restaurant = restaurant
        serviceScope.launch {
          RestaurantCartCache.saveRestaurantCart(restCart)
        }
      }
    }

    return Pair(isRefresh, isClosed)
  }

  private var refreshGroupOrderDisposable: Disposable? = null

  /**
   * interval timer for group order refreshing
   */
  fun startGroupCartTimer() {
    if (refreshGroupOrderDisposable != null) return
    serviceScope.launch {
      val isGroupExist = withContext(Dispatchers.IO) {
        GroupOrderCache.isGroupExist()
      }
      // refresh group cart is group exists
      if (isGroupExist) {
        refreshGroupOrderDisposable = Observable.interval(
          5, TimeUnit.SECONDS,
          AndroidSchedulers.mainThread()
        )
          .subscribe {
            refreshGroupCart()
          }
      }
    }
  }

  private fun refreshGroupCart() {
    val intent = Intent()
    intent.action = BroadcastConst.ACTION_REFRESH_GROUP_ORDER
    sendBroadcast(intent)
  }

  fun stopGroupCartTimer() {
    refreshGroupOrderDisposable?.dispose()
    refreshGroupOrderDisposable = null
  }
}
