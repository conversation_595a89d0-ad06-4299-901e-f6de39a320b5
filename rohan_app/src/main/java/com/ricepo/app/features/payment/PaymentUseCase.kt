package com.ricepo.app.features.payment

import android.content.Context
import androidx.activity.ComponentActivity
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.data.kv.OrderCache
import com.ricepo.app.features.payment.data.ShowPaymentCase
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.Card
import com.ricepo.app.model.Order
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.pattern.payment.PaymentGoogle
import com.ricepo.app.pattern.payment.PaymentRefer
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restaurant.RestaurantRemote
import com.ricepo.base.BaseUseCase
import com.ricepo.base.animation.Loading
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.NetworkError
import com.ricepo.style.ResourcesUtil
import com.ricepo.tripartite.stripe.StripeClient
import com.ricepo.tripartite.wechat.WeChatClient
import com.stripe.android.model.PaymentMethodCreateParams
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import javax.inject.Inject

//
// Created by Thomsen on 8/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class PaymentUseCase @Inject constructor(
  private val repository: RestaurantRemote,
  private val combineRepository: CombineRestApi,
  private val postExecutionThread: PostExecutionThread
) : BaseUseCase() {

  fun getCards(showPaymentCase: ShowPaymentCase): Observable<List<Card>> {
    return Observable.create<List<Card>> { emitter ->
      val customerId = CustomerCache.getCustomer()?.id ?: ""

      // get the customer cards
      val type = when (showPaymentCase) {
        ShowPaymentCase.all -> arrayOf("thirdParty", "card")
        ShowPaymentCase.editCard -> arrayOf("card")
        ShowPaymentCase.onlyStripe -> arrayOf("subscription")
      }
      val single = repository.getCards(customerId, type)
      addDisposable(
        single.subscribeWith(object : DisposableSingleObserver<List<Card>>() {
          override fun onSuccess(t: List<Card>) {
            if (t != null) {
              emitter.onNext(t)
            } else {
              emitter.onNext(listOf())
            }
          }

          override fun onError(e: Throwable) {
            emitter.onNext(listOf())
          }
        })
      )
    }.subscribeOn(postExecutionThread.ioScheduler)
  }

  /**
   * get default payment method
   * return pair<headerPaymentMethod, bottomPaymentMethod>
   */
  fun getDefaultPaymentMethod(): Pair<List<Card>, List<Card>> {

    var headerCards = mutableListOf<Card>()
    var bottomCards = mutableListOf<Card>()

    // relation with strings
    headerCards.add(Card(PaymentOwnMethod.ALIPAY))
    headerCards.add(Card(PaymentOwnMethod.WECHAT_PAY))

    bottomCards.add(Card(PaymentOwnMethod.ADD_NEW_CARD))

    return Pair(headerCards, bottomCards)
  }

  fun getOnlyStripePaymentMethod(): List<Card> {
    var cards = mutableListOf<Card>()

    // relation with strings
    cards.add(Card(PaymentOwnMethod.ADD_NEW_CARD))

    return cards
  }

  /**
   * return payment own method
   */
  fun createStripePaymentMethod(params: PaymentMethodCreateParams):
    Observable<Result<PaymentOwnMethod>> {
    return Observable.create { emitter ->
      StripeClient.instance.createPaymentMethod(params) {
        val result = it
        result.fold(
          onSuccess = { data ->
            val brand = data.card?.brand?.name ?: ""
            if (!brand.isNullOrEmpty()) {
              val ownMethod = PaymentOwnMethod(
                method = PaymentOwnMethod.CREDIT,
                brand = brand,
                last4 = data.card?.last4,
                stripeId = data.id
              )
              emitter.onNext(Result.success(ownMethod))
            }
          },
          onFailure = { error ->
            emitter.onNext(Result.failure(error))
          }
        )
      }
    }
  }

  /**
   * return payment card
   */
  fun createStripePaymentCard(params: PaymentMethodCreateParams):
    Observable<Result<Card>> {
    return Observable.create { emitter ->
      StripeClient.instance.createPaymentMethod(params) {
        val result = it
        result.fold(
          onSuccess = { data ->
            val brand = data.card?.brand?.name ?: ""
            if (!brand.isNullOrEmpty()) {
              val ownMethod = Card(
                method = PaymentOwnMethod.CREDIT,
                brand = brand,
                last4 = data.card?.last4,
                id = data.id
              )
              emitter.onNext(Result.success(ownMethod))
            }
          },
          onFailure = { error ->
            emitter.onNext(Result.failure(error.parseByBuzNetwork()))
          }
        )
      }
    }
  }

  /**
   * start the payment process using different method
   */
  fun handlePayment(
    context: Context,
    paymentMethod: PaymentOwnMethod?,
    paymentObj: PaymentObj,
    showLoading: Boolean = true,
    completed: (String?, String?) -> Unit,
  ) {
    val source = paymentMethod?.method ?: ""
    PaymentRefer.paymentHandler(source)?.handlePayment(context, paymentObj) { errMsg, source, html ->
      if (source == PaymentOwnMethod.CREDIT) {
        if (errMsg == null && paymentMethod != null) {
          completed(null, null)
          // update local payment method cache
          CustomerCache.savePayment(paymentMethod) {}
        } else if (errMsg != null) {
          completed(errMsg, null)
        }
      } else if (source == PaymentOwnMethod.BBVA_PAY) {
        completed(errMsg, html)
      } else {
        completed(errMsg, null)
      }
    }
  }

  /**
   * check the payment software environment
   */
  fun checkoutPaymentSoftware(context: Context, payment: Card?): Observable<String> {
    return Observable.create { cont ->
      if (payment?.method == PaymentOwnMethod.WECHAT_PAY ||
        payment?.method == PaymentOwnMethod.WECHAT_PAY_PREVIOUSLY
      ) {
        // check the app installed if wechat pay
        val r = if (WeChatClient.isAppInstall(context)) {
          ""
        } else { ResourcesUtil.getString(com.ricepo.style.R.string.error_wechat_notInstalled) }
        cont.onNext(r)
      } else if (payment?.method == PaymentOwnMethod.GOOGLE_PAY) {
        context as ComponentActivity
        Loading.showLoading(context)
        PaymentGoogle.isReadyToPay(context) {
          cont.onNext(it ?: "")
          Loading.hideLoading()
        }
      } else {
        cont.onNext("")
      }
    }
  }

  /**
   * create payent from order
   */
  fun createPayment(paymentOwnMethod: PaymentOwnMethod?, order: Order?):
    Observable<Result<Pair<PaymentOwnMethod?, PaymentObj>>> {

    // recharge order id for delivery tip
    val orderId = order?.rechargeOrder?.id ?: order?.id
      ?: return Observable.just(Result.failure(Exception("create payment error")))

    return Observable.create { emitter ->
      val single: Single<PaymentObj>? = when (paymentOwnMethod?.method) {
        PaymentOwnMethod.CREDIT_PREVIOUSLY,
        PaymentOwnMethod.CREDIT -> {
          repository.createIntentPayment(
            orderId, paymentOwnMethod.stripeId ?: ""
          )
        }
        PaymentOwnMethod.WECHAT_PAY_PREVIOUSLY,
        PaymentOwnMethod.WECHAT_PAY -> {
          GlobalScope.launch {
            // cache order for weixin pay entry
            OrderCache.saveOrder(order)
          }
          repository.createWechatPayment(orderId)
        }
        PaymentOwnMethod.ALIPAY -> {
          repository.createAlipayPayment(orderId)
        }
        PaymentOwnMethod.GOOGLE_PAY -> {
          Single.just(PaymentObj(order = order))
        }
        PaymentOwnMethod.UNION_PAY -> {
          repository.createUnionPayment(orderId)
        }
        PaymentOwnMethod.PAYPAL_PAY -> {
          repository.createPaypalPayment(orderId)
        }
        PaymentOwnMethod.BBVA_PAY -> {
          repository.createBBVAPayment(orderId, paymentOwnMethod.stripeId)
        }
        else -> null
      }
      if (single != null) {
        addDisposable(
          single.subscribeWith(object : DisposableSingleObserver<PaymentObj>() {
            override fun onSuccess(t: PaymentObj) {
              if (t != null) {
                with(emitter) { onNext(Result.success(Pair(paymentOwnMethod, t.copy(orderID = orderId)))) }
              }
            }

            override fun onError(e: Throwable) {
              if (e != null) {
                emitter.onNext(Result.failure(e))
              }
            }
          })
        )
      } else {
        emitter.onNext(Result.failure(Exception("create payment error")))
      }
    }
  }

  /**
   * request delete payment card
   */
  fun deletePaymentCard(cardId: String, method: String?): Observable<Result<Any>> {
    return Observable.create<Result<Any>> { emitter ->
      val customer = CustomerCache.getCustomer()
      val customerId = customer?.id
      if (customerId != null) {

        val single = if (PaymentOwnMethod.BBVA_PAY.equals(method)) {
          repository.deleteBbvaCard(customerId, cardId)
        } else {
          repository.deleteCard(customerId, cardId)
        }
        addDisposable(
          single.subscribeWith(object : DisposableSingleObserver<retrofit2.Response<Unit>>() {
            override fun onSuccess(t: retrofit2.Response<Unit>) {
              if (t != null) {
                emitter.onNext(Result.success(true))
              }
            }

            override fun onError(e: Throwable) {
              if (e is NetworkError && e.code == ErrorCode.RESPONSE_BODY_EMPTY) {
                emitter.onNext(Result.success(true))
              } else if (e != null) {
                emitter.onNext(Result.failure(e))
              }
            }
          })
        )
      }
    }.subscribeOn(postExecutionThread.ioScheduler)
  }

  suspend fun addPaymentCard(paymentMethodId: String): PaymentObj {
    val body = mapOf("payment_method_id" to paymentMethodId)
    return combineRepository.createPaymentCard(body)
  }
}
