package com.ricepo.app.features.order

import android.text.TextUtils
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderItem
import com.ricepo.base.model.Cart
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.LatLon
import com.ricepo.base.model.mapper.BaseMapper
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 22/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OrderMapper @Inject constructor() : BaseMapper() {

  /**
   * group food items and get qty of each food
   */
  fun groupOrderItems(its: List<OrderItem>?): List<OrderItem> {
    var items = its ?: return listOf()

    // need to match id, opt ids and name
    val groupItems = items.groupBy {
      getFoodItemIdentifier(it)
    }

    // get each group qty and assign qty to first matched order item
    groupItems?.forEach { entry ->
      val (key, value) = entry
      // find first matched fooditems
      val index = items.indexOfFirst {
        val identifier = getFoodItemIdentifier(it)
        key == identifier
      }

      // update qty for the first matched food item
      if (index != null && index > -1) {
        its[index].qty = value.count()
      }
    }

    // filter items without qty
    items = items.filter { (it.qty ?: 0) > 0 }

    return items
  }

  /**
   * use food.id, options ids, food cnName and food enName as identifier of food items
   */
  private fun getFoodItemIdentifier(item: OrderItem): String {
    val options = item.options ?: listOf()

    val optionIdStr = options.fold("") { result, option ->
      result + option.id
    }

    val cnName = item.name?.zhCN ?: ""
    val enName = item.name?.enUS ?: ""

    val id = item?.id ?: ""
    // combine the bag fee
    val price = "${item.price ?: ""}"

    return id + optionIdStr + cnName + enName + price
  }

  fun getDeliveryFinishAt(order: Order): String? {
    return formatTime(order.delivery?.finishAt, "HH:mm")
  }

  fun getRestaurantPreparedAt(order: Order): String? {
    return formatTime(order.delivery?.preparedAt, "HH:mm")
  }

  /**
   * return order address
   */
  fun getOrderAddress(order: Order): String {
    var address = ""
    val addressObj = order.delivery?.address?.innerItemValue
    if (addressObj != null) {
      if (addressObj.number != null) {
        address = if (addressObj.number.isNullOrEmpty() && addressObj.street.isNullOrEmpty()) {
          "${addressObj.city ?: ""}"
        } else {
          "${addressObj.number ?: ""} ${addressObj.street ?: ""}"
        }
      } else {
        address = addressObj.formatted?.split(delimiters = *arrayOf(","))?.get(0) ?: ""
      }
      val unit = addressObj.unit
      if (unit != null) {
        address += " $unit"
      }
    }
    return address
  }

  fun getOrderAddressFormatted(order: Order): String {
    var address = ""
    val addressObj = order.delivery?.address?.innerItemValue
    if (addressObj != null) {
      address = addressObj.formatted
      val unit = addressObj.unit
      if (!TextUtils.isEmpty(unit)) {
        address += " #$unit"
      }
    }
    return address
  }

  /**
   * map cart list from order item
   */
  fun mapCart(item: OrderItem, maxItem: Int? = null, categoryId: String? = null): List<Cart> {
    // get item quantity
    val qty = item?.qty ?: 0

    val cartList = mutableListOf<Cart>()

    for (i in 0 until qty) {
      val cart = Cart(
        id = item.id ?: "",
        price = item.price ?: 0,
        originalPrice = item.originalPrice,
        point = item.point,
        reward = item.reward,
        name = item.name ?: InternationalizationContent("", "", "", ""),
        opt = item.options ?: listOf(),
        qty = null,
        createdAt = System.currentTimeMillis().toDouble(),
        categoryMaxItem = maxItem,
        categoryId = categoryId,
        bundleRestId = item.restaurant?.id,
        minimum = item.condition?.minimum,
        foodImage = item.image,
      )
      cartList.add(cart)
    }

    return cartList
  }

  /**
   * return restaurant & customer location coordinate by [order]
   */
  fun mapCoordinate(order: Order): Pair<LatLon?, LatLon?> {
    var restaurantCoordinate: LatLon? = null
    var customerCoordinate: LatLon? = null

    val restaurantLocation = order?.restaurant?.address?.innerItemValue?.location?.coordinates
    if (restaurantLocation != null) {
      restaurantCoordinate = LatLon(
        lat = restaurantLocation.getOrNull(1) ?: 0.0,
        lon = restaurantLocation.getOrNull(0) ?: 0.0
      )
    }

    val customerLocation = order?.delivery?.address?.innerItemValue?.location?.coordinates
    if (customerLocation != null) {
      customerCoordinate = LatLon(
        lat = customerLocation.getOrNull(1) ?: 0.0,
        lon = customerLocation.getOrNull(0) ?: 0.0
      )
    }

    return Pair(restaurantCoordinate, customerCoordinate)
  }
}
