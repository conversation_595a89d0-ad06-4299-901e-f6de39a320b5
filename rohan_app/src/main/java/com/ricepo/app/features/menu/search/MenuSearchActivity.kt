package com.ricepo.app.features.menu.search

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.SpannableStringBuilder
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityMenuSearchBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.MenuGroupViewModel
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.MenuViewModel
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.base.MenuBaseActivity
import com.ricepo.app.features.menu.cart.CartAdapter
import com.ricepo.app.features.menu.cart.CartUiModel
import com.ricepo.app.features.menu.data.MenuGroupStatus
import com.ricepo.app.features.menu.data.MenuPageData
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.base.animation.Loading
import com.ricepo.base.consts.TabMode
import com.ricepo.base.extension.bindLoading
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.searchAction
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.rv.ScrollStatePersist
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.kotlin.addTo
import io.reactivex.rxjava3.observers.DisposableObserver
import io.reactivex.rxjava3.subjects.BehaviorSubject
import io.reactivex.rxjava3.subjects.PublishSubject
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

//
// Created by Thomsen on 9/7/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_MENU_SEARCH)
class MenuSearchActivity : MenuBaseActivity() {

  val menuViewModel: MenuViewModel by viewModels()

  val menuGroupViewModel: MenuGroupViewModel by viewModels()

  lateinit var binding: ActivityMenuSearchBinding

  private var menuAdapter: MenuSectionAdapter? = null

  private var cartAdapter: CartAdapter? = null

  private lateinit var mapper: MenuMapper

  private lateinit var menuSectionPersist: ScrollStatePersist

  lateinit var viewModelOutput: MenuViewModel.Output
  lateinit var viewModelInput: MenuViewModel.Input

  private val compositeDisposable = CompositeDisposable()

  lateinit var aRefreshMenu: PublishSubject<Boolean>

  lateinit var aSearchBarEditing: BehaviorSubject<String>

  lateinit var aGroupCreating: PublishSubject<Boolean>

  private var menuPageData: MenuPageData? = null

  private var isAlreadyCheckMenuStatus = false

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityMenuSearchBinding.inflate(layoutInflater)
    setContentView(binding.root)

    setTitleSubText(ResourcesUtil.getString(com.ricepo.style.R.string.search_menu))

    mapper = MenuMapper()
    menuSectionPersist = ScrollStatePersist(savedInstanceState)

    menuRestaurant = intent.getParcelableExtra(
      FeaturePageConst.PARAM_MENU_RESTAURANT
    )

    menuViewModel.promotionFoods = intent.getParcelableArrayListExtra<Food>(
      FeaturePageConst.PARAM_MENU_PROMO_FOODS
    )
    menuViewModel.bundles = intent.getStringArrayListExtra(
      FeaturePageConst.PARAM_MENU_SEARCH_BUNDLES
    )
//        val maxItem = intent.getSerializableExtra(
//            FeaturePageConst.PARAM_MENU_CATEGORY_MAXITEM)
//        if (maxItem is HashMap<*, *>) {
//            menuViewModel.mapCategoryMaxItem = maxItem as HashMap<String, Int?>
//        }

    menuViewModel.restaurantInfo = menuRestaurant
    menuViewModel.initMenuCart(menuRestaurant)

    bindPool(menuRestaurant?.pool)

//        changeMenuStatus(false)

    bindEvent(restaurant = menuRestaurant)
    initMenuRecommendCollect(binding.inMenuCart, menuViewModel)

    setupListener()
  }

  override fun onSaveInstanceState(outState: Bundle) {
    super.onSaveInstanceState(outState)
    menuSectionPersist.onSaveInstanceState(outState)
  }

  override fun onDestroy() {
    super.onDestroy()
    menuSectionPersist.clearScrollState()
  }

  private fun bindEvent(restaurant: Restaurant?) {
    // init restaurant search keyword
    bindViewModelInput(restaurant)
    bindViewModelOutput()
    Loading.hideLoading()
    aRefreshMenu.onNext(false)
  }

  private fun bindViewModelInput(restaurant: Restaurant?) {
//        aRefreshMenu = BehaviorSubject.createDefault(false)
    aRefreshMenu = PublishSubject.create()
    aSearchBarEditing = BehaviorSubject.createDefault("")
    aGroupCreating = PublishSubject.create()

    viewModelInput = MenuViewModel.Input(
      restaurant, aRefreshMenu,
      aSearchBarEditing
    )
    viewModelOutput = menuViewModel.transform(viewModelInput, true)
  }

  private fun bindViewModelOutput() {
    viewModelOutput.menuTableUpdated
      .bindLoading(this)
      .subscribeWith(object : DisposableObserver<MenuPageData>() {
        override fun onComplete() {
        }

        override fun onNext(pageData: MenuPageData) {
          // back check the restaurant by id
          if (menuViewModel.restaurantInfo?.name == null) {
            onBackPressed()
          }
          // re init menu restaurant
          menuRestaurant = menuViewModel.restaurantInfo
          checkRestaurantClosed()

          if (pageData.uiModels.isNullOrEmpty()) {
            showNotFoundView()
          } else {
            showContentView(pageData)
          }

          menuPageData = pageData

          if (!isAlreadyCheckMenuStatus) {
            // set the menu title bar tag
            changeMenuStatus(true)
//                        isAlreadyCheckMenuStatus = true
          }
        }

        override fun onError(e: Throwable) {
          // set the menu title bar when request error
          changeMenuStatus(false)
          showErrorNetworkView(e?.localizedMessage)

          // back check the restaurant by id
          if (menuViewModel.restaurantInfo?.name == null) {
            onBackPressed()
          }
        }
      }).addTo(compositeDisposable)

//        viewModelOutput.menuTableNoFood
//            .doOnNext { Loading.hideLoading() }
//            .subscribe {
//                if (it.sections?.filter { it != null }?.isNotEmpty() == false) {
// //                    showNotFoundView()
//                    changeMenuStatus(false)
//                }
//            }

    viewModelOutput.menuCheckOption
      .observe(
        this,
        Observer {
          val checkOption = it
          checkAddCartOption(checkOption)
        }
      )

    menuViewModel.observeError
      .subscribeOn(AndroidSchedulers.mainThread())
      .subscribe {
        showErrorNetworkView(it.message)
      }

    observeCartUpdated()
  }

  private fun showContentView(menuPageData: MenuPageData) {
    val position = menuPageData.sectionPosition
    val uiModels = menuPageData.uiModels.map {
      if (it is MenuUiModel.MenuVerticalSection) {
        it.isMarginTop = false
      }
      it
    }
    val itemCount = menuPageData.itemCount
    val isTableRefresh = menuPageData.isTableRefresh
    if (isTableRefresh) {
      // group refresh menu qty
//            val startPos = 0
//            val allCount = menuAdapter?.itemCount ?: 0
      menuPageData.uiModels?.mapIndexed { index, model ->
        val food = model.food
        if (food?.selectedCount != null || food == null) {
          // food is null by horizontal and gallery
          menuAdapter?.notifyItemChanged(index, food)
        } else if (model.category?.items?.filter { it.id == food?.id }?.isNotEmpty() == true) {

          if (model is MenuUiModel.MenuHorizontalSection) {
            model.models.filter { it.food?.id == food?.id }?.forEach {
              it.food?.selectedCount = food?.selectedCount
            }
          } else if (model is MenuUiModel.MenuVerticalSection) {
            model.models.filter { it.food?.id == food?.id }?.forEach {
              it.food?.selectedCount = food?.selectedCount
            }
          }
          model.category?.items?.filter { it.selectedCount != null }?.let {
            it.mapIndexed { _, galleryFood ->
              menuAdapter?.notifyItemChanged(index, galleryFood)
            }
          }
        } else {
          // pass
        }
      }
//            menuAdapter?.notifyItemRangeChanged(startPos, allCount, foods)
    } else if (position == -1) {
      menuAdapter = MenuSectionAdapter(
        uiModels, menuSectionPersist,
        addFood = { foodQty ->
          lifecycleScope.launch {
            menuViewModel.addFood(foodQty.food, foodQty.position, foodQty.foodIndex)
          }
        },
        minusFood = { foodQty ->
          menuViewModel.minusFood(foodQty.food, foodQty.position, foodQty.foodIndex)
        },
        showMore = { category, position, showSelectedCount ->
          binding.rvMenu.itemAnimator = null
          menuViewModel.limitChange(category, position, showSelectedCount)
        },
        navMenu = {
        },
        navigate = { model ->
          if (model.isJump) {
          } else {
            FeaturePageRouter.navigateMenuSubMore(
              this, menuViewModel.restaurantInfo, model.category,
              menuViewModel.promotionFoods, entrance
            )
          }
        }
      )
      menuAdapter?.entrance = entrance
      binding.rvMenu.apply {
        adapter = menuAdapter
        // itemView visibility and alpha invalid
        itemAnimator = null
      }

      // set the menu listener to nav selected
      val listNav = menuPageData.listBundle
      if (!listNav.isNullOrEmpty()) {
        binding.rvMenu.tag = listNav
      }

      // search bar animation
//            Handler().post {
//                systolicSearchBar()
//            }
    } else {
      menuAdapter?.models = uiModels
      // start position and itemCount for show more items selected
//            menuAdapter?.notifyItemRangeChanged(position, itemCount)
      // vertical section position notify data because of persist
      menuAdapter?.notifyDataSetChanged()
    }
  }

  private fun showNotFoundView() {
    val models = listOf<MenuUiModel>(
      MenuUiModel.MenuSearchPrompt(
        ResourcesUtil.getString(com.ricepo.style.R.string.search_menu_not_found)
      )
    )
    menuAdapter = MenuSectionAdapter(
      models, addFood = {}, minusFood = {},
      showMore = { _, _, _ -> }, navMenu = {}
    )
    binding.rvMenu.adapter = menuAdapter
  }

  private fun showErrorNetworkView(message: String?) {
    val alert = message ?: ResourcesUtil.getString(com.ricepo.style.R.string.error_title_load_failed)
    DialogFacade.showAlert(this, alert) {
    }
  }

  private fun setupListener() {
    binding.inMenuCart.clMenuCart.clickWithTrigger {
      menuViewModel.checkToCheckout(this, entrance)
    }
    binding.inMenuCart.rcvMenuCart.setOnTouchListener(
      CartTouchListener { isDown ->
        if (isDown && binding.inMenuCart.clMenuRecommend.isVisible) {
          resetHideRecommend(binding.inMenuCart)
        } else {
          menuViewModel.checkToCheckout(this, entrance)
        }
      }
    )

    binding.etMenuSearch.searchAction { text ->
      if (text.isNotEmpty()) {
        // search the menu food
        menuViewModel.menuSearchFilter = text
        // reset the table to refresh
        menuViewModel.isTableRefresh = false
        Loading.showLoading(this)
        menuViewModel.menuSearchObserver.onNext(true)
      }
    }

    binding.etMenuSearch.doAfterTextChanged {
      menuAdapter?.models = listOf()
      menuAdapter?.notifyDataSetChanged()
    }
  }

  private fun checkAddCartOption(checkOption: MenuViewModel.CheckOption) {
    when (val pageStatus = checkOption.pageStatus ?: MenuGroupStatus.None(null, null)) {
      is MenuGroupStatus.None,
      is MenuGroupStatus.SameGroup,
      is MenuGroupStatus.SameShareGroup -> {

        when {
          FeaturePageConst.PAGE_MENU == checkOption.message -> {
            FeaturePageRouter.navigateOptions(
              this@MenuSearchActivity,
              checkOption.food, checkOption.restaurant,
              checkOption.selectedPosition, checkOption.galleryFoodIndex
            )
            resetHideRecommend(binding.inMenuCart)
          }
          MenuViewModel.CheckOption.MESSAGE_REFRESH == checkOption.message -> {
            val food = checkOption.food
            menuAdapter?.models?.forEachIndexed { index, model ->
              if (model is MenuUiModel.MenuVerticalSection) {
                model.models.filter { it.food?.id == food.id }?.forEach {
                  it.food?.selectedCount = food.selectedCount
                }
              }
              // refresh food and related food
              val position = model.itemPosition ?: checkOption.selectedPosition ?: index
              menuAdapter?.notifyItemChanged(position, model.food)
            }
          }
          checkOption.isAlert -> {
            DialogFacade.showAlert(
              this@MenuSearchActivity,
              checkOption.message
            )
          }
          checkOption.message.isNotEmpty() -> {
            DialogFacade.showPrompt(
              this@MenuSearchActivity,
              checkOption.message
            ) {
              if (checkOption.deleteCacheCart) {
                // clear the other cache cart
                menuViewModel.deleteRestaurantCart(checkOption)
              }
            }
          }
        }
      }
      is MenuGroupStatus.DiffShareGroup,
      is MenuGroupStatus.DiffGroup -> {
        // REFACTOR: extract to method
        val restaurantName =
          pageStatus?.groupInfo?.restaurant?.name?.localize() ?: ""
        DialogFacade.showPrompt(
          this@MenuSearchActivity,
          message = ResourcesUtil.getString(
            com.ricepo.style.R.string.group_please_exit_subtitle, restaurantName
          ),
          title = ResourcesUtil.getString(com.ricepo.style.R.string.group_please_exit_title),
          positiveId = com.ricepo.style.R.string.group_please_exit_button
        ) {
          val groupId = pageStatus?.groupInfo?.groupId
          val restId = pageStatus?.groupInfo?.restaurant?.id
          FeaturePageRouter.navigateMenuAfterSubscription(
            this@MenuSearchActivity,
            Restaurant(restId), groupId
          )
        }
      }
    }
  }

  private fun changeMenuStatus(isCheckJoin: Boolean) {
    // init menu status get the restaurant
    lifecycleScope.launch {
      menuGroupViewModel.checkGroupStatus(menuViewModel.restaurantInfo)
        .collectLatest { pageStatus ->
          menuViewModel.mGroupStatus = pageStatus
        }
    }
  }

  private fun observeCartUpdated() {
    // observer the food update
    menuViewModel.menuCartObserver.observe(
      this,
      Observer {

        val cartData = it

        // update group cart
        if (cartData.update) {
          lifecycleScope.launch {
            menuGroupViewModel.updateGroupCartQuantity(
              this@MenuSearchActivity,
              cartData.carts, menuViewModel.restaurantInfo
            )?.collectLatest { orderGroup ->
              menuGroupViewModel.updateLocalGroupInfo(menuViewModel.restaurantInfo, orderGroup)
            }
          }
        }

        // combine group carts
        lifecycleScope.launch {
          val allCarts = menuGroupViewModel.getAllCarts(
            cartData.carts,
            menuViewModel.restaurantInfo
          )

          if (allCarts.isNullOrEmpty()) {
            showCart(false, binding.inMenuCart)
            binding.inMenuCart.tvDelivery.text = ""
            menuViewModel.menuRestaurantCart?.cartList = null
          } else {
            showCart(true, binding.inMenuCart)
            val triple = mapper.mapCartUiModel(
              allCarts,
              menuViewModel.menuRestaurantCart
            )

            val price = mapper.toTotalPrice(
              triple.second,
              menuViewModel.restaurantInfo
            )
            val models = mutableListOf<CartUiModel>()
            models.add(CartUiModel.CartMenuInfoUiModel(SpannableStringBuilder(price)))
            models.addAll(triple.first)

            if (cartAdapter == null) {
              cartAdapter = CartAdapter(models)
              val cartLayoutManager = LinearLayoutManager(this@MenuSearchActivity)
              cartLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
              binding.inMenuCart.rcvMenuCart.apply {
                layoutManager = cartLayoutManager
                adapter = cartAdapter
              }
              // simulation of external click events
//                        binding.inMenuCart.rcvMenuCart.simulateClickListener {
//                            binding.inMenuCart.clMenuCart.performClick()
//                        }
            } else {
              cartAdapter?.models = models
              val recyclerViewState = binding.inMenuCart.rcvMenuCart.layoutManager?.onSaveInstanceState()
              cartAdapter?.notifyDataSetChanged()
              binding.inMenuCart.rcvMenuCart.layoutManager?.onRestoreInstanceState(recyclerViewState)
            }

            menuViewModel.menuRestaurantCart = triple.third
            menuViewModel.menuRestaurantCart?.restaurant = menuRestaurant

            // pickup menu don't show delivery
            if (menuViewModel.deliveryMode != TabMode.MODE_PICKUP) {
              binding.inMenuCart.tvDelivery.text = mapper.toDeliveryMessage(
                triple.second,
                menuViewModel.restaurantInfo
              )
            }
          }
        }
      }
    )
  }

  override fun refreshGroupOrderCart() {
    super.refreshGroupOrderCart()

    lifecycleScope.launch {
      // refresh group order quantity
      menuGroupViewModel.updateGroupCartQuantity(menuViewModel.restaurantInfo)?.collectLatest { result ->
        if (menuGroupViewModel.groupId == null) {
          cancel()
        } else {
          result.fold(
            onSuccess = {
              reloadMenu()
            },
            onFailure = { error ->
              menuGroupViewModel.handleGroupError(
                this@MenuSearchActivity,
                menuRestaurant, error
              ) {
                // re create group order when last group order closed
                // groupId = null
                changeMenuStatus(false)
                reloadMenu()
              }
            }
          )
        }
      }
    }
  }

  private fun reloadMenu() {
//        menuViewModel.initMenuCart(menuViewModel.restaurantInfo)
//
//        val category = category ?: return
//        lifecycleScope.launch {
//            val categories = withContext(Dispatchers.IO) {
//                menuViewModel.updateFoodQuantity(listOf(category))
//            }
//            categories?.forEach {
//                setSubMoreView(menuViewModel.restaurantInfo, it)
//            }
//        }
    menuViewModel.reloadTable(true)
  }

  private fun bindPool(pool: RestaurantPool?) {
    val pool = pool ?: return
    binding.rtvPool.isVisible = true
    binding.rtvPool.setMessage(pool?.message?.localize())
    binding.rtvPool.setExpireAt(pool?.expiresAt)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_MENU_OPTIONS) {
        val food = data?.getParcelableExtra<Food>(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD)
        lifecycleScope.launch {
          // options select position
          menuViewModel.addFood(food, -1, null, false)
        }
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_CHECKOUT) {
        resetHideRecommend(binding.inMenuCart)
        reloadMenu()
        changeMenuStatus(false)
      }
    }
  }

  override fun onBackPressed() {
    backResultEvent(intent)
  }
}
