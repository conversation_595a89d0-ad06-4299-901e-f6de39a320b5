package com.ricepo.app.features.menu.combo

import android.app.Activity
import android.content.Intent
import android.graphics.Paint
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityOptionsCompat
import androidx.core.util.Pair
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.preview.MenuNormalPreviewActivity
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Combo
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil

class ComboDetailsAdapter(private var food: Food?, private var restaurant: Restaurant?) : RecyclerView.Adapter<ComboDetailsAdapter.ViewHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
    val contentView = LayoutInflater.from(parent.context).inflate(R.layout.menu_section_combo_details_item, parent, false)
    return ViewHolder(contentView)
  }

  override fun getItemCount() = food?.combo?.size ?: 0

  override fun onBindViewHolder(holder: ViewHolder, position: Int) {
    holder.bind(food, restaurant, position)
  }

  class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {

    private val ivFood = view.findViewById<ImageView>(R.id.iv_food)
    private val ivFoodBg = view.findViewById<ImageView>(R.id.iv_food_bg)
    private val tvFoodName = view.findViewById<TextView>(R.id.tv_food_name)
    private val tvFoodInfo = view.findViewById<TextView>(R.id.tv_food_info)
    private val tvFoodPrice = view.findViewById<TextView>(R.id.tv_food_price)
    private val tvFoodOriginalPrice = view.findViewById<TextView>(R.id.tv_food_original_price)
    private val tvDivider = view.findViewById<TextView>(R.id.tv_divider)

    private val mapper = MenuMapper()

    fun bind(food: Food?, restaurant: Restaurant?, position: Int) {

      val combo = food?.combo?.get(position)

      tvDivider.isVisible = position != 0

      // name
      tvFoodName.text = combo?.name?.localize()

      // description
      tvFoodInfo.isVisible = (combo?.description != null)
      tvFoodInfo.text = combo?.description?.localize()

      // price
      if (combo != null) {
        tvFoodPrice.text = mapper.formatPriceByRestaurant(combo.price, restaurant)
        tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, tvFoodPrice))

        tvFoodOriginalPrice.isVisible = (combo.originalPrice != null)
        tvFoodOriginalPrice.text = null
        combo.originalPrice?.let {
          tvFoodOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or
            Paint.ANTI_ALIAS_FLAG
          tvFoodOriginalPrice.text = mapper.formatPriceByRestaurant(it, restaurant)
          tvFoodOriginalPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)

          tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.green, tvFoodPrice))
        }
      }

      // combo image
      if (combo?.image != null && !combo.image?.url.isNullOrEmpty()) {
        RestViewUtils.setMenuComboBackground(ivFoodBg, restaurant, combo.image)
        ImageLoader.load(ivFood, combo.image?.url)
      } else {
        ivFoodBg.setImageResource(0)
        ivFood.setImageResource(com.ricepo.style.R.drawable.ic_placeholder_bag)
      }

      ivFood.setOnLongClickListener { view ->
        transitionToNormal(view.context as Activity, restaurant, combo, ivFood, ivFoodBg)
        false
      }
    }

    private fun transitionToNormal(
      context: Activity,
      restaurant: Restaurant?,
      combo: Combo?,
      ivFood: ImageView,
      ivFoodBg: ImageView
    ) {

      val intent = Intent(context, MenuNormalPreviewActivity::class.java)

      intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_RESTAURANT, restaurant)
      intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_COMBO, combo)
      intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_COMBO_FLAG, true)

      val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
        context,
        Pair(
          ivFood,
          MenuNormalPreviewActivity.VIEW_MENU_IMAGE
        ),
        Pair(
          ivFoodBg,
          MenuNormalPreviewActivity.VIEW_MENU_BACKGROUND
        )
      )
      ActivityCompat.startActivity(context, intent, activityOptions.toBundle())
    }
  }
}
