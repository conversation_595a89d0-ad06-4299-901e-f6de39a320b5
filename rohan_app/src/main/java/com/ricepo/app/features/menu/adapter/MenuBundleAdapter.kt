package com.ricepo.app.features.menu.adapter

import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuBundleItemBinding
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.MenuBundle
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 28/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuBundleAdapter(
  var bundles: List<MenuBundle>?,
  val translate: (
    lastPosition: Int,
    position: Int,
    isScrollMenu: Boolean,
    isScrollBundle: Boolean
  ) -> Unit
) :
  RecyclerView.Adapter<BundleViewHolder>() {

  private var lastSelectedPosition = -1
  var selectedPosition = 0
  var isBundleScroll = false

  fun selectPosition(
    position: Int,
    isScrollMenu: Boolean = true,
    isScrollBundle: Boolean = false
  ) {
    if (selectedPosition != position) {
      lastSelectedPosition = selectedPosition
      selectedPosition = position
      notifyDataSetChanged()
      translate(lastSelectedPosition, selectedPosition, isScrollMenu, isScrollBundle)

//            notifyItemChanged(lastSelectedPosition)
//            notifyItemChanged(selectedPosition)
    }
  }

  fun scrollLastPosition(): Int {
    var position = selectedPosition
    if (position == 0) return position
    position -= 1
    return position
  }

  fun scrollNextPosition(): Int {
    var position = selectedPosition
    if (position == (itemCount - 1)) return position
    position += 1
    return position
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BundleViewHolder {
    val binding = MenuBundleItemBinding.inflate(LayoutInflater.from(parent.context))
    binding.root.layoutParams = RecyclerView.LayoutParams(
      RecyclerView.LayoutParams.MATCH_PARENT,
      RecyclerView.LayoutParams.WRAP_CONTENT
    )
    binding.root.clickWithTrigger {
      val pos = it.tag as Int
      selectPosition(pos, isScrollBundle = true)
    }
    return BundleViewHolder(binding)
  }

  override fun onBindViewHolder(holder: BundleViewHolder, position: Int) {
    holder.bind(bundles?.get(position))
    holder.binding.root.tag = position

    // set the selected background
    if (selectedPosition == position) {
      translateBackground(
        holder.binding.root, com.ricepo.style.R.color.bundle_bar_background,
        com.ricepo.style.R.color.bundle_bar_background_selected
      )

//            holder.binding.root.setBackgroundColor(ResourcesUtil.getColor(
//                com.ricepo.style.R.color.primary_satisfied_button_fill))

      holder.binding.tvBundleName.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.bundle_bar_selected_text, holder.binding.root.context
        )
      )
      holder.binding.ivBundleLine.isVisible = false
    } else {
      holder.binding.root.setBackgroundColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.bundle_bar_background, holder.binding.root.context
        )
      )
//            translateBackground(holder.binding.root, com.ricepo.style.R.color.primary_satisfied_button_fill,
//                com.ricepo.style.R.color.bundle_bar_background)

      holder.binding.tvBundleName.setTextColor(
        ResourcesUtil.getColor(
          com.ricepo.style.R.color.bundle_bar_text, holder.binding.root.context
        )
      )
      // hide the last position and before selected position horizontal line
      holder.binding.ivBundleLine.isVisible = (position != itemCount - 1) &&
        (position != selectedPosition - 1)
    }
  }

  override fun getItemCount(): Int = bundles?.size ?: 0

  private fun translateBackground(view: View, fromColorId: Int, toColorId: Int) {
    val colorFrom = ResourcesUtil.getColor(fromColorId, view.context)
    val colorTo = ResourcesUtil.getColor(toColorId, view.context)
    val colorAnimation = ValueAnimator.ofObject(ArgbEvaluator(), colorFrom, colorTo)
    colorAnimation.duration = 200
    colorAnimation.addUpdateListener {
      view.setBackgroundColor(it.animatedValue as Int)
    }
    colorAnimation.start()
  }
}

class BundleViewHolder(val binding: MenuBundleItemBinding) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(bundle: MenuBundle?) {
    binding.tvBundleName.setText(bundle?.name?.localize())
  }
}
