package com.ricepo.app.features.menu.data

import com.ricepo.base.model.MenuBundle
import com.ricepo.base.model.OrderGroup
import com.ricepo.base.model.Restaurant

//
// Created by <PERSON><PERSON> on 27/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuPageData(
  val sectionPosition: Int,
  val listData: List<Any>?,
  val uiModels: List<MenuUiModel>,
  val listBundle: List<MenuBundle>?,
  val listRestaurant: List<Restaurant>?,
  val itemCount: Int = 1,
  val isTableRefresh: Boolean
)

/**
 * show menu title bar
 */
sealed class MenuGroupStatus {
  abstract val groupInfo: OrderGroup?

  // none group
  data class None(val restaurant: Restaurant?, override val groupInfo: OrderGroup? = null) : MenuGroupStatus()

  // same restaurant
  data class SameGroup(override val groupInfo: OrderGroup?) : MenuGroupStatus()

  // same restaurant from share link
  data class SameShareGroup(override val groupInfo: OrderGroup?) : MenuGroupStatus()

  // different restaurant
  data class DiffGroup(override val groupInfo: OrderGroup?) : MenuGroupStatus()

  // different restaurant from share link
  data class DiffShareGroup(override val groupInfo: OrderGroup?) : MenuGroupStatus()
}
