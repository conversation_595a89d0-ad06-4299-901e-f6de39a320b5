package com.ricepo.app.features.menu.base

//
// Created by <PERSON><PERSON> on 23/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object BroadcastConst {

  /**
   * refresh restaurant closed with menu page
   */
  const val ACTION_REFRESH_RESTAURANT_CLOSED = "refresh_restaurant_closed"

  const val PARAM_RESTAURANT_CLOSED = "restaurant_closed"

  /**
   * refresh group order cart
   */
  const val ACTION_REFRESH_GROUP_ORDER = "refresh_group_order"

  const val ACTION_DRIVER_RATING_PAYMENT = "driver_rating_payment"

  const val PARAM_PAYMENT_COMPLETED = "payment_completed"
}
