package com.ricepo.app.features.profile.datasource

import androidx.paging.PagingSource
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restapi.loadResultError
import com.ricepo.base.model.Customer

//
// Created by Thom<PERSON> on 3/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class CustomerDataSource constructor(
  private val repository: CombineRestApi,
  private val customerId: String?
) : PagingSource<Int, Customer>() {

  override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Customer> {
    val customerId = customerId ?: ""
    return try {

      val customer = repository.getCustomer(customerId)

      LoadResult.Page(
        data = listOf(customer),
        prevKey = null,
        nextKey = null
      )
    } catch (e: Exception) {
      loadResultError(e)
    }
  }
}
