package com.ricepo.app.features.address

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.ricepo.app.compose.Divider
import com.ricepo.app.compose.cardShape
import com.ricepo.app.compose.fontDimensionResource
import com.ricepo.app.compose.widget.BaseText
import com.ricepo.app.utils.activityContext
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.style.sheet.RoundedBottomSheetDialogFragment
import com.ricepo.app.R

class PostCodeChangeDialog(
  private val addressList: List<FormatUserAddress> = listOf(),
  private val onSelect: () -> Unit = {}
) : RoundedBottomSheetDialogFragment() {

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View {
    return ComposeView(activityContext()).apply {
      setContent {
        MaterialTheme {
          Sheet(addressList) {
            dismiss()
            onSelect.invoke()
          }
        }
      }
    }
  }

  @Composable
  fun Sheet(
    addressList: List<FormatUserAddress>,
    onSelect: () -> Unit
  ) {
    Column(
      modifier = Modifier
        .padding(16.dp)
        .cardShape()
    ) {
      BaseText(
        text = stringResource(id = com.ricepo.style.R.string.tittle_postcode_confirm),
        color = colorResource(id = com.ricepo.style.R.color.mainText),
        fontSize = fontDimensionResource(
          id = com.ricepo.style.R.dimen.font_size_h3
        )
      )
      LazyColumn {
        addressList.forEach {
          item {
            AddressItem(it, onSelect)
          }
        }
      }
    }
  }

  @Composable
  fun AddressItem(
    address: FormatUserAddress,
    onSelect: () -> Unit
  ) {
    Column(
      modifier = Modifier.clickable {
        AddressCache.saveAddress(address = address)
        onSelect.invoke()
      }
    ) {
      Divider()
      BaseText(
        text = address.formatted ?: "",
        color = colorResource(id = com.ricepo.style.R.color.mainText)
      )
    }
  }
}
