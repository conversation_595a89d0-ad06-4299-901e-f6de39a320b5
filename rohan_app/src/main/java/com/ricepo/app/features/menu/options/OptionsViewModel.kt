package com.ricepo.app.features.menu.options

import android.os.Handler
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.base.model.Food
import com.ricepo.base.model.Item
import com.ricepo.base.model.Option
import com.ricepo.base.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlin.math.min

//
// Created by <PERSON><PERSON> on 12/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class OptionsViewModel @Inject constructor(
  private val menuMapper: MenuMapper
) : BaseViewModel() {

  val models = mutableListOf<MenuUiModel>()

  fun addItem(
    item: Item?,
    option: Option?,
    food: Food,
    callback: (item: Item, option: Option, food: Food, position: Int) -> Unit
  ) {
    val item = item ?: return
    val option = option ?: return

    var count = item?.count ?: 0
    count += 1
    item?.count = count

    if (option.selected != null) {
      val nSelected = option.selected?.filterNot { it.id == item.id }
        ?.toMutableList() ?: mutableListOf()
      nSelected.add(item)
      option.selected = nSelected
    } else {
      option.selected = mutableListOf(item)
    }

    val position = food?.options?.indexOf(option) ?: -1

    callback(item, option, food, position)
  }

  fun minusItem(
    item: Item?,
    option: Option?,
    food: Food,
    callback: (item: Item, option: Option, food: Food, position: Int) -> Unit
  ) {
    val item = item ?: return
    val option = option ?: return

    val position = removeOptionSelected(item, option, food)

    callback(item, option, food, position)
  }

  /**
   * click motion layout delay scroll
   */
  fun smoothTo(view: RecyclerView, option: Option, food: Food) {
    // if more than max, remove the earlier items
    val max = option.max
    var optionSelectedCount = 0
    option.selected?.forEach {
      optionSelectedCount += it.count ?: 0
    }
    if (max == optionSelectedCount) {
      // smooth with equals max
      Handler().postDelayed(
        {
          smoothToPosition(view, option, food)
        },
        500
      )
    } else if (max in 1 until optionSelectedCount) {
      val pos = removeOptionSelected(option.selected?.first(), option, food)
      if (pos > -1) {
        Handler().postDelayed(
          {
            smoothToPosition(view, option, food)
          },
          500
        )
      }
    }
  }

  /**
   * return the option index
   */
  private fun removeOptionSelected(item: Item?, option: Option, food: Food): Int {
    val items = option.items ?: return -1
    val itemIndex = items.indexOfFirst { it.id == item?.id }
    if (itemIndex < 0) return -1

    val selectedIndex = option.selected?.indexOfFirst { it.id == item?.id }
    if (selectedIndex == null || selectedIndex < 0) return -1

    val optionIndex = food?.options?.indexOfFirst { it.id == option.id }
    if (optionIndex == null || optionIndex < 0) return -1

    // minus selected option item count
    var currentItemCount = items[itemIndex].count
    if (currentItemCount == null || currentItemCount < 1) currentItemCount = 1
    val size = currentItemCount - 1
    items.get(itemIndex).count = currentItemCount - 1

    if (size < 1) {
      option.selected?.removeAt(selectedIndex)
    } else {
      option.selected?.get(selectedIndex)?.count = size
    }

    food.options?.set(optionIndex, option)

    return optionIndex
  }

  /**
   * smooth scroll to position
   */
  private fun smoothToPosition(
    view: RecyclerView,
    option: Option,
    food: Food
  ) {
    var count = 0
    option.selected?.forEach {
      count += it.count ?: 0
    }
    val max = option.max ?: 0
    if (max > 0 && count == max) {
      val curPos = food.options?.find { it.id == option.id }
      if (curPos != null) {
        var position = 0
        models.firstOrNull {
          if (it is MenuUiModel.MenuVerticalSection) {
            val itemModel = it.models?.lastOrNull()
            if (itemModel is MenuUiModel.MenuOptionsItem) {
              if (itemModel.option?.id == curPos.id) {
                position = itemModel.itemPosition ?: 0
                true
              } else false
            } else {
              false
            }
          } else {
            false
          }
        }
//                    adapter.layoutManager.scrollToPositionWithOffset(curPos + 1, 0)
        var smoothScroller: RecyclerView.SmoothScroller = object : LinearSmoothScroller(view.context) {
          override fun getVerticalSnapPreference(): Int {
            return SNAP_TO_START
          }
        }
        smoothScroller.targetPosition = min(position + 1, models.size)
        view.layoutManager?.startSmoothScroll(smoothScroller)
      }
    }
  }
}
