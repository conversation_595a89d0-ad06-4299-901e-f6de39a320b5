package com.ricepo.app.features.rating

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.RatingFoodItemBinding
import com.ricepo.app.model.OrderItem
import com.ricepo.app.model.RatingItem
import com.ricepo.app.model.UiRatingItem
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.localize

class FoodItemsAdapter(
  val check: () -> Unit
) : RecyclerView.Adapter<FoodItemsAdapter.RatingItemHolder>() {

  private val datas = mutableListOf<UiRatingItem>()

  fun setData(
    list: List<OrderItem>
  ) {
    datas.clear()
    datas.addAll(
      list.map {
        it.toRatingUi()
      }
    )
    notifyDataSetChanged()
  }

  fun getResult() = datas.filter {
    it.rating > 0 && it.id != null
  }.map {
    RatingItem(id = it.id!!, stars = it.rating)
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RatingItemHolder {
    val binding = RatingFoodItemBinding.inflate(LayoutInflater.from(parent.context))
    return RatingItemHolder(binding, check)
  }

  override fun onBindViewHolder(holder: RatingItemHolder, position: Int) {
    holder.bind(datas[position]) {
      datas[position] = datas[position].copy(rating = it)
    }
  }

  override fun getItemCount(): Int {
    return datas.size
  }

  class RatingItemHolder(
    val binding: RatingFoodItemBinding,
    val check: () -> Unit
  ) : RecyclerView.ViewHolder(binding.root) {

    fun bind(
      item: UiRatingItem,
      onRating: (Int) -> Unit
    ) {
      ImageLoader.load(binding.foodImage, item.foodImage?.url)
      binding.foodName.text = item.foodName?.localize() ?: "UnKnow name"
      binding.ratingBar.rating = item.rating.toFloat()
      binding.ratingBar.setOnRatingChangeListener { _, rating ->
        onRating(rating.toInt())
        setRatingDesc(rating.toInt())
      }
      setRatingDesc(item.rating)
    }

    private fun setRatingDesc(rating: Int) {
      with(binding.ratingDesc) {
        text = RatingUiUtil.getRatingDesc(context, rating)
        setTextColor(context.getColor(if (rating >= 4) com.ricepo.style.R.color.mr else com.ricepo.style.R.color.fun_n5))
        check.invoke()
      }
    }
  }
}
