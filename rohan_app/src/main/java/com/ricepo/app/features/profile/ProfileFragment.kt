package com.ricepo.app.features.profile

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadState
import com.ricepo.app.HomeTab
import com.ricepo.app.R
import com.ricepo.app.databinding.FragmentProfileBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.profile.adapter.ProfileListAdapter
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.app.model.Order
import com.ricepo.app.restaurant.home.TabFragment
import com.ricepo.app.utils.activityContext
import com.ricepo.base.ErrorInput
import com.ricepo.base.adapter.FooterLoadStateAdapter
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Customer
import com.ricepo.base.tools.showErrorView
import com.ricepo.style.view.placeVisible
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ProfileFragment(
  val isTab: Boolean = false
) : TabFragment() {

  private val profileViewModel: ProfileViewModel by viewModels()

  private val profileAdapter = ProfileListAdapter()

  private var isLoaded = false

  lateinit var binding: FragmentProfileBinding

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View {
    return FragmentProfileBinding.inflate(inflater).apply {
      binding = this
      binding.ivBack.placeVisible(!isTab)
      binding.ivSetting.placeVisible(!isTab)
    }.root
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    initAdapter()
    setupListener()
    observeLoginLoad()
  }

  private var ratingJob: Job? = null
  private fun checkRatingOrder() {
    ratingJob = LifecycleNetworkListener.checkRatingOrder(lifecycleScope) { order ->
      order?.let {
        FeaturePageRouter.navigateDriverRating(
          activityContext(), it, 0,
          FeaturePageConst.REQUEST_CODE_DRIVER_RATING_RESUME, false
        )
      }
    }
  }

  override fun onResume() {
    super.onResume()
    profileViewModel.checkLoginChange()
  }

  override fun onStop() {
    super.onStop()
    ratingJob?.cancel()
  }

  @OptIn(ExperimentalPagingApi::class)
  private fun initAdapter() {

    // forbidden fling when load more data
    binding.rvProfile.itemAnimator = null

    val footAdapter = FooterLoadStateAdapter {
      profileAdapter.retry()
    }
    val adapter = profileAdapter
      .withLoadStateFooter(footAdapter)
    binding.rvProfile.adapter = adapter

    profileAdapter.addLoadStateListener { loadState ->
      // show loading during initial load or refresh
      if (profileViewModel.screenLoading) {
        if (loadState.refresh is LoadState.Loading) {
          binding.layProfileRefresh.isVisible = false
          Loading.showLoading(requireActivity())
        } else {
          Loading.hideLoading()
          showNetworkErrorView(loadState.refresh, true)
          profileViewModel.updateScreenLoading(false)
        }
      }
      if (profileViewModel.pullLoading) {
        if (loadState.refresh !is LoadState.Loading) {
          binding.layProfileRefresh.finishRefresh()
          showNetworkErrorView(loadState.refresh, false)
          profileViewModel.updatePullLoading(false)
        }
      }
      if (loadState.append is LoadState.Error) {
      }
    }

    lifecycleScope.launchWhenCreated {
      profileAdapter.loadStateFlow
        .distinctUntilChangedBy { it.refresh }
        .filter { it.refresh is LoadState.NotLoading }
        .collectLatest {
          // refresh after scroll when re login
          binding.rvProfile.scrollToPosition(0)
        }
    }
  }

  private fun showNetworkErrorView(state: LoadState, isScreenLoading: Boolean) {
    // only hide the list if refresh failed
    binding.layProfileRefresh.isVisible = state !is LoadState.Error
    if (state is LoadState.Error) {
      // show the retry state if refresh fails
      val errorInput = ErrorInput(
        com.ricepo.style.R.drawable.ic_error_no_network,
        com.ricepo.style.R.string.error_title_load_failed,
        message = state.error.message,
        buttonId = com.ricepo.style.R.string.retry,
        click = {
          profileViewModel.updateScreenLoading(true)
          refreshData(false)
        }
      )
      binding.flProfilePage.showErrorView(errorInput = errorInput)
    } else {
      binding.flProfilePage.removeAllViews()
    }
  }

  private fun observeLoginLoad() {
    profileViewModel.loginState.observe(
      viewLifecycleOwner
    ) {
      binding.btnLogin.visibility = if (it.savedCustomer == null) View.VISIBLE else View.GONE
      if (it.isLoginAndVipChange && it.savedCustomer != null) {
        isLoaded = false
        profileViewModel.updateScreenLoading(true)
        loadData(customer = it.savedCustomer)
        checkRatingOrder()
      }
      if (it.savedCustomer == null) {
        binding.layProfileRefresh.isVisible = false
      }
    }
  }

  private var loadJob: Job? = null

  private fun loadData(customer: Customer?) {
    if (!isLoaded) {
      loadJob?.cancel()
      loadJob = lifecycleScope.launch {

        profileViewModel.loadData(customer, isTab = isTab)
          ?.collectLatest {
            if (it != null) {
              profileAdapter.submitData(it)
            }
          }
      }
      isLoaded = true
    }
  }

  private fun refreshData(isPull: Boolean) {
    profileViewModel.updatePullLoading(isPull)
    profileAdapter.refresh()
  }

  override fun onDestroy() {
    super.onDestroy()
    loadJob?.cancel()
  }

  override fun refreshTab(type: String?) {
  }

  override fun refreshTab(tab: HomeTab) {
  }

  private fun setupListener() {
    binding.btnLogin.clickWithTrigger {
      isLoaded = false
      FeaturePageRouter.navigateLogin()
    }

    binding.ivSetting.clickWithTrigger {
      isLoaded = false
      FeaturePageRouter.navigateSettings()
    }

    // pull to refresh
    binding.layProfileRefresh.setOnRefreshListener {
      // refresh data of first index
      refreshData(true)
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_DRIVER_RATING ||
        requestCode == FeaturePageConst.REQUEST_CODE_HISTORY_ORDER
      ) {
        val position = data?.getIntExtra(FeaturePageConst.PARAM_PROFILE_ORDER_POSITION, -1)
        val order = data?.getParcelableExtra<Order>(FeaturePageConst.PARAM_PROFILE_HISTORY_ORDER)
        if (position != null && position > -1 && order != null) {
          val model = profileAdapter.getItemModel(position)
          if (model is ProfileUiModel.HistoryOrders) {
            model.order.allowRating = order.allowRating
            profileAdapter.notifyItemChanged(position)
          }
        }
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_RECENT_ORDER ||
        requestCode == FeaturePageConst.REQUEST_CODE_SUBSCRIPTION
      ) {
        refreshData(false)
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_DRIVER_RATING_RESUME) {
        profileViewModel.updateScreenLoading(true)
        refreshData(false)
      }
    }
  }
}
