package com.ricepo.app.features.jump

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.ricepo.app.R
import com.ricepo.app.databinding.FragmentCategoryJumpBinding
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.CategoryJumpData
import com.ricepo.base.model.MenuBundle
import com.ricepo.base.model.localize
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.sheet.RoundedBottomSheetDialogFragment
import kotlin.collections.ArrayList

//
// Created by <PERSON><PERSON> on 10/8/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class CategoryJumpFragment : RoundedBottomSheetDialogFragment() {

  companion object {
    val TAG = "category_jump"

    private val BUNDLE_GROUPS = "bundle_groups"
    private val BUNDLE_MODELS = "bundle_models"

    fun newInstance(
      groups: List<MenuBundle>?,
      models: List<CategoryJumpData>
    ): CategoryJumpFragment {
      val fragment = CategoryJumpFragment()
      val args = Bundle()
      groups?.let {
        args.putParcelableArrayList(BUNDLE_GROUPS, it.toCollection(ArrayList()))
      }
      models.let {
        args.putParcelableArrayList(BUNDLE_MODELS, it.toCollection(ArrayList()))
      }
      fragment.arguments = args
      return fragment
    }
  }

  var restUiModels: MutableList<RestaurantUiModel>? = null
  var restUiModel: RestaurantUiModel? = null

  var jump: (index: Int) -> Unit = {}

  var sort: (
    models: MutableList<RestaurantUiModel>?,
    data: CategoryJumpData,
    restUiModel: RestaurantUiModel?
  ) -> Unit = { _, _, _ -> }

  private var behavior: BottomSheetBehavior<View>? = null

  private lateinit var binding: FragmentCategoryJumpBinding

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View? {
    binding = FragmentCategoryJumpBinding.inflate(inflater)
    return binding.root
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    showJumpView()
    setParams()
    setupListener()
  }

  private fun setupListener() {
    binding.ivClose.clickWithTrigger {
      dismiss()
    }
  }

  private fun showJumpView() {
    val groups: List<MenuBundle>? = arguments?.getParcelableArrayList(BUNDLE_GROUPS)

    val models: List<CategoryJumpData> =
      arguments?.getParcelableArrayList(BUNDLE_MODELS) ?: return

    val uiModels = mutableListOf<CategoryUiModel>()
    models.groupBy { it.groupId }.forEach {
      val key = it.key
      val group = groups?.firstOrNull { it.id == key }
      val title = if (group == null) {
        // restaurant sort and category default title
        if (it.value?.firstOrNull()?.sort != null) {
          ResourcesUtil.getString(com.ricepo.style.R.string.restaurant_sort_title)
        } else {
          ResourcesUtil.getString(com.ricepo.style.R.string.menu_jump_title)
        }
      } else {
        group.name?.localize()
      }
      uiModels.add(CategoryUiModel.Title(title))
      val size = it.value.size
      it.value.forEachIndexed { index, it ->
        uiModels.add(CategoryUiModel.Item(it, index, size))
      }
    }

    val jumpAdapter = CategoryJumpAdapter(uiModels) { model ->
      if (model.sort != null) {
        sort(restUiModels, model, restUiModel)
      } else {
        model.itemPosition?.let {
          jump(it)
        }
      }
      dismiss()
    }
    binding.rvCategoryJump.apply {
      adapter = jumpAdapter
      layoutManager = LinearLayoutManager(context)
    }
  }

  fun setParams() {
    val dialog = dialog ?: return

    val bottomSheet = dialog.window?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
    if (bottomSheet != null) {
      val layoutParams = bottomSheet.layoutParams as CoordinatorLayout.LayoutParams
      layoutParams.width = DisplayUtil.getScreenWidth()
      bottomSheet.layoutParams = layoutParams

      try {
        behavior = BottomSheetBehavior.from(bottomSheet)
        behavior?.peekHeight = DisplayUtil.getScreenHeight()
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
  }
}
