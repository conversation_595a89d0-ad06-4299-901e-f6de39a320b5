package com.ricepo.app.features.menu.combo

import android.graphics.Paint
import android.os.Bundle
import android.util.TypedValue
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityMenuComboBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil

@Route(path = FeaturePageConst.PAGE_MENU_COMBO)
class MenuComboActivity : BaseActivity() {

  private lateinit var binding: ActivityMenuComboBinding

  var food: Food? = null

  var restaurant: Restaurant? = null

  lateinit var mapper: MenuMapper

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    food = intent.getParcelableExtra(FeaturePageConst.PARAM_MENU_COMBO_FOOD)
    restaurant = intent.getParcelableExtra(FeaturePageConst.PARAM_MENU_RESTAURANT)

    mapper = MenuMapper()

    binding = ActivityMenuComboBinding.inflate(layoutInflater)
    setContentView(binding.root)
    initView()
    initData()
    setupListener()
  }

  private fun initView() {
    binding.mRecyclerView.layoutManager = LinearLayoutManager(this)
    binding.mRecyclerView.adapter = ComboDetailsAdapter(food, restaurant)
  }

  private fun initData() {
    binding.tvFoodName.text = food?.name?.localize()
    binding.tvFoodInfo.text = food?.description?.localize()
    setFoodPrice()
  }

  private fun setFoodPrice() {
    food?.let { foodIt ->
      binding.tvFoodPrice.text = mapper.formatPriceByRestaurant(foodIt.price, restaurant)
      binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))

      binding.tvFoodOriginalPrice.isVisible = (foodIt.originalPrice != null)
      binding.tvFoodOriginalPrice.text = null
      foodIt.originalPrice?.let {
        binding.tvFoodOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or
          Paint.ANTI_ALIAS_FLAG
        binding.tvFoodOriginalPrice.text = mapper.formatPriceByRestaurant(it, restaurant)
        binding.tvFoodOriginalPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)

        binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.green, binding.root))
      }
    }
  }

  private fun setupListener() {
    binding.ivClose.clickWithTrigger {
      finish()
    }
  }
}
