package com.ricepo.app.features.menu.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuItemBottomBinding
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by <PERSON><PERSON> on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuBottomHolder(private val binding: MenuItemBottomBinding) :
  SectionItemHolder(binding.root) {

  fun bind(uiModel: MenuUiModel.MenuBottomItem) {
    val params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {
      if (uiModel.width != null) {
        params.width = uiModel.width
      }
      if (uiModel.height != null) {
        params.height = uiModel.height
      }
      binding.root.layoutParams = params
    }
  }

  companion object {
    fun create(parent: ViewGroup): MenuBottomHolder {
      val binding = MenuItemBottomBinding.inflate(LayoutInflater.from(parent.context))
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.margin_parent_bottom)
      )
      return MenuBottomHolder(binding)
    }
  }
}
