package com.ricepo.app.features.menu.adapter.holder

import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.view.isVisible
import com.ricepo.app.R
import com.ricepo.app.databinding.LayoutRestaurantInfoItemBinding
import com.ricepo.app.databinding.MenuRestaurantItemContainerBinding
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.app.restaurant.adapter.holder.RestaurantVerticalHolder
import com.ricepo.base.model.Restaurant
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by <PERSON><PERSON> on 9/6/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class MenuRestaurantHolder(val binding: MenuRestaurantItemContainerBinding) :
  SectionItemHolder(binding.root) {

  fun bind(item: Restaurant?) {
    if (item == null) return

    val holder = RestaurantVerticalHolder(binding.inRestaurantVertical, false, showMotd = false) { _, _ -> }
    holder.bind(RestaurantUiModel.RestaurantVertical(item, 0, 0), isShowCoin = false)
    binding.inRestaurantVertical.inRestaurantInfo.tvRestaurantName.maxLines = 2
    binding.inRestaurantVertical.inRestaurantInfo.tvRestaurantName.setTextSize(
      TypedValue.COMPLEX_UNIT_PX, ResourcesUtil.getDimenSize(com.ricepo.style.R.dimen.sw_24dp)
    )

    // hide the common closed layout
    val closedStatus = binding.inRestaurantVertical.inRestaurantClosed.tvRestaurantClosed.text
    binding.inRestaurantVertical.inRestaurantClosed.tvRestaurantClosed.isVisible = false

    binding.inRestaurantVertical.inRestaurantInfo.tvRestaurantSubInfo.isVisible = false

    val mapper = RestaurantBindMapper(item, binding.root.context)

    binding.layRestaurantInfo.removeAllViews()

    if (!closedStatus.isNullOrEmpty()) {
      val closedBinding = LayoutRestaurantInfoItemBinding.inflate(
        LayoutInflater.from(binding.root.context)
      )
      closedBinding.tvContent.text = closedStatus
      closedBinding.tvContent.setTextColor(
        ResourcesUtil.getColor(com.ricepo.style.R.color.alert, binding.root.context)
      )
      binding.layRestaurantInfo.addView(closedBinding.root)
    }

    val billboard = item.billboard
    binding.layRestaurantAutoinfo.isVisible = !billboard.isNullOrEmpty()
    binding.layRestaurantAutoinfo.removeAllViews()
    billboard?.forEach {
      val autoBinding = LayoutRestaurantInfoItemBinding.inflate(
        LayoutInflater.from(binding.root.context)
      )
      autoBinding.tvContent.text = mapper.bindRestaurantInfo(it)
      binding.layRestaurantAutoinfo.addView((autoBinding.root))
    }

    // hide the pool view
    binding.inRestaurantVertical.rtvPool.isVisible = false
    binding.inRestaurantVertical.dividerPool.isVisible = false

    // set background null
    binding.inRestaurantVertical.root.background = null

    // if delivery.fee is null, means address undeliverable
    if (item.delivery?.fee == null) {
      binding.inRestaurantVertical.inRestaurantInfo.tvRestaurantInfo.text =
        ResourcesUtil.getString(com.ricepo.style.R.string.error_address_out_of_delivery)
      binding.inRestaurantVertical.inRestaurantInfo.tvRestaurantInfo
        .setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.alert, binding.root))
    }
  }

  companion object {
    fun create(parent: ViewGroup): MenuRestaurantHolder {

      val bindingContainer = MenuRestaurantItemContainerBinding.inflate(
        LayoutInflater.from(parent.context)
      )
      // set width for divider not fill width
      val params = LinearLayout.LayoutParams(
        DisplayUtil.getScreenWidth(parent.context),
        ViewGroup.LayoutParams.WRAP_CONTENT
      )
      // 44 = 26 + 18
      params.leftMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_18dp)
      params.rightMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_22dp)
      bindingContainer.inRestaurantVertical.root.layoutParams = params

      val infoParams = LinearLayout.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.WRAP_CONTENT
      )
      // set the inRestaurantVertical width not need topMargin bigger
      infoParams.topMargin = -ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_3)
      infoParams.leftMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_22dp)
      infoParams.rightMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_22dp)
      bindingContainer.layRestaurantInfo.layoutParams = infoParams

      // hide the menu plate
      bindingContainer.inRestaurantVertical.layRestaurantMenu.isVisible = false

      return MenuRestaurantHolder(bindingContainer)
    }
  }
}
