package com.ricepo.app.features.menu.adapter.holder

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuItemNavigationBinding
import com.ricepo.app.databinding.MenuItemNavigationItemBinding
import com.ricepo.app.features.menu.adapter.FunNavMenu
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.localize
import com.ricepo.monitor.firebase.FirebaseEventName
import com.ricepo.monitor.firebase.FirebaseMenuSelectEvent
import com.ricepo.monitor.firebase.FirebaseMonitor
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionAdapter
import com.ricepo.style.view.headerlayout.SectionHeaderHolder
import com.ricepo.style.view.headerlayout.SectionHolder

//
// Created by Thomsen on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

private typealias FunNavTranslate = (lastPosition: Int, position: Int, isNav: Boolean) -> Unit

class MenuNavigationHolder(
  private val binding: MenuItemNavigationBinding,
  private val navMenu: FunNavMenu
) :
  SectionHeaderHolder(binding.root) {

  private var adapter: MenuNavigationAdapter? = null

  private var uiModel: MenuUiModel.MenuNavigationSection? = null

  init {
    adapter = MenuNavigationAdapter(uiModel) { lastPosition, position, isNav ->
      uiModel?.lastSelectedPosition = lastPosition
      uiModel?.selectedPosition = position

      binding.rcvMenuNavigation.layoutManager?.scrollToPosition(position)

      translateNavigationView(lastPosition, position)
      if (isNav) {
        navMenu(position)
      }
    }
    binding.rcvMenuNavigation.adapter = adapter
  }

  fun bind(uiModel: MenuUiModel.MenuNavigationSection) {
    this.uiModel = uiModel

    adapter?.uiModel = uiModel
    adapter?.notifyDataSetChanged()
  }

  private fun translateNavigationView(lastPosition: Int, targetPosition: Int) {
    val layoutManager = binding.rcvMenuNavigation.layoutManager as LinearLayoutManager
    val view = layoutManager.findViewByPosition(targetPosition) ?: return
    if (lastPosition == -1) {
      MenuNavigationItemHolder.setSelected(MenuItemNavigationItemBinding.bind(view))
      binding.rcvMenuNavigation.scrollToPosition(0)
    } else {
      val lastView = layoutManager?.findViewByPosition(lastPosition)
      binding.ivMenuNavigationTranslate.isVisible = true
      // add the text margin top and use the view to calculate size
      val viewY = view.y + ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_15)
      binding.ivMenuNavigationTranslate.y = viewY
      binding.ivMenuNavigationTranslate.x = view.x
      val params = binding.ivMenuNavigationTranslate.layoutParams
      params.width = view.width
      val height = view.height
      // subtract text margin top and bottom
      val realHeight = height - (2 * ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_15))
      params.height = realHeight
      binding.ivMenuNavigationTranslate.layoutParams = params

      val startX = lastView?.x ?: if (targetPosition > lastPosition)
        0f else DisplayUtil.getScreenWidth().toFloat()
      val endX = view.x

      val animatorX = ObjectAnimator.ofFloat(
        binding.ivMenuNavigationTranslate, "translationX",
        startX, endX
      ).setDuration(400)
      animatorX.addUpdateListener {
      }
      animatorX.doOnStart {
        lastView?.findViewById<TextView>(R.id.tv_menu_navigation)?.setBackgroundColor(Color.TRANSPARENT)
      }
      animatorX.doOnEnd {
        binding.ivMenuNavigationTranslate.isVisible = false
        binding.rcvMenuNavigation.adapter?.notifyDataSetChanged()
      }

      val animatorSet = AnimatorSet()
      animatorSet.playTogether(
        animatorX
      )
      animatorSet.start()
    }
  }

  override fun createHeaderViewHolder(
    adapter: SectionAdapter<*>,
    parent: ViewGroup
  ): SectionHolder? {
    return if (adapter is MenuSectionAdapter) {
      val holder = MenuNavigationHolder.create(parent, adapter.navMenu)
      holder.itemView.setBackgroundResource(com.ricepo.style.R.color.colorPrimary)
      holder.itemView.elevation = 5.0f
      holder
    } else null
  }

  override fun bindHeader(adapter: SectionAdapter<*>, position: Int) {
    if (adapter is MenuSectionAdapter) {
      val uiModel = adapter.models.get(position)
      if (uiModel is MenuUiModel.MenuNavigationSection) {
        bind(uiModel)
        // scroll to position
        val selectedPosition = uiModel.selectedPosition
        binding.rcvMenuNavigation.layoutManager?.scrollToPosition(selectedPosition)
      }
    }
  }

  fun selectPosition(position: Int) {
    val lastSelectedPosition = uiModel?.selectedPosition ?: 0
    // return is position not changed
    if (lastSelectedPosition == position) return
    uiModel?.lastSelectedPosition = lastSelectedPosition
    uiModel?.selectedPosition = position
    translateNavigationView(lastSelectedPosition, position)
    adapter?.notifyDataSetChanged()
  }

  companion object {
    fun create(parent: ViewGroup, navMenu: FunNavMenu): MenuNavigationHolder {
      val binding = MenuItemNavigationBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      val navLayoutManager = LinearLayoutManager(parent.context)
      navLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
      binding.rcvMenuNavigation.apply {
        layoutManager = navLayoutManager
        isNestedScrollingEnabled = false
        itemAnimator = null
      }
      return MenuNavigationHolder(binding, navMenu)
    }
  }
}

class MenuNavigationAdapter(
  var uiModel: MenuUiModel.MenuNavigationSection?,
  private val translate: FunNavTranslate
) :
  RecyclerView.Adapter<MenuNavigationItemHolder>() {

  fun selectSelection(position: Int?) {
    // return if position not changed
    if (uiModel?.selectedPosition == position || position == null) return
    translate(uiModel?.selectedPosition ?: 0, position, false)
    notifyDataSetChanged()
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MenuNavigationItemHolder {
    return MenuNavigationItemHolder.create(parent, translate)
  }

  override fun onBindViewHolder(holder: MenuNavigationItemHolder, position: Int) {
    holder.bind(uiModel, position)
  }

  override fun getItemCount() = uiModel?.items?.size ?: 0
}

class MenuNavigationItemHolder(
  private val binding: MenuItemNavigationItemBinding,
  private val translate: FunNavTranslate
) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(model: MenuUiModel.MenuNavigationSection?, position: Int) {
    // reset the tv background
    clearSelected(binding)

    val data = model?.items?.getOrNull(position) ?: return
    binding.tvMenuNavigation.text = data?.name?.localize()
    binding.root.tag = position
    val params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {
      if (position == 0) {
        params.leftMargin = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_30)
      } else {
        params.leftMargin = 0
      }
      binding.root.layoutParams = params
    }

    this.selectedPosition = model.selectedPosition
    this.lastSelectedPosition = model.lastSelectedPosition
    if (selectedPosition == position) {
      setSelected(binding)
    } else {
      clearSelected(binding)
    }
  }

  private fun clearSelected(binding: MenuItemNavigationItemBinding) {
    binding.tvMenuNavigation.setBackgroundColor(Color.TRANSPARENT)
    // need set padding after background
    val verticalPadding = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_10)
    val horizontalPadding = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_15)
    binding.tvMenuNavigation.setPadding(
      horizontalPadding, verticalPadding,
      horizontalPadding, verticalPadding
    )
  }

  private var selectedPosition = 0
  private var lastSelectedPosition = -1

  init {
    binding.root.clickWithTrigger {
      val pos = it.tag as Int
      if (selectedPosition != pos) {
        lastSelectedPosition = selectedPosition
        selectedPosition = pos
        translate(lastSelectedPosition, selectedPosition, true)
        // firebase event
        FirebaseMonitor.logEvent(
          FirebaseEventName.rMenuSelectTab,
          FirebaseMenuSelectEvent(FirebaseMenuSelectEvent.TYPE_HEADER, pos)
        )
      }
    }
  }

  companion object {

    fun create(parent: ViewGroup, translate: FunNavTranslate):
      MenuNavigationItemHolder {
      val binding = MenuItemNavigationItemBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.WRAP_CONTENT,
        RecyclerView.LayoutParams.MATCH_PARENT
      )
      binding.root.layoutParams = params

      return MenuNavigationItemHolder(binding, translate)
    }

    fun setSelected(binding: MenuItemNavigationItemBinding) {
      binding.tvMenuNavigation.setBackgroundResource(com.ricepo.style.R.drawable.button_secondary)
      // need set padding after background
      val verticalPadding = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_10)
      val horizontalPadding = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.dip_15)
      binding.tvMenuNavigation.setPadding(
        horizontalPadding, verticalPadding,
        horizontalPadding, verticalPadding
      )
    }
  }
}
