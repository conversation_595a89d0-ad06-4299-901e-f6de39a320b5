package com.ricepo.app.features.menu.feedback

import android.app.Activity
import android.os.Bundle
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.databinding.ActivityMenuFeedbackBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.model.ImageFeedbackReq
import com.ricepo.base.BaseActivity
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Food
import com.ricepo.base.view.DialogFacade
import com.ricepo.network.resource.NetworkError
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 21/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Route(path = FeaturePageConst.PAGE_MENU_FEEDBACK)
class MenuFeedbackActivity : BaseActivity() {

  lateinit var food: Food

  lateinit var reason: String

  @Inject
  lateinit var feedbackUseCase: MenuFeedbackUseCase

  private lateinit var binding: ActivityMenuFeedbackBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityMenuFeedbackBinding.inflate(layoutInflater)
    setContentView(binding.root)

    food = intent.getParcelableExtra(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD)!!
    reason = binding.tvImageHelpful.text.toString()

    setupListener()
  }

  private fun setupListener() {

    binding.ivImageHelpful.setOnClickListener {
      reason = binding.tvImageHelpful.text.toString()
      binding.ivImageHelpful.visibility = View.VISIBLE
      binding.ivWrongImage.visibility = View.INVISIBLE
      binding.ivOtherFeedback.visibility = View.INVISIBLE
    }

    binding.tvWrongImage.setOnClickListener {
      reason = binding.tvWrongImage.text.toString()
      binding.ivImageHelpful.visibility = View.INVISIBLE
      binding.ivWrongImage.visibility = View.VISIBLE
      binding.ivOtherFeedback.visibility = View.INVISIBLE
    }

    binding.ivOtherFeedback.setOnClickListener {
      reason = binding.tvOtherFeedback.text.toString()
      binding.ivImageHelpful.visibility = View.INVISIBLE
      binding.ivWrongImage.visibility = View.INVISIBLE
      binding.ivOtherFeedback.visibility = View.VISIBLE
    }

    binding.btnFeedbackSumit?.clickWithTrigger {
      Loading.showLoading(this)
      val req = ImageFeedbackReq(
        foodId = food.id,
        image = food.image?.url ?: "",
        reason = reason,
        detail = binding.etOtherFeedback.text.toString()
      )
      feedbackUseCase.imageFeedback(FeedbackSubscriber(), req)
    }
  }

  override fun onDestroy() {
    super.onDestroy()
  }

  inner class FeedbackSubscriber : DisposableSingleObserver<NetworkError>() {
    override fun onSuccess(t: NetworkError) {
      setResult(Activity.RESULT_OK)
      onBackPressed()
    }

    override fun onError(e: Throwable) {
      Loading.hideLoading()
      DialogFacade.showAlert(this@MenuFeedbackActivity, "Network Error")
    }
  }
}
