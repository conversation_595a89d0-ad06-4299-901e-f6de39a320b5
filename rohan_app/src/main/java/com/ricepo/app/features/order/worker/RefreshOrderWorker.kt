package com.ricepo.app.features.order.worker

import android.annotation.SuppressLint
import android.content.Context
import androidx.work.Data
import androidx.work.WorkerParameters
import androidx.work.rxjava3.RxWorker
import com.ricepo.app.data.kv.OrderCache
import com.ricepo.app.di.entrypoint.RestaurantRemotePoint
import com.ricepo.app.model.Order
import com.ricepo.app.restaurant.RestaurantRemote
import dagger.hilt.android.EntryPointAccessors
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

//
// Created by <PERSON><PERSON> on 22/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RefreshOrderWorker(context: Context, params: WorkerParameters) :
  RxWorker(context, params) {

  private val repository: RestaurantRemote by lazy {
    EntryPointAccessors.fromApplication(
      context,
      RestaurantRemotePoint::class.java
    ).injectRestaurantRemotePoint()
  }

  @SuppressLint("RestrictedApi")
  override fun createWork(): Single<Result> {
    val orderId = inputData.getString("orderId") ?: ""

    return Single.create { emitter ->
      val single = repository.getOrderById(orderId)
      single.subscribeWith(object : DisposableSingleObserver<Order>() {
        override fun onSuccess(t: Order) {
          // output data not support parcelable
          val outputData = Data.Builder()
            .putString("message", null)
            .build()
          GlobalScope.launch {
            OrderCache.saveRefreshOrder(t)
            emitter.onSuccess(Result.success(outputData))
          }
        }

        override fun onError(e: Throwable) {
          val outputData = Data.Builder()
            .putString("message", e?.message)
            .build()
          emitter.onSuccess(Result.failure(outputData))
        }
      })
    }
  }
}
