package com.ricepo.app.features.coupon.add

import androidx.lifecycle.viewModelScope
import com.ricepo.app.restapi.RiceApi
import com.ricepo.app.utils.CommonResponseError
import com.ricepo.app.utils.SingleLiveEvent
import com.ricepo.app.utils.toError
import com.ricepo.base.viewmodel.BaseViewModel
import com.skydoves.sandwich.onError
import com.skydoves.sandwich.onException
import com.skydoves.sandwich.onSuccess
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AddCouponViewModel @Inject constructor(
  private val riceApi: RiceApi
) : BaseViewModel() {

  val errorData = SingleLiveEvent<CommonResponseError?>()

  fun addCoupon(
    code: String,
    onSuccess: (() -> Unit)? = null,
    onError: (() -> Unit)? = null,
  ) {
    if (code.isEmpty()) {
      return
    }
    viewModelScope.launch {
      riceApi.addCoupon(code).onSuccess {
        onSuccess?.invoke()
      }.onError {
        onError?.invoke()
        errorData.postValue(
          this.errorBody?.toError()
        )
      }.onException {
        errorData.postValue(
          null
        )
      }
    }
  }
}
