package com.ricepo.app.features.menu.adapter.listener

import android.app.Activity
import android.content.Intent
import android.os.Handler
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityOptionsCompat
import androidx.core.util.Pair
import com.ricepo.app.R
import com.ricepo.app.features.menu.adapter.FoodQty
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.preview.MenuMarketPreviewActivity
import com.ricepo.app.features.menu.preview.MenuNormalPreviewActivity
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import kotlin.math.abs

//
// Created by <PERSON><PERSON> on 4/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class OnQuantityListener(
  private val view: View,
  private val food: Food,
  private val position: Int,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty,
  private val restaurant: Restaurant? = null
) {

  // position: the food or gallery category position
  // galleryFoodIndex: the food index of gallery food items
  var galleryFoodIndex: Int? = null

  init {
    view.setTag(R.id.tag_food, food)
    // solved adapter only payloads with sequential addition
    view.setOnTouchListener { v, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        addFood(FoodQty(food, position, galleryFoodIndex))
      }
      true
    }

    // long click listener for image view for market section
    view.findViewById<ImageView>(R.id.iv_food_img)?.setOnTouchListener { _, event ->
      touchLongAndClickListener(event, food, position, galleryFoodIndex, restaurant)
    }

    // add listener to resolved of conflict with item view click
    var menuTouchView = view.findViewById<ImageView>(R.id.iv_restaurant_menu_bg)
    if (menuTouchView?.visibility != View.VISIBLE) {
      menuTouchView = view.findViewById<ImageView>(R.id.iv_restaurant_menu)
    }
    menuTouchView?.setOnTouchListener { v, event ->
      // food changed when right plate no image to click
      val food = view.getTag(R.id.tag_food) as Food
      touchLongAndClickListener(event, food, position, galleryFoodIndex, restaurant)
    }

    // subtract listener
    val qtyView = view.findViewById<TextView>(R.id.btn_food_option_click)
    qtyView?.setOnTouchListener { _, event ->
      if (event?.action == MotionEvent.ACTION_DOWN) {
        qtyView.isEnabled = true
        minusFood(FoodQty(food, position, galleryFoodIndex))
      }
      true
    }
  }

  var startX = 0f
  var startY = 0f
  private var downLong: Long? = null

  private val mHandler = Handler()

  private fun touchLongAndClickListener(
    event: MotionEvent,
    food: Food,
    position: Int,
    galleryFoodIndex: Int?,
    restaurant: Restaurant?
  ): Boolean {
    if (event.action == MotionEvent.ACTION_DOWN) {
      downLong = System.currentTimeMillis()
      startX = event.x
      startY = event.y
      mHandler.removeCallbacksAndMessages(null)
      mHandler.postDelayed(
        {
//                FeaturePageRouter.navigateMenuFeedback(food, view.context)
          downLong = null
          transitionPreview(view, food, restaurant)
        },
        500
      )
    }
    if (event.action == MotionEvent.ACTION_MOVE) {
      if (abs(event.x - startX) > 5 || abs(event.y - startY) > 5) {
        mHandler.removeCallbacksAndMessages(null)
      } else {
        // no-op
      }
    }
    if (event.action == MotionEvent.ACTION_CANCEL) {
      mHandler.removeCallbacksAndMessages(null)
    }
    if (event.action == MotionEvent.ACTION_UP) {
      mHandler.removeCallbacksAndMessages(null)
      if (downLong != null) {
        addFood(FoodQty(food, position, galleryFoodIndex))
      }
    }
    return true
  }

  private fun transitionPreview(view: View, food: Food, restaurant: Restaurant?): Boolean {
    val context = view.context ?: return false
    if (context is Activity) else return false
    food.image?.url ?: return false

    val marketView = view.findViewById<ImageView?>(R.id.iv_food_img)
    if (marketView != null) {
      transitionToMarket(context, food, restaurant, view)
    } else {
      transitionToNormal(context, food, restaurant, view)
    }
    return true
  }

  private fun transitionToNormal(
    context: Activity,
    food: Food,
    restaurant: Restaurant?,
    view: View
  ) {
    val intent = Intent(context, MenuNormalPreviewActivity::class.java)

    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_FOOD, food)
    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_RESTAURANT, restaurant)

    val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
      context,
      Pair(
        view.findViewById<ImageView>(R.id.iv_restaurant_menu),
        MenuNormalPreviewActivity.VIEW_MENU_IMAGE
      ),
      Pair(
        view.findViewById<ImageView>(R.id.iv_restaurant_menu_bg),
        MenuNormalPreviewActivity.VIEW_MENU_BACKGROUND
      )
    )
    ActivityCompat.startActivity(context, intent, activityOptions.toBundle())
  }

  private fun transitionToMarket(
    context: Activity,
    food: Food,
    restaurant: Restaurant?,
    view: View
  ) {
    val intent = Intent(context, MenuMarketPreviewActivity::class.java)

    intent.putExtra(MenuMarketPreviewActivity.BUNDLE_MENU_PREVIEW_FOOD, food)
    intent.putExtra(MenuMarketPreviewActivity.BUNDLE_MENU_PREVIEW_RESTAURANT, restaurant)

    val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
      context,
      Pair(
        view.findViewById<ImageView>(R.id.iv_food_img),
        MenuMarketPreviewActivity.VIEW_MENU_IMAGE
      )
    )
    ActivityCompat.startActivity(context, intent, activityOptions.toBundle())
  }
}
