package com.ricepo.app.features.profile.datasource.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

//
// Created by <PERSON><PERSON> on 7/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@Dao
interface RemoteKeysDao {

  @Insert(onConflict = OnConflictStrategy.REPLACE)
  suspend fun insertAll(remoteKey: List<RemoteKeys>)

  @Query("SELECT * FROM remote_keys WHERE dataId = :dataId")
  suspend fun remoteKeysRepoId(dataId: String): RemoteKeys?

  @Query("DELETE FROM remote_keys")
  suspend fun clearRemoteKeys()
}
