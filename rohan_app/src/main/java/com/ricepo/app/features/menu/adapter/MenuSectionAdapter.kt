package com.ricepo.app.features.menu.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.features.menu.adapter.holder.CategoryModel
import com.ricepo.app.features.menu.adapter.holder.MenuBottomHolder
import com.ricepo.app.features.menu.adapter.holder.MenuCategoryHolder
import com.ricepo.app.features.menu.adapter.holder.MenuGalleryHorizontalHolder
import com.ricepo.app.features.menu.adapter.holder.MenuGalleryItemHolder
import com.ricepo.app.features.menu.adapter.holder.MenuNavigationHolder
import com.ricepo.app.features.menu.adapter.holder.MenuRecommendItemHolder
import com.ricepo.app.features.menu.adapter.holder.MenuRestaurantHolder
import com.ricepo.app.features.menu.adapter.holder.MenuSectionComboHorizontalHolder
import com.ricepo.app.features.menu.adapter.holder.MenuSectionComboItemHolder
import com.ricepo.app.features.menu.adapter.holder.MenuSectionHorizontalHolder
import com.ricepo.app.features.menu.adapter.holder.MenuSectionItemHolder
import com.ricepo.app.features.menu.adapter.holder.MenuSectionVerticalHolder
import com.ricepo.app.features.menu.adapter.holder.MenuShowMoreHolder
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.features.menu.options.MenuOptionsSectionItemHolder
import com.ricepo.app.features.menu.options.MenuOptionsSectionTitleHolder
import com.ricepo.app.restaurant.search.adapter.RestaurantSearchPromptHolder
import com.ricepo.base.adapter.EmptySectionHolder
import com.ricepo.base.model.Category
import com.ricepo.base.model.Food
import com.ricepo.base.model.Item
import com.ricepo.base.model.Option
import com.ricepo.style.DisplayUtil
import com.ricepo.style.view.headerlayout.SectionAdapter
import com.ricepo.style.view.headerlayout.SectionHolder
import com.ricepo.style.view.rv.ScrollStatePersist

//
// Created by Thomsen on 28/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * the menu qty add/minus operation
 * item is the options item
 */
typealias FunFoodQty = (foodQty: FoodQty) -> Unit

data class FoodQty(
  val food: Food,
  val position: Int,
  val foodIndex: Int?,
  // option item
  val item: Item? = null,
  // option
  val option: Option? = null,
  // the section position in the all
  val sectionPosition: Int? = null
)

/**
 * the menu show more operation
 */
typealias FunShowMore = (
  category: Category,
  position: Int,
  showSelectedCount: Int
) -> Unit

typealias FunNavMenu = (position: Int) -> Unit

class MenuSectionAdapter(
  var models: List<MenuUiModel>,
  val statePersist: ScrollStatePersist? = null,
  val isMenuPage: Boolean = false,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty,
  private val showMore: FunShowMore,
  val navMenu: FunNavMenu,
  val navigate: (model: CategoryModel) -> Unit = {}
) :
  SectionAdapter<SectionHolder>() {

  var isHeaderNavScroll = false

  var entrance: String? = null

  fun selectPosition(position: Int) {
    holders[R.layout.menu_item_navigation]?.let {
      if (it is MenuNavigationHolder) {
        it.selectPosition(position)
      }
    }
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SectionHolder {
    DisplayUtil.setDensity(parent.resources)
    val holder = when (viewType) {
      R.layout.layout_menu_restaurant -> MenuRestaurantHolder.create(parent)
      R.layout.menu_item_category -> MenuCategoryHolder.create(parent, entrance, navigate)
      R.layout.menu_item_show_more -> MenuShowMoreHolder.create(parent, showMore)
      R.layout.menu_item_bottom -> MenuBottomHolder.create(parent)
      R.layout.menu_item_navigation -> MenuNavigationHolder.create(parent, navMenu)
      R.layout.menu_section_vertical -> MenuSectionVerticalHolder.create(parent, addFood, minusFood, showMore)
      R.layout.menu_section_normal_item -> MenuSectionItemHolder.create(parent, addFood, minusFood)
      R.layout.menu_section_combo_item -> MenuSectionComboItemHolder.create(parent, addFood, minusFood)
      R.layout.menu_section_gallery_horizontal -> MenuGalleryHorizontalHolder.create(
        parent,
        addFood, minusFood, showMore
      )
      R.layout.menu_gallery_item -> MenuGalleryItemHolder.create(parent, addFood, minusFood)
      R.layout.menu_section_horizontal -> MenuSectionHorizontalHolder.create(
        parent, statePersist, addFood, minusFood, showMore
      )
      R.layout.menu_section_combo_horizontal -> MenuSectionComboHorizontalHolder.create(parent, statePersist, addFood, minusFood, showMore)
      R.layout.menu_option_section_title -> MenuOptionsSectionTitleHolder.create(parent)
      R.layout.menu_options_section_item -> MenuOptionsSectionItemHolder.create(parent, addFood, minusFood)
      R.layout.restaurant_item_search_prompt -> RestaurantSearchPromptHolder.create(parent)
      R.layout.menu_recommend_item -> MenuRecommendItemHolder.create(parent, addFood, minusFood)
      else -> EmptySectionHolder.create(parent)
    }
    holders[viewType] = holder
    return holder
  }

  override fun onBindViewHolder(holder: SectionHolder, position: Int) {}

  override fun onBindViewHolder(
    holder: SectionHolder,
    position: Int,
    payloads: MutableList<Any>
  ) {
    super.onBindViewHolder(holder, position, payloads)
    if (payloads.isEmpty()) {
      bindViewHolder(position, holder)
    } else {
      payloads.forEach {
        when (it) {
          is Item -> {
            // option item
            bindItemQtyViewHolder(it, holder)
          }
          is Food -> {
            bindQtyViewHolder(it, holder)
          }
          is List<*> -> {
            it.forEach { food ->
              if (food is Food) {
                bindQtyViewHolder(food, holder)
              } else if (food is Item) {
                bindItemQtyViewHolder(food, holder)
              }
            }
          }
        }
      }
    }
  }

  private fun bindViewHolder(
    position: Int,
    holder: SectionHolder
  ) {
    val model = models[position]
    when (holder) {
      is MenuRestaurantHolder -> holder.bind((model as MenuUiModel.RestaurantSection).restaurant)
      is MenuCategoryHolder -> holder.bind(model as MenuUiModel.MenuCategoryItem, isMenuPage)
      is MenuGalleryHorizontalHolder -> holder.bind(model as MenuUiModel.MenuGallerySection, position)
      is MenuShowMoreHolder -> holder.bind(model as MenuUiModel.MenuShowMoreItem, position)
      is MenuBottomHolder -> { holder.bind(model as MenuUiModel.MenuBottomItem) }
      is MenuNavigationHolder -> holder.bind(model as MenuUiModel.MenuNavigationSection)
      is MenuSectionVerticalHolder -> holder.bind(model as MenuUiModel.MenuVerticalSection, position)
      is MenuSectionItemHolder -> holder.bind(model as MenuUiModel.MenuNormalItem, position, itemCount)
      is MenuSectionComboItemHolder -> holder.bind(model as MenuUiModel.MenuComboItem, position, itemCount)
      is MenuGalleryHorizontalHolder -> holder.bind(model as MenuUiModel.MenuGallerySection, position)
      is MenuGalleryItemHolder -> holder.bind(model as MenuUiModel.MenuGalleryItem, position, itemCount)
      is MenuSectionHorizontalHolder -> holder.bind(model as MenuUiModel.MenuHorizontalSection, position)
      is MenuSectionComboHorizontalHolder -> holder.bind(model as MenuUiModel.MenuComboHorizontalSection, position)
      is MenuOptionsSectionTitleHolder -> holder.bind(model as MenuUiModel.MenuOptionsTitle)
      is MenuOptionsSectionItemHolder -> holder.bind(model as MenuUiModel.MenuOptionsItem, position, itemCount)
      is RestaurantSearchPromptHolder -> (holder).bind((model as MenuUiModel.MenuSearchPrompt).label)
      is MenuRecommendItemHolder -> (holder).bind(model as MenuUiModel.MenuRecommendItem, position, itemCount)
    }
  }

  private fun bindQtyViewHolder(
    food: Food,
    holder: RecyclerView.ViewHolder
  ) {
    when (holder) {
      is MenuGalleryHorizontalHolder -> holder.bindQtyView(food)
      is MenuSectionVerticalHolder -> holder.bindQtyView(food)
      is MenuSectionItemHolder -> holder.bindQtyView(food)
      is MenuSectionComboItemHolder -> holder.bindQtyView(food)
      is MenuGalleryItemHolder -> holder.bindQtyView(food)
      is MenuSectionHorizontalHolder -> holder.bindQtyView(food)
      is MenuSectionComboHorizontalHolder -> holder.bindQtyView(food)
    }
  }

  private fun bindItemQtyViewHolder(
    item: Item,
    holder: RecyclerView.ViewHolder
  ) {
    when (holder) {
      is MenuOptionsSectionItemHolder -> holder.bindQtyView(item)
      is MenuSectionVerticalHolder -> holder.bindItemQtyView(item)
    }
  }

  override fun getItemViewType(position: Int): Int {
    return when (models[position]) {
      is MenuUiModel.RestaurantSection -> R.layout.layout_menu_restaurant
      is MenuUiModel.MenuCategoryItem -> R.layout.menu_item_category
      is MenuUiModel.MenuNormalItem -> R.layout.menu_section_normal_item
      is MenuUiModel.MenuComboItem -> R.layout.menu_section_combo_item
      is MenuUiModel.MenuShowMoreItem -> R.layout.menu_item_show_more
      is MenuUiModel.MenuBottomItem -> R.layout.menu_item_bottom
      is MenuUiModel.MenuNavigationSection -> R.layout.menu_item_navigation
      is MenuUiModel.MenuVerticalSection -> R.layout.menu_section_vertical
      is MenuUiModel.MenuGallerySection -> R.layout.menu_section_gallery_horizontal
      is MenuUiModel.MenuGalleryItem -> R.layout.menu_gallery_item
      is MenuUiModel.MenuHorizontalSection -> R.layout.menu_section_horizontal
      is MenuUiModel.MenuComboHorizontalSection -> R.layout.menu_section_combo_horizontal
      is MenuUiModel.MenuOptionsTitle -> R.layout.menu_option_section_title
      is MenuUiModel.MenuOptionsItem -> R.layout.menu_options_section_item
      is MenuUiModel.MenuSearchPrompt -> R.layout.restaurant_item_search_prompt
      is MenuUiModel.MenuRecommendItem -> R.layout.menu_recommend_item
      else -> 0
    }
  }

  override fun getItemCount() = models.size

  override fun onViewRecycled(holder: SectionHolder) {
    super.onViewRecycled(holder)

    if (holder is MenuSectionHorizontalHolder) {
      holder.onRecycled()
    }
  }

  override fun onViewDetachedFromWindow(holder: SectionHolder) {
    super.onViewDetachedFromWindow(holder)
    if (holder is MenuSectionHorizontalHolder) {
      holder.onDetachedFromWindow()
    }
  }
}
