package com.ricepo.app.features.coupon

import com.ricepo.app.R
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.model.Coupon
import com.ricepo.app.model.ValidateCouponReq
import com.ricepo.app.model.ValidateCouponRes
import com.ricepo.app.restaurant.RestaurantRemote
import com.ricepo.base.BaseUseCase
import com.ricepo.base.model.Restaurant
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.NetworkError
import com.ricepo.style.ResourcesUtil
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 4/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class CouponUseCase @Inject constructor(
  private val repository: RestaurantRemote,
  private val postExecutionThread: PostExecutionThread
) : BaseUseCase() {

  fun getRecommendCoupon(restaurant: Restaurant?, subtotal: Int? = null): Observable<List<Coupon>> {
    return Observable.create<List<Coupon>> { emitter ->
      val restaurantId = RestaurantCartCache.getRestaurantCart(restaurant)?.restaurant?.id ?: ""
      val subtotal = subtotal ?: 0
      val body = ValidateCouponReq(restaurantId, subtotal)
      val single = repository.getRecommendCoupons(body)

      addDisposable(
        single.subscribeWith(object : DisposableSingleObserver<List<Coupon>>() {
          override fun onSuccess(t: List<Coupon>) {
            if (t != null) {
              emitter.onNext(t)
            }
          }

          override fun onError(e: Throwable) {
            e?.printStackTrace()
            emitter.onNext(listOf())
          }
        })
      )
    }.subscribeOn(postExecutionThread.ioScheduler)
  }

  fun getCustomerCoupon(customerId: String?): Observable<List<Coupon>> {
    return Observable.create<List<Coupon>> { emitter ->
      if (customerId == null) {
        emitter.onNext(listOf())
        return@create
      }
      val single = repository.getCouponByCustomerId(customerId)
      addDisposable(
        single.subscribeWith(object : DisposableSingleObserver<List<Coupon>>() {
          override fun onSuccess(t: List<Coupon>) {
            if (t != null) {
              emitter.onNext(t)
            }
          }

          override fun onError(e: Throwable) {
            e?.printStackTrace()
            emitter.onError(e)
          }
        })
      )
    }.subscribeOn(postExecutionThread.ioScheduler)
  }

  fun addCoupon(
    restaurant: Restaurant?,
    option: CouponViewModel.CouponOption,
    couponId: String
  ): Observable<CouponViewModel.CouponOption> {
    return Observable.create { emitter ->

      val restaurant = RestaurantCartCache.getRestaurantCart(restaurant)?.restaurant

      val body = ValidateCouponReq(restaurant = restaurant?.id ?: "", subtotal = option.subtotal ?: 0)

      val single = repository.validateCoupon(couponId, body)
      addDisposable(
        single.subscribeWith(object : DisposableSingleObserver<ValidateCouponRes>() {
          override fun onSuccess(res: ValidateCouponRes) {
            if (res != null) {
              option.coupon = res.coupon
              option.message = null
            } else {
              option.message = ResourcesUtil.getString(com.ricepo.style.R.string.error_invalid_coupon)
            }
            emitter.onNext(option)
          }

          override fun onError(e: Throwable) {
            if (e is NetworkError && e.code != ErrorCode.NOT_FOUND) {
              option.message = e.message
            } else {
              option.message = ResourcesUtil.getString(com.ricepo.style.R.string.error_invalid_coupon)
            }
            emitter.onNext(option)
          }
        })
      )
    }
  }
}

sealed class CouponUiModel {

  data class CouponItem(val data: Coupon) : CouponUiModel()

  data class AddItem(val label: String?) : CouponUiModel()

  data class LoginItem(val Label: String? = null) : CouponUiModel()

  data class EmptyItem(val label: String?) : CouponUiModel()
}
