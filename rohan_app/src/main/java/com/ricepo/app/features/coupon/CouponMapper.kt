package com.ricepo.app.features.coupon

import com.ricepo.app.R
import com.ricepo.app.model.Coupon
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 4/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class CouponMapper : BaseMapper() {

  fun mapDetailExpires(coupon: Coupon): String {
    var details = ""
//        if (coupon.description != null) {
//            details += coupon.description
//        }

    val expiresAt = formatTime(coupon.expiresAt, "MM/dd")
    if (expiresAt != null) {
      details = if (details.isNullOrEmpty()) ResourcesUtil.getString(com.ricepo.style.R.string.expires_at, expiresAt) else
        "${details}\n${ResourcesUtil.getString(com.ricepo.style.R.string.expires_at, expiresAt)}"
    }

    return details
  }
}
