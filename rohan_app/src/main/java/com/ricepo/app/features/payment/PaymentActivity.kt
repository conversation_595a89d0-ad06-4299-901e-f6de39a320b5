package com.ricepo.app.features.payment

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.databinding.ActivityPaymentBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.payment.data.PaymentHandleMode
import com.ricepo.app.features.payment.data.ShowPaymentCase
import com.ricepo.app.features.subscription.SubscriptionViewModel
import com.ricepo.app.model.Card
import com.ricepo.app.model.Order
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.app.model.localize
import com.ricepo.app.pattern.payment.PaymentGoogle
import com.ricepo.app.pattern.payment.PaymentRefer
import com.ricepo.base.BaseActivity
import com.ricepo.base.adapter.BindListAdapter
import com.ricepo.base.adapter.OnBindViewListener
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.uiSubscribe
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import com.ricepo.tripartite.alipay.AlipayClient
import com.ricepo.tripartite.wechat.WeChatPay
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

//
// Created by Thomsen on 8/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_PAYMENT)
class PaymentActivity : BaseActivity() {

  val paymentViewModel: PaymentViewModel by viewModels()

  @Inject
  lateinit var paymentUseCase: PaymentUseCase

  lateinit var showPaymentCase: ShowPaymentCase
  lateinit var payHandleMode: PaymentHandleMode

  /**
   * not refresh with back handle payment
   */
  private var isNotRefresh = false

  private lateinit var binding: ActivityPaymentBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityPaymentBinding.inflate(layoutInflater)
    setContentView(binding.root)

    showPaymentCase = intent.getParcelableExtra(FeaturePageConst.PARAM_PAGE_PAYMENT_SHOWCASE)!!
    payHandleMode = intent.getParcelableExtra(FeaturePageConst.PARAM_PAGE_PAYMENT_HANDLE)!!
    val order: Order? = intent.getParcelableExtra(FeaturePageConst.PARAM_PAGE_PAYMENT_ORDER)
    paymentViewModel.initData(order, payHandleMode)

    // set title
    if (showPaymentCase == ShowPaymentCase.editCard) {
      binding.tvTitle.text = ResourcesUtil.getString(com.ricepo.style.R.string.edit_payment)
    } else {
      binding.tvTitle.text = ResourcesUtil.getString(com.ricepo.style.R.string.choose_payment)
    }

    setupListener()

    buildViewModel()
  }

  private fun buildViewModel() {

    paymentViewModel.observeGetCard(this)
      .subscribe {
        showPaymentView(it)
      }

    paymentViewModel.observeError
      .subscribe {
        val message = it
        if (!message.isNullOrEmpty()) {
          DialogFacade.showAlert(this, message)
        }
      }
  }

  private fun showPaymentView(cards: List<Card>) {

    val paymentAdapter = BindListAdapter(
      cards, com.ricepo.base.R.layout.view_string_divider,
      object : OnBindViewListener<View, Card> {
        override fun onBindView(view: View, value: Card?, position: Int) {
          val textView = view.findViewById<TextView>(com.ricepo.base.R.id.tv_item_string)
          val detailView = view.findViewById<TextView>(com.ricepo.base.R.id.tv_item_detail)
          val dividerView = view.findViewById<TextView>(com.ricepo.base.R.id.tv_item_divider)
          val cardIcon = view.findViewById<ImageView>(com.ricepo.base.R.id.card_icon)

          if (cards.last() == value) {
            dividerView?.visibility = View.GONE
          } else {
            dividerView?.visibility = View.VISIBLE
          }

          if (value?.name != null) {
            textView.text = value.name.localize()
          } else {
            textView.text = ResourcesUtil.getString(value?.method ?: "")
          }

          if (value?.method == PaymentOwnMethod.ADD_NEW_CARD) {
            cardIcon.setImageResource(com.ricepo.style.R.drawable.ic_addcard)
          } else {
            ImageLoader.load(
              cardIcon,
              value?.images?.localize(),
              com.ricepo.style.R.drawable.ic_payment_none
            )
          }

          val rightIconView = view.findViewById<ImageView>(com.ricepo.base.R.id.iv_item_right_icon)
          if (showPaymentCase == ShowPaymentCase.editCard &&
            value?.method != PaymentOwnMethod.ADD_NEW_CARD
          ) {
            rightIconView.visibility = View.VISIBLE
            rightIconView.clickWithTrigger {
              // delete credit
              bindSelectPayment(value)
            }
          } else {
            rightIconView.visibility = View.GONE
          }

          // card description
          detailView.isVisible = value?.description != null
          detailView.text = value?.description?.localize()

          view.clickWithTrigger {

            if (value?.method == PaymentOwnMethod.ADD_NEW_CARD) {
              // add card (credit or unionPay)
              FeaturePageRouter.navigatePaymentCard(
                this@PaymentActivity,
                showPaymentCase
              )
            } else {
              // selected payment
              bindSelectPayment(value)
            }
          }
        }
      }
    )

    binding.rvPaymentList.apply {
      adapter = paymentAdapter
      layoutManager = LinearLayoutManager(this@PaymentActivity)
    }
  }

  private fun bindSelectPayment(card: Card?, isTempCard: Boolean = false) {
    paymentViewModel.selectPayment(this, card, isTempCard)
      .subscribe {
        if (it is Result<*>) {
          // auto payment
          val result = it as Result<Pair<PaymentOwnMethod, PaymentObj>>
          handlePayment(result)
        } else if (it is String) {
          // alter info and back
          DialogFacade.showAlert(this, it) {
            backEvent()
          }
        } else if (it is SubscriptionViewModel.SubscriptionOption) {
          // subscription update
          handleSubscription(it)
        } else {
          if (showPaymentCase == ShowPaymentCase.editCard) {
            // refresh page when delete cart success
            paymentViewModel.triggerGetCards(showPaymentCase)
          } else {
            if (it is Card) {
              if (it.method == PaymentOwnMethod.GOOGLE_PAY) {
                handlePayment(
                  Result.success(
                    Pair(
                      PaymentOwnMethod(
                        method = PaymentOwnMethod.GOOGLE_PAY
                      ),
                      PaymentObj(order = paymentViewModel.order)
                    )
                  )
                )
              } else {
                // save payment and back
                if (isTempCard) {
                  // back add temp card
                  val payment = PaymentOwnMethod(
                    PaymentOwnMethod.CREDIT,
                    it.brand,
                    it.last4,
                    it.id
                  )
                  intent.putExtra(
                    FeaturePageConst.PARAM_PAGE_PAYMENT_NEW_CARD,
                    payment
                  )
                  backResultEvent(intent)
                } else {
                  CustomerCache.savePayment(it) {
                    backEvent()
                  }
                }
              }
            }
          }
        }
      }
  }

  /**
   * handle subscription update
   */
  private fun handleSubscription(it: SubscriptionViewModel.SubscriptionOption) {
    val message = it.message
    if (message != null) {
      DialogFacade.showAlert(this, message) {
        backEvent()
      }
    } else {
      DialogFacade.showAlert(this, com.ricepo.style.R.string.subscription_update_payment_success) {
        backResultEvent(intent)
      }
    }
  }

  private fun handlePayment(result: Result<Pair<PaymentOwnMethod?, PaymentObj>>) {
    result.fold(
      onSuccess = { data ->
        val paymentMethod = data.first
        val paymentObj = data.second
        isNotRefresh = true
        paymentUseCase.handlePayment(
          this,
          paymentMethod,
          paymentObj
        ) { paymentError, html ->
          if (paymentError.isNullOrEmpty()) {
            // payment success
            paymentCompleted()
            if (!html.isNullOrEmpty()) {
              FeaturePageRouter.navigateHtmlWeb(html)
            }
          } else {
            if (paymentError == WeChatPay.PENDING) {
              backEvent()
            } else if (paymentError == AlipayClient.CANCELED) {
              // for WXPayEntryActivity callback and alipay canceled
              onBackPressed()
            } else {
              paymentFailed(paymentError)
            }
          }
        }
      },
      onFailure = { error ->
        paymentFailed(error.message)
      }
    )
  }

  private fun paymentFailedByGoogle(message: String?) {
    if (message != null) {
      DialogFacade.showAlert(this, message) {
        when (paymentViewModel.paymentHandleMode) {
          PaymentHandleMode.none -> {
          }
          PaymentHandleMode.autoPayment -> {
            onBackPressed()
          }
          PaymentHandleMode.autoUpdateSubscriptionPay -> {
          }
        }
      }
    }
  }

  private fun paymentFailed(message: String?) {
    if (message != null) {
      DialogFacade.showAlert(this, message) {
        onBackPressed()
      }
    }
  }

  private fun paymentCompleted() {
    Loading.hideLoading()
    if (payHandleMode == PaymentHandleMode.autoUpdateSubscriptionPay) {
    } else {
      backResultEvent(intent)
    }
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_PAYMENT_CARD) {
        isNotRefresh = true
        val payment: Card? = data?.getParcelableExtra(
          FeaturePageConst.PARAM_PAGE_PAYMENT_NEW_CARD
        )
        bindSelectPayment(payment, true)
      }
    }
    if (requestCode == PaymentGoogle.LOAD_PAYMENT_DATA_REQUEST_CODE) {
      if (paymentViewModel.paymentHandleMode != PaymentHandleMode.autoPayment) {
        Loading.showLoading(this)
      }
      PaymentRefer.onPaymentGoogleResult(
        resultCode, data,
        success = { pm ->
//                paymentCompleted()
          when (paymentViewModel.paymentHandleMode) {
            PaymentHandleMode.none -> {
              Loading.hideLoading()
              pm?.let {
                CustomerCache.savePayment(it) {
                  backEvent()
                }
              }
            }
            PaymentHandleMode.autoPayment -> {
              paymentViewModel.createPayment(
                pm,
                paymentViewModel.order
              ).uiSubscribe().subscribe {
                handlePayment(it)
              }
            }
            PaymentHandleMode.autoUpdateSubscriptionPay -> {
              paymentViewModel.updateSubscription(
                Card(
                  method = pm?.method,
                  id = pm?.stripeId
                )
              )
                .uiSubscribe().subscribe {
                  Loading.hideLoading()
                  if (it is SubscriptionViewModel.SubscriptionOption) {
                    // subscription update
                    handleSubscription(it)
                  } else {
                    paymentFailedByGoogle(ResourcesUtil.getString(com.ricepo.style.R.string.payment_select_failed))
                  }
                }
            }
          }
        },
        failed = {
          Loading.hideLoading()
          paymentFailedByGoogle(ResourcesUtil.getString(com.ricepo.style.R.string.payment_select_failed))
        }
      )
    }

    PaymentRefer.onPaymentCardResult(
      this, requestCode, data,
      capture = {
        paymentViewModel.saveTempPaymentCard()
        paymentCompleted()
      },
      completed = {
        paymentCompleted()
      },
      failed = {
        paymentFailed(ResourcesUtil.getString(com.ricepo.style.R.string.payment_failed))
      }
    )

  }

  override fun onStart() {
    super.onStart()
    if (isNotRefresh) return
    paymentViewModel.checkLogin().observe(
      this
    ) {
      if (it.isNullOrEmpty()) {
        binding.btnLogin?.visibility = View.VISIBLE
      } else {
        binding.btnLogin?.visibility = View.GONE
      }
      paymentViewModel.isLogin = !(it.isNullOrEmpty())
      paymentViewModel.triggerGetCards(showPaymentCase)
    }
  }

  private fun setupListener() {
    binding.btnLogin?.clickWithTrigger {
      FeaturePageRouter.navigateLogin()
    }
  }

  override fun onBackPressed() {
    if (paymentViewModel.order?.rechargeOrder != null) {
      backResultCancel()
    } else {
      super.onBackPressed()
    }
  }

  private fun backResultCancel() {
    setResult(Activity.RESULT_CANCELED, intent)
    backEvent()
  }
}
