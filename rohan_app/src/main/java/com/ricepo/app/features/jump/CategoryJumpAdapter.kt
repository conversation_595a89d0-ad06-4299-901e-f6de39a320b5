package com.ricepo.app.features.jump

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.databinding.CategoryJumpItemBinding
import com.ricepo.app.databinding.CategoryJumpItemImageBinding
import com.ricepo.app.databinding.CategoryJumpTitleBinding
import com.ricepo.base.adapter.EmptyViewHolder
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.CategoryJumpData
import com.ricepo.base.model.CategoryJumpItem
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 10/8/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

sealed class CategoryUiModel {
  class Title(var name: String?) : CategoryUiModel()

  class Item(
    var data: CategoryJumpData,
    val index: Int,
    val size: Int
  ) : CategoryUiModel()
}

class CategoryJumpAdapter(
  private var datas: List<CategoryUiModel>,
  private var itemClick: (model: CategoryJumpData) -> Unit
) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return when (viewType) {
      R.layout.category_jump_item -> {
        CategoryJumpHolder.create(parent, itemClick)
      }
      R.layout.category_jump_title -> {
        CategoryJumpTitleHolder.create(parent)
      }
      else -> EmptyViewHolder.create(parent)
    }
  }

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val model = datas[position]
    when (model) {
      is CategoryUiModel.Item -> {
        (holder as CategoryJumpHolder).bind(model, position)
      }
      is CategoryUiModel.Title -> {
        (holder as CategoryJumpTitleHolder).bind(model, position)
      }
    }
  }

  override fun getItemCount() = datas.size

  override fun getItemViewType(position: Int): Int {
    val model = datas[position]
    return when (model) {
      is CategoryUiModel.Item -> R.layout.category_jump_item
      is CategoryUiModel.Title -> R.layout.category_jump_title
      else -> com.ricepo.base.R.layout.layout_empty_holder
    }
  }
}

class CategoryJumpHolder(
  private val binding: CategoryJumpItemBinding,
  private var itemClick: (model: CategoryJumpData) -> Unit
) :
  RecyclerView.ViewHolder(binding.root) {

  var itemWidth = 0

  init {
    binding.root.clickWithTrigger {
      val model = it.tag
      if (model is CategoryJumpData) {
        itemClick(model)
      }
    }
  }

  fun bind(uiModel: CategoryUiModel.Item, position: Int) {
    val model = uiModel.data
    binding.root.tag = model
    binding.tvDivider.isVisible = (uiModel.index != 0)

    val width = DisplayUtil.getScreenWidth(binding.root.context) - (
      2 *
        ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_30)
      )
    itemWidth = width.div(3)

    binding.tvJumpCategory.text = model.title

    val selected = model.sortSelectedId == model.sort?.id
    if (model.sort != null && selected) {
      binding.ivJumpCategory.isVisible = true
      binding.ivJumpCategory.setImageResource(com.ricepo.style.R.drawable.ic_path_checkmark)
    } else {
      binding.ivJumpCategory.isVisible = false
      binding.ivJumpCategory.setImageResource(0)
    }

    val params = binding.tvJumpCategory.layoutParams
    if (params is ConstraintLayout.LayoutParams) {
      if (model.sort != null) {
        // restaurant sort item
        params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
        params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
        params.topMargin = 0
      } else {
        params.width = itemWidth - ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_15)
      }
      binding.tvJumpCategory.layoutParams = params
    }
    setBackground(uiModel.index, uiModel.size)

    binding.llJumpImage.removeAllViews()
    val items = mutableListOf<CategoryJumpItem?>()
    for (i in 0..1) {
      items.add(model.items?.getOrNull(i))
    }
    items.forEach {
      addFoodImage(it?.name, it?.image, it?.available)
    }
  }

  fun setBackground(position: Int, size: Int) {
    when (size) {
      1 -> {
        binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card)
      }
      2 -> {
        when (position) {
          0 -> {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_top)
          }
          1 -> {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_bottom)
          }
        }
      }
      else -> {
        when (position) {
          0 -> {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_top)
          }
          (size - 1) -> {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_bottom)
          }
          else -> {
            binding.root.setBackgroundResource(com.ricepo.style.R.color.card_background)
          }
        }
      }
    }
  }

  private fun addFoodImage(leftName: String?, leftImage: String?, available: Boolean?) {
    val itemBinding = CategoryJumpItemImageBinding.inflate(LayoutInflater.from(binding.root.context))
    itemBinding.tvJumpName.text = leftName
    if (leftName != null) {
      bindImage(itemBinding, leftImage)
    }

    val params = LinearLayout.LayoutParams(itemWidth, LinearLayout.LayoutParams.WRAP_CONTENT)
    itemBinding.root.layoutParams = params

    binding.llJumpImage.addView(itemBinding.root)

    if (available == false) {
      itemBinding.root.alpha = 0.2f
    } else {
      itemBinding.root.alpha = 1f
    }
  }

  private fun bindImage(binding: CategoryJumpItemImageBinding, imageUrl: String?) {
    if ((imageUrl == null)) {
      binding.ivJumpBg.visibility = View.INVISIBLE
      binding.ivJump.setImageResource(com.ricepo.style.R.drawable.dish_placeholder)
    } else {
      binding.ivJumpBg.setImageResource(com.ricepo.style.R.drawable.bg_plate)
      binding.ivJumpBg.visibility = View.VISIBLE
      ImageLoader.load(binding.ivJump, imageUrl)
    }
  }

  companion object {
    fun create(parent: ViewGroup, itemClick: (model: CategoryJumpData) -> Unit): CategoryJumpHolder {
      val binding = CategoryJumpItemBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      return CategoryJumpHolder(binding, itemClick)
    }
  }
}

class CategoryJumpTitleHolder(private val binding: CategoryJumpTitleBinding) :
  RecyclerView.ViewHolder(binding.root) {

  fun bind(model: CategoryUiModel.Title, position: Int) {
    binding.tvJumpTitle.text = model.name
  }

  companion object {
    fun create(parent: ViewGroup): CategoryJumpTitleHolder {
      val binding = CategoryJumpTitleBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      return CategoryJumpTitleHolder(binding)
    }
  }
}
