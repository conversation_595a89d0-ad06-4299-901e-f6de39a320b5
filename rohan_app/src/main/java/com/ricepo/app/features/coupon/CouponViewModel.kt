package com.ricepo.app.features.coupon

import android.app.Activity
import androidx.lifecycle.viewModelScope
import com.ricepo.app.R
import com.ricepo.app.consts.IGlobalValueHolder
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.coupon.data.ShowCouponCase
import com.ricepo.app.model.Coupon
import com.ricepo.base.extension.bindLoading
import com.ricepo.base.model.Restaurant
import com.ricepo.base.remoteconfig.IRiceRemoteConfig
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.annotations.NonNull
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.kotlin.Observables
import io.reactivex.rxjava3.subjects.PublishSubject
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch
import javax.inject.Inject

//
// Created by Thomsen on 4/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
@OptIn(FlowPreview::class)
@HiltViewModel
class CouponViewModel @Inject constructor(
  private val postExecutionThread: PostExecutionThread,
  private val useCase: CouponUseCase,
  private val globalValueHolder: IGlobalValueHolder,
  private val riceRemoteConfig: IRiceRemoteConfig
) : BaseViewModel() {

  var coupons: List<Coupon>? = null

  private var isLoginChange = false

  var restaurant: Restaurant? = null

  private var showCouponCase: ShowCouponCase = ShowCouponCase.all

  val showRedeem = MutableStateFlow(false)

  init {
    viewModelScope.launch {
      globalValueHolder.address.debounce(100).collectLatest {
        showRedeem.value = riceRemoteConfig.shouldShowRedeem(it?.country)
      }
    }
  }

  fun setShowCouponCase(showCouponCase: ShowCouponCase) {
    this.showCouponCase = showCouponCase
  }

  fun getShowCouponCase(): ShowCouponCase {
    return showCouponCase
  }

  data class Output(
    val couponUpdated: Observable<List<Coupon>>
  )

  data class CouponOption(
    val showCase: ShowCouponCase,
    val subtotal: Int,
    var isNotLogin: Boolean = true,
    var isLoginChange: Boolean = false,
    var coupon: Coupon? = null,
    var message: String? = null
  )

  private val willAppear = PublishSubject.create<CouponOption>()

  fun reloadCoupon(couponOption: CouponOption) {
    if (couponOption != null) {
      willAppear.onNext(couponOption)
    }
  }

  /**
   * coupon and login ui model datas
   */
  fun couponUpdate(activity: Activity): Observable<List<CouponUiModel>> {

    return Observable.merge(
      observeLogin(),
      Observables.zip(
        observeCoupon(activity),
        observeLogin()
      ) { coupons, login ->
        val datas = mutableListOf<CouponUiModel>()
        datas.addAll(coupons)
        datas.addAll(login)
        datas
      }
    ).flatMap { it ->
      Observable.just(it)
    }
  }

  /**
   * get login ui model
   */
  private fun observeLogin(): Observable<List<CouponUiModel>> {
    return willAppear.flatMap { option ->
      val datas = mutableListOf<CouponUiModel>()
      isLoginChange = option.isLoginChange
      val isNotLogin = option.isNotLogin
      if (isNotLogin) {
        // add login
        datas.add(CouponUiModel.LoginItem())
      }
      Observable.just(datas)
    }
  }

  /**
   * get the customer or all coupon
   */
  private fun observeCoupon(activity: Activity): Observable<List<CouponUiModel>> {
    return willAppear.flatMap { option ->
      val showCase = option.showCase
      val subtotal = option.subtotal

      when (showCase) {
        ShowCouponCase.all -> {
          fetchCoupon(coupons, subtotal, activity)
        }
        ShowCouponCase.customer -> {
          Observable.create<List<CouponUiModel>> { emitter ->
            CustomerCache.liveCustomer { customer ->
              val customerId = customer?.id
              if (customerId != null) {
                useCase.getCustomerCoupon(customerId)
                  .bindLoading(activity)
                  .subscribe {
                    emitter.onNext(
                      it.map { data ->
                        CouponUiModel.CouponItem(data)
                      }
                    )
                  }
              } else {
                emitter.onNext(listOf())
              }
            }
          }.flatMap {
            val dataList = mutableListOf<CouponUiModel>()
            if (it.isNullOrEmpty() && !option.isNotLogin) {
              // empty coupon label with already login
              dataList.add(CouponUiModel.EmptyItem(ResourcesUtil.getString(com.ricepo.style.R.string.No_coupon_available)))
            } else {
              dataList.addAll(it)
            }
            Observable.just(dataList)
          }
        }
      }!!
    }
  }

  private fun fetchCoupon(
    coupons: List<Coupon>?,
    subtotal: Int,
    activity: Activity
  ):
    @NonNull Observable<MutableList<CouponUiModel>>? {
    val coupons = if (isLoginChange) {
      return getRecommendCoupon(subtotal, activity)
    } else {
      coupons ?: return getRecommendCoupon(subtotal, activity)
    }

    return constructCoponUiModel(coupons)
  }

  private fun getRecommendCoupon(
    subtotal: Int,
    activity: Activity
  ) = useCase.getRecommendCoupon(restaurant, subtotal)
    .bindLoading(activity)
    .subscribeOn(postExecutionThread.mainScheduler)
    .flatMap { coupons ->
      constructCoponUiModel(coupons)
    }

  private fun constructCoponUiModel(coupons: List<Coupon>): @NonNull Observable<MutableList<CouponUiModel>> {
    val dataList = mutableListOf<CouponUiModel>()

    // coupons
    dataList.addAll(
      coupons.map {
        CouponUiModel.CouponItem(it)
      }
    )
    // add label
    dataList.add(CouponUiModel.AddItem(ResourcesUtil.getString(com.ricepo.style.R.string.add_coupon)))
    return Observable.just(dataList)
  }
}
