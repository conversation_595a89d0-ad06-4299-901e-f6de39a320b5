package com.ricepo.app.features.login

import android.os.Bundle
import android.text.InputFilter
import android.view.MotionEvent
import android.view.View
import androidx.activity.viewModels
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.Observer
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityLoginBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.base.BaseActivity
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.base.model.GlobalConfigModel
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.sheet.BaseBottomSheetFragment
import com.ricepo.style.view.CaptchaCodeTextView
import dagger.hilt.android.AndroidEntryPoint

//
// Created by <PERSON><PERSON> on 1/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_LOGIN)
class LoginActivity : BaseActivity() {

  val loginViewModel: LoginViewModel by viewModels()

  private var isChat = false

  private lateinit var binding: ActivityLoginBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    isChat = intent.getBooleanExtra(FeaturePageConst.PARAM_LOGIN_CHAT, false)
    binding = ActivityLoginBinding.inflate(layoutInflater)
    setContentView(binding.root)

    setupListener()
    bindViewModel()
  }

  override fun onResume() {
    super.onResume()
    // show keyboard with et_phone_number
    KeyboardUtil.showKeyboard(this, binding.etPhoneNumber)
  }

  private fun bindViewModel() {
    loginViewModel.initCountryArea(this).observe(
      this,
      Observer {
        val pair = it
        binding.etPhoneRegion.setText("+${pair.first.area}")
        binding.etPhoneNumber.filters = if ("33".equals(pair.first.area)) {
          arrayOf(dynamicLengthFilter)
        } else {
          arrayOf(InputFilter.LengthFilter(pair.first.digit))
        }
        binding.etPhoneNumber.setText("")
      }
    )

    loginViewModel.liveCountdown.observe(
      this,
      Observer {
        binding.tvVcodeResend.text = it.countdown
        if (it.countdown == ResourcesUtil.getString(com.ricepo.style.R.string.resend)) {
          binding.tvVcodeResend.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.mainText, binding.root))
        } else {
          binding.tvVcodeResend.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))
        }
      }
    )

    loginViewModel.liveGetVcode.observe(
      this,
      Observer {
        val option = it
        if (option.message == LoginViewModel.LoginOption.MSG_IS_LOADING) {
          Loading.showLoading(this)
        } else if (option.message?.isNotEmpty() == true) {
          Loading.hideLoading()
          binding.etPhoneNumber.setText("")
          DialogFacade.showAlert(this, option.message)
          if (option.tooMany) {
            showVcodeView(option)
          }
        } else {
          Loading.hideLoading()
          showVcodeView(option)
        }
      }
    )

    // 监听region数据加载状态
    loginViewModel.liveRegionLoading.observe(
      this,
      Observer { isLoading ->
        if (isLoading) {
          Loading.showLoading(this)
        } else {
          Loading.hideLoading()
        }
      }
    )
  }

  private fun showVcodeView(option: LoginViewModel.LoginOption) {
    // send sms success
    binding.groupLoginPhone.visibility = View.GONE
    binding.groupLoginVcode.visibility = View.VISIBLE
    binding.tvVcodePhone.text = option.phone
    KeyboardUtil.hideKeyboard(this, binding.ivClose)
    binding.ctVcode.showSoftInput()
  }

  private fun setupListener() {

    binding.etPhoneRegion.touchWithTrigger { _, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        // 优化：直接观察数据变化，避免重复创建Observer
        loginViewModel.showAreaBottomSheet().observe(
          this,
          Observer { pair ->
            if (pair.first.isNotEmpty() && pair.second.isNotEmpty()) {
              showAreaBottomSheet(pair.first, pair.second)
            } else {
              // data empty
            }
          }
        )
      }
    }

    binding.etFocusPhoneNumber.setOnTouchListener(object : View.OnTouchListener {
      override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        when (event?.action) {
          MotionEvent.ACTION_DOWN -> {
            KeyboardUtil.toggleKeyboard(binding.etPhoneNumber)
          }
        }
        return true
      }
    })

    binding.etPhoneNumber.doAfterTextChanged { text ->
      loginViewModel.checkLogin(text.toString(), "sms")
    }

    binding.ctVcode.onCompleteListener = object : CaptchaCodeTextView.OnCompleteListener {
      override fun onCompleted(code: String?) {
        loginViewModel.validateCode(code).observe(
          this@LoginActivity,
          Observer {
            val option = it
            if (option.message == LoginViewModel.LoginOption.MSG_IS_LOADING) {
              Loading.showLoading(this@LoginActivity)
            } else if (option.message?.isNotEmpty() == true) {
              Loading.hideLoading()
              binding.ctVcode.resetCode()
              DialogFacade.showAlert(this@LoginActivity, option.message)
            } else {
              Loading.hideLoading()
              if (isChat) {
                FeaturePageRouter.navigateOrderSupportChat(this@LoginActivity)
                finish()
              } else {
                // success
                backResultEvent(intent)
              }
            }
          }
        )
      }
    }

    binding.tvVcodeResend.touchWithTrigger { textView, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        if (textView.text == ResourcesUtil.getString(com.ricepo.style.R.string.resend)) {
          // resend vscode
          showResendBottomSheet()
        }
      }
    }
  }

  private fun showResendBottomSheet() {
    val datas = listOf(
      ResourcesUtil.getString(com.ricepo.style.R.string.resend_by_sms),
      ResourcesUtil.getString(com.ricepo.style.R.string.resend_by_voice)
    )
    val bottomSheet = BaseBottomSheetFragment.newInstance(datas, datas)
    bottomSheet.onItemTextClickListener = object : BaseBottomSheetFragment.OnItemTextClickListener {
      override fun onItemClick(text: String) {
        binding.ctVcode.resetCode()
        loginViewModel.resendVcode(text)
      }
    }
    bottomSheet.show(supportFragmentManager, "sheet_resend_vcode")
  }

  // select phone region
  private fun showAreaBottomSheet(datas: List<String>, models: List<GlobalConfigModel>) {
//        val datas = mutableListOf("China +86", "UK +44", "United States +1", "France +33",
//            "Spain +34", "Cancel")
    val bottomSheet = BaseBottomSheetFragment.newInstance(datas, models)
    bottomSheet.onItemClickListener = object : BaseBottomSheetFragment.OnItemClickListener<GlobalConfigModel> {
      override fun onItemClick(text: String, data: GlobalConfigModel?) {
        binding.etPhoneRegion.setText("+${data?.area}")
        binding.etPhoneNumber.filters = if ("33".equals(data?.area)) {
          arrayOf(dynamicLengthFilter)
        } else {
          arrayOf(InputFilter.LengthFilter(data?.digit ?: 10))
        }
        binding.etPhoneNumber.setText("")
        binding.etPhoneNumber.setHint(data?.placeholder)
        loginViewModel.setGlobalConfigModel(data)
      }
    }
    bottomSheet.show(supportFragmentManager, "sheet_phone_region")
  }

  // 33 france validate phone length
  val dynamicLengthFilter = InputFilter { source, start, end, dest, dstart, dend ->

    val futureText = StringBuilder(dest).replace(dstart, dend, source.subSequence(start, end).toString())

    val maxLength = if (futureText.startsWith("0")) {
      10
    } else {
      9
    }

    if (futureText.length > maxLength) {
      // 如果超限，则返回一个空字符串，表示阻止本次输入
      ""
    } else {
      // 如果没有超限，返回 null 表示接受本次输入
      null
    }
  }

  override fun onDestroy() {
    super.onDestroy()
  }
}
