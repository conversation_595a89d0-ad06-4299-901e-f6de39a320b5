package com.ricepo.app.features.payment.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 8/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/*
 * Show mode for payment page
 * If the flow is Checkout -> payment, show all payment
 * If the flow is Setting-> payment, only show credit card and can delete
 */
@Parcelize
enum class ShowPaymentCase : Parcelable {
  /**
   * show all payment
   */
  all,

  /**
   * only show credit card payment
   */
  editCard,

  /**
   * only show stripe payment(credit) for subscription
   */
  onlyStripe
}

/**
 * Mark handle mode after choose one payment method
 * - none: do nothing after choose payment method and return to last page
 * - autoPayment: auto create payment method for pay
 * - autoUpdateSubscriptionPay: auto update subscription payment method after choose payment method
 */
@Parcelize
enum class PaymentHandleMode : Parcelable {
  none,

  autoPayment,

  autoUpdateSubscriptionPay
}
