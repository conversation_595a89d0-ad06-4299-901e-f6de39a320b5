package com.ricepo.app.features.menu.adapter.holder

import android.graphics.Paint
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.consts.CategoryConst
import com.ricepo.app.consts.FoodSize
import com.ricepo.app.databinding.MenuSectionNormalItemBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.adapter.listener.OnQuantityChocolateListener
import com.ricepo.app.features.menu.adapter.listener.QuantityDecorator
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.restaurant.data.HotImage
import com.ricepo.app.restaurant.utils.RestViewUtils
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.span.CenteredImageSpan

//
// Created by Thomsen on 05/07/2021.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class MenuSectionItemHolder(
  private val binding: MenuSectionNormalItemBinding,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty
) :
  BaseFoodLargeImageHolder(binding.root) {

  val mapper = MenuMapper()

  /**
   * [position] the ui model list index
   */
  fun bind(
    model: MenuUiModel.MenuNormalItem,
    position: Int,
    size: Int
  ) {
    // completing the background
    setItemBackground(model, position, size)

    binding.btnFoodOption.isVisible = (model.food != null)

    // show divider
    binding.dividerFood.isVisible = (model.food != null)

    val food = model.food ?: return
    val restaurant = model.restaurant

    bindView(food, restaurant, position)
    bindQtyView(food)

    // qty operation listener
    OnQuantityChocolateListener(
      binding, food, position, restaurant,
      model.isRootClick, addFood, minusFood
    )
  }

  private fun setItemBackground(
    model: MenuUiModel.MenuNormalItem,
    position: Int,
    size: Int
  ) {
    var params = binding.root.layoutParams
    if (params is RecyclerView.LayoutParams) {
      var width = params.width
      var leftMargin = 0
      var rightMargin = 0
      var leftPadding = 0
      var rightPadding = 0
      if (model.type == CategoryConst.HORIZONTAL) {
        val rows = model.rows ?: 1
        binding.root.setBackgroundResource(0)
        binding.dividerFood.isVisible = true
        // the middle width (22 + 44) + extra space for layout 8 otherwise qty scroll top
        width = DisplayUtil.getScreenWidth(binding.root.context) -
          ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_74dp)
        val posSize = position + 1
        val mod = posSize % rows
        val lastPos = (size - 1 - position)

        leftPadding = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_22dp)

        if (position < rows) {
          width = DisplayUtil.getScreenWidth(binding.root.context) -
            ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_70dp)
        } else if (lastPos < rows) {
          // 22 + 22 + 8
          width = DisplayUtil.getScreenWidth(binding.root.context) -
            ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_52dp)
          // 22 + 4
          rightPadding = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_26dp)
          leftPadding = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_14dp)
        }
        if (rows == 1) {
          binding.dividerFood.isVisible = false
          if (position < rows) {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_left)
          } else if (lastPos < rows) {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_right)
          } else {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_middle)
          }
        } else if (mod == 1) {
          binding.dividerFood.isVisible = false
          if (position < rows) {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_left_top)
          } else if (lastPos < rows) {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_right_top)
          } else {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_middle_top)
          }
        } else if (mod == 0) {
          if (position < rows) {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_left_bottom)
          } else if (lastPos < rows) {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_right_bottom)
          } else {
            binding.root.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_middle_bottom)
          }
        } else {
          binding.root.setBackgroundColor(
            ResourcesUtil.getColor(
              com.ricepo.style.R.color.card_background, binding.root.context
            )
          )
        }

        // button right margin
        val buttonParams = binding.btnFoodOption.layoutParams
        if (buttonParams is ConstraintLayout.LayoutParams) {
          buttonParams.rightMargin = 0
          binding.btnFoodOption.layoutParams = buttonParams
        }
      } else {
        // top divider visible
        binding.dividerFood.isVisible = !(position == 0)
        leftPadding = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_22dp)
        rightPadding = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.sw_26dp)
      }
      params.width = width
      // same height with vertical don't set height
//            params.height = height
      params.leftMargin = leftMargin
      params.rightMargin = rightMargin
      binding.root.layoutParams = params
      binding.root.setPadding(leftPadding, 0, rightPadding, 0)
    }
  }

  fun bindQtyView(food: Food) {
    val selectedCount = food.selectedCount ?: 0
    binding.btnFoodOption.isPlus = food.isPlus
    binding.btnFoodOption.isMinus = food.isMinus
    QuantityDecorator.bindQtyChocolate(binding, selectedCount)
    food.isPlus = null
    food.isMinus = null
  }

  private fun setRatingOrDesc(food: Food) {
    with(binding.tvFoodInfo) {
      isVisible = (food.description != null || food.hasRating())
      text = food.humanizeRating() ?: food.description?.localize()
      setCompoundDrawablesWithIntrinsicBounds(
        if (food.hasRating()) {
          ResourcesUtil.getDrawable(com.ricepo.style.R.drawable.ic_thumb)
        } else {
          null
        },
        null,
        null,
        null
      )
    }
  }

  private fun bindView(food: Food, restaurant: Restaurant?, position: Int) {

    // set food image
    setFoodImage(food, restaurant, binding.ivFood, binding.ivFoodBg)

    binding.tvFoodName.text = bindName(food)

    setRatingOrDesc(food)

    val isReward = (food.reward == true)
    binding.ivRewardIcon.isVisible = isReward
    binding.btnFoodOption.setReward(isReward)
    if (isReward) {
      setFoodReward(food)
    } else {
      setFoodPrice(food, restaurant)
    }

    if (food.available == false) {
      binding.laySectionItem.isEnabled = false
      binding.laySectionItem.alpha = 0.2f
      binding.btnFoodOption.isEnabled = false
      // recyclerview animations handle the itemView alpha animation
      // making it not work when show
    } else {
      binding.laySectionItem.isEnabled = true
      binding.laySectionItem.alpha = 1f
      binding.btnFoodOption.isEnabled = true
    }
  }

  private fun bindName(food: Food): SpannableStringBuilder {
    return if (food.featured == true) {
      val hotImage = ResourcesUtil.getDrawable(
        HotImage().localize(),
        binding.root.context
      ) ?: return SpannableStringBuilder().append(food.name.localize())
      hotImage.setBounds(
        0, 0, ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_36dp),
        ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_26dp)
      )
      val imageSpan = CenteredImageSpan(hotImage)
      val spanText = SpannableStringBuilder(" ")
      spanText.append(food.name.localize())
      spanText.setSpan(imageSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_INCLUSIVE)
      spanText
    } else {
      SpannableStringBuilder().append(food.name.localize())
    }
  }

  private fun setFoodReward(food: Food) {
    binding.tvFoodPrice.let {
      it.text = "${mapper.calcCoinCount(food.point)}"
    }
  }

  private fun setFoodPrice(food: Food, restaurant: Restaurant?) {
    binding.tvFoodPrice.text = mapper.formatPriceByRestaurant(food.price, restaurant)
    binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.subText, binding.root))

    binding.tvFoodOriginalPrice.isVisible = (food.originalPrice != null)
    binding.tvFoodOriginalPrice.text = null
    food.originalPrice?.let {
      binding.tvFoodOriginalPrice.paint.flags = Paint.STRIKE_THRU_TEXT_FLAG or
        Paint.ANTI_ALIAS_FLAG
      binding.tvFoodOriginalPrice.text = mapper.formatPriceByRestaurant(it, restaurant)
      binding.tvFoodOriginalPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)

      binding.tvFoodPrice.setTextColor(ResourcesUtil.getColor(com.ricepo.style.R.color.green, binding.root))
    }
  }

  companion object {
    fun create(
      parent: ViewGroup,
      addFood: FunFoodQty,
      minusFood: FunFoodQty
    ): MenuSectionItemHolder {
      val binding = MenuSectionNormalItemBinding.inflate(LayoutInflater.from(parent.context))
      binding.root.layoutParams = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      RestViewUtils.setFoodViewSize(
        FoodSize.BIG_WIDTH, FoodSize.BIG_HEIGHT,
        binding.ivFood, binding.ivFoodBg
      )
      return MenuSectionItemHolder(binding, addFood, minusFood)
    }
  }
}
