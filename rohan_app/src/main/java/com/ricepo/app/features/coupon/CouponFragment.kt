package com.ricepo.app.features.coupon

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.ricepo.app.HomeTab
import com.ricepo.app.R
import com.ricepo.app.databinding.FragmentCouponBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.coupon.add.AddCouponFragment
import com.ricepo.app.features.coupon.data.ShowCouponCase
import com.ricepo.app.model.Coupon
import com.ricepo.app.restaurant.home.TabFragment
import com.ricepo.app.utils.activityContext
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.inputmanager.KeyboardUtil
import com.ricepo.base.model.Restaurant
import com.ricepo.style.view.hide
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest

@AndroidEntryPoint
class CouponFragment(
  val isTab: Boolean = false
) : TabFragment() {

  val viewModel: CouponViewModel by viewModels()

  private lateinit var couponOption: CouponViewModel.CouponOption

  private lateinit var binding: FragmentCouponBinding

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View {
    return FragmentCouponBinding.inflate(layoutInflater).apply {
      binding = this
    }.root
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    val intent: Intent? = requireActivity().intent
    val subtotal = intent?.getIntExtra(FeaturePageConst.PARAM_PAGE_COUPON_SUBTOTAL, 0) ?: 0
    intent?.getParcelableExtra<ShowCouponCase>(
      FeaturePageConst.PARAM_PAGE_COUPON_SHOWCASE
    )?.let {
      viewModel.setShowCouponCase(it)
    }
    if (isTab) {
      binding.ivClose.hide()
      viewModel.setShowCouponCase(ShowCouponCase.customer)
    }
    couponOption = CouponViewModel.CouponOption(
      viewModel.getShowCouponCase(),
      subtotal
    )

    viewModel.coupons = intent?.getParcelableArrayListExtra(FeaturePageConst.PARAM_PAGE_COUPON_OPTIONS)
    viewModel.restaurant = intent?.getParcelableExtra(FeaturePageConst.PARAM_RESTAURANT)

    if (viewModel.getShowCouponCase() == ShowCouponCase.customer) {
      if (!isTab) {
        requireActivity().setTitle(com.ricepo.style.R.string.my_coupon)
      }
      binding.ivClose.setImageResource(com.ricepo.style.R.drawable.ic_back)
      binding.ivAdd.apply {
        isVisible = false
        touchWithTrigger { _, _ ->
          AddCouponFragment(
            onSuccess = {
              viewModel.reloadCoupon(couponOption)
            }
          ).show(
            requireActivity().supportFragmentManager,
            ""
          )
        }
      }
    }
    setupListener()
    bindViewModel()
    observeLoginLoad()
  }

  private fun observeLoginLoad() {
    viewModel.loginState.observe(
      viewLifecycleOwner
    ) {

      couponOption.isLoginChange = it.isLoginAndVipChange && (!it.isFirstCreate)
      couponOption.isNotLogin = (it.savedCustomer == null)

      viewModel.reloadCoupon(couponOption)
    }
  }

  override fun onResume() {
    super.onResume()
    viewModel.checkLoginChange()
  }

  override fun refreshTab(type: String?) {
  }

  override fun refreshTab(tab: HomeTab) {
  }

  private fun setupListener() {
    binding.btnAddCoupon.clickWithTrigger {
      val text = binding.etAddCoupon.text.toString()
      if (text.isEmpty()) return@clickWithTrigger
      val coupon = Coupon(text, code = text)
      backWithCoupon(coupon)
    }
  }

  private fun showAddCoupon() {
    binding.rvCouponList.visibility = View.GONE
    binding.groupCouponAdd.visibility = View.VISIBLE
    binding.tvTitle.text = ""
    binding.etFocusAddCoupon.touchWithTrigger { _, _ ->
      KeyboardUtil.showKeyboard(activityContext(), binding.etAddCoupon)
    }
  }

  private fun bindViewModel() {
    viewModel.couponUpdate(requireActivity())
      .subscribe {
        // show coupons etc. and login ui model
        binding.rvCouponList.apply {
          layoutManager = LinearLayoutManager(activityContext())
          adapter = CouponAdapter(
            it, viewModel.restaurant,
            addCoupon = {
              showAddCoupon()
            },
            itemSelected = { coupon ->
              backWithCoupon(coupon)
            }
          )
        }
      }
    lifecycleScope.launchWhenResumed {
      viewModel.showRedeem.collectLatest {
        binding.ivAdd.isVisible = it
      }
    }
  }

  private fun backWithCoupon(coupon: Coupon?) {
    if (viewModel.getShowCouponCase() == ShowCouponCase.customer) {
      // if only show customer coupon goto restaurant
      coupon?.condition?.restaurant?.let {
        FeaturePageRouter.navigateMenuByEntrance(
          activityContext(), Restaurant(it),
          entrance = FeaturePageConst.PAGE_COUPON
        )
      }
    } else {
      // if coupon is valid back to last page
      requireActivity().intent.putExtra(FeaturePageConst.PARAM_PAGE_COUPON_SELECTED, coupon)
      (requireActivity() as? CouponActivity)?.backResultEvent(
        requireActivity().intent
      )
    }
  }
}
