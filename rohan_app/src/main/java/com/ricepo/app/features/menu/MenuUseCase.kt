package com.ricepo.app.features.menu

import com.ricepo.app.consts.CategoryConst
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.data.kv.GroupOrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restaurant.RestaurantMapper
import com.ricepo.app.restaurant.RestaurantRemote
import com.ricepo.base.BaseUseCase
import com.ricepo.base.extension.deepCopy
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Category
import com.ricepo.base.model.Food
import com.ricepo.base.model.MenuBean
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.model.localize
import com.ricepo.network.executor.PostExecutionThread
import io.reactivex.rxjava3.core.Single
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import java.util.Locale
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 30/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class MenuUseCase @Inject constructor(
  private val repository: RestaurantRemote,
  private val combineRepo: CombineRestApi,
  private val mapper: MenuMapper,
  private val postExecutionThread: PostExecutionThread
) : BaseUseCase() {

  /**
   * map category max item
   */
  fun associateCategoryMaxItem(categories: List<Category>) =
    categories.associate {
      it.id to it.maxItem
    }

  /**
   * construct ui models
   */
  fun constructUiModels(
    restaurant: Restaurant?,
    categories: List<Category>,
    mapMaxItem: Map<String, Int?>?,
    categoryExpand: Map<String, Int?>,
    restaurantMenu: MenuBean?,
    isSearch: Boolean
  ): List<MenuUiModel> {
    var mutableData = mutableListOf<MenuUiModel>()

    // don't contain restaurant section in menu search page
    if (!isSearch) {
      mutableData.add(MenuUiModel.RestaurantSection(restaurant ?: Restaurant()))
    }

    // header navigation data
    if ((
      restaurantMenu?.groups?.isNotEmpty() == true &&
        restaurantMenu.bundles.isNullOrEmpty()
      ) && !isSearch
    ) {
      mutableData.add(MenuUiModel.MenuNavigationSection(restaurantMenu.groups))
    }

//        val mapMaxItem = associateCategoryMaxItem(categories)
    val jumps = categories.mapIndexed { index, category ->
      MenuUiModel.MenuCategoryItem(
        restaurant, category = category,
        groups = restaurantMenu?.groups, categoryIndex = index
      )
    }

    categories.mapIndexed { categoryIndex, category ->
      val items = category.items ?: listOf()
      val groupId = category.groupId
      val navigationId = category.group?.id

      if (category.name != null) {
        mutableData.add(
          MenuUiModel.MenuCategoryItem(
            restaurant = restaurant, jumps = jumps,
            category = category, navigationId = navigationId,
            foodIndex = categoryIndex, groupId = groupId
          )
        )
      }

      // value is empty when bundle in search
      val limitKey = category.id + category.groupId + category.name?.localize()
      val limit = categoryExpand.get(limitKey) ?: category.limit ?: 0

      // reward title item
      if (category.balance != null) {
        mutableData.add(
          MenuUiModel.MenuCategoryItem(
            restaurant, balance = category.balance,
            jumps = jumps, groupId = groupId, category = category
          )
        )
      }

      when (category.type) {
        CategoryConst.GALLERY -> {
          // gallery construct
          // set food max item
          category.items = items.mapIndexed { foodIndex, food ->
            setCategoryMaxItem(food, foodIndex, category, categoryIndex, mapMaxItem)
            food
          }
          // gallery section need to display image cell and background image
          mutableData.add(
            MenuUiModel.MenuGallerySection(
              category = category,
              restaurant = restaurant, groupId = groupId
            )
          )
        }
        CategoryConst.VERTICAL -> {
          // backend vertical
          constructVerticalUiModels(
            mutableData, items, limit, category,
            categoryIndex, restaurant, mapMaxItem
          )
        }
        CategoryConst.HORIZONTAL -> {
          if (category.id == CategoryConst.FOOD_SEARCH) {
            // local food search result
            constructVerticalUiModels(
              mutableData, items, limit, category,
              categoryIndex, restaurant, mapMaxItem
            )
          } else {
            constructHorizontalUiModels(
              mutableData, items, limit, category,
              categoryIndex, restaurant, mapMaxItem
            )
          }
        }
        CategoryConst.COMBO -> {
          constructComboHorizontalUiModels(
            mutableData, items, limit, category,
            categoryIndex, restaurant, mapMaxItem
          )
        }
        else -> {
          // local search food type (category.id == CategoryConst.FOOD_SEARCH)
          constructVerticalUiModels(
            mutableData, items, limit, category,
            categoryIndex, restaurant, mapMaxItem
          )
        }
      }
    }

    // last item bottom margin
    mutableData.add(MenuUiModel.MenuBottomItem())

    return mutableData.mapIndexed { index, uiModel ->
      uiModel.itemPosition = index
      // set item position for jumps data
      if (uiModel is MenuUiModel.MenuCategoryItem) {
        uiModel.jumps?.firstOrNull {
          it.category?.id == uiModel.category?.id &&
            it.category?.name == uiModel.category?.name &&
            it.category?.groupId == uiModel.category?.groupId
        }
          ?.itemPosition = index
      }
      uiModel
    }
  }

  /**
   * the menu horizontal style
   */
  private fun constructHorizontalUiModels(
    mutableData: MutableList<MenuUiModel>,
    items: List<Food>,
    limit: Int,
    category: Category,
    categoryIndex: Int,
    restaurant: Restaurant?,
    mapMaxItem: Map<String, Int?>?
  ) {
    val groupId = category.groupId
    val rows = category.rows ?: 1

    val sectionItems = mutableListOf<MenuUiModel>()
    val rowItems = mutableListOf<MenuUiModel>()

    items.forEachIndexed { foodIndex, food ->
      setCategoryMaxItem(food, foodIndex, category, categoryIndex, mapMaxItem)

      sectionItems.add(
        MenuUiModel.MenuNormalItem(
          food,
          foodIndex, groupId = groupId, restaurant = restaurant
        )
      )
    }

    // add vertical section
    mutableData.add(
      MenuUiModel.MenuHorizontalSection(
        category = category, restaurant = restaurant,
        models = sectionItems, groupId = groupId
      )
    )
  }

  /**
   * the combo menu horizontal style
   */
  private fun constructComboHorizontalUiModels(
    mutableData: MutableList<MenuUiModel>,
    items: List<Food>,
    limit: Int,
    category: Category,
    categoryIndex: Int,
    restaurant: Restaurant?,
    mapMaxItem: Map<String, Int?>?
  ) {
    val groupId = category.groupId
    val rows = category.rows ?: 1

    val sectionItems = mutableListOf<MenuUiModel>()
    val rowItems = mutableListOf<MenuUiModel>()

    items.forEachIndexed { foodIndex, food ->
      setCategoryMaxItem(food, foodIndex, category, categoryIndex, mapMaxItem)

      sectionItems.add(
        MenuUiModel.MenuComboItem(
          food,
          foodIndex, groupId = groupId, restaurant = restaurant
        )
      )
    }

    // add vertical section
    mutableData.add(
      MenuUiModel.MenuComboHorizontalSection(
        category = category, restaurant = restaurant,
        models = sectionItems, groupId = groupId
      )
    )
  }

  /**
   * the menu vertical style
   */
  private fun constructVerticalUiModels(
    mutableData: MutableList<MenuUiModel>,
    items: List<Food>,
    limit: Int,
    category: Category,
    categoryIndex: Int,
    restaurant: Restaurant?,
    mapMaxItem: Map<String, Int?>?
  ) {
    val itemSize = items.size
    var selectedItemCount = 0
    val groupId = category.groupId

    val sectionItems = mutableListOf<MenuUiModel>()

    items.forEachIndexed { foodIndex, food ->
      setCategoryMaxItem(food, foodIndex, category, categoryIndex, mapMaxItem)
      // limit > 0 && index >= limit
      if (limit in 1..foodIndex) {
        // split show more selected food
        when {
          (food.selectedCount ?: 0) > 0 -> {
            // insert the before of show more item
            sectionItems.add(
              MenuUiModel.MenuNormalItem(
                food,
                foodIndex, groupId = groupId, restaurant = restaurant
              )
            )
            selectedItemCount += 1
          }
          foodIndex == (itemSize - 1) -> {
            sectionItems.add(
              MenuUiModel.MenuShowMoreItem(
                category = category,
                categoryIndex = categoryIndex,
                showSelectedCount = selectedItemCount, groupId = groupId
              )
            )
          }
          else -> {
            // nothing
          }
        }
      } else {
        sectionItems.add(
          MenuUiModel.MenuNormalItem(
            food,
            foodIndex, groupId = groupId, restaurant = restaurant
          )
        )
      }
    }

    // add vertical section
    mutableData.add(
      MenuUiModel.MenuVerticalSection(
        category = category, restaurant = restaurant,
        models = sectionItems, groupId = groupId
      )
    )
  }

  /**
   * set category total max item
   */
  private fun setCategoryMaxItem(
    food: Food,
    foodIndex: Int,
    category: Category,
    categoryIndex: Int,
    mapMaxItem: Map<String, Int?>?
  ) {
    val maxItem = category.maxItem
    if (maxItem != null) {
      food.category?.maxItem = maxItem
    } else {
      // top category need find maxItem
      val categoryId = food.category?.id ?: ""
      val otherMaxItem = mapMaxItem?.getOrElse(categoryId, { null })
      if (otherMaxItem != null) {
        food.category?.maxItem = otherMaxItem
      }
    }
    food.category?.categoryType = category.type
    food.category?.categoryIndex = categoryIndex
    food.category?.foodIndex = foodIndex
    food.category?.restBalance = category.balance
  }

  fun constructPromotionFood(categories: List<Category>?): ArrayList<Food>? {
    val foods = arrayListOf<Food>()

    categories?.forEach { category ->
      val maxItem = category.maxItem
      // record promotion food added automatically
      if ((maxItem ?: 0) > 0 && category.promotion == true) {
        category.items?.let {
          foods.addAll(it)
        }
      }
    }

    return foods
  }

  fun getRestaurantById(restaurantId: String, location: String?): Single<Restaurant> {
    return Single.create<Restaurant> { emitter ->
      val address = AddressCache.getAddress()
      // loc: (lng,lat)
      val loc = location ?: address?.location?.coordinates?.joinToString(",")
      val single = repository.getRestaurantById(restaurantId, loc)
      addDisposable(
        single.subscribeWith(object : DisposableSingleObserver<Restaurant>() {
          override fun onSuccess(t: Restaurant) {
            if (t != null) {
              emitter.onSuccess(t)
            }
          }

          override fun onError(e: Throwable) {
            emitter.onSuccess(Restaurant())
          }
        })
      )
    }.subscribeOn(postExecutionThread.ioScheduler)
  }

  fun getMenuBean(
    restaurantId: String,
    loc: String,
    search: String?,
    bundles: List<String>? = null,
    searches: List<String>?,
    filter: String?
  ): Single<Result<MenuBean>> {

    return Single.create<Result<MenuBean>> { emitter ->

      val single = repository.getMenu(restaurantId, loc, search, bundles, searches, filter)

      addDisposable(
        single.subscribeWith(object : DisposableSingleObserver<MenuBean>() {
          override fun onSuccess(t: MenuBean) {
            if (t != null) {
              emitter.onSuccess(Result.success(t))
            }
          }

          override fun onError(e: Throwable) {
            emitter.onSuccess(Result.failure(e))
          }
        })
      )
    }.subscribeOn(postExecutionThread.ioScheduler)
      .observeOn(postExecutionThread.mainScheduler)
  }

  /**
   * filter the local search categories
   */
  fun filterLocalSearchCategory(categories: List<Category>, searchText: String): List<Category> {

    // check the categories
    if (categories == null) return listOf()

    // filter normal and market (new vertical and horizontal)
    val localCategories = categories.filter {
      (
        it.type == CategoryConst.NORMAL || it.type == CategoryConst.MARKET ||
          it.type == CategoryConst.VERTICAL ||
          it.type == CategoryConst.HORIZONTAL
        ) &&
        // filter not reward food
        (it.balance == null)
    }

    val type = localCategories.firstOrNull()?.type ?: CategoryConst.VERTICAL

    val localSearchCategory = Category(
      id = CategoryConst.FOOD_SEARCH,
      type = type,
      name = null,
      groupId = CategoryConst.FOOD_SEARCH,
      expand = 0,
      limit = 0,
      items = listOf()
    )

    localSearchCategory.items = matchSearchCategory(localCategories, searchText)

    return listOf(localSearchCategory)
  }

  /**
   * Get matched menu items with search text
   * will hide all images for searched food items
   */
  private fun matchSearchCategory(categories: List<Category>, searchText: String): List<Food>? {
    val items = categories.fold(mutableListOf<Food>()) { result, category ->

      val categoryItems = category.items
      if (categoryItems.isNullOrEmpty()) return listOf()

      val matchedItems = categoryItems.filter {
        if (result.contains(it)) {
          false
        } else {
          isMatchSearch(it, searchText)
        }
      }

      val res = mutableListOf<Food>()
      res.addAll(result)
      if (matchedItems != null && matchedItems.isNotEmpty()) {
        res.addAll(matchedItems)
      }
      res
    }
    try {
      // sort by available and zscore for searched food items
      items.sortWith(sortSearchFood())
    } catch (e: Exception) {
      e.printStackTrace()
    }

    return searchCategoryShowImage(items.distinctBy { it.id })
  }

  /**
   * search category with image rules
   */
  private fun searchCategoryShowImage(items: List<Food>): List<Food>? {
    // the search results show image only copy value
    val items = items.map {
      it.deepCopy()
    }

    val foodItems = markFirstTwoImages(items, true)
    // sort according to whether need to display images
    return sortFoodWithImage(foodItems)
  }

  /**
   * sort food items by available and zscore
   * comparison method violates its general contract
   */
  private fun sortSearchFood(): Comparator<in Food> {
    // order by ('hasImage' 'available' 'zscore')
    return kotlin.Comparator { o1, o2 ->
      val showImage1 = if (o1?.hasImage() == true) 1 else 0
      val showImage2 = if (o2?.hasImage() == true) 1 else 0
      val available1 = if (o1?.available == true) 1 else 0
      val available2 = if (o2?.available == true) 1 else 0
      val zscore1 = o1?.zscore ?: 0.0
      val zscore2 = o2?.zscore ?: 0.0
      if (showImage1 == showImage2) {
        if (available1 == available2) {
          zscore2.compareTo(zscore1)
        } else {
          // true -> false
          // -1 not change
          // 1 change
          available2.compareTo(available1)
        }
      } else {
        showImage2.compareTo(showImage1)
      }
    }
  }

    /*
     * Get if food is matched with the search text
     */
  private fun isMatchSearch(it: Food, searchText: String): Boolean {

    val textBuilder = StringBuilder()

    // food name to json
    if (it.name != null) {
//            textBuilder.append(ParserModelFacade.toJson(it.name))
      textBuilder.append(it.name.toString())
    }

    // food description to json
    if (it.description != null) {
//            textBuilder.append(ParserModelFacade.toJson(it.description))
      textBuilder.append(it.description.toString())
    }

    return searchText.lowercase(Locale.getDefault()).let { it1 -> textBuilder.toString().lowercase(Locale.getDefault()).contains(it1) }
  }

  private fun sortFoodWithImage(foodItems: List<Food>?): List<Food>? {
    // sort according to whether need to display images
    return foodItems?.sortedByDescending { it?.isHiddenImage == false }
  }

  // mark the first two foods with images
  private fun markFirstTwoImages(items: List<Food>?, isLocalSearch: Boolean = false): List<Food>? {

    if (items?.count() == 0) {
      return items
    }

    var foodItems = mutableListOf<Food>()
    foodItems.addAll(items ?: listOf())

    var imageCount = 0

    // the maximum number of images is 2
    for (i in foodItems?.indices!!) {
      var food = foodItems[i]
      var imageFood = food
      if (food.image?.url?.isNotEmpty() == true && food.available == true && imageCount < 2) {
        imageFood.isHiddenImage = false
        // local search flag
//                imageFood.isLocalSearch = isLocalSearch
        imageCount += 1
      } else {
        imageFood.isHiddenImage = true
      }

      foodItems[i] = imageFood
    }

    return foodItems
  }

  /**
   * calibrate each restaurant's credit dish (reward item)
   */
  fun validReward(
    restaurantCart: RestaurantCart?,
    restaurant: Restaurant?,
    itemRestId: String?
  ): Cart? {
    val restaurant = restaurant ?: return null
//        val reward = restaurant.reward
//        if (reward?.enabled == true) else return null
    val cartRest = restaurantCart?.restaurant
    if (cartRest?.id == restaurant.id) else return null

    val carts = restaurantCart?.cartList ?: listOf()
    val rewards = carts.filter { it.reward == true }
    val cartMap = rewards.associateBy { it.bundleRestId }
    return cartMap[itemRestId]
  }

  /**
   * get category max item
   */
  fun getCategoryCount(
    categoryId: String?,
    restaurantCart: RestaurantCart?,
    restaurant: Restaurant?
  ): Int {
    val categoryId = categoryId ?: return 0
    val restaurant = restaurant ?: return 0
    if (restaurant.id == restaurantCart?.restaurant?.id) else return 0

    val carts = restaurantCart?.cartList ?: listOf()
    // carts saved form
    val mapCarts = carts.filter { it.categoryId == categoryId }
      .groupBy { it.id }
    var count = 0
    mapCarts.values.forEach { cs ->
      cs.distinctBy { it.qty }.forEach { cart ->
        val qty = cart.qty ?: 0
        count += qty
        count
      }
    }
    return count
  }

  /**
   * check point for food item
   */
  fun checkCardPoint(
    restaurantCart: RestaurantCart?,
    restaurantInfo: Restaurant?,
    food: Food? = null
  ): List<String>? {
    val cart = if (food != null) {
      mapper.mapCart(food, restaurantInfo?.name)
    } else {
      null
    }
    return checkCardPoint(restaurantCart, restaurantInfo, cart)
  }

  /**
   * check point for cart item
   */
  fun checkCardPoint(
    restaurantCart: RestaurantCart?,
    restaurantInfo: Restaurant?,
    cart: Cart?
  ): List<String>? {
    val restaurant = restaurantInfo ?: return null

    var carts: List<Cart> = listOf()
    val rcId = restaurantCart?.restaurant?.id
    if (rcId == restaurant.id) {
      carts = restaurantCart?.cartList ?: listOf()
    }

    var mapCart = carts.groupBy { it.bundleRestId }

    // normal restaurant to map
    if (mapCart.isEmpty()) {
      mapCart = mapOf(rcId to carts)
    }

    if (cart != null) {
      val bundleCart = carts.firstOrNull { it.bundleRestId == cart.bundleRestId }
      val balance = cart.bundleRestBalance ?: bundleCart?.bundleRestBalance ?: restaurant.reward?.balance ?: 0
      val items = mapCart[cart.bundleRestId] ?: mapCart[rcId]
      val total = items?.fold(cart.point ?: 0) { sum, item ->
        sum + (item.point ?: 0)
      } ?: 0
      return if (total <= balance) {
        null
      } else listOf()
    } else {
      val restIds = mutableListOf<String>()
      mapCart.forEach { entry ->
        val restId = entry.key
        val items = entry.value
        val bundleCart = items.firstOrNull()

        val balance = bundleCart?.bundleRestBalance ?: restaurant.reward?.balance ?: 0
        val total = items.fold(0) { sum, item ->
          sum + (item.point ?: 0)
        }
        if (total > balance && restId != null) {
          restIds.add(restId)
        }
      }
      return if (restIds.isNotEmpty()) {
        return restIds
      } else null
    }
  }

  /**
   * The following conditions need to be met simultaneously in order to be valid.
   * 1. already login
   * 2. is not a member of a group
   * 3. has enough points in this restaurant
   * 4. this restaurant's reward not empty
   */
  suspend fun checkCartReward(restaurant: Restaurant?, menu: MenuBean?): List<String>? {
    val restaurant = restaurant ?: return null
    val restCart = RestaurantCartCache.getRestaurantCartSuspend(restaurant)
    if (restaurant.id == restCart?.restaurant?.id) else return listOf()
    val rewardSections = menu?.sections?.filter { it?.type == CategoryConst.REWARD }
    if (rewardSections?.isNotEmpty() == true) else return null

    val isLogin = (CustomerCache.getCustomer() != null)
    // clear all reward when not login
    if (!isLogin) return listOf()
    val memberInGroup = GroupOrderCache.isGroupExist() && !GroupOrderCache.isOwner(restaurant?.id ?: "")
    // clear all reward when member in group
    if (memberInGroup) return listOf()

    // update new point
    restCart?.cartList?.forEach { cart ->
      val category = rewardSections.firstOrNull { cart.bundleRestId == it?.groupId }
      val newCart = category?.items?.firstOrNull { it.id == cart.id }
      if (newCart?.point != cart.point) {
        cart.point = newCart?.point
      }
    }

    return checkCardPoint(restCart, restaurant)
  }

  fun checkNeedSelectPromotionFood(
    promotionFoods: List<Food>?,
    cartList: List<Cart>?
  ): Food? {
    // filter promotion food not available
    val foods = promotionFoods?.filter { it.available != false } ?: return null
    val cartList = cartList ?: return null

    // just return anyway if has selected promotion food
    foods.forEach { food ->
      if (cartList.filter { it.id == food.id }.size > 0) {
        return null
      }
    }

    val total = getTotalPrice(cartList)
    // select the promotion food if no select promotion food
    var candidates = mutableListOf<Food>()
    // filter contains options is null or empty
    foods.filter { it.options.isNullOrEmpty() }.forEach { food ->
      val minimum = food.condition?.minimum
      if (minimum != null && minimum <= total) {
        candidates.add(food)
      }
    }

    candidates.sortBy { it.condition?.minimum ?: 0 }
    return candidates.lastOrNull()
  }

  private fun getTotalPrice(cartList: List<Cart>?): Int {
    val cartList = cartList ?: return 0

    return cartList.fold(0) { acc, item ->
      val sum = acc + (item.price * (item.qty ?: 0))
      sum
    }
  }

  fun checkAllClosed(restaurants: List<Restaurant>?): Boolean {
    val openRestaurants = restaurants?.filter {
      val closed = if (it.closed != null) {
        it.closed?.localize()
      } else {
        RestaurantMapper().mapClosed(it)
      }
      closed == null
    }
    return openRestaurants.isNullOrEmpty()
  }

  /**
   * set the food options item count and selected
   */
  fun setFoodOptionsSelected(
    cart: Cart?,
    food: Food?
  ) {
    cart?.opt?.groupBy { it.id }?.map { entities ->
      val itemId = entities.key
      val qty = entities.value.size
      food?.options?.map { option ->
        val item = option.items?.find { it.id == itemId }
        if (item != null) {
          item.count = qty
          // option selected for map total price
          val selected = option.selected ?: mutableListOf()
          selected.add(item)
          option.selected = selected
        }
      }
    }
  }

  suspend fun getMenuRecommend(
    restaurantId: String,
    foodIds: List<String>,
    bundles: java.util.ArrayList<String>?
  ): List<Food> {

    return combineRepo.getMenuRecommend(
      restaurantId,
      foodIds?.toTypedArray(), bundles?.toTypedArray()
    )
  }
}
