package com.ricepo.app.features.profile

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.add
import androidx.fragment.app.commit
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityProfileBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

//
// Created by <PERSON><PERSON> on 2/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_PROFILE)
class ProfileActivity : BaseActivity() {

  private lateinit var binding: ActivityProfileBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityProfileBinding.inflate(layoutInflater)
    setContentView(binding.root)
    supportFragmentManager.commit {
      add<ProfileFragment>(R.id.profile_container)
    }
  }

  override fun onBackPressed() {
    if (entrance == FeaturePageConst.PAGE_CHECKOUT) {
      FeaturePageRouter.navigateHome(false)
    }
    finish()
  }
}
