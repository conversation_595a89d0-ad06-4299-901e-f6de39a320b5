package com.ricepo.app.features.menu.adapter.listener

import android.app.Activity
import android.content.Intent
import android.os.Handler
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import androidx.core.app.ActivityCompat
import androidx.core.app.ActivityOptionsCompat
import androidx.core.util.Pair
import com.ricepo.app.R
import com.ricepo.app.features.menu.adapter.FoodQty
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.preview.MenuMarketPreviewActivity
import com.ricepo.app.features.menu.preview.MenuNormalPreviewActivity
import com.ricepo.base.model.Food
import com.ricepo.base.model.FoodImage
import com.ricepo.base.model.Item
import com.ricepo.base.model.Option
import com.ricepo.base.model.Restaurant
import com.ricepo.style.button.ChocolateButton
import kotlin.math.abs

open class OnChocolateListener(
  private val food: Food,
  private val position: Int,
  private val restaurant: Restaurant? = null,
  private val option: Option? = null,
  private val item: Item? = null,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty
) {

  // position: the food or gallery category position
  // sectionFoodIndex: the food index of section(gallery/vertical) food items
  var sectionFoodIndex: Int? = null

  private fun injectAddFood(btnFoodOption: ChocolateButton, food: Food) {
  }

  private fun injectMinusFood(btnFoodOption: ChocolateButton, food: Food) {
  }

  protected fun init(
    rootView: View,
    menuTouchView: ImageView?,
    btnFoodOption: ChocolateButton,
    isRootClick: Boolean = true
  ) {

    rootView.setTag(R.id.tag_food, food)

    menuTouchView?.setOnTouchListener { v, event ->
      // food changed when right plate no image to click and disable can't click
      val food = rootView.getTag(R.id.tag_food)
      if (food is Food && btnFoodOption.isEnabled) {
        touchLongAndClickListener(
          rootView, event, food, item, position,
          sectionFoodIndex, restaurant, isRootClick
        )
      } else {
        false
      }
    }

    // solved adapter only payloads with sequential addition
    val laySectionItem = rootView.findViewById(R.id.lay_section_item) ?: rootView
    laySectionItem.setOnTouchListener { v, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        if (event.action == MotionEvent.ACTION_UP) {
          if ((
            btnFoodOption.maxCount == 1 &&
              btnFoodOption.currentCount == 1
            ) ||
            !isRootClick
          ) {
            // pass
          } else {
            food.isPlus = true
            food.isMinus = false
            item?.isPlus = true
            item?.isMinus = false
            addFood(FoodQty(food, position, sectionFoodIndex, item, option, position))
          }
        }
        injectAddFood(btnFoodOption, food)
      }
      true
    }

    // add listener
    btnFoodOption.setPlusListener {
      if (btnFoodOption.maxCount == 1 &&
        btnFoodOption.currentCount == 1
      ) {
        food.isPlus = false
        food.isMinus = true
        item?.isPlus = false
        item?.isMinus = true
        minusFood(FoodQty(food, position, sectionFoodIndex, item, option, position))
      } else {
        food.isPlus = true
        food.isMinus = false
        item?.isPlus = true
        item?.isMinus = false
        addFood(FoodQty(food, position, sectionFoodIndex, item, option, position))
      }
      injectAddFood(btnFoodOption, food)
    }

    // subtract listener
    btnFoodOption.setMinusListener {
      food.isPlus = false
      food.isMinus = true
      item?.isPlus = false
      item?.isMinus = true
      minusFood(FoodQty(food, position, sectionFoodIndex, item, option, position))
      injectMinusFood(btnFoodOption, food)
    }
  }

  var startX = 0f
  var startY = 0f
  private var downLong: Long? = null

  private val mHandler = Handler()

  private fun touchLongAndClickListener(
    rootView: View,
    event: MotionEvent,
    food: Food,
    item: Item?,
    position: Int,
    sectionFoodIndex: Int?,
    restaurant: Restaurant?,
    isRootClick: Boolean
  ): Boolean {
    if (event.action == MotionEvent.ACTION_DOWN) {
      downLong = System.currentTimeMillis()
      startX = event.x
      startY = event.y
      mHandler.removeCallbacksAndMessages(null)
      mHandler.postDelayed(
        {
//                FeaturePageRouter.navigateMenuFeedback(food, view.context)
          downLong = null
          transitionPreview(rootView, food, restaurant)
        },
        500
      )
    }
    if (event.action == MotionEvent.ACTION_MOVE) {
      if (abs(event.x - startX) > 5 || abs(event.y - startY) > 5) {
        mHandler.removeCallbacksAndMessages(null)
      } else {
        // no-op
      }
    }
    if (event.action == MotionEvent.ACTION_CANCEL) {
      mHandler.removeCallbacksAndMessages(null)
    }
    if (event.action == MotionEvent.ACTION_UP) {
      mHandler.removeCallbacksAndMessages(null)
      val laySectionView = rootView.findViewById<View?>(R.id.lay_section_item)
      if (downLong != null && laySectionView?.isEnabled != false && isRootClick) {
        food.isPlus = true
        food.isMinus = false
        item?.isPlus = true
        item?.isMinus = false
        addFood(FoodQty(food, position, sectionFoodIndex, item, option, position))
      }
    }
    return true
  }

  private fun transitionPreview(rootView: View, food: Food, restaurant: Restaurant?): Boolean {
    val context = rootView.context ?: return false
    if (context is Activity) else return false
    food.image?.url ?: return false

    if (food.image?.placeholder == FoodImage.MARKET) {
      transitionToMarket(context, food, restaurant, rootView)
    } else {
      transitionToNormal(context, food, restaurant, rootView)
    }
    return true
  }

  private fun transitionToNormal(
    context: Activity,
    food: Food,
    restaurant: Restaurant?,
    rootView: View
  ) {

    val ivFood = rootView.findViewById<View?>(R.id.iv_food) ?: return
    val ivFoodBg = rootView.findViewById<View?>(R.id.iv_food_bg)

    val intent = Intent(context, MenuNormalPreviewActivity::class.java)

    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_FOOD, food)
    intent.putExtra(MenuNormalPreviewActivity.BUNDLE_MENU_PREVIEW_RESTAURANT, restaurant)

    val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
      context,
      Pair(
        ivFood,
        MenuNormalPreviewActivity.VIEW_MENU_IMAGE
      ),
      Pair(
        ivFoodBg,
        MenuNormalPreviewActivity.VIEW_MENU_BACKGROUND
      )
    )
    ActivityCompat.startActivity(context, intent, activityOptions.toBundle())
  }

  private fun transitionToMarket(
    context: Activity,
    food: Food,
    restaurant: Restaurant?,
    rootView: View
  ) {
    val ivFood = rootView.findViewById<View?>(R.id.iv_food) ?: return

    val intent = Intent(context, MenuMarketPreviewActivity::class.java)

    intent.putExtra(MenuMarketPreviewActivity.BUNDLE_MENU_PREVIEW_FOOD, food)
    intent.putExtra(MenuMarketPreviewActivity.BUNDLE_MENU_PREVIEW_RESTAURANT, restaurant)

    val activityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(
      context,
      Pair(
        ivFood,
        MenuMarketPreviewActivity.VIEW_MENU_IMAGE
      )
    )
    ActivityCompat.startActivity(context, intent, activityOptions.toBundle())
  }
}
