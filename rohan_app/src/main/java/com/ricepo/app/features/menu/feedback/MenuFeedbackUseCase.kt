package com.ricepo.app.features.menu.feedback

import com.ricepo.app.model.ImageFeedbackReq
import com.ricepo.app.restaurant.RestaurantRemote
import com.ricepo.base.BaseUseCase
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.network.resource.NetworkError
import io.reactivex.rxjava3.observers.DisposableSingleObserver
import io.reactivex.rxjava3.schedulers.Schedulers
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 21/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuFeedbackUseCase @Inject constructor(
  private val repository: RestaurantRemote,
  private val postExecutionThread: PostExecutionThread
) : BaseUseCase() {

  fun imageFeedback(
    singleObserver: DisposableSingleObserver<NetworkError>,
    req: ImageFeedbackReq
  ) {
    val single = repository.imageFeedback(req)
      .subscribeOn(Schedulers.io())
      .observeOn(postExecutionThread.mainScheduler)
    addDisposable(single.subscribeWith(singleObserver))
  }
}
