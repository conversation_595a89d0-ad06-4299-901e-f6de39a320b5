package com.ricepo.app.features.refer

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityReferBinding
import com.ricepo.app.databinding.ReferHistoryItemBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.ReferHistory
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.view.ShareRicepoUtil
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Customer
import com.ricepo.base.model.CustomerRefer
import com.ricepo.base.model.ReferShare
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

//
// Created by Thomsen on 4/8/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_REFER)
class ReferActivity : BaseActivity() {

  val viewModel: ReferViewModel by viewModels()

  private lateinit var binding: ActivityReferBinding

  private val mapper = ReferMapper()

  private var referShare: ReferShare? = null
  private var isShowRefer: Boolean = false

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityReferBinding.inflate(layoutInflater)
    setContentView(binding.root)

    setTitleSubText(ResourcesUtil.getString(com.ricepo.style.R.string.refer_title))

    setupListener()
    initReferRefresh()
    viewModel.refreshRefer(this, true)
  }

  private fun initReferRefresh() {
    lifecycleScope.launch {
      viewModel.referChannel.collectLatest { result ->
        result.fold(
          onSuccess = {
            setReferView(it)
            referShare = it.share
            if (isShowRefer) {
              showRefer()
            }
          },
          onFailure = {
            showErrorNetworkView(it.parseByBuzNetwork().message ?: "")
          }
        )
      }
    }
  }

  private fun setReferView(refer: ReferInfo) {
    binding.llReferInfo.isVisible = true
    binding.groupReferPlace.isVisible = true
    binding.tvReferTitle.text = mapper.formatPriceByRestaurant(
      refer.reward ?: 0, null
    )
    binding.tvReferPrice.text = mapper.formatPriceByRestaurant(
      refer.reward ?: 0, null, 0
    )
    binding.tvReferDescription.text = refer.description?.localize()

    binding.tvReferTotalPrice.text = mapper.formatPriceByRestaurant(
      refer.record?.amount ?: 0, null
    )
    binding.tvReferTotalNumber.text = (refer.record?.count ?: 0).toString()

    binding.clReferRecord.isVisible = refer.record != null
    binding.tvReferHistoryEmpty.isVisible = (refer.record?.history?.isNullOrEmpty() == true)

    binding.llReferHistory.removeAllViews()
    refer?.record?.history?.forEach {
      addHistoryView(it)
    }
  }

  private fun addHistoryView(history: ReferHistory) {
    val itemView = ReferHistoryItemBinding.inflate(layoutInflater)
    itemView.tvReferUser.text = history.referee
    itemView.tvReferGet.text = "+${mapper.formatPriceByRestaurant(
      history.amount ?: 0, null
    )}"

    binding.llReferHistory.addView(itemView.root)
  }

  private fun setupListener() {
    binding.btnRefer.clickWithTrigger {
      showRefer()
    }
  }

  private fun showRefer() {
    if (referShare == null) {
      // stop share and redirect to login page if not login
      FeaturePageRouter.navigateLogin(this)
    } else {
      val customer = Customer("", refer = CustomerRefer(share = referShare))
      ShareRicepoUtil.showActionSheet(customer, this) {}
    }
  }

  private fun showErrorNetworkView(message: String?) {
    showErrorView(
      binding.flMenuPage, com.ricepo.style.R.drawable.ic_error_no_network,
      com.ricepo.style.R.string.error_title_load_failed, message, com.ricepo.style.R.string.retry,
      isCleanWhenClick = true,
      View.OnClickListener {
        viewModel.refreshRefer(this, true)
      }
    )
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_LOGIN) {
        isShowRefer = true
        viewModel.refreshRefer(this, true)
      }
    }
  }
}
