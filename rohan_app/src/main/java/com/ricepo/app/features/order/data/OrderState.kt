package com.ricepo.app.features.order.data

object OrderState {
  const val CREATED: String = "created"

  const val SENT = "sent"

  const val CONFIRMED = "confirmed"

  const val PENDING = "pending"

  const val CANCELLED = "cancelled"

  const val CANCELED = "canceled"

  const val DECLINED = "declined"
}

object DeliveryState {
  const val PROCESSING = "processing"

  const val SCHEDULED = "scheduled"

  const val EN_ROUTE_TO_PICKUP = "en-route-to-pickup"

  const val AT_PICKUP = "at-pickup"

  const val COMPLETED = "completed"

  const val PICKUP_COMPLETED = "pickup-completed"

  const val EN_ROUTE_TO_DROPOFF = "en-route-to-dropoff"

  const val AT_DROPOFF = "at-dropoff"

  const val DELIVERY = "delivery"

  const val DELIVERED = "delivered"
}
