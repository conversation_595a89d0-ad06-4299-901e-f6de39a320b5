package com.ricepo.app.features.menu.submore

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.SpannableStringBuilder
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.consts.CategoryConst
import com.ricepo.app.databinding.ActivityMenuSubmoreBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.menu.MenuGroupViewModel
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.MenuViewModel
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.base.MenuBaseActivity
import com.ricepo.app.features.menu.cart.CartAdapter
import com.ricepo.app.features.menu.cart.CartUiModel
import com.ricepo.app.features.menu.data.MenuGroupStatus
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.base.consts.TabMode
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Category
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantPool
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.rv.ScrollStatePersist
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by Thomsen on 9/7/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_MENU_SUBMORE)
class MenuSubMoreActivity : MenuBaseActivity() {

  val menuViewModel: MenuViewModel by viewModels()

  val menuGroupViewModel: MenuGroupViewModel by viewModels()

  lateinit var binding: ActivityMenuSubmoreBinding

  private var menuAdapter: MenuSectionAdapter? = null

  private var cartAdapter: CartAdapter? = null

  private lateinit var mapper: MenuMapper

  private lateinit var menuSectionPersist: ScrollStatePersist

  private var category: Category? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityMenuSubmoreBinding.inflate(layoutInflater)
    setContentView(binding.root)

    mapper = MenuMapper()
    menuSectionPersist = ScrollStatePersist(savedInstanceState)

    menuRestaurant = intent.getParcelableExtra(
      FeaturePageConst.PARAM_MENU_RESTAURANT
    )

    category = intent.getParcelableExtra<Category?>(
      FeaturePageConst.PARAM_MENU_SUBMORE_CATEGORY
    )

    menuViewModel.promotionFoods = intent.getParcelableArrayListExtra(
      FeaturePageConst.PARAM_MENU_PROMO_FOODS
    )

    menuViewModel.restaurantInfo = menuRestaurant
    menuViewModel.initMenuCart(menuRestaurant)

    bindPool(menuRestaurant?.pool)

    changeMenuStatus(false)

    setSubMoreView(menuRestaurant, category)
    bindViewModel()
    initMenuRecommendCollect(binding.inMenuCart, menuViewModel)

    setupListener()
  }

  override fun onSaveInstanceState(outState: Bundle) {
    super.onSaveInstanceState(outState)
    menuSectionPersist.onSaveInstanceState(outState)
  }

  private fun setupListener() {
    binding.inMenuCart.clMenuCart.clickWithTrigger {
      menuViewModel.checkToCheckout(this, entrance)
    }
    binding.inMenuCart.rcvMenuCart.setOnTouchListener(
      CartTouchListener { isDown ->
        if (isDown && binding.inMenuCart.clMenuRecommend.isVisible) {
          resetHideRecommend(binding.inMenuCart)
        } else {
          menuViewModel.checkToCheckout(this, entrance)
        }
      }
    )
  }

  private fun setSubMoreView(restaurant: Restaurant?, category: Category?) {
    val category = category ?: return

    category.type = CategoryConst.VERTICAL

    val models = mutableListOf<MenuUiModel>()

    // category item
    if (category.balance != null) {
      models.add(
        MenuUiModel.MenuCategoryItem(
          restaurant,
          balance = category.balance, category = category
        )
      )
    } else {
      models.add(
        MenuUiModel.MenuCategoryItem(
          category = category,
          restaurant = restaurant
        )
      )
    }

    // vertical section
    val foodModels = category?.items?.mapIndexed { index, food ->
      MenuUiModel.MenuNormalItem(
        food, index, category,
        restaurant = restaurant, isRootClick = false
      )
    } ?: listOf()

    models.add(MenuUiModel.MenuVerticalSection(restaurant, foodModels, isMarginTop = false))
    models.add(MenuUiModel.MenuBottomItem())

    menuAdapter = MenuSectionAdapter(
      models, menuSectionPersist,
      addFood = { foodQty ->
        lifecycleScope.launch {
          menuViewModel.menuRestaurantCart?.restaurant = menuViewModel.restaurantInfo
          menuViewModel.addFood(foodQty.food, foodQty.position, foodQty.foodIndex)
        }
      },
      minusFood = { foodQty ->
        menuViewModel.minusFood(foodQty.food, foodQty.position, foodQty.foodIndex)
      },
      showMore = { _, _, _ ->
      },
      navMenu = {
      }
    )

    binding.rvMenuSubmore.adapter = menuAdapter
  }

  private fun bindViewModel() {
    menuViewModel.menuCheckOption
      .observe(
        this,
        Observer {

          val checkOption = it
          checkAddCartOption(checkOption)
        }
      )

    observeCartUpdated()
  }

  private fun checkAddCartOption(checkOption: MenuViewModel.CheckOption) {
    when (val pageStatus = checkOption.pageStatus ?: MenuGroupStatus.None(null, null)) {
      is MenuGroupStatus.None,
      is MenuGroupStatus.SameGroup,
      is MenuGroupStatus.SameShareGroup -> {

        when {
          FeaturePageConst.PAGE_MENU == checkOption.message -> {
            FeaturePageRouter.navigateOptions(
              this@MenuSubMoreActivity,
              checkOption.food, checkOption.restaurant,
              checkOption.selectedPosition, checkOption.galleryFoodIndex
            )
            resetHideRecommend(binding.inMenuCart)
          }
          MenuViewModel.CheckOption.MESSAGE_REFRESH == checkOption.message -> {
            val food = checkOption.food
            menuAdapter?.models?.forEachIndexed { index, model ->
              if (model is MenuUiModel.MenuVerticalSection) {
                model.models.filter { it.food?.id == food.id }?.forEach {
                  it.food?.selectedCount = food.selectedCount
                }
              }
              menuAdapter?.notifyItemChanged(1, food)
            }
          }
          checkOption.isAlert -> {
            DialogFacade.showAlert(
              this@MenuSubMoreActivity,
              checkOption.message
            )
          }
          checkOption.message.isNotEmpty() -> {
            DialogFacade.showPrompt(
              this@MenuSubMoreActivity,
              checkOption.message
            ) {
              if (checkOption.deleteCacheCart) {
                // clear the other cache cart
                menuViewModel.deleteRestaurantCart(checkOption)
              }
            }
          }
        }
      }
      is MenuGroupStatus.DiffShareGroup,
      is MenuGroupStatus.DiffGroup -> {
        // REFACTOR: extract to method
        val restaurantName =
          pageStatus?.groupInfo?.restaurant?.name?.localize() ?: ""
        DialogFacade.showPrompt(
          this@MenuSubMoreActivity,
          message = ResourcesUtil.getString(
            com.ricepo.style.R.string.group_please_exit_subtitle, restaurantName
          ),
          title = ResourcesUtil.getString(com.ricepo.style.R.string.group_please_exit_title),
          positiveId = com.ricepo.style.R.string.group_please_exit_button
        ) {
          val groupId = pageStatus?.groupInfo?.groupId
          val restId = pageStatus?.groupInfo?.restaurant?.id
          FeaturePageRouter.navigateMenuAfterSubscription(
            this@MenuSubMoreActivity,
            Restaurant(restId), groupId
          )
        }
      }
    }
  }

  private fun changeMenuStatus(isCheckJoin: Boolean) {
    // init menu status get the restaurant
    lifecycleScope.launch {
      menuGroupViewModel.checkGroupStatus(menuViewModel.restaurantInfo)
        .collectLatest { pageStatus ->
          menuViewModel.mGroupStatus = pageStatus
        }
    }
  }

  private fun observeCartUpdated() {
    // observer the food update
    menuViewModel.menuCartObserver.observe(
      this,
      Observer {

        val cartData = it

        // update group cart
        if (cartData.update) {
          lifecycleScope.launch {
            menuGroupViewModel.updateGroupCartQuantity(
              this@MenuSubMoreActivity,
              cartData.carts, menuViewModel.restaurantInfo
            )?.collectLatest { orderGroup ->
              menuGroupViewModel.updateLocalGroupInfo(menuViewModel.restaurantInfo, orderGroup)
            }
          }
        }

        // combine group carts
        lifecycleScope.launch {
          val allCarts = menuGroupViewModel.getAllCarts(
            cartData.carts,
            menuViewModel.restaurantInfo
          )

          if (allCarts.isNullOrEmpty()) {
            showCart(false, binding.inMenuCart)
            binding.inMenuCart.tvDelivery.text = ""
            menuViewModel.menuRestaurantCart?.cartList = null
          } else {
            showCart(true, binding.inMenuCart)
            val triple = mapper.mapCartUiModel(
              allCarts,
              menuViewModel.menuRestaurantCart
            )

            val price = mapper.toTotalPrice(
              triple.second,
              menuViewModel.restaurantInfo
            )
            val models = mutableListOf<CartUiModel>()
            models.add(CartUiModel.CartMenuInfoUiModel(SpannableStringBuilder(price)))
            models.addAll(triple.first)

            if (cartAdapter == null) {
              cartAdapter = CartAdapter(models)
              val cartLayoutManager = LinearLayoutManager(this@MenuSubMoreActivity)
              cartLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
              binding.inMenuCart.rcvMenuCart.apply {
                layoutManager = cartLayoutManager
                adapter = cartAdapter
              }
              // simulation of external click events
//                        binding.inMenuCart.rcvMenuCart.simulateClickListener {
//                            binding.inMenuCart.clMenuCart.performClick()
//                        }
            } else {
              cartAdapter?.models = models
              val recyclerViewState = binding.inMenuCart.rcvMenuCart.layoutManager?.onSaveInstanceState()
              cartAdapter?.notifyDataSetChanged()
              binding.inMenuCart.rcvMenuCart.layoutManager?.onRestoreInstanceState(recyclerViewState)
            }

            menuViewModel.menuRestaurantCart = triple.third

            // pickup menu don't show delivery
            if (menuViewModel.deliveryMode != TabMode.MODE_PICKUP) {
              binding.inMenuCart.tvDelivery.text = mapper.toDeliveryMessage(
                triple.second,
                menuViewModel.restaurantInfo
              )
            }
          }
        }
      }
    )
  }

  override fun refreshGroupOrderCart() {
    super.refreshGroupOrderCart()

    lifecycleScope.launch {
      // refresh group order quantity
      menuGroupViewModel.updateGroupCartQuantity(menuViewModel.restaurantInfo)?.collectLatest { result ->
        if (menuGroupViewModel.groupId == null) {
          cancel()
        } else {
          result.fold(
            onSuccess = {
              reloadMenu()
            },
            onFailure = { error ->
              menuGroupViewModel.handleGroupError(
                this@MenuSubMoreActivity,
                menuRestaurant, error
              ) {
                // re create group order when last group order closed
                // groupId = null
                changeMenuStatus(false)
                reloadMenu()
              }
            }
          )
        }
      }
    }
  }

  private fun reloadMenu() {
    menuViewModel.initMenuCart(menuViewModel.restaurantInfo)

    val category = category ?: return
    lifecycleScope.launch {
      val categories = withContext(Dispatchers.IO) {
        menuViewModel.updateFoodQuantity(listOf(category))
      }
      categories?.forEach {
        setSubMoreView(menuViewModel.restaurantInfo, it)
      }
    }
  }

  private fun bindPool(pool: RestaurantPool?) {
    val pool = pool ?: return
    binding.rtvPool.isVisible = true
    binding.rtvPool.setMessage(pool?.message?.localize())
    binding.rtvPool.setExpireAt(pool?.expiresAt)
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    if (resultCode == Activity.RESULT_OK) {
      if (requestCode == FeaturePageConst.REQUEST_CODE_MENU_OPTIONS) {
        val food = data?.getParcelableExtra<Food>(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD)
        lifecycleScope.launch {
          // options select position
          menuViewModel.addFood(food, -1, null, false)
        }
      }
      if (requestCode == FeaturePageConst.REQUEST_CODE_CHECKOUT) {
        resetHideRecommend(binding.inMenuCart)
        reloadMenu()
        changeMenuStatus(false)
      }
    }
  }

  override fun onBackPressed() {
    backResultEvent(intent)
  }
}
