package com.ricepo.app.features.points

import androidx.activity.ComponentActivity
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.PointsSummaryModel
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.viewmodel.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 6/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
@HiltViewModel
class PointsViewModel @Inject constructor(
  private val useCase: PointsUseCase
) : BaseViewModel() {

  fun getCoins(context: ComponentActivity, errorHandle: (message: String) -> Unit):
    Flow<List<PointsSummaryModel>?> {
    return flowLoading(
      context,
      error = {
        errorHandle(it.parseByBuzNetwork().message ?: "")
      }
    ) {
      emit(useCase.getMyPoints())
    }
  }
}
