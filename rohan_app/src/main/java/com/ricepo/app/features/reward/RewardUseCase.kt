package com.ricepo.app.features.reward

import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.model.RewardSummaryModel
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseUseCase
import javax.inject.Inject

//
// Created by Thomsen on 6/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RewardUseCase @Inject constructor(
  private val repository: CombineRestApi
) : BaseUseCase() {

  /**
   * get customer reward coins
   */
  suspend fun getMyCoins(): List<RewardSummaryModel>? {

    val customer = CustomerCache.getCustomerSuspend()
    val customerId = customer?.id ?: return null

    return repository.getRewardsByCustomer(customer.id)
  }
}
