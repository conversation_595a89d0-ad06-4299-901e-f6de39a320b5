package com.ricepo.app.features.address

import android.os.Bundle
import android.text.TextUtils
import android.text.method.TextKeyListener
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityAddressBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.base.BaseActivity
import com.ricepo.base.adapter.BindListAdapter
import com.ricepo.base.adapter.OnBindViewListener
import com.ricepo.base.animation.Loading
import com.ricepo.base.view.DialogFacade
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.map.model.PlaceAutocompleteResult
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.animateFade
import com.ricepo.style.view.hide
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.subjects.PublishSubject

//
// Created by Thomsen on 7/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_ADDRESS)
class AddressActivity : BaseActivity() {

  /**
   * the address view model is auto inject by dagger in @link ActivityModule
   */

  val addressViewModel: AddressViewModel by viewModels()

  /**
   * the page status and data show by viewmodel output object controller
   */
  lateinit var addressOutput: AddressViewModel.Output

  /**
   * the address search input object
   */
  val aSearchText = PublishSubject.create<CharSequence>()

  /**
   * the prediction address select input object
   */
  val aPredictionSelected = PublishSubject.create<PlaceAutocompleteResult>()

  /**
   * the history address select input object
   */
  val aHistorySelected = PublishSubject.create<FormatUserAddress>()

  /**
   * the dispose controller of the bottom type status
   */
  private var disposableBottomType: Disposable? = null

  /**
   * the dispose controller of the prediction request
   */
  private var disposablePrediction: Disposable? = null

  /**
   * the dispose controller of the place detail request
   */
  private var disposablePlaceDetail: Disposable? = null

  /**
   * to save the formatted address
   */
  private var formatSelectAddress: FormatUserAddress? = null

  private lateinit var binding: ActivityAddressBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    binding = ActivityAddressBinding.inflate(layoutInflater)
    setContentView(binding.root)

    // bind viewmodel input
    bindViewModelInput()

    bindSaveAble()

    // bind viewmodel output
    bindViewModelOutput()
  }

  private fun bindSaveAble() {
    addressViewModel.saveAble.observe(this) {
      binding.btnSaveAddress.isEnabled = it
      binding.btnSaveAddress.alpha = if (it) 1.0f else 0.2f
    }
    addressViewModel.showPostCOde.observe(this) {
      binding.etAddressCode.isVisible = it
    }
    addressViewModel.showPostChangeResult.observe(this) {
      PostCodeChangeDialog(it) {
        backResultEvent(intent)
      }.show(supportFragmentManager, null)
    }
  }

  /**
   * the view model input construct and operator listener
   */
  private fun bindViewModelInput() {
    // view model input object
    val aOnSave = PublishSubject.create<Boolean>()
    val aAptText = PublishSubject.create<String>()
    val aNoteText = PublishSubject.create<String>()
    val aZipCodeText = PublishSubject.create<String>()

    bindAddressEdit()
    // first load view to request focus
    binding.etAddress.requestFocus()
    binding.etAddress.onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
      v as EditText
      if (hasFocus) {
        aSearchText.onNext(v.editableText.toString())
        v.keyListener = TextKeyListener(TextKeyListener.Capitalize.NONE, false)
        v.ellipsize = null
      } else {
        v.keyListener = null
        v.ellipsize = TextUtils.TruncateAt.END
      }
    }

    binding.etAddressApt.onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
      v as EditText
      if (hasFocus) {
        v.keyListener = TextKeyListener(TextKeyListener.Capitalize.NONE, false)
        v.ellipsize = null
      } else {
        v.keyListener = null
        v.ellipsize = TextUtils.TruncateAt.END
      }
    }

    binding.etAddressNote.onFocusChangeListener = View.OnFocusChangeListener { v, hasFocus ->
      v as EditText
      if (hasFocus) {
        v.keyListener = TextKeyListener(TextKeyListener.Capitalize.NONE, false)
        v.ellipsize = null
      } else {
        v.keyListener = null
        v.ellipsize = TextUtils.TruncateAt.END
      }
    }

    // save address event
    binding.btnSaveAddress.setOnClickListener {
      // judge the apartment number
      aOnSave.onNext(true)
    }

    // enter apartment number
    binding.etAddressApt.doAfterTextChanged {
      aAptText.onNext(it?.toString() ?: "")
    }

    // enter address note
    binding.etAddressNote.doAfterTextChanged {
      aNoteText.onNext(it?.toString() ?: "")
    }

    binding.etAddressCode.doAfterTextChanged {
      aZipCodeText.onNext(it?.toString() ?: "")
    }

    // bind input model transform output model
    addressOutput = addressViewModel.transform(
      AddressViewModel.Input(
        aSearchText,
        aPredictionSelected,
        aOnSave,
        aAptText,
        aNoteText,
        aZipCodeText,
        aHistorySelected,
        onLoading = {
          Loading.showLoading(this)
        },
        dismissLoading = {
          Loading.hideLoading()
        },
        onError = {
          DialogFacade.showPrompt(
            context = this,
            message = it ?: ResourcesUtil.getString(com.ricepo.style.R.string.error_load_failed)
          )
        }
      ) {
        backResultEvent(intent)
      }
    )
  }

  /**
   * the view model output change page status
   */
  private fun bindViewModelOutput() {
    disposableBottomType = addressOutput.showAddressBottom.subscribe {
      when (it) {
        AddressViewModel.AddressUiType.ShowHistory -> {
          binding.llAddressHistory.animateFade()
        }
        AddressViewModel.AddressUiType.HideHistory -> {
          binding.llAddressHistory.hide()
        }
        AddressViewModel.AddressUiType.ShowPredictions -> {
          binding.llAddressPrediction.animateFade()
        }
        AddressViewModel.AddressUiType.HidePredictions -> {
          binding.llAddressPrediction.hide()
        }
        AddressViewModel.AddressUiType.ShowForm -> {
          binding.llAddressForm.animateFade()
        }
        AddressViewModel.AddressUiType.HideForm -> {
          binding.llAddressForm.hide()
        }
      }
    }

    val viewManager = LinearLayoutManager(this@AddressActivity)
    val predictionAdapter = BindListAdapter<TextView, PlaceAutocompleteResult>(
      null, com.ricepo.base.R.layout.view_string_item,
      object : OnBindViewListener<TextView, PlaceAutocompleteResult> {
        override fun onBindView(
          view: TextView,
          value: PlaceAutocompleteResult?,
          position: Int
        ) {
          view.text = value?.description
          view.maxLines = 1
          view.ellipsize = TextUtils.TruncateAt.END
          view.setOnClickListener {
            binding.etAddress.clearFocus()
            if (value != null) {
              aPredictionSelected.onNext(value)
            }
          }
        }
      }
    )

    binding.rvAddressPrediction.apply {
      // use this setting to improve performance if you know that changes
      // in content do not change the layout size of the RecyclerView
      setHasFixedSize(true)

      // use a linear layout manager
      layoutManager = viewManager

      // specify an viewAdapter (see also next example)
      adapter = predictionAdapter
    }

    disposablePrediction = addressOutput.predictions.subscribe {
      predictionAdapter.setData(it)
    }

    val historyAdapter = BindListAdapter(
      null, R.layout.item_address_history,
      object : OnBindViewListener<LinearLayout, FormatUserAddress> {
        override fun onBindView(
          view: LinearLayout,
          value: FormatUserAddress?,
          position: Int
        ) {
          view.setOnClickListener {
            if (value != null) {
              aHistorySelected.onNext(value)
            }
          }
          view.findViewById<TextView>(R.id.tv_address_name).text = value?.name
          if (value?.unit.isNullOrEmpty() && value?.note.isNullOrEmpty()) {
            view.findViewById<TextView>(R.id.tv_address_unit).visibility = View.GONE
          } else {
            val subtitle = StringBuilder(value?.unit ?: "")
            if (value?.unit?.isNotBlank() == true && value.note?.isNotBlank() == true) {
              subtitle.append(" · ")
            }
            subtitle.append(value?.note ?: "")

            view.findViewById<TextView>(R.id.tv_address_unit).text = subtitle.toString()
          }
        }
      }
    )

    binding.rvAddressHistory.apply {
      // improve performance
      setHasFixedSize(true)

      // use a linear layout manager is only attached to a RecyclerView
      layoutManager = LinearLayoutManager(this@AddressActivity)

      // specify an viewAdapter (see also next example)
      adapter = historyAdapter
    }

    addressOutput.historyData.observe(
      this
    ) {
      historyAdapter.setData(it)
    }

    disposablePlaceDetail = addressOutput.addressUpdated.subscribe(
      {
        formatSelectAddress = it
        with(binding) {
          // setText don't call text watcher
          shouldResponse = false
          etAddress.setText(it.formatted)
          etAddress.clearFocus()
          shouldResponse = true

          process(etAddressApt, it.unit)
          process(etAddressNote, it.note)
          process(etAddressCode, it.zipcode)
        }
      },
      {
      }
    )
  }

  private fun process(et: EditText, text: String?) {
    et.requestFocus()
    et.setText(text ?: "")
    et.clearFocus()
  }

  private var shouldResponse = true
  private fun bindAddressEdit() {
    binding.etAddress.doAfterTextChanged {
      if (shouldResponse) {
        if (it != null) {
          aSearchText.onNext(it)
        }
      }
    }
  }

  override fun onDestroy() {
    super.onDestroy()

    // dispose subscribe
    disposableBottomType?.dispose()
    disposablePrediction?.dispose()
    disposablePlaceDetail?.dispose()
  }
}
