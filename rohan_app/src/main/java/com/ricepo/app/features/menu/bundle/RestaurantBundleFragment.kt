package com.ricepo.app.features.menu.bundle

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.ricepo.app.R
import com.ricepo.app.databinding.FragmentBundleRestaurantBinding
import com.ricepo.app.listener.PromotionMarqueeListener
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.RestaurantAdapter
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantGroup
import com.ricepo.style.DisplayUtil
import com.ricepo.style.sheet.RoundedBottomSheetDialogFragment

//
// Created by <PERSON><PERSON> on 9/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RestaurantBundleFragment(
  private val hostRestaurant: Restaurant? = null,
  private val restaurants: List<Restaurant> = listOf(),
  private var bundles: ArrayList<String>? = null,
  private var isHalf: Boolean = true,
  private val confirm: (value: ArrayList<String>?) -> Unit = {},
  private val refreshMenu: () -> Unit = {}
) : RoundedBottomSheetDialogFragment() {

  companion object {
    val TAG = "menu_bundle_restaurant"
  }

  private lateinit var binding: FragmentBundleRestaurantBinding

  private var behavior: BottomSheetBehavior<View>? = null

  private var isHalfState: Boolean = false

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View? {
    binding = FragmentBundleRestaurantBinding.inflate(inflater)
    return binding.root
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    showRestaurantView()
    setupListener()
    setParams()
  }

  private fun setParams() {
    val dialog = dialog ?: return

    val bottomSheet = dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
    if (bottomSheet != null) {
      // show the corner
      (bottomSheet.parent as View).setBackgroundColor(Color.TRANSPARENT)

      val layoutParams = bottomSheet.layoutParams as CoordinatorLayout.LayoutParams
      layoutParams.height = DisplayUtil.getScreenHeight()
      layoutParams.width = DisplayUtil.getScreenWidth()
      bottomSheet.layoutParams = layoutParams
    }

    view?.let {
      it.post {
        val parent = it.parent as View
        val params = parent.layoutParams as CoordinatorLayout.LayoutParams
        behavior = params.behavior as BottomSheetBehavior

        behavior?.isHideable = true
        behavior?.peekHeight = peekHeight()
        behavior?.addBottomSheetCallback(bottomSheetCallback)
      }
    }
  }

  private val bottomSheetCallback = object : BottomSheetBehavior.BottomSheetCallback() {
    override fun onStateChanged(bottomSheet: View, newState: Int) {
      when (newState) {
        BottomSheetBehavior.STATE_DRAGGING -> {
        }
        BottomSheetBehavior.STATE_SETTLING -> {
        }
        BottomSheetBehavior.STATE_EXPANDED -> {
          isHalfState = false
        }
        BottomSheetBehavior.STATE_COLLAPSED -> {
          isHalfState = true
          dismiss()
        }
      }
    }

    override fun onSlide(bottomSheet: View, slideOffset: Float) {
    }
  }

  private fun peekHeight(): Int {
    val peekHeight = DisplayUtil.getScreenHeight()
    isHalfState = isHalf
    return if (isHalf) {
      peekHeight - peekHeight / 2
    } else {
      peekHeight
    }
  }

  /**
   * show the bundle restaurant select popup page
   */
  private fun showRestaurantView() {
    val uiModels: MutableList<RestaurantUiModel> = mutableListOf()
    val models = restaurants.mapIndexed { index, restaurant ->
      RestaurantUiModel.RestaurantBundle(
        restaurant, hostRestaurant,
        refreshMenu = refreshMenu
      )
    }
    uiModels.add(
      RestaurantUiModel.RestaurantCategory(
        group = RestaurantGroup(
          name = InternationalizationContent(
            "選擇拼單飯店", "选择拼单饭店", "Choose Bundle Restaurant", "Elija el restaurante del paquete"
          ),
          description = InternationalizationContent(
            "同時享受多家餐館的美味",
            "同时享受多家餐馆的美味", "Order From Multiple Restaurants", "Pedir a varios restaurantes"
          )
        ),
        position = 0, isSubMore = true
      )
    )
    uiModels.addAll(models)
    var restaurantAdapter: RestaurantAdapter? = null
    restaurantAdapter = RestaurantAdapter(uiModels, this) { it, _ ->
      if (it is RestaurantUiModel.RestaurantBundle) {
        bundles = it.bundles
        restaurantAdapter?.setBundles(it.bundles)
      }
      if (it is RestaurantUiModel.RestaurantBigImageBundle) {
        bundles = it.bundles
        restaurantAdapter?.setBundles(it.bundles)
      }
      restaurantAdapter?.notifyDataSetChanged()
    }
    restaurantAdapter.setBundles(bundles)
    binding.rvBundleRestaurant.apply {
      adapter = restaurantAdapter
      layoutManager = HalfLinearLayoutManager(context, isHalf)
      itemAnimator = null
    }

    binding.rvBundleRestaurant.post {
      PromotionMarqueeListener.setPromotionMarquee(binding.rvBundleRestaurant)
    }
  }

  private fun setupListener() {
    binding.rvBundleRestaurant.addOnScrollListener(object : RecyclerView.OnScrollListener() {
      override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
          val layoutManager = recyclerView.layoutManager
          if (layoutManager is LinearLayoutManager) {
            try {
              val start = layoutManager.findFirstVisibleItemPosition()
              val end = layoutManager.findLastVisibleItemPosition()
              val count = end - start
              recyclerView.adapter?.notifyItemRangeChanged(start, count)
            } catch (e: Exception) {
              e.printStackTrace()
            }
          }
        }
      }

      override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        if (dy > 0.5 && isHalfState) {
          behavior?.state = BottomSheetBehavior.STATE_EXPANDED
        }
      }
    })

    binding.inTitle.ivClose.clickWithTrigger {
      dismiss()
    }

    binding.btnBundleConfirm.clickWithTrigger {
      confirm(bundles)
      dismiss()
    }

    // promotion marquee
    binding.rvBundleRestaurant.addOnScrollListener(PromotionMarqueeListener())
  }

  override fun show(manager: FragmentManager, tag: String?) {
    try {
      val bundleFragment = manager.findFragmentByTag(tag)
      val ft = manager.beginTransaction()
      if (bundleFragment?.isAdded == true) {
        ft.show(bundleFragment)
      } else {
        ft.remove(this).commitAllowingStateLoss()
        ft.add(this, tag)
        ft.commitAllowingStateLoss()
      }
    } catch (e: Exception) {
      // Fragment already added
      e.printStackTrace()
    }
  }

  fun setBundles(bundles: ArrayList<String>?) {
    this.bundles = bundles
  }

  class HalfLinearLayoutManager(
    private val context: Context,
    private val isHalf: Boolean = true
  ) : LinearLayoutManager(context) {

    override fun canScrollVertically(): Boolean {
      return super.canScrollVertically()
    }
  }
}
