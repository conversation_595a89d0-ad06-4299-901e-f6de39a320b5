package com.ricepo.app.features.coupon

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.doOnDetach
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.databinding.CommonItemLoginBinding
import com.ricepo.app.databinding.CouponItemLabelBinding
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.model.Coupon
import com.ricepo.base.adapter.EmptyViewHolder
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 4/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class CouponAdapter(
  private val coupons: List<CouponUiModel>,
  private val restaurant: Restaurant?,
  private val addCoupon: () -> Unit,
  private val itemSelected: (coupon: Coupon) -> Unit
) :
  RecyclerView.Adapter<RecyclerView.ViewHolder>() {

  override fun getItemViewType(position: Int): Int {
    val model = coupons[position]
    return when (model) {
      is CouponUiModel.CouponItem -> R.layout.coupon_item
      is CouponUiModel.EmptyItem,
      is CouponUiModel.AddItem -> R.layout.coupon_item_label
      is CouponUiModel.LoginItem -> R.layout.common_item_login
      else -> 0
    }
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
    return if (viewType == R.layout.coupon_item) {
      val contentView = LayoutInflater.from(parent.context).inflate(R.layout.coupon_item, parent, false)
      CouponViewHolder(contentView, restaurant, itemSelected)
    } else if (viewType == R.layout.coupon_item_label) {
      val binding = CouponItemLabelBinding.inflate(LayoutInflater.from(parent.context))
      binding.root.layoutParams = LinearLayout.LayoutParams(
        LinearLayout.LayoutParams.MATCH_PARENT,
        LinearLayout.LayoutParams.WRAP_CONTENT
      )
      LabelViewHolder(binding)
    } else if (viewType == R.layout.common_item_login) {
      val binding = CommonItemLoginBinding.inflate(LayoutInflater.from(parent.context))
      binding.root.layoutParams = LinearLayout.LayoutParams(
        LinearLayout.LayoutParams.MATCH_PARENT,
        LinearLayout.LayoutParams.WRAP_CONTENT
      )
      LoginViewHolder(binding)
    } else {
      EmptyViewHolder.create(parent)
    }
  }

  override fun getItemCount(): Int = coupons.size

  override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
    val coupon = coupons[position]
    if (holder is CouponViewHolder) {
      holder.bind(coupon)
    }
    if (holder is LabelViewHolder) {
      holder.bind(coupon, addCoupon)
    }
    if (holder is LoginViewHolder) {
      holder.bind(coupon)
    }
  }

  class LoginViewHolder constructor(val binding: CommonItemLoginBinding) :
    RecyclerView.ViewHolder(binding.root) {

    fun bind(coupon: CouponUiModel) {
      if (coupon is CouponUiModel.LoginItem) {
        binding.btnLogin.clickWithTrigger {
          FeaturePageRouter.navigateLogin(it.context)
        }
      }
    }
  }

  class LabelViewHolder constructor(val binding: CouponItemLabelBinding) :
    RecyclerView.ViewHolder(binding.root) {
    fun bind(couponUiModel: CouponUiModel, addCoupon: () -> Unit) {
      if (couponUiModel is CouponUiModel.AddItem) {
        binding.tvItemLabel.text = couponUiModel.label
        binding.root.clickWithTrigger {
          addCoupon()
        }
      }
      if (couponUiModel is CouponUiModel.EmptyItem) {
        binding.tvItemLabel.text = couponUiModel.label
      }
    }
  }

  class CouponViewHolder constructor(
    private val view: View,
    private val restaurant: Restaurant?,
    private val itemSelected: (coupon: Coupon) -> Unit
  ) : RecyclerView.ViewHolder(view) {

    private val mapper = CouponMapper()

    fun bind(couponUiModel: CouponUiModel) {

      val coupon = (couponUiModel as CouponUiModel.CouponItem).data

      view.findViewById<TextView>(R.id.tv_coupon_code).text = coupon.code

      view.findViewById<TextView>(R.id.tv_coupon_detail).text = mapper.mapDetailExpires(coupon)

      val condition = coupon.detail?.localize()
      view.findViewById<TextView>(R.id.tv_coupon_condition).text = condition
      view.findViewById<TextView>(R.id.tv_coupon_condition).isVisible = !condition.isNullOrEmpty()

      val job = CoroutineScope(Dispatchers.IO).launch {
        val restaurant = withContext(Dispatchers.IO) {
          val restaurantCart = RestaurantCartCache.getRestaurantCart(restaurant)
          restaurantCart?.restaurant
        }

        withContext(Dispatchers.Main) {
          var text = "- ${mapper.formatPriceByRestaurant(coupon.amount ?: 0, restaurant)}"
          val percentage = coupon.percentage ?: 0.0
          if (percentage > 0) {
            text = "-${percentage.times(100).toInt()}%"
          }
          view.findViewById<TextView>(R.id.tv_coupon_price).text = text
        }
      }

      view.post {
        view.doOnDetach {
          job.cancel()
        }
      }

      if (coupon.invalid == true) {
        view.rootView.alpha = 0.2f
        view.setOnClickListener(null)
      } else {
        view.rootView.alpha = 1f
        view.clickWithTrigger {
          itemSelected(coupon)
        }
      }
    }
  }
}
