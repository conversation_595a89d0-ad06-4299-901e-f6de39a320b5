package com.ricepo.app.features.menu.bundle

import android.util.ArrayMap
import android.view.ContextThemeWrapper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.databinding.RestaurantBigImageItemVerticalBinding
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.app.restaurant.adapter.holder.FunRestaurantMenuClick
import com.ricepo.app.restaurant.adapter.holder.RestaurantVerticalBigImageHolder
import com.ricepo.base.model.Restaurant
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 11/9/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * using on menu page bottom sheet and search page
 */
class RestaurantBigImageBundleHolder(
  private val binding: RestaurantBigImageItemVerticalBinding,
  private val bundleSelected: ArrayMap<String, Boolean>,
  private val lifecycleOwner: LifecycleOwner,
  private val itemClick: FunRestaurantMenuClick = { _, _ -> }
) :
  RecyclerView.ViewHolder(binding.root) {

  init {
    // bind listener
    binding.root.setOnClickListener { view ->
      selectItem(view)
    }
  }

  private fun selectItem(view: View) {
    val item = view.tag
    if (item is RestaurantUiModel.RestaurantBigImageBundle) {
      val restaurantId = item.restaurant.id ?: return
      //  restaurant can click when already selected and closed or not closed
      if (binding.root.alpha != 1f && !bundleSelected.contains(restaurantId)) return
      if (bundleSelected.containsKey(restaurantId)) {
        handleCarts(item, restaurantId, view)
      } else {
        bindCheckSelected()
        addBundle(item, restaurantId)
      }
    }
  }

  private fun handleCarts(
    item: RestaurantUiModel.RestaurantBigImageBundle,
    restaurantId: String,
    view: View,
  ) {
    lifecycleOwner.lifecycle.coroutineScope.launch {
      val restaurantCart = withContext(Dispatchers.IO) {
        RestaurantCartCache.getRestaurantCartSuspend(item.hostRestaurant)
      }
      val isCarts = (
        restaurantCart?.cartList?.filter {
          it.bundleRestId == restaurantId
        }?.size ?: 0
        ) > 0
      if (isCarts) {
        val message = ResourcesUtil.getString(com.ricepo.style.R.string.bundle_remove_alert)
        val context = if (view.context is ContextThemeWrapper) {
          (view.context as ContextThemeWrapper).baseContext
        } else {
          view.context
        }
        DialogFacade.showPrompt(context, message) {
          lifecycleOwner.lifecycle.coroutineScope.launch {
            withContext(Dispatchers.IO) {
              RestaurantCartCache.deleteCartsWithRestaurantId(
                item.hostRestaurant, item.restaurant
              )
            }
            bindCheckNormal()
            item.refreshMenu()
            removeBundle(item, restaurantId)
          }
        }
      } else {
        bindCheckNormal()
        removeBundle(item, restaurantId)
      }
    }
  }

  private fun addBundle(item: RestaurantUiModel.RestaurantBigImageBundle, restaurantId: String) {
    bundleSelected[restaurantId] = true
    item.bundles = bundleSelected.keys.distinct()?.toCollection(ArrayList())
    itemClick(item, null)
  }

  private fun removeBundle(item: RestaurantUiModel.RestaurantBigImageBundle, restaurantId: String) {
    bundleSelected.remove(restaurantId)
    item.bundles = bundleSelected.keys.distinct()?.toCollection(ArrayList())
    itemClick(item, null)
  }

  private fun bindCheck(restaurant: Restaurant) {
    if (bundleSelected.containsKey(restaurant.id)) {
      bindCheckSelected()
    } else {
      bindCheckNormal()
    }
  }

  private fun bindCheckNormal() {
    binding.btnRestaurantBundle.setSelectedCount(0)
  }

  private fun bindCheckSelected() {
    binding.btnRestaurantBundle.setSelectedCount(1)
  }

  fun bind(item: RestaurantUiModel.RestaurantBigImageBundle, position: Int) {

    binding.btnRestaurantBundle.apply {
      isVisible = true
      maxCount = 1
    }
    bindCheck(item.restaurant)

    val holder = RestaurantVerticalBigImageHolder(
      binding,
    )

    holder.bind(
      RestaurantUiModel.RestaurantBigImageVertical(
        item.restaurant, position, 0
      )
    )

    // reset the layout and recyclerview tag
    binding.root.tag = item

    // the restaurant closed can't select
    val mapper = RestaurantBindMapper(item.restaurant)
    val pairClosed = mapper.bindClosed()
    if ((pairClosed.first != null) || item.restaurant.hourlyClosed == true) {
      binding.root.alpha = 0.2f
    } else {
      binding.root.alpha = 1f
    }
  }

  companion object {
    fun create(
      parent: ViewGroup,
      cacheSelected: ArrayMap<String, Boolean>,
      lifecycleOwner: LifecycleOwner,
      itemClick: FunRestaurantMenuClick
    ): RestaurantBigImageBundleHolder {
      val binding = RestaurantBigImageItemVerticalBinding.inflate(LayoutInflater.from(parent.context))

      RestaurantVerticalBigImageHolder.initRestaurantVerticalBinding(
        binding, false
      ) { _, _ -> }

      return RestaurantBigImageBundleHolder(
        binding,
        cacheSelected, lifecycleOwner, itemClick
      )
    }
  }
}
