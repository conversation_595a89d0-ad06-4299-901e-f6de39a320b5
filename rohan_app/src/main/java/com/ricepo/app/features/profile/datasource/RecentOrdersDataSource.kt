package com.ricepo.app.features.profile.datasource

import androidx.paging.PagingSource
import com.ricepo.app.model.Order
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restapi.loadResultError
import com.ricepo.app.restaurant.CustomerOrderType

//
// Created by Thomsen on 3/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class RecentOrdersDataSource constructor(
  private val repository: CombineRestApi,
  private val customerId: String?
) : PagingSource<Int, Order>() {

  override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Order> {
    val customerId = customerId ?: ""
    return try {
      var recentParams = mapOf<String, Any>(
        "type" to CustomerOrderType.recent
      )
      val recentOrders = repository.getOrders(customerId, recentParams)

      LoadResult.Page(
        data = recentOrders,
        prevKey = null,
        nextKey = null
      )
    } catch (e: Exception) {
      loadResultError(e)
    }
  }
}
