package com.ricepo.app.features.profile.datasource

import androidx.paging.PagingSource
import com.ricepo.app.model.Order
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restapi.loadResultError
import com.ricepo.app.restaurant.CustomerOrderType

//
// Created by <PERSON><PERSON> on 3/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class HistoryOrdersDataSource constructor(
  private val repository: CombineRestApi,
  private val customerId: String?
) : PagingSource<Int, Order>() {

  private val PAGE_INDEX_STARTING = 0

  val PAGE_SIZE = 30

  override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Order> {
    val position = params.key ?: PAGE_INDEX_STARTING
    val customerId = customerId ?: ""
    return try {
      val historyParams = mapOf<String, Any>(
        "limit" to PAGE_SIZE,
        "type" to CustomerOrderType.history
      )
      val historyOrders = repository.getOrders(customerId, historyParams)

      LoadResult.Page(
        data = historyOrders,
        prevKey = if (position == PAGE_INDEX_STARTING) null else position - 1,
        nextKey = if (historyOrders.isNullOrEmpty()) null else position + 1
      )
    } catch (e: Exception) {
      loadResultError(e)
    }
  }
}
