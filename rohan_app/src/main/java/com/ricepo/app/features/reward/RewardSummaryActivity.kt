package com.ricepo.app.features.reward

import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityRewardSummaryBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.address.AddressCache
import com.ricepo.app.features.reward.adapter.RewardSummaryAdapter
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.mapper.BaseMapper
import com.ricepo.base.tools.StringUtils
import com.ricepo.style.ResourcesUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 5/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_REWARD_SUMMARY)
class RewardSummaryActivity : BaseActivity() {

  val viewModel: RewardViewModel by viewModels()

  lateinit var binding: ActivityRewardSummaryBinding

  private var isLoaded = false

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    binding = ActivityRewardSummaryBinding.inflate(layoutInflater)
    setContentView(binding.root)
//        setTitle(com.ricepo.style.R.string.restaurant_reward)

    setRewardView()
    setupListener()
  }

  override fun onResume() {
    super.onResume()
    viewModel.checkLogin().observe(
      this,
      Observer {
        binding.btnLogin.isVisible = it.isNullOrEmpty()
        binding.nslCoins.isVisible = !it.isNullOrEmpty()

        // get coins
        reloadCoins()
      }
    )
  }

  private fun setupListener() {
    binding.btnLogin.clickWithTrigger {
      FeaturePageRouter.navigateLogin()
    }
  }

  private fun reloadCoins() {
    if (isLoaded) return
    lifecycleScope.launch {
      viewModel.getCoins(this@RewardSummaryActivity) {
        showErrorNetworkView(it)
      }.collectLatest { rewards ->
        if (rewards?.isNullOrEmpty() == false) {
          val adapter = RewardSummaryAdapter(rewards)
          binding.rvCoinsList.adapter = adapter
          isLoaded = true
        } else {
          // empty list
        }
      }
    }
  }

  private fun showErrorNetworkView(message: String?) {
    showErrorView(
      binding.flMenuPage, com.ricepo.style.R.drawable.ic_error_no_network,
      com.ricepo.style.R.string.error_title_load_failed, message, com.ricepo.style.R.string.retry,
      isCleanWhenClick = true,
      View.OnClickListener {
        reloadCoins()
      }
    )
  }

  private fun setRewardView() {

    binding.rvCoinsList.isNestedScrollingEnabled = false

    lifecycleScope.launchWhenCreated {
      val country = withContext(Dispatchers.IO) {
        AddressCache.getCountrySuspend()
      }
      val tenPrice = BaseMapper().formatPrice(1000, country, null)
      val subTitle = ResourcesUtil.getString(com.ricepo.style.R.string.reward_subtitle0, tenPrice)
      binding.tvRewardSubtitle.text = StringUtils.convertCoinPlainToImage(binding.root, subTitle)
    }
  }
}
