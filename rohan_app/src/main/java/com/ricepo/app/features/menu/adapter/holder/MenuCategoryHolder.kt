package com.ricepo.app.features.menu.adapter.holder

import android.text.SpannableStringBuilder
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.consts.CategoryConst
import com.ricepo.app.databinding.MenuItemCategoryBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.model.Category
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.localize
import com.ricepo.base.tools.StringUtils
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by <PERSON><PERSON> on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

data class CategoryModel(
  val category: Category? = null,
  val isJump: Boolean = false,
  val jumps: List<MenuUiModel.MenuCategoryItem>? = null
)

class MenuCategoryHolder(
  private val binding: MenuItemCategoryBinding,
  private val entrance: String?,
  private val navigate: (model: CategoryModel) -> Unit
) :
  SectionItemHolder(binding.root) {

  val mapper = MenuMapper()

  var balance: Int? = null
  var restaurant: Restaurant? = null
  var mJumps: List<MenuUiModel.MenuCategoryItem>? = null

  init {
    binding.tvCategoryShowMoreClick.clickWithTrigger {
      navigateSubMore()
    }

    binding.llCategoryName.clickWithTrigger {
      navigateJump()
    }
  }

  private fun navigateJump() {
    val tag = binding.root.tag
    if (tag is Category) {
      val jumps = mJumps?.map {
        if (it.category?.balance != null) {
          var category = it.category
          category?.name = InternationalizationContent(
            "積分兌換區", "积分兑换区", "Redeem Coin", "Canjear monedas"
          )
          MenuUiModel.MenuCategoryItem(
            restaurant, categoryIndex = it.categoryIndex,
            groups = it.groups, category = category, itemPosition = it.itemPosition
          )
        } else it
      }
      navigate(CategoryModel(tag, isJump = true, jumps = jumps))
    }
  }

  private fun navigateSubMore() {
    val tag = binding.root.tag
    val restaurant = binding.root.getTag(R.id.tag_restaurant) as? Restaurant
    if (tag is Category) {
//            FeaturePageRouter.navigateMenuSubMore(
//                binding.root.context, restaurant, tag, entrance
//            )
      navigate(CategoryModel(tag))
    }
  }

  fun bind(uiModel: MenuUiModel.MenuCategoryItem, isMenuPage: Boolean) {
    val category = uiModel.category ?: return
    restaurant = uiModel.restaurant
    balance = uiModel.balance
    mJumps = uiModel.jumps
    bindView(uiModel.restaurant, category, isMenuPage)

    if (uiModel.isOptionsCateogry == true) {
      binding.tvCategoryName.setTextSize(
        TypedValue.COMPLEX_UNIT_PX,
        ResourcesUtil.getDimenSize(com.ricepo.style.R.dimen.sw_19dp)
      )
      val params = binding.llCategoryName.layoutParams
      if (params is ConstraintLayout.LayoutParams) {
        params.leftMargin = ResourcesUtil.getDimenPixelOffset(
          binding.root, com.ricepo.style.R.dimen.sw_48dp
        )
        binding.llCategoryName.layoutParams = params
      }
    } else {
      binding.tvCategoryName.setTextSize(
        TypedValue.COMPLEX_UNIT_PX,
        ResourcesUtil.getDimenSize(com.ricepo.style.R.dimen.sw_24dp)
      )
    }
  }

  private fun bindView(restaurant: Restaurant?, category: Category, isMenuPage: Boolean) {

    if (balance == null) {
      binding.tvCategoryName.text = category.name?.localize()
      val desc = category.description?.localize()
      binding.tvCategoryDescription.text = desc
      binding.tvCategoryDescription.isVisible = !(desc.isNullOrEmpty())
    } else {
      binding.tvCategoryName.text = ResourcesUtil.getString(com.ricepo.style.R.string.reward_title)
      val desc = getRewardDesc()
      binding.tvCategoryDescription.text = getRewardDesc()
      binding.tvCategoryDescription.isVisible = !(desc.isNullOrEmpty())
    }
    binding.ivCategoryJump.isVisible = isMenuPage

    // show more hidden
    val isHorizontal = (category.type == CategoryConst.HORIZONTAL)
    binding.tvCategoryShowMore.isVisible = isHorizontal
    binding.ivCategoryShowMore.isVisible = isHorizontal

    binding.root.tag = category
    binding.root.setTag(R.id.tag_restaurant, restaurant)

//        binding.tvDivider.isVisible = (category.name?.localize()?.isNotEmpty() == true)
  }

  private fun getRewardDesc(): SpannableStringBuilder {
    val span = getRewardSubtitle0()
    span.append(" ")
    span.append(getRewardSubtitle1())

    return span
  }
  private fun getRewardSubtitle0(): SpannableStringBuilder {
    val tenPrice = mapper.formatPriceByRestaurant(1000, restaurant, null)
    val subTitle = ResourcesUtil.getString(com.ricepo.style.R.string.reward_subtitle0, tenPrice)

    return StringUtils.convertCoinPlainToImage(binding.root, subTitle)
  }

  private fun getRewardSubtitle1(): SpannableStringBuilder {
    val balance = balance ?: restaurant?.reward?.balance ?: 0
    val count = mapper.calcCoinCount(balance)
    val str = ResourcesUtil.getString(com.ricepo.style.R.string.reward_subtitle1, count)

    return StringUtils.convertCoinPlainToImage(binding.root, str)
  }

  companion object {
    fun create(
      parent: ViewGroup,
      entrance: String?,
      navigate: (model: CategoryModel) -> Unit
    ): MenuCategoryHolder {
      val binding = MenuItemCategoryBinding.inflate(LayoutInflater.from(parent.context))
      val params = RecyclerView.LayoutParams(
        RecyclerView.LayoutParams.MATCH_PARENT,
        RecyclerView.LayoutParams.WRAP_CONTENT
      )
      binding.root.layoutParams = params
      return MenuCategoryHolder(binding, entrance, navigate)
    }
  }
}
