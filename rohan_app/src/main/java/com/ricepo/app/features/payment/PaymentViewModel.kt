package com.ricepo.app.features.payment

import android.app.Activity
import androidx.activity.ComponentActivity
import androidx.lifecycle.viewModelScope
import com.ricepo.app.R
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.features.payment.data.PaymentHandleMode
import com.ricepo.app.features.payment.data.ShowPaymentCase
import com.ricepo.app.features.subscription.SubscriptionUseCase
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.Card
import com.ricepo.app.model.Order
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PaymentOwnMethod
import com.ricepo.base.animation.Loading
import com.ricepo.base.extension.bindLoading
import com.ricepo.base.extension.bindLoadingWithoutNext
import com.ricepo.base.extension.uiSubscribe
import com.ricepo.base.model.localize
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.style.ResourcesUtil
import com.stripe.android.model.PaymentMethodCreateParams
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.annotations.NonNull
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.ObservableEmitter
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.kotlin.addTo
import io.reactivex.rxjava3.subjects.PublishSubject
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import javax.inject.Inject

//
// Created by Thomsen on 8/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@HiltViewModel
class PaymentViewModel @Inject constructor(
  private val postExecutionThread: PostExecutionThread,
  private val subscriptionUseCase: SubscriptionUseCase,
  private val useCase: PaymentUseCase
) : BaseViewModel() {

  private val disposables = CompositeDisposable()

  val observeError = PublishSubject.create<String>()

  private val observerCards: PublishSubject<Boolean> = PublishSubject.create()

  private var showPaymentCase: ShowPaymentCase = ShowPaymentCase.all

  private val observerCreateCard: PublishSubject<PaymentMethodCreateParams> =
    PublishSubject.create()

  var order: Order? = null

  private var paymentTempCard: Card? = null

  var paymentHandleMode: PaymentHandleMode = PaymentHandleMode.none

  var isLogin: Boolean = false

  fun initData(order: Order?, paymentHandleMode: PaymentHandleMode) {
    this.order = order
    this.paymentHandleMode = paymentHandleMode
  }

  fun triggerGetCards(showPaymentCase: ShowPaymentCase) {
    this.showPaymentCase = showPaymentCase
    observerCards.onNext(true)
  }

  fun observeGetCard(activity: Activity): Observable<List<Card>> {
    return observerCards.flatMap {
      useCase.getCards(showPaymentCase)
        .bindLoading(activity)
    }.flatMap {
      val cards = mutableListOf<Card>()

      // add customer cards
      cards.addAll(it)

      // add the local addCard method
      if (showPaymentCase == ShowPaymentCase.editCard) {
        if (isLogin) {
//          cards.add(Card(PaymentOwnMethod.ADD_NEW_CARD))
        }
      } else if (cards.filter { PaymentOwnMethod.BBVA_PAY.equals(it.method) }.isEmpty()
        || ShowPaymentCase.onlyStripe.equals(showPaymentCase)) {
        cards.add(Card(PaymentOwnMethod.ADD_NEW_CARD))
      }

      Observable.just(cards)
    }
  }

  fun triggerCreateCard(params: PaymentMethodCreateParams?) {
    if (params != null) {
      observerCreateCard.onNext(params)
    }
  }

  fun observeCreateCard(activity: Activity): Observable<Result<Card>> {
    return observerCreateCard.flatMap {
      useCase.createStripePaymentCard(it)
        .bindLoadingWithoutNext(activity)
    }
  }

  fun selectPayment(context: ComponentActivity, value: Card?, isTempCard: Boolean): Observable<Any> {
    val payment = value ?: return Observable.just(false)
    // google pay need select card
    if (payment.method != PaymentOwnMethod.GOOGLE_PAY) {
      paymentTempCard = payment
    }

    return if (showPaymentCase == ShowPaymentCase.editCard) {
      handleDeletePayment(context, value)
    } else {
      // check the payment environment
      return checkoutPaymentEnvironment(context, payment).flatMap {
        if (it) {
          when (paymentHandleMode) {
            PaymentHandleMode.none -> {
              Observable.create<Any> { emitter -> emitter.onNext(payment) }
            }
            PaymentHandleMode.autoPayment -> {
              handleAutoPayment(context, payment, isTempCard)
            }
            PaymentHandleMode.autoUpdateSubscriptionPay -> {
              handleAutoSubscriptionPayment(context, payment, isTempCard)
            }
          }
        } else {
          Observable.just(false)
        }
      }
    }
  }

  /**
   * delete payment card
   */
  private fun handleDeletePayment(
    context: ComponentActivity,
    payment: Card
  ): Observable<Any> {
    return Observable.create { emitter ->
//            val cardNum = "${payment.brand ?: ""}-${payment.last4 ?: ""}"
      val cardNum = payment.name?.localize() ?: ""
      val title = ResourcesUtil.getString(com.ricepo.style.R.string.ricepo)
      val message = ResourcesUtil.getString(com.ricepo.style.R.string.confirm_remove_card, cardNum)
      DialogFacade.showPrompt(context, message = message, title = title) {
        Loading.showLoading(context)
        useCase.deletePaymentCard(payment.id ?: "", payment.method)
          .uiSubscribe()
          .subscribe { result ->
            result.fold(
              onSuccess = {
                // delete local payment
                viewModelScope.launch {
                  val paymentMethod = CustomerCache.getPayment()
                  if (paymentMethod?.last4 == payment.last4 ||
                    paymentMethod?.stripeId == payment.id
                  ) {
                    CustomerCache.deletePayment()
                  }
                }
                emitter.onNext(true)
              },
              onFailure = { error ->
                Loading.hideLoading()
                DialogFacade.showAlert(context, error.parseByBuzNetwork().message ?: "")
              }
            )
          }
      }
    }
  }

  private fun checkoutPaymentEnvironment(context: ComponentActivity, payment: Card?):
    Observable<Boolean> {
    // stop if not support choose payment method
    val support = useCase.checkoutPaymentSoftware(context, payment)
    return support.flatMap { ready ->
      if (!ready.isNullOrEmpty()) {
        observeError.onNext(ready)
        Observable.just(false)
      } else {
        Observable.just(true)
      }
    }
  }

  /**
   * will only trigger when enter from order page to payment page
   */
  private fun handleAutoPayment(
    context: ComponentActivity,
    payment: Card,
    isTempCard: Boolean
  ): Observable<Any> {
    val order: Order = this.order ?: return Observable.just(false)

    return checkoutPaymentEnvironment(context, payment).flatMap {
      it
      if (it) {
        Observable.create { emitter ->
          val methodString = payment.method
          var paymentOwnMethod: PaymentOwnMethod? = null
          if (payment?.brand != null) {
            paymentOwnMethod = PaymentOwnMethod(PaymentOwnMethod.CREDIT, payment.brand, payment.last4, payment.id)
          } else if (methodString != null && methodString != PaymentOwnMethod.ADD_NEW_CARD) {
            paymentOwnMethod = PaymentOwnMethod(methodString, stripeId = payment.id)
          }
          if (isTempCard) {
            createPayment(context, paymentOwnMethod, order, emitter)
          } else {
            DialogFacade.showPrompt(
              context,
              ResourcesUtil.getString(
                com.ricepo.style.R.string.payment_confirm_method,
                payment.name?.localize() ?: payment.last4 ?: ""
              )
            ) {
              createPayment(context, paymentOwnMethod, order, emitter)
            }
          }
        }
      } else {
        Observable.just(false)
      }
    }
  }

  fun createPayment(
    paymentMethod: PaymentOwnMethod?,
    order: Order?,
  ): Observable<Result<Pair<PaymentOwnMethod?, PaymentObj>>> {
    paymentMethod?.let {
      paymentTempCard = Card(
        method = it.method, brand = it.brand,
        last4 = it.last4, id = it.stripeId
      )
    }

    return useCase.createPayment(paymentMethod, order)
  }

  private fun createPayment(
    context: ComponentActivity,
    paymentMethod: PaymentOwnMethod?,
    order: Order?,
    emitter: @NonNull ObservableEmitter<Any>
  ) {
    useCase.createPayment(paymentMethod, order)
      .uiSubscribe()
      .doOnSubscribe {
//                CheckoutLoading.showLoading(context, com.ricepo.style.R.string.placing_order)
        Loading.showLoading(context)
      }.subscribe {
        emitter.onNext(it)
      }.addTo(disposables)
  }

  private fun handleAutoSubscriptionPayment(
    context: ComponentActivity,
    payment: Card,
    isTempCard: Boolean
  ): Observable<Any> {
    return Observable.create { emitter ->
      if (isTempCard) {
        updateSubscription(context, payment, emitter)
      } else {
        DialogFacade.showPrompt(
          context,
          ResourcesUtil.getString(
            com.ricepo.style.R.string.payment_confirm_method,
            payment.name?.localize() ?: payment.last4 ?: ""
          )
        ) {
          if (payment.method == PaymentOwnMethod.GOOGLE_PAY) {
            emitter.onNext(
              Result.success(
                Pair(
                  PaymentOwnMethod(
                    method = PaymentOwnMethod.GOOGLE_PAY
                  ),
                  PaymentObj(order = order)
                )
              )
            )
          } else {
            updateSubscription(context, payment, emitter)
          }
        }
      }
    }
  }

  private fun updateSubscription(
    context: ComponentActivity,
    payment: Card,
    emitter: @NonNull ObservableEmitter<Any>
  ) {
    val stripeId = payment.id
    if (stripeId != null) {
      subscriptionUseCase.updateSubscription(stripeId)
        .bindLoading(context)
        .subscribe {
          if (it.message.isNullOrEmpty()) {
            // save new payment
            CustomerCache.savePayment(payment) {}
          }
          emitter.onNext(it)
        }.addTo(disposables)
    }
  }

  fun updateSubscription(payment: Card): Observable<Any> {
    val stripeId = payment.id
    return if (stripeId != null) {
      subscriptionUseCase.updateSubscription(stripeId).map {
        if (it.message.isNullOrEmpty()) {
          // save new payment
          CustomerCache.savePayment(payment) {}
        }
        it
      }
    } else {
      Observable.just(false)
    }
  }

  fun saveTempPaymentCard() {
    val card = paymentTempCard
    if (card != null) {
      CustomerCache.savePayment(card) {}
    }
  }

  fun addPaymentCard(context: ComponentActivity, card: Card): Flow<PaymentObj> {
    return flow<PaymentObj> {
      val cardId = card.id
      if (cardId.isNullOrEmpty()) {
      } else {
        try {
          emit(useCase.addPaymentCard(cardId))
        } catch (e: Exception) {
          Loading.hideLoading()
          DialogFacade.showAlert(context, e.parseByBuzNetwork().message ?: "")
        }
      }
    }
  }

  override fun onCleared() {
    super.onCleared()
    disposables.dispose()
  }
}
