package com.ricepo.app.features.profile.datasource

import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingSource
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import com.ricepo.app.R
import com.ricepo.app.data.AppDatabase
import com.ricepo.app.features.profile.ProfileUiModel
import com.ricepo.app.features.profile.datasource.db.RemoteKeys
import com.ricepo.app.model.Order
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.app.restapi.loadResultError
import com.ricepo.app.restaurant.CustomerOrderType
import com.ricepo.base.model.Customer
import com.ricepo.style.ResourcesUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.withContext
import java.io.InvalidObjectException

//
// Created by <PERSON><PERSON> on 3/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@OptIn(ExperimentalPagingApi::class)
class ProfileDataMediator constructor(
  private val repository: CombineRestApi,
  private val customerId: String?,
  private val database: AppDatabase
) : RemoteMediator<Int, ProfileUiModel>() {

  private val PAGE_INDEX_STARTING = 1

  private var prevKey: Int? = null
  private var nextKey: Int? = null

  private var lastOrderCreatedAt: String? = null

//    private var dataSource: MutableList<PagingSource.LoadResult.Page<Int, ProfileUiModel>> = mutableListOf()

  private var _cacheModel: MutableList<ProfileUiModel> = mutableListOf()

  private var uiModels: List<ProfileUiModel> = listOf()

  private var endOfPaginationReached: Boolean = false

  override suspend fun load(loadType: LoadType, state: PagingState<Int, ProfileUiModel>): MediatorResult {

    val page = when (loadType) {
      LoadType.REFRESH -> {
        val remoteKeys = getRemoteKeyClosestToCurrentPosition(state)
        lastOrderCreatedAt = remoteKeys?.createdAt
        remoteKeys?.nextKey?.minus(1) ?: PAGE_INDEX_STARTING
      }
      LoadType.PREPEND -> {
        val remoteKeys = getRemoteKeyForFirstItem(state)
//                    ?: throw InvalidObjectException("Remote key and the prevKey should not be null")
        // If the previous key is null, then we can't request more data
        var prevKey: Int? = null
        if (remoteKeys != null) {
          prevKey = remoteKeys.prevKey ?: return MediatorResult.Success(endOfPaginationReached = true)
          lastOrderCreatedAt = remoteKeys.createdAt
        }
        prevKey ?: PAGE_INDEX_STARTING
      }
      LoadType.APPEND -> {
        val remoteKeys = getRemoteKeyForLastItem(state)
        if (remoteKeys?.nextKey == null) {
          throw InvalidObjectException("Remote key should not be null for $loadType")
        }
        lastOrderCreatedAt = remoteKeys.createdAt
        remoteKeys.nextKey
      }
    }

    val customerId = customerId ?: ""

    return withContext(Dispatchers.Default) {
      try {

        var customer: Customer? = null
        var recentOrders: List<Order>? = null
        var referInfo: ReferInfo? = null

        if (page == PAGE_INDEX_STARTING) {
          // request can trigger load state
          customer = withContext(Dispatchers.IO) {
            repository.getCustomer(customerId)
          }

          var recentParams = mapOf<String, Any>(
            "type" to CustomerOrderType.recent
          )
          recentOrders = withContext(Dispatchers.IO) {
            repository.getOrders(customerId, recentParams)
          }

          referInfo = withContext(Dispatchers.IO) {
            repository.getRefer()
          }
        }

        var historyParams = mutableMapOf<String, Any>(
          "limit" to state.config.pageSize,
          "type" to CustomerOrderType.history
        )
        val lastOrderCreatedAt = lastOrderCreatedAt
        if (lastOrderCreatedAt != null) {
          historyParams["isBefore"] = lastOrderCreatedAt
        }
        val historyOrders = withContext(Dispatchers.IO) {
          repository.getOrders(customerId, historyParams)
        }

        endOfPaginationReached = historyOrders.isNullOrEmpty() ||
          historyOrders.size < state.config.pageSize
        uiModels = constructUiModels(customer, recentOrders, historyOrders, referInfo)

        if (loadType == LoadType.REFRESH) {
          database.remoteKeysDao().clearRemoteKeys()
          _cacheModel.clear()
        }

        prevKey = if (page == PAGE_INDEX_STARTING) null else page - 1
        nextKey = if (endOfPaginationReached) null else page + 1
        val keys = historyOrders.map {
          RemoteKeys(it.id, prevKey, nextKey, it.createdAt)
        }
        database.remoteKeysDao().insertAll(keys)

        uiModels
          .forEach { model ->
            val index = _cacheModel.indexOf(model)
            if (index > -1) {
              _cacheModel.set(index, model)
            } else {
              _cacheModel.add(model)
            }
          }
        _channel.send(uiModels)

        MediatorResult.Success(endOfPaginationReached = endOfPaginationReached)
      } catch (e: Exception) {
        MediatorResult.Error(e)
      }
    }
  }

//    val cacheModel: List<ProfileUiModel>
//        get() = _cacheModel

//    fun getDataSource(): MediatorDataSource {
//        return MediatorDataSource(_cacheModel)
//    }

  // rendezvous channel (meet on time)
  // unlimited channel
  // buffered channel
  // conflated channel (old by overried new)
  private val _channel = Channel<List<ProfileUiModel>>(Channel.CONFLATED)

  fun getDataSource(): PagingSource<Int, ProfileUiModel> {
    return MediatorDataSource(_channel)
  }

  inner class MediatorDataSource(
    private val channel:
      Channel<List<ProfileUiModel>>
  ) : PagingSource<Int, ProfileUiModel>() {

    private var curPage = PAGE_INDEX_STARTING

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ProfileUiModel> {
      // replace by database to cache
      try {
        val page = params.key ?: PAGE_INDEX_STARTING
//            val pageSize = params.loadSize
//            val startIndex = (page - 1) * pageSize
//            var endIndex = page * pageSize

        if (page > curPage) {
          return LoadResult.Page(
            data = listOf(),
            prevKey = if (curPage == 1) null else curPage - 1,
            nextKey = curPage + 1
          )
        }

        val data = channel.receiveCatching().getOrNull()
//                val data = channel.consumeAsFlow().map { it }
//            if (endIndex > cacheModel.size) {
//                endIndex = cacheModel.size
//            }
//            endIndex -= 1
//            var data = mutableListOf<ProfileUiModel>()
//            if (startIndex > cacheModel.size) {
//                data = mutableListOf()
//            } else {
//                // ConcurrentModificationException
// //                data = cacheModel.subList(startIndex, endIndex)
//                for (i in startIndex..endIndex) {
//                    data.add(cacheModel[i])
//                }
//            }

        curPage = page

        return LoadResult.Page(
          data = data ?: listOf(),
          prevKey = if (page == 1) null else page - 1,
          nextKey = if (data.isNullOrEmpty()) null else page + 1
        )
      } catch (e: Exception) {
        return loadResultError<Int, ProfileUiModel>(e)
      }
    }

    override val keyReuseSupported: Boolean
      get() = true
  }

  private fun constructUiModels(
    customer: Customer?,
    recentOrders: List<Order>?,
    historyOrders: List<Order>,
    referInfo: ReferInfo?
  ): List<ProfileUiModel> {
    val uiModels = mutableListOf<ProfileUiModel>()
    // customer model
    if (customer != null) {
      uiModels.add(ProfileUiModel.CustomerHeader(customer))
    }
    // recent orders model
    recentOrders?.forEach { order ->
      uiModels.add(ProfileUiModel.RecentOrders(order))
    }
    if (customer != null) {
      // share ricepo model
      uiModels.add(ProfileUiModel.ShareRicepo(referInfo))
      // history title model
      uiModels.add(ProfileUiModel.HistoryTitle(ResourcesUtil.getString(com.ricepo.style.R.string.past_order)))
    }

    // history orders model
    historyOrders?.forEach { order ->
      uiModels?.add(ProfileUiModel.HistoryOrders(order))
    }

    return uiModels
  }

  private suspend fun getRemoteKeyForLastItem(state: PagingState<Int, ProfileUiModel>): RemoteKeys? {
    // Get the last page that was retrieved, that contained items.
    // From that last page, get the last item
    return state.pages.lastOrNull() { it.data.isNotEmpty() }?.data?.lastOrNull()
      ?.let { repo ->
        if (repo is ProfileUiModel.HistoryOrders) {
          // Get the remote keys of the last item retrieved
          database.remoteKeysDao().remoteKeysRepoId(repo.order.id)
        } else {
          RemoteKeys("", prevKey, nextKey, null)
        }
      }
  }

  private suspend fun getRemoteKeyForFirstItem(state: PagingState<Int, ProfileUiModel>): RemoteKeys? {
    // Get the first page that was retrieved, that contained items.
    // From that first page, get the first item
    return state.pages.firstOrNull { it.data.isNotEmpty() }?.data?.firstOrNull()
      ?.let { repo ->
        if (repo is ProfileUiModel.HistoryOrders) {
          // Get the remote keys of the first items retrieved
          database.remoteKeysDao().remoteKeysRepoId(repo.order.id)
        } else {
          RemoteKeys("", prevKey, nextKey, null)
        }
      }
  }

  private suspend fun getRemoteKeyClosestToCurrentPosition(
    state: PagingState<Int, ProfileUiModel>
  ): RemoteKeys? {
    // The paging library is trying to load data after the anchor position
    // Get the item closest to the anchor position
    return state.anchorPosition?.let { position ->
      val model = state.closestItemToPosition(position)
      if (model is ProfileUiModel.HistoryOrders) {
        model.order.id?.let { repoId ->
          database.remoteKeysDao().remoteKeysRepoId(repoId)
        }
      } else {
        RemoteKeys("", prevKey, nextKey, null)
      }
    }
  }
}
