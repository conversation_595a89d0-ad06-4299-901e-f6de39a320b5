package com.ricepo.app.features.order

import android.content.Context
import androidx.activity.ComponentActivity
import androidx.lifecycle.viewModelScope
import androidx.work.Constraints
import androidx.work.Data
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import com.google.android.gms.maps.model.LatLng
import com.ricepo.app.R
import com.ricepo.app.consts.OptionMessage
import com.ricepo.app.data.kv.OrderCache
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.checkout.data.OrderCellInfo
import com.ricepo.app.features.order.data.OrderState
import com.ricepo.app.features.order.worker.RefreshOrderWorker
import com.ricepo.app.listener.LifecycleNetworkListener
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.model.Order
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.model.SupportRuleGroup
import com.ricepo.app.restapi.RiceApi
import com.ricepo.app.utils.SingleLiveEvent
import com.ricepo.base.ErrorInput
import com.ricepo.base.animation.Loading
import com.ricepo.base.consts.TabMode
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.extension.uiSubscribe
import com.ricepo.base.model.DriverPoint
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantCart
import com.ricepo.base.view.DialogFacade
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.map.utils.PolylineUtils
import com.ricepo.network.executor.PostExecutionThread
import com.ricepo.style.ResourcesUtil
import com.skydoves.sandwich.onError
import com.skydoves.sandwich.onException
import com.skydoves.sandwich.onSuccess
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.annotations.NonNull
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.ObservableEmitter
import io.reactivex.rxjava3.disposables.CompositeDisposable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.kotlin.Observables
import io.reactivex.rxjava3.kotlin.addTo
import io.reactivex.rxjava3.subjects.BehaviorSubject
import io.reactivex.rxjava3.subjects.PublishSubject
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.UUID
import java.util.concurrent.TimeUnit
import javax.inject.Inject

//
// Created by Thomsen on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
@HiltViewModel
class OrderViewModel @Inject constructor(
  private val useCase: OrderUseCase,
  private val postExecutionThread: PostExecutionThread,
  private val riceApi: RiceApi
) : BaseViewModel() {

  data class OrderOption(
    var order: Order? = null,
    val cellInfos: List<OrderCellInfo> = listOf(),
    var orderLast4: String? = null,
    var orderBrand: String? = null,
    var driverLocation: DriverPoint? = null,
    var driverPolyline: List<LatLng>? = null,
    var message: String? = null,
    var errorInput: ErrorInput? = null
  )

  private val disposables = CompositeDisposable()

  val observerOrderError = PublishSubject.create<OrderOption>()

  /**
   * params is with loading indicator
   */
  val onRefreshLoading = BehaviorSubject.create<Boolean>()

  private val onBaseOrder = PublishSubject.create<Order>()

  var order: Order? = null

  private var currentOrderId: String? = null

  private var driverLocation: DriverPoint? = null

  private var driverPolyline: List<LatLng>? = null

  var restaurant: Restaurant? = null

  val driverPhone = SingleLiveEvent<String>()

  // SharedFlow for refer info
  private val _referInfoFlow = MutableSharedFlow<ReferInfo?>(replay = 1)
  val referInfoFlow: SharedFlow<ReferInfo?> = _referInfoFlow.asSharedFlow()

  fun callPhoneMasking(
    order: Order
  ) {
    viewModelScope.launch {
      riceApi.fetchPhoneMasking(order.id).onSuccess {
        data?.phone?.let {
          driverPhone.postValue(it)
        }
      }.onError {
        order.delivery?.courier?.phone?.let {
          driverPhone.postValue(it)
        }
      }.onException {
        order.delivery?.courier?.phone?.let {
          driverPhone.postValue(it)
        }
      }
    }
  }

  fun observeOrderUpdate(context: ComponentActivity): Observable<OrderOption> {
    return Observable.merge(
      onBaseOrder,
      observeLoadOrder(context).filter { it == true }
    )
      .doOnSubscribe { disposables.add(it) }
      .observeOn(postExecutionThread.ioScheduler)
      .flatMap {
        // observe the order pending status
        // valueOf, No enum constant com.ricepo.app.features.order.data.OrderStatus.pending
        if (this.order?.status == OrderState.PENDING) {
          onPending.onNext(true)
        } else {
          onPending.onNext(false)
        }

        // calculation of time consumption
        val option = useCase.parse(context, this.order, this.driverLocation, this.driverPolyline)
        option
      }
  }

  private fun observeLoadOrder(context: ComponentActivity): @NonNull Observable<out Any> {
    return onRefreshLoading.flatMap {
      val showLoading = it
      Observables.zip(
        getPoint(),
        getOrderInfo(showLoading, context)
      ) { isPoint, isOrder ->
        isPoint || isOrder
      }.flatMap {
        Observable.just(it)
      }.doOnSubscribe {
        disposables.add(it)
      }
    }
  }

  /**
   * get order from user info
   * will only called once when:
   *  - from profile to order
   *  - checkout to order page
   *  - group closed error to intent
   */
  fun initBaseOrder(order: Order?, orderId: String?) {
    this.order = order
    if (order != null) {
      onBaseOrder.onNext(order)
    }
    if (orderId != null) {
      currentOrderId = orderId
    }
    // 获取refer信息
    getReferInfo()
  }

  /**
   * 获取refer信息并通过SharedFlow发送
   */
  private fun getReferInfo() {
    viewModelScope.launch {
      try {
        val referInfo = useCase.getReferInfo()
        _referInfoFlow.emit(referInfo)
      } catch (e: Exception) {
        _referInfoFlow.emit(null)
      }
    }
  }

  /**
   * get the driver's position on the map
   */
  private fun getPoint(): Observable<Boolean> {
    val orderId = order?.id ?: currentOrderId ?: return Observable.just(false)
    return Observable.create { emitter ->
      useCase.getDriverLocation(orderId)
        .uiSubscribe()
        .subscribe { result ->
          result.fold(
            onSuccess = { trace ->
              // driver location
              if (trace != null && trace?.location?.coordinates?.isNotEmpty() == true) {
                driverLocation = trace.location
              } else {
                driverLocation = null
              }
              // driver polyline
              driverPolyline = PolylineUtils.decode(trace?.polyline)

              emitter.onNext(true)
            },
            onFailure = {
              emitter.onNext(false)
            }
          )
        }.addTo(disposables)
    }
  }

  private fun getOrderInfo(showLoading: Boolean, context: ComponentActivity): Observable<Boolean> {
    val orderId = order?.id ?: currentOrderId
    if (orderId == null) {
      // pop to last page
      observerOrderError.onNext(OrderOption(message = OptionMessage.DATA_EMPTY))
      return Observable.just(false)
    }

    return useCase.getOrderById(orderId)
      .doOnSubscribe {
        disposables.add(it)
        if (showLoading) { Loading.showLoading(context) }
      }
      .subscribeOn(postExecutionThread.mainScheduler)
      .observeOn(postExecutionThread.mainScheduler)
      .doOnNext { if (showLoading) { Loading.hideLoading() } }
      .doOnError { if (showLoading) { Loading.hideLoading() } }
      .doOnTerminate { if (showLoading) { Loading.hideLoading() } }
      .observeOn(postExecutionThread.ioScheduler)
      .flatMap {
        it.fold(
          onSuccess = {
            this.order = it
            Observable.just(true)
          },
          onFailure = {
            // if order is not null don't show error view
            if (this.order != null) {
              Observable.just(true)
            } else {
              // the error view only needs to show:
              // if enter order page with notification
              // and don't get order information
              val errorInput = ErrorInput(
                com.ricepo.style.R.drawable.ic_error_empty,
                com.ricepo.style.R.string.error_title_load_failed,
                message = it.message,
                buttonId = com.ricepo.style.R.string.retry,
                click = {
                  // retry
                  onRefreshLoading.onNext(true)
                }
              )
              observerOrderError.onNext(OrderOption(errorInput = errorInput))
              Observable.just(false)
            }
          }
        )
      }
  }

  private val onPending = PublishSubject.create<Boolean>()

  fun observePendingWarningTimer(): Observable<Any> {
    return Observable.create { emitter ->
      onPending.subscribe {
        val isPending = it
        if (isPending) {
          // create waring timer if is pending
          createPendingWarningTask(emitter)
        } else {
          // clear waring timer
          clearPendingWaringTimer()
          emitter.onNext(false)
        }
      }
    }
  }

  private var refreshOrderDisposable: Disposable? = null

  /**
   * create order update timer
   * start after 1s and trigger every 10s
   */
  fun startOrderUpdateTask(isFirst: Boolean) {
    if (refreshOrderDisposable != null) {
      clearOrderRefreshTimer()
    }
    refreshOrderDisposable = Observable.interval(0, 10, TimeUnit.SECONDS)
      .subscribe { count ->
        // avoid simultaneous execution refresh and auto payment
        // scroll to top from back auto payment
        if (count == 0L && isFirst) {
          onRefreshLoading.onNext(true)
        } else {
          onRefreshLoading.onNext(false)
        }
      }
  }

  fun clearOrderRefreshTimer() {
    disposables.clear()
    refreshOrderDisposable?.dispose()
    refreshOrderDisposable = null
  }

  private var pendingWaringDisposable: Disposable? = null

  /**
   * create pending info for payment
   */
  private fun createPendingWarningTask(emitter: ObservableEmitter<Any>) {
    // clear if timer has been created
    if (pendingWaringDisposable != null) {
      clearPendingWaringTimer()
    }
    pendingWaringDisposable = Observable.interval(
      0, 1, TimeUnit.SECONDS,
      postExecutionThread.ioScheduler
    )
      .subscribe { _ ->
        // stop if order created at not found
        val createdAt = this.order?.createdAt ?: return@subscribe

        // stop if created at is invalid
        var time = useCase.validCreatedAt(createdAt) ?: return@subscribe

        // set time limit to 5 minutes
        time.add(Calendar.MINUTE, 5)

        // get the relative remaining time
        val now = Calendar.getInstance()
        val diffMillis = time.timeInMillis - now.timeInMillis
        var minute = diffMillis / (1000 * 60)
        var seconds = diffMillis % (1000 * 60) / 1000

        // stop if reach the time limit
        if (minute < 0 || seconds < 0) {
          clearPendingWaringTimer()
          return@subscribe
        }

        if (minute < 0) {
          minute = 0
        }
        var m = "$minute"
        if (minute < 10) {
          m = "0$minute"
        }

        if (seconds < 0) {
          seconds = 0
        }
        var s = "$seconds"
        if (seconds < 10) {
          s = "0$seconds"
        }

        // update the warning
        emitter.onNext(ResourcesUtil.getString(com.ricepo.style.R.string.order_pending_warning, m, s))
      }
  }

  /**
   * stop pending warn timer when leave order page,
   * otherwise slowly loading payment page
   */
  fun clearPendingWaringTimer() {
    this.pendingWaringDisposable?.dispose()
    this.pendingWaringDisposable = null
  }

  /**
   * get the latest order information when show help page
   */
  fun clickOrderHelp(activity: ComponentActivity) {
    getOrderInfo(true, activity)
      .subscribe {
        val order = this.order
        if (order != null) {
          // stop timer task
          clearOrderRefreshTimer()
          clearPendingWaringTimer()
          // show help page
          FeaturePageRouter.navigateOrderSupport(activity, order)
        }
      }
  }

  fun clickOrderConfigHelp(activity: ComponentActivity) {
    viewModelScope.launch {
      val order = order ?: return@launch
      flowLoading(
        activity,
        { error ->
          DialogFacade.showAlert(activity, error.parseByBuzNetwork().message ?: "")
        }
      ) {
        emit(useCase.getOrderSupportConfig(order.id))
      }.collectLatest {
        // stop timer task
        clearOrderRefreshTimer()
        clearPendingWaringTimer()
        val items = arrayListOf<SupportRuleGroup>()
        items.addAll(it)
        // show help page
        FeaturePageRouter.navigateOrderSupport(activity, order, items)
      }
    }
  }

  fun renewOrder(context: ComponentActivity) {

    // stop if order is not found
    val order = this.order ?: return

    // stop if order restaurant is not found
    var restaurant = restaurant ?: order.restaurant ?: return

    // filter out extra fees
    // food id is not empty means it's a real food, not extra fees
    // reorder should filter reward item
    val orderItems = (order.items ?: listOf()).filter {
      it.id?.isNullOrEmpty() == false && it?.reward != true
    }

    // log add food event
    val foodIds = orderItems.map { it.id }
    LifecycleNetworkListener.logAddFood(restaurant.id, foodIds)

    viewModelScope.launch {
      if (order.restaurant?.tags == null) {
        // compatibility the old order no restaurant tags
        RestaurantCartCache.clearRestaurantCart()
      } else {
        // first clear old cart
        RestaurantCartCache.deleteRestaurantCart(order.restaurant)
      }

      // update restaurant info
      val cartList = useCase.toCartList(orderItems)
      // update restaurant closed info
      restaurant = useCase.updateRestaurant(restaurant)
      val restaurantCart = RestaurantCart(cartList, restaurant, order.comments)
      RestaurantCartCache.saveRestaurantCart(restaurantCart)

      val deliveryMode = if (order.delivery == null) {
        TabMode.MODE_PICKUP
      } else {
        null
      }

      // show checkout page
      FeaturePageRouter.navigateCheckoutAfterSubscription(
        context, restaurant,
        deliveryMode = deliveryMode
      )
      context.finish()
    }
  }

  private var currentRequestId: UUID? = null

  fun refreshWork(context: ComponentActivity) {
    // Interval duration for `PeriodicWorkRequest`s must be at least 15 minutes.
//        val request = PeriodicWorkRequest.Builder(RefreshOrderWorker::class.java,
//            Duration.ofSeconds(10)).build()

    val data = Data.Builder()
      .putString("orderId", this.order?.id ?: currentOrderId)
      .build()

    val constraints = Constraints.Builder()
      .setRequiredNetworkType(NetworkType.CONNECTED)
      .build()

    val request = OneTimeWorkRequest.Builder(RefreshOrderWorker::class.java)
      .setConstraints(constraints)
      .setInputData(data)
      .build()

    WorkManager.getInstance(context)
      .enqueue(request)

    currentRequestId = request.id

    observerWorker(context)
  }

  private fun observerWorker(context: ComponentActivity) {
    val id = currentRequestId
    if (id != null) {
      WorkManager.getInstance(context).getWorkInfoByIdLiveData(id)
        .observe(
          context,
          androidx.lifecycle.Observer { info ->
            val result = info.outputData.getString("message")
            if (result == null) {
              // success
              OrderCache.getRefreshOrder {
                this.order = it
              }
            }
          }
        )
    }
  }

  fun cancelWorker(context: Context) {
    val id = currentRequestId
    if (id != null) {
      WorkManager.getInstance(context).cancelWorkById(id)
    }
  }

  override fun onCleared() {
    super.onCleared()
    clearOrderRefreshTimer()
    clearPendingWaringTimer()
    disposables.dispose()
  }
}
