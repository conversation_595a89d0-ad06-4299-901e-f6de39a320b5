package com.ricepo.app.features.rating

import android.content.Context
import com.ricepo.app.R

object RatingUiUtil {
  internal fun getLikes(
    context: Context
  ) = listOf(
    context.getString(com.ricepo.style.R.string.quick_efficient),
    context.getString(com.ricepo.style.R.string.good_communication),
    context.getString(com.ricepo.style.R.string.delivered_with_care),
    context.getString(com.ricepo.style.R.string.friendly_service),
  )

  internal fun getDisLikes(
    context: Context
  ) = listOf(
    context.getString(com.ricepo.style.R.string.slow_inefficient),
    context.getString(com.ricepo.style.R.string.bad_communication),
    context.getString(com.ricepo.style.R.string.delivered_with_careless),
    context.getString(com.ricepo.style.R.string.unfriendly_service),
  )

  internal fun getRatingDesc(
    context: Context,
    rating: Int
  ) = when (rating) {
    0 -> ""
    1 -> context.getString(com.ricepo.style.R.string.one_star)
    2 -> context.getString(com.ricepo.style.R.string.two_star)
    3 -> context.getString(com.ricepo.style.R.string.there_star)
    4 -> context.getString(com.ricepo.style.R.string.four_star)
    5 -> context.getString(com.ricepo.style.R.string.five_star)
    else -> { "" }
  }
}
