package com.ricepo.app.features.menu.bundle

import android.util.ArrayMap
import android.view.ContextThemeWrapper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.app.R
import com.ricepo.app.data.kv.RestaurantCartCache
import com.ricepo.app.databinding.RestaurantItemVerticalBinding
import com.ricepo.app.restaurant.RestaurantUiModel
import com.ricepo.app.restaurant.adapter.RestaurantBindMapper
import com.ricepo.app.restaurant.adapter.holder.FunRestaurantMenuClick
import com.ricepo.app.restaurant.adapter.holder.RestaurantVerticalHolder
import com.ricepo.base.extension.touchWithClickTrigger
import com.ricepo.base.model.Restaurant
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.ResourcesUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//
// Created by <PERSON><PERSON> on 11/9/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * using on menu page bottom sheet and search page
 */
class RestaurantBundleHolder(
  private val binding: RestaurantItemVerticalBinding,
  private val bundleSelected: ArrayMap<String, Boolean>,
  private val lifecycleOwner: LifecycleOwner,
  private val itemClick: FunRestaurantMenuClick = { _, _ -> }
) :
  RecyclerView.ViewHolder(binding.root) {

  init {
    // the conflict with motion layout on swipe (don't used setOnTouch*)
    binding.rvRestaurantMenu.touchWithClickTrigger { view, event ->
      stopScroll(binding)
      selectItem(view)
    }

    // bind listener
    binding.root.setOnClickListener { view ->
      stopScroll(binding)
      selectItem(view)
    }
  }

  private fun stopScroll(binding: RestaurantItemVerticalBinding) {
    // item click when horizontal menu scroll stop
    binding.rvRestaurantMenu.stopScroll()
    binding.rvRestaurantMenu.adapter?.notifyDataSetChanged()
  }

  private fun selectItem(view: View) {
    val item = view.tag
    if (item is RestaurantUiModel.RestaurantBundle) {
      val restaurantId = item.restaurant.id ?: return
      //  restaurant can click when already selected and closed or not closed
      if (binding.root.alpha != 1f && !bundleSelected.contains(restaurantId)) return
      if (bundleSelected.containsKey(restaurantId)) {
        handleCarts(item, restaurantId, view)
      } else {
        bindCheckSelected()
        addBundle(item, restaurantId)
      }
    }
  }

  private fun handleCarts(
    item: RestaurantUiModel.RestaurantBundle,
    restaurantId: String,
    view: View,
  ) {
    lifecycleOwner.lifecycle.coroutineScope.launch {
      val restaurantCart = withContext(Dispatchers.IO) {
        RestaurantCartCache.getRestaurantCartSuspend(item.hostRestaurant)
      }
      val isCarts = (
        restaurantCart?.cartList?.filter {
          it.bundleRestId == restaurantId
        }?.size ?: 0
        ) > 0
      if (isCarts) {
        val message = ResourcesUtil.getString(com.ricepo.style.R.string.bundle_remove_alert)
        val context = if (view.context is ContextThemeWrapper) {
          (view.context as ContextThemeWrapper).baseContext
        } else {
          view.context
        }
        DialogFacade.showPrompt(context, message) {
          lifecycleOwner.lifecycle.coroutineScope.launch {
            withContext(Dispatchers.IO) {
              RestaurantCartCache.deleteCartsWithRestaurantId(
                item.hostRestaurant, item.restaurant
              )
            }
            bindCheckNormal()
            item.refreshMenu()
            removeBundle(item, restaurantId)
          }
        }
      } else {
        bindCheckNormal()
        removeBundle(item, restaurantId)
      }
    }
  }

  private fun addBundle(item: RestaurantUiModel.RestaurantBundle, restaurantId: String) {
    bundleSelected[restaurantId] = true
    item.bundles = ArrayList<String>().apply {
      add(restaurantId)
    }
    itemClick(item, null)
  }

  private fun removeBundle(item: RestaurantUiModel.RestaurantBundle, restaurantId: String) {
    bundleSelected.remove(restaurantId)
    item.bundles = bundleSelected.keys?.distinct()?.toCollection(ArrayList())
    itemClick(item, null)
  }

  private fun bindCheck(restaurant: Restaurant) {
    if (bundleSelected.containsKey(restaurant.id)) {
      bindCheckSelected()
    } else {
      bindCheckNormal()
    }
  }

  private fun bindCheckNormal() {
    binding.inRestaurantInfo.btnRestaurantBundle.setSelectedCount(0)
  }

  private fun bindCheckSelected() {
    binding.inRestaurantInfo.btnRestaurantBundle.setSelectedCount(1)
  }

  fun bind(item: RestaurantUiModel.RestaurantBundle, position: Int) {

    binding.inRestaurantInfo.btnRestaurantBundle.isVisible = true
    binding.inRestaurantInfo.btnRestaurantBundle.maxCount = 1
    bindCheck(item.restaurant)

    val holder = RestaurantVerticalHolder(
      binding = binding,
      isClickMenu = false,
      itemClick = itemClick
    )

    holder.bind(RestaurantUiModel.RestaurantVertical(item.restaurant, position, 0))

    // reset the layout and recyclerview tag
    binding.root.tag = item
    binding.rvRestaurantMenu.tag = item

    // the restaurant closed can't select
    val mapper = RestaurantBindMapper(item.restaurant)
    val pairClosed = mapper.bindClosed()
    if ((pairClosed.first != null) || item.restaurant.hourlyClosed == true) {
      binding.root.alpha = 0.2f
    } else {
      binding.root.alpha = 1f
    }
  }

  companion object {
    fun create(
      parent: ViewGroup,
      cacheSelected: ArrayMap<String, Boolean>,
      lifecycleOwner: LifecycleOwner,
      itemClick: FunRestaurantMenuClick
    ): RestaurantBundleHolder {
      val binding = RestaurantItemVerticalBinding.inflate(LayoutInflater.from(parent.context))

      RestaurantVerticalHolder.initRestaurantVerticalBinding(binding, false) { _, _ -> }

      return RestaurantBundleHolder(
        binding,
        cacheSelected, lifecycleOwner, itemClick
      )
    }
  }
}
