package com.ricepo.app.features.profile.adapter

import android.content.res.ColorStateList
import android.graphics.Color
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.ricepo.app.R
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.FeaturePageRouter
import com.ricepo.app.features.order.data.OrderState
import com.ricepo.app.features.profile.ProfileMapper
import com.ricepo.app.model.Order
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.touchWithTrigger
import com.ricepo.base.model.localize
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.dp
import com.ricepo.style.view.getColor
import com.ricepo.style.view.placeVisible
import com.ricepo.style.view.setEnableForeground

//
// Created by <PERSON><PERSON> on 2/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class HistoryOrderHolder(private val view: View) : RecyclerView.ViewHolder(view) {

  private val restaurantNameView = view.findViewById<TextView>(R.id.tv_order_restaurant_name)
  private val detailView = view.findViewById<TextView>(R.id.tv_order_detail)
  private val priceView = view.findViewById<TextView>(R.id.tv_order_price)
  private val ratingView = view.findViewById<MaterialButton>(R.id.btn_order_rating)
  private val groupOrderView = view.findViewById<TextView>(R.id.tv_order_group_order)
  private val orderTv = view.findViewById<TextView>(R.id.tv_order_number)
  private val root = view.findViewById<View>(R.id.order_history_root)

  init {
    view.clickWithTrigger { v ->
      val order = v.tag
      val position = v.getTag(com.ricepo.base.R.id.tag_position)
      if (order is Order && position is Int) {
        FeaturePageRouter.navigateOrderFromProfile(
          view.context, order,
          FeaturePageConst.REQUEST_CODE_HISTORY_ORDER, position
        )
      }
    }
    ratingView.touchWithTrigger { v, event ->
      if (event.action == MotionEvent.ACTION_UP) {
        val order = v.tag
        val position = v.getTag(com.ricepo.base.R.id.tag_position)
        if (order is Order && position is Int) {
          FeaturePageRouter.navigateDriverRating(view.context, order, position)
        }
      }
    }
  }

  fun bind(order: Order, position: Int, size: Int) {

    val params = view.layoutParams
    if (params is RecyclerView.LayoutParams) {
      params.leftMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.card_side_margin)
      params.rightMargin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.card_side_margin)
      view.layoutParams = params
    }
    if (position == (size - 1)) {
      view.setBackgroundResource(com.ricepo.style.R.drawable.bg_restaurant_card_bottom)
    } else {
      view.setBackgroundResource(com.ricepo.style.R.color.card_background)
    }

    // cache the order
    view.tag = order
    view.setTag(com.ricepo.base.R.id.tag_position, position)
    ratingView.tag = order
    ratingView.setTag(com.ricepo.base.R.id.tag_position, position)

    // restaurant name
    restaurantNameView.text = order.restaurant?.name?.localize() ?: ""

    // group order flag
    groupOrderView.isVisible = order.group != null

    // order created at
    detailView.text = ProfileMapper.mapOrderCreatedAt(order)

    // order price
    priceView.text = ProfileMapper.mapOrderPrice(order)

    orderTv.text = ResourcesUtil.getString(com.ricepo.style.R.string.support_order_number, order.passcode)

    val allowRating = order.allowRating ?: false

    val ratingText = when {
      order.status == OrderState.CANCELLED -> {
        ResourcesUtil.getString(com.ricepo.style.R.string.canceld)
      }
      allowRating -> {
        ResourcesUtil.getString(com.ricepo.style.R.string.rating_now)
      }
      order.rating != null -> {
        ResourcesUtil.getString(com.ricepo.style.R.string.rated)
      }
      else -> {
        ""
      }
    }
    with(ratingView) {
      text = ratingText
      placeVisible(ratingText.isNotBlank())
      isEnabled = allowRating
      if (allowRating) {
        strokeWidth = 0
        backgroundTintList = ColorStateList.valueOf(root.getColor(com.ricepo.style.R.color.mr))
        setTextColor(Color.WHITE)
      } else {
        setStrokeColorResource(com.ricepo.style.R.color.fun_n6)
        backgroundTintList = ColorStateList.valueOf(root.getColor(com.ricepo.style.R.color.card_background))
        setTextColor(root.getColor(com.ricepo.style.R.color.mainText))
        strokeWidth = 1.dp
      }
    }

    // order status
    root.setEnableForeground(order.status != OrderState.CANCELLED)
  }

  companion object {
    fun create(parent: ViewGroup): HistoryOrderHolder {
      val view = LayoutInflater.from(parent.context)
        .inflate(R.layout.profile_item_history_order, parent, false)
      return HistoryOrderHolder(view)
    }
  }
}
