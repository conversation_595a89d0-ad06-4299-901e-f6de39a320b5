package com.ricepo.app.features.address

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ricepo.app.utils.SingleLiveEvent
import com.ricepo.app.utils.log
import com.ricepo.base.viewmodel.BaseViewModel
import com.ricepo.map.PlacesFacade
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.map.model.PlaceAutocompleteResult
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers
import io.reactivex.rxjava3.subjects.PublishSubject
import java.util.concurrent.TimeUnit
import javax.inject.Inject

//
// Created by <PERSON><PERSON> on 7/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * search address view model by google places
 * need bind context target to controller view model lifecycle by create
 * provided an @Inject or an @Provides-annotated method
 */
@HiltViewModel
class AddressViewModel @Inject constructor(
  private val dataSource: AddressDao,
  private val ukPostcodeAddressUseCase: UkPostcodeAddressUseCase

) : BaseViewModel() {

  data class Input(
    val searchText: PublishSubject<CharSequence>,
    val predictionSelected: PublishSubject<PlaceAutocompleteResult>,
    val onSave: Observable<Boolean>,
    val aptText: PublishSubject<String>,
    val noteText: PublishSubject<String>,
    val zipCode: PublishSubject<String>,
    val historySelected: Observable<FormatUserAddress>,
    val onLoading: () -> Unit,
    val dismissLoading: () -> Unit,
    val onError: (String?) -> Unit,
    val onBackEvent: () -> Unit,
  )

  interface Output {
    val predictions: PublishSubject<List<PlaceAutocompleteResult>>
    val historyData: LiveData<List<FormatUserAddress>>
    val showAddressBottom: PublishSubject<AddressUiType>
    val addressUpdated: PublishSubject<FormatUserAddress>
  }

  enum class AddressUiType {
    /**
     * show search address history
     */
    ShowHistory,

    /**
     * show search places predictions
     */
    ShowPredictions,

    /**
     * show save address form
     */
    ShowForm,

    /**
     * hide search address history
     */
    HideHistory,

    /**
     * hide search places predictions
     */
    HidePredictions,

    /**
     * hide save address form
     */
    HideForm
  }

  var disposeOnSave: Disposable? = null

  val saveAble = MutableLiveData(false)

  val showPostCOde = MutableLiveData(false)

  val showPostChangeResult = SingleLiveEvent<List<FormatUserAddress>>()

  var currentNetworkPostcode: String? = null

  fun transform(input: Input): Output {
//    fun transform(): AddressViewModel.Output {

    val predictions = PublishSubject.create<List<PlaceAutocompleteResult>>()
    val showAddressBottom = PublishSubject.create<AddressUiType>()
    val addressUpdated = PublishSubject.create<FormatUserAddress>()

    // observable search text is not empty
    val searchIsNotEmpty = input.searchText.flatMap {
      Observable.just(it.isNotEmpty())
    }

    // observable autocomplete is not empty
    val fetchAutoCompleteIsNotEmpty = predictions.flatMap {
      // not need judge null
      // onNext called with a null value. Null values are generally not allowed in 3.x operators and sources.
      Observable.just(it.isNotEmpty())
    }

    fetchAutoCompleteIsNotEmpty.subscribe()

    // observable address detail it not empty
    val fetchAddressDetailIsNotEmpty = addressUpdated.flatMap {
      Observable.just(!(it.placeId.isNullOrEmpty()))
    }.doOnError {
      it.printStackTrace()
    }

    var searchJob: Disposable? = null
    input.searchText
      .filter { it.isNotEmpty() }
      .throttleLast(700, TimeUnit.MILLISECONDS)
      .map {
        "fetch data from api".log()
        searchJob?.dispose()
        PlacesFacade.instance.findAutocompletePredictions(it.toString())
          .doOnSubscribe { dispose ->
            searchJob = dispose
          }
          .subscribe(predictions)
      }
      .subscribe()

    input.predictionSelected.subscribe {
      PlacesFacade.instance.fetchPlaceById(it.placeID, it.description)
        .subscribe(
          { formattedAddress ->
            if (formattedAddress.placeId.isNullOrEmpty()) {
              input.onError.invoke(null)
            } else {
              // save the zipcode from google backend,
              // when is uk not fetch by postcode again in this situation
              currentNetworkPostcode = formattedAddress.zipcode
              addressUpdated.onNext(formattedAddress)
            }
          },
          {
          }
        )
    }

    val historySelectedIsNotEmpty = input.historySelected.flatMap { Observable.just(it is Any) }

    // history address item selected
    input.historySelected.subscribe {
      addressUpdated.onNext(it)
    }

    // show history
    searchIsNotEmpty
      .filter { it == false }
      .subscribe {
        showAddressBottom.onNext(AddressUiType.ShowHistory)
      }

    // hide history
    Observable.merge(searchIsNotEmpty, historySelectedIsNotEmpty)
      .filter { it == true }
      .subscribe {
        showAddressBottom.onNext(AddressUiType.HideHistory)
      }

    // show prediction
    searchIsNotEmpty
      .filter { it == true }
      .subscribe {
        showAddressBottom.onNext(AddressUiType.ShowPredictions)
      }

    // hide prediction
    Observable.merge(searchIsNotEmpty.map { it == false }, fetchAddressDetailIsNotEmpty)
      .filter { it == true }
      .subscribe(
        {
          showAddressBottom.onNext(AddressUiType.HidePredictions)
        },
        {
          it.printStackTrace()
        }
      )

    // show form
    Observable.merge(historySelectedIsNotEmpty, fetchAddressDetailIsNotEmpty)
      .subscribe(
        {
          showAddressBottom.onNext(AddressUiType.ShowForm)
        },
        {
          it.printStackTrace()
        }
      )

    // hide form
    searchIsNotEmpty
      .filter { it == true }
      .subscribe {
        showAddressBottom.onNext(AddressUiType.HideForm)
      }

    // save history address data

    // must all combine observable already emmit value callback
    val combineAddress = Observable.combineLatest(
      addressUpdated,
      input.aptText,
      input.noteText,
      input.zipCode,
    ) { address, apt, note, zipcode ->
      address.apply {
        this.unit = apt
        this.note = note
        this.zipcode = zipcode
        val cantSave = (notUsOrIsNewYork() && apt.isNullOrEmpty()) ||
          (isUk() && !isValidUkPostCode())
        showPostCOde.postValue(isUk())
        saveAble.postValue(!cantSave)
      }
    }.onErrorComplete {
      it.printStackTrace()
      true
    }
    // only main observable emmit new value callback, after aux observable already emmit value
    input.onSave.withLatestFrom(
      combineAddress
    ) { _, address ->
      (
        if (
          address.notUsOrIsNewYork() &&
          address.unit.isNullOrEmpty()
        )
          FormatUserAddress() else address
        )
    }
      .observeOn(Schedulers.io())
      .doOnSubscribe {
        // the monitor hooker need dispose on subscribe
        disposeOnSave = it
      }
      .onErrorComplete {
        it.printStackTrace()
        true
      }
      .subscribe(
        {
          if (it.name.isNullOrEmpty()) {
            // alert with context and main looper
          } else {
            // Cannot access database on the main thread since it may potentially lock the UI for a long period of time.
            // need observeOn schedulers.io
            ukPostcodeAddressUseCase.updateAddressByPostcode(
              scope = viewModelScope,
              address = it,
              networkPostcode = currentNetworkPostcode,
              onSuccess = { result ->
                input.dismissLoading()
                if (result == null) {
                  AddressCache.saveAddress(it) {
                    // navigate prev page
                    input.onBackEvent()
                  }
                } else {
                  showPostChangeResult.postValue(result)
                }
              },
              onLoading = input.onLoading,
              onError = { message ->
                input.dismissLoading()
                input.onError.invoke(message)
              }
            )
          }
        },
        {
          it.printStackTrace()
        }
      )

    // query history address data
    val historyData = dataSource.getAllAddress()

    return object : Output {
      override val predictions: PublishSubject<List<PlaceAutocompleteResult>>
        get() = predictions
      override val historyData: LiveData<List<FormatUserAddress>>
        get() = historyData
      override val showAddressBottom: PublishSubject<AddressUiType>
        get() = showAddressBottom
      override val addressUpdated: PublishSubject<FormatUserAddress>
        get() = addressUpdated
    }
  }

  override fun onCleared() {
    super.onCleared()
    disposeOnSave?.dispose()
  }
}
