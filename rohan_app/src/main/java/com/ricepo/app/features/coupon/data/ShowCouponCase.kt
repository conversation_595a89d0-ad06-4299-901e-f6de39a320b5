package com.ricepo.app.features.coupon.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

//
// Created by <PERSON><PERSON> on 4/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/*
 * Show mode for coupon page
 * If the flow is Checkout -> Coupon, show all coupon
 * If the flow is Setting-> Coupon, only show customer coupon and coupon is readonly
 */
@Parcelize
enum class ShowCouponCase : Parcelable {
  all,
  customer
}
