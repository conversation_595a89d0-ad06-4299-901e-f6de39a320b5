package com.ricepo.app.features.menu.adapter.holder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.ricepo.app.R
import com.ricepo.app.databinding.MenuSectionGalleryHorizontalBinding
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.FunFoodQty
import com.ricepo.app.features.menu.adapter.FunShowMore
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.model.localize
import com.ricepo.base.image.ImageLoader
import com.ricepo.base.model.Category
import com.ricepo.base.model.Food
import com.ricepo.base.model.Restaurant
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.headerlayout.SectionItemHolder

//
// Created by <PERSON><PERSON> on 3/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MenuGalleryHorizontalHolder(
  private val binding: MenuSectionGalleryHorizontalBinding,
  private val addFood: FunFoodQty,
  private val minusFood: FunFoodQty,
  private val showMore: FunShowMore
) :
  SectionItemHolder(binding.root) {

  val mapper = MenuMapper()

  var menuAdapter: MenuSectionAdapter? = null

  var foodItems: List<Food>? = null

  fun bind(
    uiModel: MenuUiModel.MenuGallerySection,
    galleryPosition: Int
  ) {

    val restaurant = uiModel.restaurant
    val uiModels = mutableListOf<MenuUiModel>()
    foodItems = uiModel.category?.items
    // width = 135 + 26
    uiModels.add(
      MenuUiModel.MenuBottomItem(
        width = ResourcesUtil.getDimenPixelOffset(
          binding.root, com.ricepo.style.R.dimen.sw_151dp
        )
      )
    )
    foodItems?.forEachIndexed { index, food ->
      uiModels.add(MenuUiModel.MenuGalleryItem(restaurant, galleryPosition, food, index))
    }

    if (menuAdapter == null) {
      menuAdapter = MenuSectionAdapter(
        uiModels, addFood = addFood, minusFood = minusFood,
        showMore = showMore, navMenu = {}
      )
      binding.rvMenuContainer.adapter = menuAdapter
      binding.rvMenuContainer.recycledViewPool.setMaxRecycledViews(R.layout.menu_gallery_item, 0)
    } else {
      menuAdapter?.models = uiModels
      menuAdapter?.notifyDataSetChanged()
    }
    setGalleryBackground(uiModel.category, restaurant)
  }

  fun bindQtyView(food: Food) {
    val foodPosition = foodItems?.indexOfFirst { it.id == food.id } ?: -1

    if (foodPosition == -1) {
      menuAdapter?.notifyDataSetChanged()
    } else {
      menuAdapter?.notifyItemChanged(foodPosition, food)
    }
  }

  private fun setGalleryBackground(category: Category?, restaurant: Restaurant?) {
    if (category?.background != null) {
      // use post otherwise view width and height is zero
      binding.ivMenuFeaturedBg.post {
        ImageLoader.loadTargetHorizontal(
          binding.ivMenuFeaturedBg,
          category.background?.localize()
        )
      }
    } else {
      binding.ivMenuFeaturedBg.setImageResource(0)
    }
  }

  companion object {
    fun create(
      parent: ViewGroup,
      addFood: FunFoodQty,
      minusFood: FunFoodQty,
      showMore: FunShowMore
    ): MenuGalleryHorizontalHolder {
      val binding = MenuSectionGalleryHorizontalBinding.inflate(LayoutInflater.from(parent.context))
      val height = ResourcesUtil.getDimenPixelOffset(binding.root, com.ricepo.style.R.dimen.sw_368dp)

      val params = ConstraintLayout.LayoutParams(
        ConstraintLayout.LayoutParams.MATCH_PARENT,
        height
      )
      binding.rvMenuContainer.layoutParams = params
      val layoutManager = StaggeredGridLayoutManager(
        2, LinearLayoutManager.HORIZONTAL
      )
      binding.rvMenuContainer.layoutManager = layoutManager
      binding.rvMenuContainer.isNestedScrollingEnabled = false
      binding.rvMenuContainer.itemAnimator = null
      return MenuGalleryHorizontalHolder(binding, addFood, minusFood, showMore)
    }
  }
}
