package com.ricepo.app.features.order

import android.graphics.PointF
import android.view.Gravity
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.view.setPadding
import androidx.fragment.app.FragmentActivity
import com.ricepo.app.R
import com.ricepo.app.databinding.MapLabelMarkerBinding
import com.ricepo.app.features.order.data.DeliveryState
import com.ricepo.app.features.order.data.OrderState
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderStatus
import com.ricepo.map.CompassPoint
import com.ricepo.style.DisplayUtil
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.DrawableAnimationView

//
// Created by <PERSON><PERSON> on 16/7/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
open class OrderMapRender(
  private val activity: FragmentActivity,
  private val view: View?
) {

  fun addDriverOverlayView(driverPoint: PointF?) {
    val driverPoint = driverPoint ?: return
    val act = activity
    val layout = view?.parent
    if (act != null && layout is FrameLayout) {

      // clear restaurant view if exists
      clearDriverOverlay(layout)

      val animView = DrawableAnimationView(act)
      animView.setTag(com.ricepo.map.R.id.map_driver_icon)
      val params = FrameLayout.LayoutParams(
        FrameLayout.LayoutParams.WRAP_CONTENT,
        FrameLayout.LayoutParams.WRAP_CONTENT
      )
      params.width = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_icon_size)
      params.height = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_icon_size)
      animView.layoutParams = params

      animView.setData(getDriverAnimDatas())
      animView.isRepeat = true
      animView.setGravity(Gravity.FILL)

      animView.y = driverPoint.y - (params.height / 2)
      animView.x = driverPoint.x - (params.width / 2)

      layout.addView(animView)

      animView.start()
    }
  }

  fun addHomeOverlayView(
    point: PointF,
    resId: Int,
    labelView: View,
    direction: CompassPoint
  ) {
    val act = activity
    val layout = view?.parent
    if (act != null && layout is FrameLayout) {

      // clear home view if exists
      clearHomeOverlay(layout)

      val imageView = ImageView(act)
      imageView.setTag(com.ricepo.map.R.id.map_home_icon)
      imageView.setImageResource(resId)
      val params = FrameLayout.LayoutParams(
        FrameLayout.LayoutParams.WRAP_CONTENT,
        FrameLayout.LayoutParams.WRAP_CONTENT
      )
      params.width = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_icon_size)
      params.height = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_icon_size)
      imageView.layoutParams = params

      imageView.y = point.y - (params.height / 2)
      imageView.x = point.x - (params.width / 2)

      layout.addView(imageView)

      // add label view
      addOverlayLabelView(direction, labelView, imageView, params, layout)
    }
  }

  private fun clearHomeOverlay(layout: FrameLayout) {
    val custIconView = layout.findViewWithTag<View>(com.ricepo.map.R.id.map_home_icon)
    val custLabelView = layout.findViewWithTag<View>(com.ricepo.map.R.id.map_home_label)
    if (custIconView != null) {
      layout.removeView(custIconView)
    }
    if (custLabelView != null) {
      layout.removeView(custLabelView)
    }
  }

  private fun clearRestOverlay(layout: FrameLayout) {
    val restIconView = layout.findViewWithTag<DrawableAnimationView>(com.ricepo.map.R.id.map_rest_icon)
    val restLabelView = layout.findViewWithTag<View>(com.ricepo.map.R.id.map_rest_label)
    if (restIconView != null) {
      restIconView.quit()
      layout.removeView(restIconView)
    }
    if (restLabelView != null) {
      layout.removeView(restLabelView)
    }
  }

  fun clearDriverOverlay(layout: FrameLayout) {
    val driverIconView = layout.findViewWithTag<DrawableAnimationView>(com.ricepo.map.R.id.map_driver_icon)
    if (driverIconView != null) {
      driverIconView.quit()
      layout.removeView(driverIconView)
    }
  }

  fun addRestOverlayAnimView(
    point: PointF,
    animDatas: ArrayList<DrawableAnimationView.AnimData>?,
    labelView: View,
    direction: CompassPoint
  ) {
    val act = activity
    val layout = view?.parent
    if (act != null && layout is FrameLayout) {

      // clear restaurant view if exists
      clearRestOverlay(layout)

      val animView = DrawableAnimationView(act)
      animView.setTag(com.ricepo.map.R.id.map_rest_icon)
      val params = FrameLayout.LayoutParams(
        FrameLayout.LayoutParams.WRAP_CONTENT,
        FrameLayout.LayoutParams.WRAP_CONTENT
      )
      params.width = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_icon_size)
      params.height = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_icon_size)
      animView.layoutParams = params

      animView.setData(animDatas)
      animView.isRepeat = true
      // 1.2 seconds
      animView.mAnimTime = 1200 / (animDatas?.size ?: 75)
      animView.setGravity(Gravity.FILL)

      animView.y = point.y.toFloat() - (params.height / 2)
      animView.x = point.x.toFloat() - (params.width / 2)

      layout.addView(animView)

      animView.start()

      // add label view
      addOverlayLabelView(direction, labelView, animView, params, layout)
    }
  }

  private fun addOverlayLabelView(
    direction: CompassPoint,
    labelView: View,
    anchorView: View,
    anchorParams: FrameLayout.LayoutParams,
    layout: FrameLayout
  ) {
    val widthMeasure = View.MeasureSpec.makeMeasureSpec(
      0,
      View.MeasureSpec.UNSPECIFIED
    )
    val heightMeasure = View.MeasureSpec.makeMeasureSpec(
      0,
      View.MeasureSpec.UNSPECIFIED
    )
    labelView.measure(widthMeasure, heightMeasure)

    val paddingHori = ResourcesUtil.getDimenPixelOffset(labelView, com.ricepo.style.R.dimen.sw_15dp)
    val paddingVert = ResourcesUtil.getDimenPixelOffset(labelView, com.ricepo.style.R.dimen.sw_15dp)
    val xTransOffset = ResourcesUtil.getDimenPixelOffset(labelView, com.ricepo.style.R.dimen.sw_26dp)

    val width = labelView.measuredWidth + + (2 * paddingHori)
    val height = labelView.measuredHeight
    val xOffset = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.map_label_horizontal_offset)
//        val yOffset = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.map_label_vertical_offset)
    val yOffset = 0

    // label x position
    when (direction) {
      CompassPoint.south,
      CompassPoint.southEast,
      CompassPoint.east,
      CompassPoint.northEast -> {
        // aligned to the right
        labelView.x = anchorView.x + anchorParams.width - width - xOffset + (xTransOffset)
        // max label length
        val margin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.margin_right)
        val maxLength = anchorView.x + anchorParams.width - xOffset - margin - paddingHori
        val length = if (width > maxLength) {
          labelView.x = margin.toFloat()
          maxLength
        } else {
          width
        }
        labelView.layoutParams = FrameLayout.LayoutParams(
          length.toInt(),
          FrameLayout.LayoutParams.WRAP_CONTENT
        )
      }
      CompassPoint.north,
      CompassPoint.northWest,
      CompassPoint.west,
      CompassPoint.southWest -> {
        // aligned to the left
        labelView.x = anchorView.x + xOffset - (xTransOffset)
        // max label length
        val margin = ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.margin_right)

        val maxLength = DisplayUtil.getScreenWidth(activity) - labelView.x - margin - paddingHori
        val length = if (width > maxLength) {
          maxLength
        } else {
          width
        }
        labelView.layoutParams = FrameLayout.LayoutParams(
          length.toInt(),
          FrameLayout.LayoutParams.WRAP_CONTENT
        )
      }
    }

    // label y position
    when (direction) {
      CompassPoint.east,
      CompassPoint.southEast,
      CompassPoint.south,
      CompassPoint.southWest -> {
        // bottom of anchor view
        labelView.y = anchorView.y + anchorParams.height + yOffset
      }
      CompassPoint.west,
      CompassPoint.northWest,
      CompassPoint.north,
      CompassPoint.northEast -> {
        // top of anchor view
        labelView.y = anchorView.y - height - yOffset
      }
    }

    labelView.setPadding(
      paddingHori, paddingVert, paddingHori,
      ResourcesUtil.getDimenPixelOffset(labelView, com.ricepo.style.R.dimen.sw_20dp)
    )

    layout.addView(labelView)
    // delivery note to bring front
    layout.findViewById<CardView?>(R.id.card_delivery_note)?.bringToFront()
  }

  fun getRestAnimDatas(order: Order): java.util.ArrayList<DrawableAnimationView.AnimData>? {
    return when (order.obtainState()) {
      OrderState.CREATED,
      OrderState.SENT -> {
        getRestPendingAnimDatas()
      }
      OrderState.CONFIRMED -> {
        getRestPreAnimDatas()
      }
      DeliveryState.EN_ROUTE_TO_PICKUP,
      DeliveryState.AT_PICKUP,
      DeliveryState.COMPLETED -> {
        getRestPickupAnimDatas()
      }
      DeliveryState.PICKUP_COMPLETED,
      DeliveryState.EN_ROUTE_TO_DROPOFF,
      DeliveryState.AT_DROPOFF,
      DeliveryState.DELIVERY -> {
        getRestStillAnimDatas()
      }
      DeliveryState.DELIVERED -> {
        getRestStillAnimDatas()
      }
      else -> {
        getRestStillAnimDatas()
      }
    }
  }

  private fun getDriverAnimDatas(): ArrayList<DrawableAnimationView.AnimData>? {
    return getAnimDatas("approach", 47)
  }

  private fun getRestPendingAnimDatas(): ArrayList<DrawableAnimationView.AnimData>? {
    return getAnimDatas("rest_pending", 31)
  }

  private fun getRestPickupAnimDatas(): ArrayList<DrawableAnimationView.AnimData>? {
    return getAnimDatas("rest_pickup", 28)
  }

  private fun getRestPreAnimDatas(): ArrayList<DrawableAnimationView.AnimData>? {
    return getAnimDatas("rest_pre", 29)
  }

  private fun getRestStillAnimDatas(): ArrayList<DrawableAnimationView.AnimData>? {
    val dataList = ArrayList<DrawableAnimationView.AnimData>()
    var fileName = "rest_still"
    val data = DrawableAnimationView.AnimData()
    data.filePath = ResourcesUtil.getDrawableId(fileName)
    dataList.add(data)
    return dataList
  }

  private fun getAnimDatas(fileName: String, n: Int): ArrayList<DrawableAnimationView.AnimData>? {
    val dataList = ArrayList<DrawableAnimationView.AnimData>()
    var data: DrawableAnimationView.AnimData
    var resId: Int
    var fileName = fileName
    var suffixValue = ""
    for (i in 1..n) {
      if (i < 10) {
        suffixValue += "0"
      }
      try {
        resId = ResourcesUtil.getDrawableId("${fileName}_00$suffixValue$i")
        data = DrawableAnimationView.AnimData()
        data.filePath = resId
        dataList.add(data)
        suffixValue = ""
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
    return dataList
  }

  fun initMarkerLabel(
    text: String,
    textColor: Int,
    direction: CompassPoint,
    order: Order,
    finishAt: String? = null,
    preparedAt: String? = null
  ): View {
//        val view = layoutInflater.inflate(R.layout.map_label_marker, null)
    val binding = MapLabelMarkerBinding.inflate(activity.layoutInflater)
//        val labelView = view.findViewById<TextView>(R.id.tv_map_label_marker)
    binding.tvMapLabelMarker.text = text
    binding.tvMapLabelMarker.setTextColor(textColor)

    val padding = ResourcesUtil.getDimenPixelSize(com.ricepo.style.R.dimen.map_text_padding)

//        binding.llMapFinishAt.isVisible = (finishAt != null || preparedAt != null)
    binding.llMapFinishAt.isVisible = false
    when {
      finishAt != null -> {
        binding.llMapFinishAt.isVisible = true
        binding.ivMapFinishAt.setImageResource(com.ricepo.style.R.drawable.map_delivery_finish_at)
        binding.tvMapFinishAt.text = ResourcesUtil.getString(
          com.ricepo.style.R.string.delivery_finish_at,
          finishAt
        )
      }
      preparedAt != null -> {
        binding.llMapFinishAt.isVisible = OrderStatus.Confirmed.equals(order.status, true) &&
          (
            !listOf(
              DeliveryState.PICKUP_COMPLETED, DeliveryState.EN_ROUTE_TO_DROPOFF,
              DeliveryState.AT_DROPOFF, DeliveryState.COMPLETED
            ).contains(order.delivery?.status)
            )
        binding.ivMapFinishAt.setImageResource(com.ricepo.style.R.drawable.map_restaurant_prepared_at)
        binding.tvMapFinishAt.text = ResourcesUtil.getString(
          com.ricepo.style.R.string.restaurant_prepared_at,
          preparedAt
        )
      }
    }

    if (binding.llMapFinishAt.isVisible) {
      binding.tvMapFinishAt.setTextColor(textColor)
      when (direction) {
        CompassPoint.south,
        CompassPoint.southEast,
        CompassPoint.east,
        CompassPoint.northEast -> {
          // aligned to the right
          val labelParams = binding.llMapLabel.layoutParams
          if (labelParams is ConstraintLayout.LayoutParams) {
            labelParams.leftMargin = padding
            labelParams.rightMargin = padding
            binding.llMapLabel.layoutParams = labelParams
          }
          binding.llMapLabel.gravity = Gravity.RIGHT

          // font size to padding + 1 align right
          val tvPaddingRight = (padding * 2) + ResourcesUtil.getDimenPixelOffset(com.ricepo.style.R.dimen.dip_1)
          binding.ivMapFinishAt.setPadding(padding, padding, padding / 2, 0)
          binding.tvMapFinishAt.setPadding(0, padding, tvPaddingRight, 0)

//                    // the address align right
          binding.tvMapLabelMarker.gravity = Gravity.RIGHT
        }
        CompassPoint.north,
        CompassPoint.northWest,
        CompassPoint.west,
        CompassPoint.southWest -> {
          // aligned to the left
          val labelParams = binding.llMapLabel.layoutParams
          if (labelParams is ConstraintLayout.LayoutParams) {
            labelParams.leftMargin = padding
            labelParams.rightMargin = padding
            binding.llMapLabel.layoutParams = labelParams
          }

          binding.llMapLabel.gravity = Gravity.LEFT

          binding.ivMapFinishAt.setPadding(padding * 2, padding, 0, 0)
          binding.tvMapFinishAt.setPadding(padding / 2, padding, padding, 0)

//                    // the address align left
          binding.tvMapLabelMarker.gravity = Gravity.LEFT
        }
      }
      // fade out/in animation
      val anim = AnimationUtils.loadAnimation(binding.root.context, com.ricepo.style.R.anim.fade_map_finish_at)
      binding.ivMapFinishAt.startAnimation(anim)
    }

    val params = FrameLayout.LayoutParams(
      FrameLayout.LayoutParams.WRAP_CONTENT,
      FrameLayout.LayoutParams.WRAP_CONTENT
    )
    binding.root.layoutParams = params

    if (finishAt != null || preparedAt != null) {
      binding.tvMapLabelMarker.setPadding(padding, 0, padding, padding)
    } else {
      binding.tvMapLabelMarker.setPadding(padding)
    }
    return binding.root
  }

  fun resumeAnimOverlay(isResume: Boolean = false) {
    val layout = view?.parent
    if (layout != null && layout is FrameLayout) {
      pauseDriverOverlay(layout, isResume)
      pauseRestOverlay(layout, isResume)
    }
  }

  fun pauseRestOverlay(layout: FrameLayout, isResume: Boolean = false) {
    val restIconView = layout.findViewWithTag<DrawableAnimationView>(com.ricepo.map.R.id.map_rest_icon)
    if (restIconView != null) {
      if (isResume) {
        restIconView.resume()
      } else {
        restIconView.pause()
      }
    }
  }

  fun pauseDriverOverlay(layout: FrameLayout, isResume: Boolean = false) {
    val driverIconView = layout.findViewWithTag<DrawableAnimationView>(com.ricepo.map.R.id.map_driver_icon)
    if (driverIconView != null) {
      if (isResume) {
        driverIconView.resume()
      } else {
        driverIconView.pause()
      }
    }
  }

  fun quitOverlay(layout: FrameLayout) {
    val restIconView = layout.findViewWithTag<DrawableAnimationView>(com.ricepo.map.R.id.map_rest_icon)
    restIconView?.quit()
    val driverIconView = layout.findViewWithTag<DrawableAnimationView>(com.ricepo.map.R.id.map_driver_icon)
    driverIconView?.quit()
  }
}
