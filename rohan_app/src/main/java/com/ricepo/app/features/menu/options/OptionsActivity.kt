package com.ricepo.app.features.menu.options

import android.os.Bundle
import android.text.SpannableStringBuilder
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.ricepo.app.R
import com.ricepo.app.databinding.ActivityMenuOptionsBinding
import com.ricepo.app.features.FeaturePageConst
import com.ricepo.app.features.menu.MenuMapper
import com.ricepo.app.features.menu.adapter.MenuSectionAdapter
import com.ricepo.app.features.menu.cart.CartAdapter
import com.ricepo.app.features.menu.cart.CartUiModel
import com.ricepo.app.features.menu.data.MenuUiModel
import com.ricepo.app.listener.parseByBuzNetwork
import com.ricepo.app.restapi.CombineRestApi
import com.ricepo.base.BaseActivity
import com.ricepo.base.extension.clickWithTrigger
import com.ricepo.base.extension.flowLoading
import com.ricepo.base.extension.simulateClickListener
import com.ricepo.base.extension.update
import com.ricepo.base.model.Cart
import com.ricepo.base.model.Category
import com.ricepo.base.model.Food
import com.ricepo.base.model.InternationalizationContent
import com.ricepo.base.model.Item
import com.ricepo.base.model.Option
import com.ricepo.base.model.Restaurant
import com.ricepo.base.view.DialogFacade
import com.ricepo.style.view.rv.TopSmoothScroller
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.math.max

//
// Created by Thomsen on 12/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

@AndroidEntryPoint
@Route(path = FeaturePageConst.PAGE_MENU_OPTIONS)
class OptionsActivity : BaseActivity() {

  var food: Food? = null

  var cart: Cart? = null

  var restaurant: Restaurant? = null

  lateinit var mapper: MenuMapper

  var sectionAdapter: MenuSectionAdapter? = null

  val optionsViewModel: OptionsViewModel by viewModels()

  @Inject
  lateinit var combineApi: CombineRestApi

  private lateinit var binding: ActivityMenuOptionsBinding

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    food = intent.getParcelableExtra(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD)
    cart = intent.getParcelableExtra(FeaturePageConst.PARAM_MENU_OPTIONS_CART)
    restaurant = intent.getParcelableExtra(FeaturePageConst.PARAM_MENU_RESTAURANT)

    mapper = MenuMapper()

    binding = ActivityMenuOptionsBinding.inflate(layoutInflater)
    setContentView(binding.root)

    if (food != null) {
      food?.let { setOptionsSectionView(it) }
    } else if (cart != null) {
      getFoodFromServer()
    }
  }

  /**
   * get the food by food id in cart
   */
  private fun getFoodFromServer() {
    lifecycleScope.launch {
      flowLoading<Food>(
        this@OptionsActivity,
        { e ->
          DialogFacade.showAlert(
            this@OptionsActivity,
            e.parseByBuzNetwork().message ?: ""
          )
        }
      ) {
        val restaurantId = restaurant?.id ?: return@flowLoading
        val foodId = cart?.id ?: return@flowLoading
        combineApi.getFood(restaurantId, foodId)
        // original food opt for remove item in cart
        food?.opt = cart?.opt?.toMutableList()
        // set food default qty
        cart?.opt?.groupBy { it.id }?.map { entities ->
          val itemId = entities.key
          val qty = entities.value.size
          food?.options?.map { option ->
            val item = option.items?.find { it.id == itemId }
            if (item != null) {
              item.count = qty
              // option selected for map total price
              val selected = option.selected ?: mutableListOf()
              selected.add(item)
              option.selected = selected
            }
          }
        }
        food?.let { emit(it) }
      }.collectLatest { food ->
        setOptionsSectionView(food)
      }
    }
  }

  private fun setOptionsSectionView(food: Food) {

    var itemPosition = 0

    optionsViewModel.models.clear()
    // option menu
    optionsViewModel.models.add(
      MenuUiModel.MenuOptionsTitle(
        food = food,
        restaurant = restaurant,
        itemPosition = itemPosition,
      )
    )

    // options category and item
    food.options?.forEachIndexed { index, option ->

      itemPosition += 1

      val desc = mapper.mapRange(option)
      optionsViewModel.models.add(
        MenuUiModel.MenuCategoryItem(
          category = Category(
            id = "",
            name = option.name, description = InternationalizationContent(desc, desc, desc, desc)
          ),
          itemPosition = itemPosition, isOptionsCateogry = true
        )
      )

      itemPosition += 1

      val itemsModel = mutableListOf<MenuUiModel.MenuOptionsItem>()
      option.items?.forEach {
        itemsModel.add(
          MenuUiModel.MenuOptionsItem(
            it, option,
            food = food, itemPosition = itemPosition
          )
        )
      }
      optionsViewModel.models.add(MenuUiModel.MenuVerticalSection(restaurant, itemsModel))
    }

    optionsViewModel.models.add(MenuUiModel.MenuBottomItem())

    binding.inMenuCart.tvDelivery.isVisible = false
    binding.inMenuCart.root.isVisible = true
    val observerFood = MutableLiveData(food)
    observerFood.observe(
      this
    ) { food ->
      val pair = mapper.mapOptionsUiModel(this, food)
      val uiModels = mutableListOf<CartUiModel>()
      val price = mapper.toTotalPrice(pair.second, restaurant)
      uiModels.add(
        CartUiModel.CartMenuInfoUiModel(
          SpannableStringBuilder(price),
          showVert = false
        )
      )
      uiModels.addAll(pair.first)
      val cartLayoutManager = LinearLayoutManager(this@OptionsActivity)
      cartLayoutManager.orientation = LinearLayoutManager.HORIZONTAL
      val cartAdapter = CartAdapter(uiModels)
      binding.inMenuCart.rcvMenuCart.apply {
        layoutManager = cartLayoutManager
        adapter = cartAdapter
      }
    }

    val linearLayoutManager = LinearLayoutManager(this)
    sectionAdapter = MenuSectionAdapter(
      optionsViewModel.models,
      addFood = { foodQty ->
        optionsViewModel.addItem(
          foodQty.item, foodQty.option,
          foodQty.food
        ) { item: Item, option: Option, food: Food, position: Int ->
          val sectionPosition = foodQty.sectionPosition ?: -1
          if (sectionPosition > -1) {
            val payload = listOf(item)
            sectionAdapter?.notifyItemChanged(sectionPosition, payload)
          }

          // smooth scroll
          optionsViewModel.smoothTo(binding.recyclerOptionsList, option, food)

          // update food and total in cart, need after removeOptionSelected
          observerFood.update(food)
        }
      },
      minusFood = { foodQty ->
        optionsViewModel.minusItem(
          foodQty.item, foodQty.option,
          foodQty.food
        ) { item: Item, option: Option, food: Food, position: Int ->
          val sectionPosition = foodQty.sectionPosition ?: -1
          if (sectionPosition > -1) {
            val payload = listOf(item)
            sectionAdapter?.notifyItemChanged(sectionPosition, payload)
          }

          // smooth scroll
          optionsViewModel.smoothTo(binding.recyclerOptionsList, option, food)

          // update food and total in cart, need after removeOptionSelected
          observerFood.update(food)
        }
      },
      showMore = { _, _, _ ->
      },
      navMenu = { _ ->
      }
    )

    binding.recyclerOptionsList.apply {
      layoutManager = linearLayoutManager
      adapter = sectionAdapter
    }
    binding.recyclerOptionsList.recycledViewPool.setMaxRecycledViews(R.layout.menu_options_section_item, 0)

    // menu cart click listener
    setupInMenuCart(food)
  }

  private fun setupInMenuCart(food: Food) {
    binding.inMenuCart.rcvMenuCart.simulateClickListener {
      binding.inMenuCart.clMenuCart.performClick()
    }
    binding.inMenuCart.clMenuCart.clickWithTrigger {
      val pair = mapper.mapOptionsComplete(food, restaurant)

      // if invalid message is empty, go success
      if (pair.first.isNullOrEmpty()) {
        // navigate back to menu page and update cart
        intent.putExtra(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD, pair.second)
        intent.putExtra(FeaturePageConst.PARAM_MENU_OPTIONS_FOOD_ORIGINAL, food)
        setResult(RESULT_OK, intent)
        onBackPressed()
      } else {
        DialogFacade.showAlert(this@OptionsActivity, pair.first.toString()) {
          val firstOption = food.options?.firstOrNull {
            var count = 0
            it.selected?.forEach {
              count += it.count ?: 0
            }
            count < (it.min ?: 0)
          }
          if (firstOption != null) {
            var position = 0
            optionsViewModel.models.firstOrNull {
              if (it is MenuUiModel.MenuVerticalSection) {
                val itemModel = it.models?.firstOrNull()
                if (itemModel is MenuUiModel.MenuOptionsItem) {
                  if (itemModel.option?.id == firstOption.id) {
                    position = itemModel.itemPosition ?: 0
                    true
                  } else false
                } else {
                  false
                }
              } else {
                false
              }
            }
            val bottomScroller = TopSmoothScroller(this)
            bottomScroller.targetPosition = max(position - 1, 0)
            val layoutManager = binding.recyclerOptionsList.layoutManager as LinearLayoutManager
            layoutManager.startSmoothScroll(bottomScroller)
          }
        }
      }
    }
  }
}
