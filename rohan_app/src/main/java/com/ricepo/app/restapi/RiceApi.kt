package com.ricepo.app.restapi

import com.ricepo.app.model.ChatModel
import com.ricepo.app.model.EmailReq
import com.ricepo.app.model.PaypalCaptureReq
import com.ricepo.app.model.PhoneMasking
import com.ricepo.app.model.PopUp
import com.ricepo.app.utils.FeedBack
import com.skydoves.sandwich.ApiResponse
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

interface RiceApi {
  /**
   * add coupon
   */
  @POST("/v1/coupons/{code}/redeem")
  suspend fun addCoupon(
    @Path("code") code: String,
  ): ApiResponse<Unit>

  /**
   * phoneMasking
   */
  @POST("/v1/twilio/phoneMasking/orders/{order_id}")
  suspend fun fetchPhoneMasking(
    @Path("order_id") order_id: String
  ): ApiResponse<PhoneMasking>

  @POST("/v1/flex/chat/token")
  suspend fun getSupportChatToken(): ApiResponse<ChatModel>

  /**
   *  paypal capture
   */

  @POST("/v1/paypal/{paypal_order_id}/capture")
  suspend fun paypalCapture(
    @Path("paypal_order_id") paypal_order_id: String,
    @Body paypalCaptureReq: PaypalCaptureReq
  ): ApiResponse<Unit>

  @POST("/v1/email/verify")
  suspend fun userEmail(
    @Body emailReq: EmailReq
  ): ApiResponse<Unit>

  @GET("/v1/popup/region/{region_id}")
  suspend fun fetchPopup(
    @Path("region_id") region_id: String
  ): ApiResponse<List<PopUp>>

  @POST("/v1/customers/feedback")
  suspend fun appRatingFeedback(
    @Body feedBack: FeedBack
  ): ApiResponse<Unit>

  @DELETE("/v2/customers/{customer_id}")
  suspend fun deleteAccount(
    @Path("customer_id") customer_id: String
  ): ApiResponse<Unit>
}
