package com.ricepo.app.restapi

import com.ricepo.app.model.Card
import com.ricepo.app.model.ChatModel
import com.ricepo.app.model.Coupon
import com.ricepo.app.model.ImageFeedbackReq
import com.ricepo.app.model.Order
import com.ricepo.app.model.OrderReq
import com.ricepo.app.model.OrderResponse
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.QuoteRequest
import com.ricepo.app.model.QuoteResponse
import com.ricepo.app.model.SupportReq
import com.ricepo.app.model.ValidateCouponReq
import com.ricepo.app.model.ValidateCouponRes
import com.ricepo.base.model.DriverPoint
import com.ricepo.base.model.DriverTrace
import com.ricepo.base.model.Menu
import com.ricepo.base.model.MenuBean
import com.ricepo.base.model.RegionModel
import com.ricepo.base.model.Restaurant
import com.ricepo.network.resource.NetworkError
import io.reactivex.rxjava3.core.Single
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query
import retrofit2.http.QueryMap

interface RestaurantRestApi {

  @GET("/v1/restaurants")
  fun getRestaurants(
    @Query(value = "location", encoded = true) loc: String?,
    @Query("search") search: String?
  ): Single<List<Restaurant>>

  @GET("/v1/restaurants/{id}")
  fun getRestaurantById(
    @Path("id") restaurantId: String,
    @Query("location", encoded = true) loc: String?
  ): Single<Restaurant>

  /**
   * get home banners and motd
   */
  @GET("/v1/regions/{id}")
  fun getRegion(@Path("id") regionId: String): Single<RegionModel>

  @GET("/v1/restaurants/{id}/menu")
  fun getMenu(@Path("id") restaurantId: String): Single<Menu>

  @GET("/v2/restaurants/{id}/menu")
  fun getMenu(
    @Path("id") restaurantId: String,
    @QueryMap params: Map<String, String>?,
    @Query(value = "bundles[]", encoded = true) type: Array<String>?,
    @Query(value = "searches[]", encoded = true) searches: Array<String>?
  ): Single<MenuBean>

  @POST("/v1/tickets")
  fun imageFeedback(@Body req: ImageFeedbackReq): Single<NetworkError>

  @POST("/v2/restaurants/{id}/quote")
  fun getQuote(
    @Path("id") restaurantId: String,
    @Body body: QuoteRequest
  ): Single<QuoteResponse>

  @GET("/v1/customers/{customerId}/coupons")
  fun getCouponByCustomerId(@Path("customerId") customerId: String): Single<List<Coupon>>

  @GET("/v1/restaurants/{restaurantId}/coupons")
  fun getCouponByRestId(@Path("restaurantId") restaurantId: String): Single<List<Coupon>>

  @GET("/v1/region/{regionId}/coupons")
  fun getCouponByRegionId(@Path("regionId") regionId: String): Single<List<Coupon>>

  @POST("/v1/coupons/{couponId}/validate")
  fun validateCoupon(
    @Path("couponId") couponId: String,
    @Body body: ValidateCouponReq
  ): Single<ValidateCouponRes>

  @POST("/v1/coupons/recommend")
  fun getRecommendCoupons(@Body body: ValidateCouponReq): Single<List<Coupon>>

  @GET("/v2/cards")
  fun getCards(@Query(value = "type[]", encoded = true) type: Array<String>): Single<List<Card>>

  @GET("/v2/customers/{customerId}/cards")
  fun getCards(
    @Path("customerId") customerId: String,
    @Query(value = "type[]", encoded = true) type: Array<String>
  ): Single<List<Card>>

  @DELETE("/v1/customers/{customerId}/cards/{cardId}")
  fun deleteCard(
    @Path("customerId") customerId: String,
    @Path("cardId") cardId: String
  ): Single<retrofit2.Response<Unit>>

  @DELETE("/v2/customers/{customerId}/cards/{cardId}")
  fun deleteBbvaCard(
    @Path("customerId") customerId: String,
    @Path("cardId") cardId: String
  ): Single<retrofit2.Response<Unit>>

  @POST("/v2/restaurants/{restaurantId}/orders")
  fun createOrder(
    @Path("restaurantId") restaurantId: String,
    @Body body: OrderReq
  ): Single<OrderResponse>

  // if add @Path need url
  // URL "/v1/orders/{orderId}/payment/intent" does not contain "{stripeId}"
  @POST("/v1/orders/{orderId}/payment/intent")
  fun createIntentPayment(
    @Path("orderId") orderId: String,
    @Body body: Map<String, String>
  ): Single<PaymentObj>

  @POST("/v1/orders/{orderId}/payment/wechat")
  fun createWechatPayment(@Path("orderId") orderId: String): Single<PaymentObj>

  @POST("/v1/orders/{orderId}/payment/alipay")
  fun createAlipayPayment(@Path("orderId") orderId: String): Single<PaymentObj>

  @POST("/v1/orders/{orderId}/payment/unionpay")
  fun createUnionPayment(@Path("orderId") orderId: String): Single<PaymentObj>

  @POST("/v1/orders/{orderId}/payment/paypal")
  fun createPaypalPayment(@Path("orderId") orderId: String): Single<PaymentObj>

  @POST("/v1/orders/{orderId}/payment/bbva")
  fun createBBVAPayment(
    @Path("orderId") orderId: String,
    @Body body: Map<String, String>): Single<PaymentObj>

  @POST("/v1/subscription")
  fun createSubscription(@Body body: Map<String, String>): Single<PaymentObj>

  @PUT("/v1/subscription/{subscriptionId}")
  fun cancelSubscription(
    @Path("subscriptionId") subscriptionId: String,
    @Body body: Map<String, Boolean>
  ): Single<Void>

  @PUT("/v1/subscription/{subscriptionId}")
  fun updateSubscriptionPayment(
    @Path("subscriptionId") subscriptionId: String,
    @Body body: Map<String, String>
  ): Single<Void>

  @GET("/v1/restaurants/5a4af4116984300014ff1670/orders/{orderId}")
  fun getOrderById(@Path("orderId") orderId: String): Single<Order>

  @GET("/v1/orders/{orderId}/location")
  fun getPoint(@Path("orderId") orderId: String): Single<DriverPoint>

  @GET("/v2/orders/{orderId}/location")
  fun getDriverTrace(@Path("orderId") orderId: String): Single<DriverTrace>

  @GET("/v1/customers/{customerId}/orders")
  fun getOrders(
    @Path("customerId") customerId: String,
    @QueryMap params: Map<String, @JvmSuppressWildcards Any>
  ): Single<List<Order>>

  @POST("/v1/support/orders/{orderId}/tickets")
  fun createTicket(@Path("orderId") orderId: String, @Body body: SupportReq): Single<Any>

  @POST("/v1/flex/chat/token")
  fun getSupportChatToken(): Single<ChatModel>

  @POST("/v1/file")
  fun uploadImage(): Single<String>
}
