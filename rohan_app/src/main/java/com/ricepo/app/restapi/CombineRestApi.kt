package com.ricepo.app.restapi

import androidx.paging.PagingSource
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.model.FeedbackReq
import com.ricepo.app.model.Order
import com.ricepo.app.model.PaymentObj
import com.ricepo.app.model.PointsSummaryModel
import com.ricepo.app.model.PromoInfo
import com.ricepo.app.model.RatingReq
import com.ricepo.app.model.ReferInfo
import com.ricepo.app.model.RestaurantPrediction
import com.ricepo.app.model.RewardSummaryModel
import com.ricepo.app.model.SearchTag
import com.ricepo.app.model.SupportReq
import com.ricepo.app.model.SupportRuleGroup
import com.ricepo.app.model.SupportRuleItem
import com.ricepo.app.model.UpdateInfo
import com.ricepo.base.model.CreateOrderGroupReq
import com.ricepo.base.model.Customer
import com.ricepo.base.model.Food
import com.ricepo.base.model.LuckRecommendBean
import com.ricepo.base.model.OrderGroup
import com.ricepo.base.model.RegionModel
import com.ricepo.base.model.Restaurant
import com.ricepo.base.model.RestaurantRemoteGroup
import com.ricepo.base.model.RestaurantRemoteSection
import com.ricepo.base.model.RestaurantSearchResult
import com.ricepo.base.model.UpdateOrderGroupReq
import com.ricepo.network.resource.ErrorCode
import com.ricepo.network.resource.NetworkError
import com.ricepo.network.resource.parseByNetwork
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query
import retrofit2.http.QueryMap

//
// Created by Thomsen on 3/7/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * the api communication setup via retrofit and kotlin suspend
 */
interface CombineRestApi {

  // java.lang.IllegalArgumentException: Parameter type must not include a type variable or wildcard:
  // java.util.Map<java.lang.String, ?> (parameter #2) @JvmSuppressWildcards Any
  @GET("/v1/customers/{customerId}/orders")
  suspend fun getOrders(
    @Path("customerId") customerId: String,
    @QueryMap params: Map<String, @JvmSuppressWildcards Any>
  ): List<Order>

  @GET("/v1/customers/{id}")
  suspend fun getCustomer(@Path("id") customerId: String): Customer

  /**
   * rating api
   */
  @POST("/v1/orders/{orderId}/rating")
  suspend fun driverRating(@Path("orderId") orderId: String, @Body body: RatingReq): Order

  /**
   * order support create ticket
   */
  @POST("/v1/support/orders/{orderId}/tickets")
  suspend fun createTicket(@Path("orderId") orderId: String, @Body body: SupportReq): Any

  @Multipart
  @POST("/v1/file")
  suspend fun uploadImage(
    @Part("description") description: RequestBody,
    @Part() parts: List<MultipartBody.Part>
  ): String

  /**
   * restaurant search tag
   */
  @GET("/v1/regions/{regionId}/search")
  suspend fun getSearchTags(@Path("regionId") regionId: String): List<SearchTag>

  /**
   * get the restaurants
   */
  @GET("/v1/restaurants")
  suspend fun getRestaurants(
    @Query(value = "location", encoded = true) loc: String?,
    @Query("search") search: String?,
    @Query("distance") distance: Int?,
  ): RestaurantSearchResult

  /**
   * get the pickup restaurants in map
   */
  @GET("/v1/restaurants")
  suspend fun getPickupRestaurants(
    @Query(value = "location", encoded = true) loc: String?,
    @Query("search") search: String?,
    @Query("distance") distance: Int?,
  ): List<Restaurant>

  /**
   * get the rank
   */
  @GET("/v1/restaurants")
  suspend fun getRanks(
    @Query(value = "location", encoded = true) loc: String?,
    @Query(value = "ranking", encoded = true) ranking: String = "true"
  ): List<Food>

  @POST("/v1/restaurants/5a4af4116984300014ff1670/carts")
  suspend fun createGroupOrder(@Body req: CreateOrderGroupReq): OrderGroup

  @PUT("/v1/carts/{groupId}")
  suspend fun updateGroupOrder(
    @Path("groupId") groupId: String,
    @Body req: UpdateOrderGroupReq
  ): OrderGroup

  @DELETE("/v1/carts/{groupId}/customer/{deviceId}")
  suspend fun deleteGroupOrder(
    @Path("groupId") groupId: String,
    @Path("deviceId") deviceId: String
  ): retrofit2.Response<Unit>

  @GET("/v1/restaurants/5a4af4116984300014ff1670/carts/{groupId}/deviceId/{deviceId}")
  suspend fun getGroupOrder(
    @Path("groupId") groupId: String,
    @Path("deviceId") deviceId: String
  ): OrderGroup

  @GET("/v1/customers/{customerId}/rewards")
  suspend fun getRewardsByCustomer(@Path("customerId") customerId: String): List<RewardSummaryModel>

  @GET("/v1/customers/{customerId}/point/history")
  suspend fun getPointsByCustomer(@Path("customerId") customerId: String): List<PointsSummaryModel>

  /**
   * firebase messaging token
   */
  @POST("/v1/customers/{customerId}/apn")
  suspend fun postApn(
    @Path("customerId") customerId: String,
    @Body token: List<String>
  ): retrofit2.Response<Unit>

  /**
   * firebase messaging token
   */
  @POST("/v2/customers/{customerId}/apn")
  suspend fun postApnFcm(
    @Path("customerId") customerId: String,
    @Body req: Map<String, String>
  ): retrofit2.Response<Unit>

  /**
   * get near group restaurants
   */
  @GET("/v3/restaurants")
  suspend fun getNearRestaurants(@QueryMap params: Map<String, String?>): List<RestaurantRemoteGroup>?

  /**
   * get near group restaurants section
   * combine restaurants and region
   */
  @GET("/v4/restaurants")
  suspend fun getRestaurantSection(@QueryMap params: Map<String, String?>): RestaurantRemoteSection?

  /**
   * get home banners and motd
   */
  @GET("/v1/regions/{id}")
  suspend fun getRegion(@Path("id") regionId: String): RegionModel?

  /**
   * user feedback
   */
  @POST("/v1/tickets")
  suspend fun imageFeedback(@Body req: FeedbackReq): NetworkError

  /**
   * lucky recommendation
   */
  @GET("/v1/restaurants")
  suspend fun requestLuckyRecommend(
    @Query("lucky[items][]") items: List<String>?,
    @Query("lucky[people]") people: String?,
    @Query("location") location: String,
    @Query("lucky[restaurant]") restaurant: String?
  ): LuckRecommendBean?

  @GET("/v1/support/orders/{orderId}")
  suspend fun getOrderSupportConfig(@Path("orderId") orderId: String): List<SupportRuleItem>

  @GET("/v2/support/orders/{orderId}")
  suspend fun getOrderSupportGroupConfig(@Path("orderId") orderId: String): List<SupportRuleGroup>

  /**
   * get rating order
   */
  @GET("/v1/customers/{customerId}/orders")
  suspend fun getRatingOrder(
    @Path("customerId") customerId: String,
    @Query("rating") rating: Boolean = true
  ): Order?

  @GET("/v1/update")
  suspend fun checkUpdate(): UpdateInfo

  @GET("/v1/promo")
  suspend fun checkPromoImage(): PromoInfo

  @POST("/v1/customers/card")
  suspend fun createPaymentCard(@Body body: Map<String, String>): PaymentObj

  /**
   *  get feeling lucky data by location params
   */
  @GET("/v2/lucky")
  suspend fun getLuckyData(@Query("location") loc: String?): List<Restaurant>?

  /**
   * get the food by food id
   */
  @GET("/v1/restaurants/{restaurantId}/food/{foodId}")
  suspend fun getFood(
    @Path("restaurantId") restaurantId: String,
    @Path("foodId") food: String
  ): Food?

  /**
   * get the restaurant history comments
   */
  @GET("/v1/restaurants/{restaurantId}/comments")
  suspend fun getHistoryComments(@Path("restaurantId") restaurantId: String): List<String>

  /**
   * get the restaurant recommend prediction
   */
  @GET("/v1/regions/{regionId}/search/recommend")
  suspend fun getRestaurantPrediction(
    @Path("regionId") regionId: String,
    @Query("q") q: String?
  ): List<RestaurantPrediction>

  /**
   * log add food to cart
   */
  @POST("/v1/restaurants/{restId}/carts/items")
  suspend fun logAddFood(
    @Path("restId") restId: String,
    @Body body: Map<String, @JvmSuppressWildcards Any>
  ): retrofit2.Response<Unit>

  /**
   * get the menu recommend when add and minus food
   */
  @GET("/v1/restaurants/{restId}/cart/recommend")
  suspend fun getMenuRecommend(
    @Path("restId") restId: String,
    @Query(value = "items[]", encoded = true) tiems: Array<String>?,
    @Query(value = "bundles[]", encoded = true) bundles: Array<String>?
  ): List<Food>

  /**
   * the the refer info for share
   */
  @GET("/v1/refer")
  suspend fun getRefer(): ReferInfo

  /**
   * log xiaohongshu link
   */
  @POST("/v1/orders/{orderId}/red")
  suspend fun logRedLink(
    @Path("orderId") orderId: String,
    @Body body: Map<String, @JvmSuppressWildcards Any>
  ): retrofit2.Response<Unit>
}

inline fun <T : Any, R : Any> loadResultError(e: Exception): PagingSource.LoadResult.Error<T, R> {
  return PagingSource.LoadResult.Error(
    e.parseByNetwork {
      if (ErrorCode.AUTH_FAILED == it) {
        CustomerCache.clearWithLogout()
      }
    }
  )
}
