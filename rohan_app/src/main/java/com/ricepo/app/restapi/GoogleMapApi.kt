package com.ricepo.app.restapi

import com.ricepo.map.model.UserAddress
import com.skydoves.sandwich.ApiResponse
import retrofit2.http.GET
import retrofit2.http.Query

// https://maps.googleapis.com/maps/api
interface GoogleMapApi {
  @GET("geocode/json")
  suspend fun fetchAddressByPostalCode(
    @Query("components")
    components: String,
    @Query("key")
    key: String = "AIzaSyCcY20aVdwmhvP7Zr9BRsia44kRxd-iC10"
  ): ApiResponse<UserAddress>
}
