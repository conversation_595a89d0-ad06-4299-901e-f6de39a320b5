package com.ricepo.app.restapi

import com.ricepo.app.model.UserInformation
import com.ricepo.base.model.Customer
import com.ricepo.base.model.GlobalConfigModel
import io.reactivex.rxjava3.core.Single
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import kotlin.collections.HashMap as HashMap1

//
// Created by <PERSON><PERSON> on 7/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
interface AuthRestApi {

  @POST("/v1/auth/customer")
  fun getVcode(@Body params: HashMap1<String, String>): Single<Any>

  @POST("/v1/auth/customer")
  fun login(@Body params: HashMap1<String, String>): Single<UserInformation>

  @GET("/v1/auth/country")
  suspend fun getCountry(): Map<String, GlobalConfigModel>

  @GET("/v1/customers/{id}")
  fun getCustomer(@Path("id") customerId: String): Single<Customer>

  @GET("/v1/auth")
  fun resetToken(): Single<UserInformation>
}
