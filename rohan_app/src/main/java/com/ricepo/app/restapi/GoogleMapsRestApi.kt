package com.ricepo.app.restapi

import com.ricepo.map.MapConsts
import com.ricepo.map.model.UserAddress
import com.ricepo.style.LocaleUtil
import retrofit2.http.GET
import retrofit2.http.Query

//
// Created by <PERSON><PERSON> on 3/15/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
interface GoogleMapsRestApi {

  /**
   * need geocoding api authenticate
   * "This IP, site or mobile application is not authorized to use this API key.
   * Request received from IP address *************, with empty referer"
   */
  @GET("/maps/api/geocode/json")
  suspend fun fetchCurrentPlace(
    @Query("latlng") latlng: String,
    @Query("language") language: String = LocaleUtil.getResourcesLocale(),
    @Query("key") map: String = MapConsts.API_KEY
  ): UserAddress
}
