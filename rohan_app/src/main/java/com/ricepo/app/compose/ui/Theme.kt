package com.ricepo.app.compose.ui

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Colors
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Shapes
import androidx.compose.material.Typography
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ricepo.app.R

@Composable
internal fun isDarkTheme(): Boolean {
  return isSystemInDarkTheme()
}

@Composable
fun RiceTheme(
  isDarkTheme: Boolean = isDarkTheme(),
  content: @Composable () -> Unit,
) {
  MaterialTheme(
    colors = provideColor(isDarkTheme),
    shapes = provideShapes(),
    typography = provideTypography(isDarkTheme),
  ) {
    CompositionLocalProvider(
      content = content,
    )
  }
}

@Composable
fun provideTypography(isDarkTheme: Boolean): Typography {
  return Typography(
    h1 = TextStyle(
      fontSize = 32.sp,
      lineHeight = 38.4.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W700,
      color = if (isDarkTheme) Color.White.copy(0.8f) else Color.Black.copy(0.8f)
    ),
    h2 = TextStyle(
      fontSize = 24.sp,
      lineHeight = 36.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W700,
      color = if (isDarkTheme) Color.White.copy(0.8f) else Color.Black.copy(0.8f)
    ),
    h3 = TextStyle(
      fontSize = 20.sp,
      lineHeight = 30.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W700,
      color = if (isDarkTheme) Color.White.copy(0.8f) else Color.Black.copy(0.8f)
    ),
    h4 = TextStyle(
      fontSize = 18.sp,
      lineHeight = 26.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W700,
      color = if (isDarkTheme) Color.White.copy(0.8f) else Color.Black.copy(0.8f)
    ),
    h5 = TextStyle(
      fontSize = 16.sp,
      lineHeight = 24.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W700,
      color = if (isDarkTheme) Color.White.copy(0.8f) else Color.Black.copy(0.8f)
    ),
    h6 = TextStyle(
      fontSize = 14.sp,
      lineHeight = 21.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W700,
      color = if (isDarkTheme) Color.White.copy(0.8f) else Color.Black.copy(0.8f)
    ),
    subtitle1 = TextStyle(
      fontSize = 18.sp,
      lineHeight = 21.6.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W400,
      color = if (isDarkTheme) Color.White.copy(0.4f) else Color.Black.copy(0.4f)
    ),
    subtitle2 = TextStyle(
      fontSize = 16.sp,
      lineHeight = 24.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight(510),
      color = if (isDarkTheme) Color.White.copy(0.4f) else Color.Black.copy(0.4f)
    ),
    body1 = TextStyle(
      fontSize = 16.sp,
      lineHeight = 21.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight(510),
      color = if (isDarkTheme) Color.White.copy(0.4f) else Color.Black.copy(0.4f)
    ),
    body2 = TextStyle(
      fontSize = 13.sp,
      lineHeight = 19.5.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W400,
      color = if (isDarkTheme) Color.White.copy(0.4f) else Color.Black.copy(0.4f)
    ),
    button = TextStyle(
      fontSize = 16.sp,
      lineHeight = 24.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W700,
      color = Color.Unspecified,
      textAlign = TextAlign.Center,
    ),
    caption = TextStyle(
      fontSize = 14.sp,
      lineHeight = 21.sp,
      fontStyle = FontStyle.Normal,
      fontWeight = FontWeight.W400,
      color = Color.Unspecified,
    )
  )
}

@Composable
fun provideShapes(): Shapes {
  return Shapes(
    small = RoundedCornerShape(8.dp),
    medium = RoundedCornerShape(12.dp),
    large = RoundedCornerShape(20.dp),
  )
}

@Composable
fun provideColor(isDarkTheme: Boolean): Colors {
  val primary = colorResource(id = com.ricepo.style.R.color.mr)
  return Colors(
    primary = primary,
    onPrimary = Color.White,
    secondary = primary,
    onSecondary = Color.White,
    background = colorResource(id = com.ricepo.style.R.color.background),
    onBackground = colorResource(id = com.ricepo.style.R.color.fun_n1),
    secondaryVariant = primary,
    surface = colorResource(id = com.ricepo.style.R.color.card_background),
    onSurface = colorResource(id = com.ricepo.style.R.color.fun_n1),
    error = Color(0xFFCF6679),
    onError = Color.Black,
    isLight = !isDarkTheme,
    primaryVariant = primary
  )
}
