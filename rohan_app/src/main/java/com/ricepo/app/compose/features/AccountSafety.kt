package com.ricepo.app.compose.features

import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.ricepo.app.R
import com.ricepo.app.compose.ComposeActivity
import com.ricepo.app.compose.ComposeUiType
import com.ricepo.app.compose.Divider
import com.ricepo.app.compose.ui.RiceTheme
import com.ricepo.app.compose.vm.AccountViewModel
import com.ricepo.app.compose.widget.AlertDialog
import com.ricepo.app.compose.widget.RiceTopBar
import com.ricepo.base.animation.Loading
import com.ricepo.base.tools.ToastUtil
import com.ricepo.base.view.DialogFacade

fun ComposeActivity.accountSafety(
  composeView: ComposeView
) {
  val accountViewModel: AccountViewModel by viewModels()
  composeView.setContent {
    ComposeUi(
      onDeleteAccount = {
        accountViewModel.deleteAccount(
          onStart = {
            Loading.showLoading(this)
          },
          onSuccess = {
            ToastUtil.showToast(
              resources.getString(com.ricepo.style.R.string.account_delete_success)
            )
            setResult(ComposeUiType.AccountSafety.code)
            finish()
          },
          onError = {
            DialogFacade.showPrompt(this, "error")
          }
        )
      }
    ) {
      finish()
    }
  }
}

@Composable
private fun ComposeUi(
  onDeleteAccount: () -> Unit,
  onBack: () -> Unit,
) {
  RiceTheme {
    Scaffold(
      topBar = {
        RiceTopBar(
          tittle = stringResource(id = com.ricepo.style.R.string.account_safety)
        ) {
          onBack.invoke()
        }
      },
      backgroundColor = colorResource(id = com.ricepo.style.R.color.background),
      modifier = Modifier.imePadding()
    ) {
      Box(modifier = Modifier.padding(it)) {
        Body {
          onDeleteAccount.invoke()
        }
      }
    }
  }
}

@Composable
@Preview
fun preview() {
  Body {
  }
}

@Composable
private fun Body(
  onDeleteAccount: () -> Unit,
) {
  var openDialog by remember { mutableStateOf(false) }
  Column(modifier = Modifier.padding(horizontal = 36.dp, vertical = 20.dp)) {
    Text(
      text = stringResource(id = com.ricepo.style.R.string.account_delete),
      style = MaterialTheme.typography.h5,
      modifier = Modifier
        .clickable {
          openDialog = true
        }
        .fillMaxWidth()
    )
    Divider(
      modifier = Modifier.background(
        color = colorResource(id = com.ricepo.style.R.color.card_background)
      )
    )
  }
  AlertDialog(
    openDialog = openDialog,
    tittle = stringResource(id = com.ricepo.style.R.string.account_delete),
    content = stringResource(id = com.ricepo.style.R.string.account_delete_message),
    positive = stringResource(id = com.ricepo.style.R.string.cancel),
    negative = stringResource(id = com.ricepo.style.R.string.confirm),
    onNegative = {
      openDialog = false
      onDeleteAccount.invoke()
    }
  ) {
    openDialog = false
  }
}
