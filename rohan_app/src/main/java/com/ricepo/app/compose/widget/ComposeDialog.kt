package com.ricepo.app.compose.widget

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.AlertDialog
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.ricepo.app.R

@Composable
fun AlertDialog(
  openDialog: Boolean,
  tittle: String,
  content: String,
  positive: String,
  negative: String,
  onPositive: (() -> Unit) ? = null,
  onNegative: (() -> Unit) ? = null,
  onClose: (() -> Unit)? = null,
) {
  if (openDialog) {
    AlertDialog(
      onDismissRequest = {
        onClose?.invoke()
      },
      buttons = {
        Column(modifier = Modifier.fillMaxWidth()) {
          Icon(
            painter = painterResource(id = com.ricepo.style.R.drawable.ic_close),
            contentDescription = "",
            modifier = Modifier
              .align(Alignment.End)
              .padding(top = 16.dp, end = 16.dp)
              .clickable {
                onClose?.invoke()
              },
            tint = colorResource(id = com.ricepo.style.R.color.mainText)
          )
          Text(
            text = tittle,
            modifier = Modifier.align(Alignment.CenterHorizontally),
            style = MaterialTheme.typography.h3
          )
          Text(
            text = content,
            modifier = Modifier
              .align(
                Alignment.CenterHorizontally
              )
              .padding(24.dp)
          )
          DialogButtons(
            positiveStr = positive,
            negativeStr = negative,
            onPositiveClick = {
              (onPositive ?: onClose)?.invoke()
            },
            onNegativeClick = {
              (onNegative ?: onClose)?.invoke()
            }
          )
        }
      }
    )
  }
}
