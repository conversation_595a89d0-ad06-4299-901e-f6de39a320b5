package com.ricepo.app.compose.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ricepo.app.data.kv.CustomerCache
import com.ricepo.app.restapi.RiceApi
import com.skydoves.sandwich.suspendOnError
import com.skydoves.sandwich.suspendOnException
import com.skydoves.sandwich.suspendOnSuccess
import com.skydoves.whatif.whatIfNotNullOrEmpty
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class AccountViewModel @Inject constructor(
  private val riceApi: RiceApi,
) : ViewModel() {
  fun deleteAccount(
    onStart: () -> Unit,
    onSuccess: () -> Unit,
    onError: () -> Unit,
  ) {
    viewModelScope.launch {
      onStart.invoke()
      val customerId = withContext(Dispatchers.Default) {
        CustomerCache.getCustomerSuspend()?.id
      }
      customerId.whatIfNotNullOrEmpty {
        riceApi.deleteAccount(it).suspendOnSuccess {
          withContext(Dispatchers.IO) {
            CustomerCache.clearWithLogoutSuspend()
            onSuccess.invoke()
          }
          onSuccess.invoke()
        }.suspendOnError {
          onError.invoke()
        }.suspendOnException {
          onError.invoke()
        }
        return@launch
      }
      onError.invoke()
    }
  }
}
