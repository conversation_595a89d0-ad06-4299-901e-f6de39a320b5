package com.ricepo.app.compose.widget

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import com.ricepo.app.R
import com.ricepo.app.compose.ui.isDarkTheme

@Composable
fun RiceTopBar(
  icon: Int = com.ricepo.style.R.drawable.ic_back,
  tittle: String,
  onIconClick: () -> Unit,
) {
  Box(modifier = Modifier.fillMaxWidth()) {
    Icon(
      painter = painterResource(id = icon),
      tint = if (isDarkTheme()) Color.White else Color.Black,
      contentDescription = "",
      modifier = Modifier
        .align(Alignment.CenterStart)
        .clickable {
          onIconClick.invoke()
        }
    )
    Text(
      text = tittle,
      modifier = Modifier.align(Alignment.Center),
      style = MaterialTheme.typography.h5
    )
  }
}
