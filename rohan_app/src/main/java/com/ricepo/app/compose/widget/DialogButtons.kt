package com.ricepo.app.compose.widget

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import com.ricepo.app.R

private typealias cColor = androidx.compose.ui.graphics.Color
@Composable
fun DialogButtons(
  positiveStr: String,
  negativeStr: String,
  onNegativeClick: (() -> Unit)? = null,
  onPositiveClick: (() -> Unit)? = null
) {
  Row(
    horizontalArrangement = Arrangement.SpaceEvenly,
    modifier = Modifier
      .fillMaxWidth()
      .padding(bottom = 16.dp)
  ) {
    TextButton(
      onClick = {
        onNegativeClick?.invoke()
      },
      border = BorderStroke(2.dp, colorResource(id = com.ricepo.style.R.color.divider)),
      colors = ButtonDefaults.buttonColors(
        backgroundColor = colorResource(id = com.ricepo.style.R.color.card_background)
      )
    ) {
      Text(text = negativeStr)
    }
    Button(
      onClick = {
        onPositiveClick?.invoke()
      }
    ) {
      Text(
        text = positiveStr,
        style = TextStyle(color = cColor.White)
      )
    }
  }
}
