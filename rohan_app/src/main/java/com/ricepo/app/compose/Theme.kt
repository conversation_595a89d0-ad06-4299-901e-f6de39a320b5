package com.ricepo.app.compose

import androidx.annotation.DimenRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ricepo.app.R

val fontFamily = FontFamily(
//    Font(com.ricepo.style.R.font.source_serif_pro, FontWeight.SemiBold),
  Font(com.ricepo.style.R.font.font_regular, FontWeight.Normal),
  Font(com.ricepo.style.R.font.font_semibold, FontWeight.SemiBold),
  Font(com.ricepo.style.R.font.font_medium, FontWeight.Medium),
  Font(com.ricepo.style.R.font.font_bold, FontWeight.Bold)
)

@Composable
fun Modifier.cardShape() = apply {
  clip(
    RoundedCornerShape(
      topStart = dimensionResource(id = com.ricepo.style.R.dimen.card_radius),
      topEnd = dimensionResource(id = com.ricepo.style.R.dimen.card_radius)
    )
  )
}

@Composable
fun Divider(
  modifier: Modifier = Modifier
) {
  Box(
    modifier = Modifier
      .padding(top = 12.dp, bottom = 12.dp)
      .fillMaxWidth()
      .height(1.dp)
      .background(
        colorResource(id = com.ricepo.style.R.color.divider)
      ).then(modifier)
  )
}

@Composable
@ReadOnlyComposable
fun fontDimensionResource(@DimenRes id: Int) = dimensionResource(id = id).value.sp
