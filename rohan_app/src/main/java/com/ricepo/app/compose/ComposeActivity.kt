package com.ricepo.app.compose

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.compose.ui.platform.ComposeView
import com.ricepo.app.R
import com.ricepo.app.compose.features.accountSafety
import com.ricepo.app.compose.features.rating
import com.ricepo.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint
import java.io.Serializable

sealed class ComposeUiType(val type: String, val code: Int = -1) : Serializable {
  object Rating : ComposeUiType("RATING")
  object AccountSafety : ComposeUiType("AccountSafety", code = 1001)
}

fun Activity.launchComposeUi(
  composeUiType: ComposeUiType
) {
  startActivityForResult(
    Intent(this, ComposeActivity::class.java).apply {
      putExtra("type", composeUiType)
    },
    composeUiType.code
  )
}

@AndroidEntryPoint
class ComposeActivity : BaseActivity() {
  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    setContentView(R.layout.activity_compose)
    val composeView = findViewById<ComposeView>(R.id.greeting)
    dispatchEntrance(composeView)
  }

  private fun dispatchEntrance(
    composeView: ComposeView
  ) {

    when (intent.getSerializableExtra("type")) {
      is ComposeUiType.Rating -> {
        rating(composeView = composeView)
      }
      is ComposeUiType.AccountSafety -> {
        accountSafety(composeView = composeView)
      }
      else -> {}
    }
  }
}
