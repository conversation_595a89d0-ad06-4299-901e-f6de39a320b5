package com.ricepo.app.compose.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ricepo.app.restapi.RiceApi
import com.ricepo.app.utils.FeedBack
import com.skydoves.sandwich.suspendOnError
import com.skydoves.sandwich.suspendOnException
import com.skydoves.sandwich.suspendOnSuccess
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FeedbackViewModel @Inject constructor(
  private val riceApi: RiceApi
) : ViewModel() {

  fun feedback(
    feedBack: String,
    onStart: () -> Unit,
    onSuccess: () -> Unit,
    onError: () -> Unit,
  ) {
    onStart.invoke()
    viewModelScope.launch {
      riceApi.appRatingFeedback(FeedBack(feedBack)).suspendOnSuccess {
        onSuccess.invoke()
      }.suspendOnError {
        onError.invoke()
      }.suspendOnException {
        onError.invoke()
      }
    }
  }
}
