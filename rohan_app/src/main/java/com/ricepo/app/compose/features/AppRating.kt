package com.ricepo.app.compose.features

import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.ricepo.app.R
import com.ricepo.app.compose.ComposeActivity
import com.ricepo.app.compose.ui.RiceTheme
import com.ricepo.app.compose.vm.FeedbackViewModel
import com.ricepo.app.compose.widget.RiceTopBar
import com.ricepo.base.animation.Loading
import com.ricepo.base.view.DialogFacade

@Preview
@Composable
fun Preview() {
  AppRating({}, {})
}

fun ComposeActivity.rating(
  composeView: ComposeView
) {
  val feedbackViewModel: FeedbackViewModel by viewModels()
  composeView.setContent {
    RiceTheme {
      AppRating(
        onBack = {
          finish()
        },
        onSubmit = {
          feedbackViewModel.feedback(
            feedBack = it,
            onStart = {
              Loading.showLoading(this)
            },
            onSuccess = {
              Loading.hideLoading()
              DialogFacade.showAlert(
                this, resources.getString(com.ricepo.style.R.string.app_rating_success),
                positive = {
                  finish()
                }
              )
            },
            onError = {
              Loading.hideLoading()
              DialogFacade.showPrompt(this, "error")
            }
          )
        }
      )
    }
  }
}

@Composable
fun AppRating(
  onBack: () -> Unit,
  onSubmit: (String) -> Unit
) {

  Scaffold(
    topBar = {
      RiceTopBar(
        tittle = stringResource(id = com.ricepo.style.R.string.app_rating_feedback_title)
      ) {
        onBack.invoke()
      }
    },
    backgroundColor = colorResource(id = com.ricepo.style.R.color.background),
    modifier = Modifier.imePadding()
  ) {
    Box(modifier = Modifier.padding(it)) {
      Content(onSubmit)
    }
  }
}

@Composable
fun Content(
  onSubmit: (String) -> Unit
) {
  var result by remember {
    mutableStateOf("")
  }
  val enabled by remember(result) {
    derivedStateOf {
      result.isNotBlank()
    }
  }
  val focusRequester = remember { FocusRequester() }
  LaunchedEffect(Unit) {
    focusRequester.requestFocus()
  }
  Column(
    modifier = Modifier
      .fillMaxWidth()
      .fillMaxHeight(),
  ) {
    Text(
      text = stringResource(
        id = com.ricepo.style.R.string.app_rating_feedback_secondtitle
      ),
      modifier = Modifier.padding(
        top = 24.dp, start = 24.dp
      ),
      style = MaterialTheme.typography.h6
    )
    OutlinedTextField(
      value = result,
      onValueChange = {
        result = it
      },
      modifier = Modifier
        .align(Alignment.CenterHorizontally)
        .height(120.dp)
        .fillMaxWidth()
        .padding(24.dp)
        .background(
          colorResource(id = com.ricepo.style.R.color.card_background),
          shape = RoundedCornerShape(8.dp)
        )
        .focusRequester(focusRequester),
      placeholder = {
        Text(text = stringResource(id = com.ricepo.style.R.string.app_rating_feedback_hint))
      }
    )
    Spacer(modifier = Modifier.weight(1f))
    Button(
      modifier = Modifier
        .align(Alignment.CenterHorizontally)
        .fillMaxWidth()
        .alpha(if (enabled) 1f else 0.4f)
        .padding(bottom = 100.dp, start = 20.dp, end = 20.dp),
      onClick = {
        if (enabled) {
          onSubmit.invoke(result)
        }
      }
    ) {
      Text(
        text = stringResource(id = com.ricepo.style.R.string.submit),
        style = TextStyle(color = Color.White)
      )
    }
  }
}
