apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'com.ricepo.style'
    def globalConfiguration = rootProject.extensions.getByName("ext")
    compileSdk globalConfiguration["androidcompileSdk"]
//    buildToolsVersion globalConfiguration["androidBuildToolsVersion"]

    defaultConfig {
        minSdkVersion globalConfiguration["androidMinSdkVersion"]
        targetSdkVersion globalConfiguration["androidTargetSdkVersion"]

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildFeatures {
        buildConfig true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    viewBinding {
        enabled = true
    }
    compileOptions {
        sourceCompatibility javaVersion
        targetCompatibility javaVersion
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation styleDependencies.appCompat
    api styleDependencies.material
    implementation styleDependencies.constraintLayout

    implementation styleDependencies.coreKtx

    implementation styleDependencies.gson
}
