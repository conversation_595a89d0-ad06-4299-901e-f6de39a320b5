<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content"
    android:layout_width="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/sheet_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_sheet_title"
            android:text="@string/ricepo"
            android:gravity="center"
            android:padding="12dp"
            android:visibility="gone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_divider_title"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:visibility="gone"
            style="@style/DividerHorizontalStyle" />

    </LinearLayout>
</androidx.core.widget.NestedScrollView>