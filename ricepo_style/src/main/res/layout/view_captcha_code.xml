<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_captcha_one"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textAlignment="inherit"
        android:inputType="number"
        style="@style/EditTextStyle"
        android:gravity="center"
        android:textSize="17dp"
        android:layout_marginBottom="@dimen/text_bottom_padding"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_captcha_two"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_captcha_two"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textAlignment="inherit"
        android:inputType="number"
        style="@style/EditTextStyle"
        android:gravity="center"
        android:textSize="17dp"
        android:layout_marginBottom="@dimen/text_bottom_padding"
        app:layout_constraintLeft_toRightOf="@+id/tv_captcha_one"
        app:layout_constraintRight_toLeftOf="@+id/tv_captcha_three"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_captcha_three"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textAlignment="inherit"
        android:inputType="number"
        style="@style/EditTextStyle"
        android:gravity="center"
        android:textSize="17dp"
        android:layout_marginBottom="@dimen/text_bottom_padding"
        app:layout_constraintLeft_toRightOf="@+id/tv_captcha_two"
        app:layout_constraintRight_toLeftOf="@+id/tv_captcha_four"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_captcha_four"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textAlignment="inherit"
        android:inputType="number"
        style="@style/EditTextStyle"
        android:gravity="center"
        android:textSize="17dp"
        android:layout_marginBottom="@dimen/text_bottom_padding"
        app:layout_constraintLeft_toRightOf="@+id/tv_captcha_three"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_captcha"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:inputType="number"
        android:cursorVisible="false"
        android:background="@android:color/transparent"
        android:textColor="@android:color/transparent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>