<?xml version="1.0" encoding="utf-8"?>

<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/mp"
    app:cardCornerRadius="@dimen/button_radius"
    android:foregroundGravity="center_vertical"
    app:cardElevation="0dp"
    android:layout_width="wrap_content" >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_qty_add"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        android:layout_gravity="right"
        tools:visibility="visible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_plus"
            android:src="@drawable/ic_add"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:scaleType="fitCenter"
            android:adjustViewBounds="true"
            android:tint="@color/mr"
            android:layout_width="@dimen/sw_30dp"
            android:layout_height="@dimen/sw_34dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_qty_num"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="@+id/lay_qty_add"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_selected_minus"
            android:src="@drawable/ic_subtract"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/iv_selected_plus"
            android:layout_marginRight="8dp"
            android:scaleType="fitCenter"
            android:layout_width="@dimen/sw_34dp"
            android:layout_height="@dimen/chocolate_button_size" />

        <TextView
            android:id="@+id/tv_qty_num"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:gravity="center"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="99"
            android:textColor="@color/white"
            android:layout_width="@dimen/sw_20dp"
            android:layout_height="@dimen/sw_28dp" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_selected_plus"
            android:src="@drawable/ic_add"
            app:layout_constraintLeft_toRightOf="@+id/iv_selected_minus"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:scaleType="fitCenter"
            android:layout_width="@dimen/sw_34dp"
            android:layout_height="@dimen/chocolate_button_size" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>