<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--    new theme color-->

    <color name="mr">#EB5B55</color>

    <color name="n1">#F6F6F6</color>
    <color name="n2">#FAFAFA</color>
    <color name="n3">#D7D7D7</color>
    <color name="n4">#CCCCCC</color>
    <color name="n5">#8D9093</color>
    <color name="n6">#333333</color>
    <color name="n7">#E1E1E1</color>

    <color name="d1">#B7B9C1</color>
    <color name="d2">#51535F</color>
    <color name="d3">#373943</color>
    <color name="d4">#2A2B33</color>
    <color name="d5">#1C1D23</color>
    <color name="d6">#B7B9C1</color>

    <color name="b">#000000</color>
    <color name="w">#FFFFFF</color>

    <color name="mp">#FFF5F5</color>
    <color name="mg">#40CFC7</color>
    <color name="my">#EECA5E</color>


    <color name="fun_n1">@color/n1</color>
    <color name="fun_n2">@color/n2</color>
    <color name="fun_n3">@color/n3</color>
    <color name="fun_n4">@color/n4</color>
    <color name="fun_n5">@color/n5</color>
    <color name="fun_n6">@color/n6</color>

    <color name="disable_mr">#FFC4C1</color>

    <color name="global_background">@color/n1</color>
    <color name="menu_banner_background">@color/n2</color>
    <color name="banner_background">@color/w</color>
    <color name="input_divider">@color/n4</color>
    <color name="divider">@color/n7</color>
    <color name="title">@color/n6</color>
    <color name="normal_text">@color/n5</color>
    <color name="card_level1">@color/n1</color>
    <color name="card_level2">@color/n3</color>

    <color name="shape_color">#990B0B0B</color>

    <color name="banner_button_boarder_color">#cccccc</color>
    <!--    new theme color-->


    <color name="black">#ff000000</color>
    <color name="gray1">#ff1e1e1e</color>
    <color name="gray2">#ff2b2b2b</color>
    <color name="gray3">#ff313131</color>
    <color name="gray4">#ff3e3e3e</color>
    <color name="gray5">#ff4b4b4b</color>
    <color name="gray6">#ff5d5d5d</color>
    <color name="gray7">#ff888888</color>
    <color name="gray8">#ffafafaf</color>
    <color name="gray9">#ffcbcbcb</color>
    <color name="gray10">#ffd0d0d0</color>
    <color name="gray11">#ffdedede</color>
    <color name="gray12">#ffe3e3e3</color>
    <color name="gray13">#ffe0e0e0</color>
    <color name="gray14">#ffefefef</color>
    <color name="gray15">#fff5f5f5</color>
    <color name="gray16">#fff8f8f8</color>
    <color name="gray17">#ff333333</color>
    <color name="white">#ffffffff</color>
    <color name="red1">#ffbf4c4c</color>
    <color name="red2">#ffaa4848</color>
    <color name="red3">#ffce7c71</color>
    <color name="red4">#ffb15e58</color>
    <color name="red5">#ff88514d</color>
    <color name="red6">#ff693e3c</color>
    <color name="red_7">#ffd8887e</color>
    <color name="red_8">#ffd58176</color>
    <color name="red_9">#ffce7c71</color>
    <color name="red_10">#ffe39992</color>
    <color name="coral1">#fff08670</color>
    <color name="orange1">#ffdb6600</color>
    <color name="orange2">#ffe86c00</color>
    <color name="green1">#ff00a353</color>
    <color name="green2">#ff498e59</color>
    <color name="blue">#ff088ed5</color>
    <color name="gold">#ff927d56</color>
    <color name="gold3">#ffb19654</color>
    <color name="gold4">#ffa18145</color>
    <color name="gold5">#ffc9b06f</color>

    <color name="green">@color/green2</color>

    <color name="colorPrimary">@color/gray15</color>
    <color name="colorPrimaryDark">@color/gray15</color>
    <color name="colorAccent">#D81B60</color>

    <color name="colorPrimaryByDark">@color/gray3</color>
    <color name="colorPrimaryDarkByDark">@color/gray3</color>
    <color name="colorAccentByDark">#D81B60</color>

    <color name="dividerShadow">@color/gray10</color>
    <color name="dividerHighlight">@color/white</color>
    <color name="dividerRestaurantShadow">@color/gray12</color>

    <color name="mainText">@color/fun_n6</color>
    <color name="subText">@color/gray7</color>
    <color name="subTextMenu">@color/gray7</color>
    <color name="disableText">@color/gray10</color>
    <color name="goldSubText">@color/gold4</color>
    <color name="goldPrimarySatisfied">@color/gold5</color>
    <color name="deliveryNoteText">@color/gray8</color>
    <color name="ricePoolText">@color/mr</color>

    <color name="alert">@color/mr</color>

    <!-- input text field -->
    <color name="inputLineShadow">@color/gray8</color>
    <color name="inputLineHighLight">@color/fun_n4</color>
    <color name="inputLineText">@color/gray7</color>

    <!-- focus text field -->
    <color name="textLineShadow">@color/gray5</color>
    <color name="textLineHighlight">@color/white</color>
    <color name="textLineText">@color/gray5</color>

    <!-- text box -->
    <color name="textBoxShadow">@color/gray8</color>
    <color name="textBoxFill">@color/gray11</color>

    <!-- button -->
    <color name="second_satisfied_button_highlight">@color/white</color>
    <color name="second_satisfied_button_shadow">@color/gray8</color>
    <color name="second_satisfied_button_fill">@color/gray15</color>
    <color name="second_satisfied_button_text">@color/gray5</color>

    <color name="second_unsatisfied_button_highlight">@color/gray10</color>
    <color name="second_unsatisfied_button_shadow">@color/gray15</color>
    <color name="second_unsatisfied_button_fill">@color/gray15</color>
    <color name="second_unsatisfied_button_text">@color/gray10</color>

    <color name="primary_satisfied_button_highlight">@color/white</color>
    <color name="primary_satisfied_button_shadow">@color/gray8</color>
    <color name="primary_unsatisfied_button_highlight">@color/gray11</color>
    <color name="primary_unsatisfied_button_shadow">@color/gray11</color>

    <color name="primary_satisfied_button_fill">@color/gray5</color>
    <color name="primary_satisfied_button_text">@color/white</color>

    <color name="background">@color/gray15</color>
    <color name="background_light">@color/gray15</color>

    <color name="bundle_bar_background">@color/gray16</color>
    <color name="bundle_bar_background_selected">@color/gray5</color>
    <color name="bundle_bar_border">#26afafaf</color>
    <color name="bundle_bar_text">@color/gray7</color>
    <color name="bundle_bar_selected_border">@color/gray7</color>
    <color name="bundle_bar_selected_text">@color/white</color>

    <!-- common with restaurant card background -->
    <color name="card_background">#fffbfbfb</color>
    <color name="card_top_shadow">@color/gray12</color>
    <color name="card_border">@color/white</color>

    <!-- map -->
    <color name="rest_address_shadow">@color/red5</color>
    <color name="rest_address_fill">@color/red3</color>
    <color name="rest_address_text">@color/white</color>
    <color name="home_address_shadow">@color/gray13</color>
    <color name="home_address_fill">@color/white</color>
    <color name="home_address_text">@color/gray5</color>
    <color name="driver_polyline">@color/gray7</color>
    <color name="driver_polyline_shadow">#12000000</color>
    <color name="driver_polyline_shadow2">#32000000</color>


    <!-- tag -->
    <color name="tag_shadow_dark">@color/gray3</color>

    <!-- restaurant horizontal -->
    <color name="restaurant_see_all_shadow">#88afafaf</color>

    <!-- lucky -->
    <color name="luckyMenuShadow">@color/red_9</color>
    <color name="luckyMenuBorder">#ffeda49b</color>
    <color name="luckyMenuSelectedBorder">@color/gray15</color>
    <color name="luckyMenuSelected">@color/red3</color>
    <color name="luckyRefreshBackground">@color/white</color>
    <color name="luckyRefreshBorder">@color/gray15</color>
    <color name="luckyMenuTextColor">@color/red_9</color>
    <color name="luckyBottomShadow">#70000000</color>

    <!-- fast delivery -->
    <color name="fast_delivery_progress_color">@color/red3</color>
    <color name="fast_delivery_progress_shadow_color">#ff88514d</color>
    <color name="fast_delivery_background_color">@color/gray14</color>
    <color name="fast_delivery_background_shadow_color">@color/gray8</color>
    <color name="fast_delivery_label_color">@color/gray8</color>


    <color name="indicator_normal">@color/white</color>
    <color name="indicator_normal_shadow">@color/gray10</color>
    <color name="indicator_selected">@color/gray7</color>

    <color name="pickup_item_bg">@color/white</color>
    <color name="pickup_item_shadow">@color/gray10</color>

    <color name="place_order_shadow">#57000000</color>

    <color name="cart_count">@color/white</color>
    <color name="cart_name">@color/gray5</color>
    <color name="tab_item">@color/gray8</color>
    <color name="tab_item_pressed">@color/gray5</color>

    <color name="pb_menu_recommend_default">@color/gray5</color>
    <color name="pb_menu_recommend">@color/gray10</color>

    <color name="refer_main_text">@color/gray5</color>
    <color name="refer_sub_text">@color/gray7</color>
    <color name="refer_button_text">#ffffffff</color>

    <color name="bg_diary_image">#4d000000</color>

    <color name="restaurant_name_shadow">#1f000000</color>

    <color name="stroke_text">@color/white</color>
    <color name="pickup_restaurant_name">@color/gray5</color>

    <color name="map_circle_border_shadow">@color/gray12</color>

    <color name="food_cascade_top_shadow">@color/gray12</color>
    <color name="food_cascade_bottom_shadow">@color/white</color>

    <color name="rating_color">#FFE100</color>
    <color name="rating_text_color">#FFB800</color>
    <color name="unrating_color">@color/n3</color>


</resources>
