<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- round attr boolean-->
    <attr name="round_as_circle" format="boolean" />

    <!-- all round corner -->
    <attr name="round_corner" format="integer|dimension" />

    <!-- self round corner -->
    <attr name="round_corner_top_left" format="integer|dimension" />
    <attr name="round_corner_top_right" format="integer|dimension" />
    <attr name="round_corner_bottom_left" format="integer|dimension" />
    <attr name="round_corner_bottom_right" format="integer|dimension" />

    <!-- round stroke -->
    <attr name="stroke_color" format="color|reference" />
    <attr name="stroke_width" format="integer|dimension" />

    <!-- clip the background -->
    <attr name="clip_background" format="boolean" />

    <!-- all rclayout attributes -->
    <declare-styleable name="RoundCornerAtts">
        <attr name="round_as_circle" />
        <attr name="round_corner" />
        <attr name="round_corner_top_left" />
        <attr name="round_corner_top_right" />
        <attr name="round_corner_bottom_left" />
        <attr name="round_corner_bottom_right" />
        <attr name="stroke_color" />
        <attr name="stroke_width" />
        <attr name="clip_background" />
    </declare-styleable>

    <!-- prompt for relative layout -->
    <declare-styleable name="RoundLayout">
        <attr name="round_as_circle" />
        <attr name="round_corner" />
        <attr name="round_corner_top_left" />
        <attr name="round_corner_top_right" />
        <attr name="round_corner_bottom_left" />
        <attr name="round_corner_bottom_right" />
        <attr name="stroke_color" />
        <attr name="stroke_width" />
        <attr name="clip_background" />
    </declare-styleable>

    <!-- prompt for image view -->
    <declare-styleable name="RoundImageView">
        <attr name="round_as_circle" />
        <attr name="round_corner" />
        <attr name="round_corner_top_left" />
        <attr name="round_corner_top_right" />
        <attr name="round_corner_bottom_left" />
        <attr name="round_corner_bottom_right" />
        <attr name="stroke_color" />
        <attr name="stroke_width" />
        <attr name="clip_background" />
    </declare-styleable>

    <!-- shadow attribute -->
    <declare-styleable name="ShadowLayout">
        <!-- shadow color-->
        <attr name="shadowColor" format="color"/>
        <!-- shadow radius default 0-->
        <attr name="shadowRadius" format="dimension"/>
        <!-- shadow blur radius -->
        <attr name="blurRadius" format="dimension" />
        <!-- has click effect-->
        <attr name="hasEffect" format="boolean"/>
        <attr name="bgColor" format="color"/>
        <!-- x offset-->
        <attr name="xOffset" format="dimension"/>
        <!-- y offset -->
        <attr name="yOffset" format="dimension"/>
        <!-- shadow type -->
        <attr name="shadowType" format="integer" />

    </declare-styleable>

    <declare-styleable name="LuckyShadowLayout">
        <attr name="luckyShadowRadius" format="dimension" />
        <attr name="luckyShadowLength" format="dimension" />
        <attr name="luckyShadowColor" format="color" />
        <attr name="luckyShadowTranslationX" format="dimension" />
        <attr name="luckyShadowTranslationY" format="dimension" />
        <attr name="luckyShadowDirection">

            <flag name="all" value="0x1111"/>
            <flag name="left" value="0x0001"/>
            <flag name="top" value="0x0010"/>
            <flag name="right" value="0x0100"/>
            <flag name="bottom" value="0x1000"/>

        </attr>
    </declare-styleable>

    <declare-styleable name="VerticalTextView">
        <attr name="needSepLine" format="boolean" />
        <attr name="textColor" format="color" />
        <attr name="textSize" format="dimension" />
        <attr name="text" format="string" />
        <attr name="needCollipseEnd" format="boolean" />
        <attr name="normalCharPadding" format="dimension" />
        <attr name="customMaxHeight" format="dimension" />
        <attr name="maxLines" format="integer" />
        <attr name="normalCharPaddingTop" format="dimension" />
        <attr name="needLeftLine" format="boolean" />
        <attr name="lineSpace" format="dimension" />
    </declare-styleable>

    <declare-styleable name="StrokedTextAttrs">
        <attr name="textStrokeColor" format="color"/>
        <attr name="textStrokeWidth" format="float"/>
        <attr name="textHintStrokeColor" format="color"/>
        <attr name="textHintStrokeWidth" format="float"/>
        <attr name="textShadowColor" format="color" />
        <attr name="textShadowDy" format="dimension" />
        <attr name="textShadowRadius" format="dimension" />
    </declare-styleable>

</resources>