<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name_rice">"RICE"</string>
    <string name="app_name_dev">"RICE-Dev"</string>
    <string name="title_profile">"MY RICE"</string>
    <string name="add">"Add"</string>
    <string name="delete">"Delete"</string>
    <string name="cancel">"Cancel"</string>
    <string name="retry">"Retry"</string>
    <string name="ok">"OK"</string>
    <string name="or">"or"</string>
    <string name="and">"and"</string>
    <string name="get">"Get"</string>
    <string name="feedback">"Feedback"</string>
    <string name="show_all">"Show More"</string>
    <string name="show_more">"Show More"</string>
    <string name="initializing">"Initializing..."</string>
    <string name="swipe_to_delete">"Swipe Left to Delete"</string>
    <string name="choose">"Choose"</string>
    <string name="enter">"Enter"</string>
    <string name="submit">"Submit"</string>
    <string name="confirm">"Confirm"</string>
    <string name="edit">"Edit"</string>
    <string name="setting">"Settings  "</string>
    <string name="upload_pics">"Upload Pics"</string>
    <string name="camera">"Camera"</string>
    <string name="choose_from_album">"Choose from Album"</string>
    <string name="back">"Back"</string>
    <string name="Language">"Language"</string>
    <string name="Personal_info">"My Info"</string>
    <string name="account">"Account"</string>
    <string name="No_credit_card_available">"No Credit Card Available"</string>
    <string name="No_coupon_available">"No Coupon Available"</string>
    <string name="address">"Address"</string>
    <string name="enter_address">"Enter street name and street number"</string>
    <string name="enter_apt">"Please enter apartment number"</string>
    <string name="confirm_address">"Confirm Address"</string>
    <string name="use_as_address">"Are you sure you want to use %s as your delivery address?"</string>
    <string name="try_other_address">"Explore other places"</string>
    <string name="use_current_location">"Use Current Location"</string>
    <string name="explore">"Explore"</string>
    <string name="history">"History"</string>
    <string name="note">"Note...  "</string>
    <string name="delivery_fee">"Delivery"</string>
    <string name="delivery_min">"Minimum"</string>
    <string name="free_delivery">"%2$s Delivery on %1$s"</string>
    <string name="free_delivery_diff">"%1$s Until %2$s Delivery"</string>
    <string name="free_delivery_ok">"Enjoy %s Delivery"</string>
    <string name="delivered_by">"This order is delivered by %s\, for delivery questions please "</string>
    <string name="delivered_by_phone">"contact restaurant."</string>
    <string name="delivered_by_ricepo">"Delivered by RICE"</string>
    <string name="delivered_by_others">"Delivered by %s"</string>
    <string name="postmates">"Postmates"</string>
    <string name="uber">"Uber"</string>
    <string name="ricepo">"RICE"</string>
    <string name="rate_delivery">"Rate Your Delivery"</string>
    <string name="min">"Min"</string>
    <string name="issues_missing_items">"Missing or Incorrect items"</string>
    <string name="issues_late_delivery">"Late Delivery"</string>
    <string name="issues_food_quality">"Food Quality"</string>
    <string name="issues_driver_service">"Driver Service"</string>
    <string name="support_order_number">"Order Number: %s"</string>
    <string name="get_help">"Get Help"</string>
    <string name="view_receipt">"View Receipt"</string>
    <string name="ticket_received">"We've received your message, and will get in touch with you."</string>
    <string name="enter_detail">"Please enter detail."</string>
    <string name="please_choose_item">"Please choose item"</string>
    <string name="please_choose_photo">"Please upload a photo"</string>
    <string name="estimate_wait">"Estimate"</string>
    <string name="current_wait">"Current"</string>
    <string name="already_delivered">"Your order is delivered."</string>
    <string name="support_change_address_title">"Change Address"</string>
    <string name="support_change_address_button">"Submit"</string>
    <string name="support_change_phone_button">"Submit"</string>
    <string name="support_add_food_notes_button">"Submit"</string>
    <string name="support_add_delivery_notes_button">"Submit"</string>
    <string name="support_missing_order_upload_placeholder">"Upload Pics"</string>
    <string name="support_missing_order_button">"Submit"</string>
    <string name="maxinum_select_pics">"Select a maximum of 4 pics"</string>
    <string name="support_wrong_order_upload_placeholder">"Upload Pics"</string>
    <string name="support_wrong_order_button">"Submit"</string>
    <string name="support_tasting_bad_upload_placeholder">"Upload Pics"</string>
    <string name="support_tasting_bad_button">"Submit"</string>
    <string name="support_packaging_issue_upload_placeholder">"Upload Pics"</string>
    <string name="support_packaging_issue_button">"Submit"</string>
    <string name="support_processing_method_refund">"Refund Selected Items"</string>
    <string name="support_processing_method_refund_order">"Refund Entire Order"</string>
    <string name="support_processing_method_redelivery">"Redelivery"</string>
    <string name="support_processing_method_other">"Other"</string>
    <string name="support_arrived_late_button">"Submit"</string>
    <string name="support_never_arrived_button">"Chat"</string>
    <string name="support_never_arrived_submit">"Submit"</string>
    <string name="support_problem_with_drivers_button">"Submit"</string>
    <string name="support_problem_with_drivers_bad_attitudes">"Bad Attitudes"</string>
    <string name="support_problem_with_drivers_nonresponse">"Nonresponse"</string>
    <string name="support_problem_with_drivers_nonprofessional">"Nonprofessional"</string>
    <string name="support_problem_with_drivers_poor_communication">"Poor Communication"</string>
    <string name="support_refund_time_button">"Submit"</string>
    <string name="support_help_with_something_else_button">"Submit"</string>
    <string name="support_feedback_title">"Feedback"</string>
    <string name="support_feedback_button">"Submit"</string>
    <string name="support_change_food_button">"Submit"</string>
    <string name="support_add_comment_title">"Add Comments"</string>
    <string name="support_add_comment_button">"Submit"</string>
    <string name="support_missing_items_button">"Submit"</string>
    <string name="support_food_quality_button">"Submit"</string>
    <string name="support_use_coupon_title">"Use coupon"</string>
    <string name="support_use_coupon_button">"Submit"</string>
    <string name="support_other_issue_button">"Submit"</string>
    <string name="support_no_phone_number">"The restaurant does not have a phone number. Please contact RICE support."</string>
    <string name="search">"Search"</string>
    <string name="search_rest">"Search restaurant or menu.."</string>
    <string name="search_menu">"Search menu"</string>
    <string name="food_minimum">"Minimum"</string>
    <string name="required_option">"Required"</string>
    <string name="recommend">"RICE Picks"</string>
    <string name="image_hint_text">"Press and hold food image to send feed back."</string>
    <string name="option_min">"Choose at least %s"</string>
    <string name="option_max">"Choose at most %s"</string>
    <string name="option_exact">"Choose %s items"</string>
    <string name="option_range">"Choose %1$s - %2$s items"</string>
    <string name="search_not_found">"No Result Found"</string>
    <string name="search_menu_not_found">"No Result Found"</string>
    <string name="try_other_restaurant">"Check other restaurants"</string>
    <string name="restaurant_closed">"This restaurant is closed, please order from other restaurant."</string>
    <string name="restaurant_open">"This restaurant just opened, you can now place order."</string>
    <string name="closed_at">"Closed at %s"</string>
    <string name="opens_at">"Opens at %s"</string>
    <string name="closed_today">"Closed Today"</string>
    <string name="closed_at_scheduled">"Cuttoff at %s"</string>
    <string name="opens_at_scheduled">"Start Order at %s"</string>
    <string name="status_text">"Status"</string>
    <string name="status_pending">"Waiting for payment."</string>
    <string name="status_created">"We got your order!"</string>
    <string name="status_sent">"We got your order!"</string>
    <string name="status_confirmed">"Your order is being prepared."</string>
    <string name="status_delivery">"Your food is on its way."</string>
    <string name="status_delivered">"Your food is delivered."</string>
    <string name="status_completed">"Your order is confirmed."</string>
    <string name="status_cancelled">"Your order was canceled."</string>
    <string name="status_canceled">"Your order was canceled."</string>
    <string name="status_declined">"Your order was canceled."</string>
    <string name="status_ready">"Your food is ready."</string>
    <string name="status_en_route_to_pickup">"En route to pick up"</string>
    <string name="status_at_pickup">"Courier at restaurant"</string>
    <string name="status_pickup_completed">"Pickup completed"</string>
    <string name="status_en_route_to_dropoff">"En route to dropoff"</string>
    <string name="status_at_dropoff">"Your food has arrived nearby"</string>
    <string name="events_order_confirmed">"Order Confirmed"</string>
    <string name="events_order_cancelled">"Order Cancelled"</string>
    <string name="events_order_delivered">"Delivered"</string>
    <string name="events_order_driver_assigned">"Driver Assigned"</string>
    <string name="events_order_driver_unassigned">"Change Driver"</string>
    <string name="events_order_delivery_pickup">"Driver Picking Up"</string>
    <string name="events_order_delivery_pickup_complete">"Driver Picked Up"</string>
    <string name="events_order_delivery_dropoff">"Driver Dropping off"</string>
    <string name="i_have_read">"I have read and agreed to"</string>
    <string name="terms">"Terms"</string>
    <string name="policy">"Policy"</string>
    <string name="cannot_order">"Cannot place order?"</string>
    <string name="placing_order">"We are processing your order"</string>
    <string name="placing_order_success">"Order successful"</string>
    <string name="phone">"Phone"</string>
    <string name="call">"Call"</string>
    <string name="help">"Help"</string>
    <string name="call_support">"Call RICE Support"</string>
    <string name="call_driver">"Call Driver"</string>
    <string name="delivered_by_rest">"Need Help?"</string>
    <string name="call_rest_for_delivery">"This order is delivered by restaurant. Call restaurant for any delivery issues."</string>
    <string name="call_rest">"Call Restaurant"</string>
    <string name="call_cs">"RICE"</string>
    <string name="fees_subtotal">"Subtotal"</string>
    <string name="fees_tax">"Tax"</string>
    <string name="fees_delta">"Reach Deli. Min"</string>
    <string name="fees_tip">"Tip"</string>
    <string name="fees_delivery">"Delivery"</string>
    <string name="fees_service">"Service Fee"</string>
    <string name="fees_credit">"Credit Card Fee"</string>
    <string name="total">"Total"</string>
    <string name="custom">"Custom Amount"</string>
    <string name="fees_tax_and_service">"Tax &amp; Fees"</string>
    <string name="fees_tax_service_include">"Include"</string>
    <string name="fees_tax_service_tax">"Tax"</string>
    <string name="fees_tax_service_and">" and"</string>
    <string name="fees_tax_service_fee">"Service Fee"</string>
    <string name="order">"Order"</string>
    <string name="cart">"Cart"</string>
    <string name="delivery">"Delivery"</string>
    <string name="not_reach_min">"Add %s to reach the minimum, You can order more items, or pay the difference."</string>
    <string name="require_minimum_tip">"Restaurant requires %s minimum tip, not editable."</string>
    <string name="place_order">"Place Order"</string>
    <string name="order_number_is">"Your order number is"</string>
    <string name="tips_special_time_support_driver">"Support your drivers druing this special time! 100% tips are paid to RICE drivers."</string>
    <string name="delivery_window">"Delivery Window"</string>
    <string name="delivery_change_to_delivery">"Switch to Delivery"</string>
    <string name="delivery_change_to_package">"Switch to Self Pickup"</string>
    <string name="delivery_alert_to_delivery">"Do you wish to switch to delivery?"</string>
    <string name="delivery_alert_to_package">"Do you wish to switch to self pickup?"</string>
    <string name="delivery_title">"Self Pickup"</string>
    <string name="comments">"Comments"</string>
    <string name="add_comments">"Add Comments"</string>
    <string name="coupon">"Coupon"</string>
    <string name="use_coupon">"Use Coupon"</string>
    <string name="add_coupon">"Add Coupon"</string>
    <string name="invalid_coupon">"Invalid Coupon"</string>
    <string name="invalid_coupon_retry">"Your coupon cannot be applied to this order, we have removed the coupon for you."</string>
    <string name="expires_at">"Expires at %s"</string>
    <string name="my_coupon">"My Coupons"</string>
    <string name="n_coupon">"%s Coupons"</string>
    <string name="choose_tip">"Choose Tip"</string>
    <string name="add_phone">"Add Phone Number"</string>
    <string name="enter_phone">"Enter Phone Number"</string>
    <string name="enter_code">"Enter Code"</string>
    <string name="not_receive_sms">"Did not receive SMS?"</string>
    <string name="wait_seconds">"Wait %ss and"</string>
    <string name="resend">"Resend"</string>
    <string name="resend_by_sms">"Resend via SMS"</string>
    <string name="resend_by_voice">"Let RICE call you"</string>
    <string name="how_to_resend">"Choose Method"</string>
    <string name="type_on_keyboard">"Please type on keyboard, no need to select the text box"</string>
    <string name="logout_confirm">"Are you sure you want to log out?"</string>
    <string name="logout">"Log out"</string>
    <string name="login">"Log in"</string>
    <string name="require_login">"Please enter your phone number before placing order."</string>
    <string name="pay">"Pay"</string>
    <string name="alipay">"Alipay"</string>
    <string name="wechatPay">"WeChat Pay"</string>
    <string name="wechat_pay">"WeChat Pay"</string>
    <string name="applePay">"Apple Pay"</string>
    <string name="payment">"Payment"</string>
    <string name="choose_payment">"Select Payment"</string>
    <string name="edit_payment">"Edit Cards"</string>
    <string name="add_new_card">"Add Card"</string>
    <string name="new_card">"New Card"</string>
    <string name="no_card">"You currently have no cards"</string>
    <string name="confirm_remove">"Confirm Remove"</string>
    <string name="confirm_remove_card">"Are you sure you want to remove card %s ?"</string>
    <string name="order_count">"Orders"</string>
    <string name="days_on_ricepo">"Days"</string>
    <string name="past_order">"Past Orders"</string>
    <string name="saving">"Saved"</string>
    <string name="no_order">"You have no past orders"</string>
    <string name="your_credit">"Your Credit"</string>
    <string name="we_cover">"We launched our service at NYC, Boston and Chicago."</string>
    <string name="refer_now">"Share RICE to your friends and get %s when they place their first orders."</string>
    <string name="how_to_refer">"Choose Method"</string>
    <string name="refer_friend">"Invite Friends,"</string>
    <string name="refer_text">"Refer a Friend"</string>
    <string name="refer_friend_n_coins">Refer a friend to earn %s points</string>
    <string name="refer_recommend_now">"Refer Now"</string>
    <string name="refer_sms">"SMS"</string>
    <string name="refer_wechat">"WeChat"</string>
    <string name="refer_wechat_moment">"WeChat Moment"</string>
    <string name="refer_copy_title">"Copy Refer Link"</string>
    <string name="refer_copy_alert">"Refer link %s copied"</string>
    <string name="refer_copy_text">"RICE Chinese Delivery APP, use my referral code %s and get 15%% off your first order"</string>
    <string name="refer_share_title">"推荐你一个超好用的订中餐APP，首单还有15%优惠哦！"</string>
    <string name="set_language">"Set Language"</string>
    <string name="select_language">"Select Language  "</string>
    <string name="error_address_out_of_delivery">"Your current address is out of delivery zone"</string>
    <string name="error_address_verify_failed">"Cannot verify your address"</string>
    <string name="error_address_verify_failed_tap">"Address not verified, tap to retry"</string>
    <string name="error_incomplete_address">"Incomplete address, please use street address"</string>
    <string name="error_too_many_requests">"Please wait %s seconds and retry."</string>
    <string name="error_429">"Please wait %s seconds and retry."</string>
    <string name="error_auth_failed">"Authentication failed, please try again."</string>
    <string name="error_invalid_phone_number">"Invalid Phone Number"</string>
    <string name="error_not_found">"Not found"</string>
    <string name="error_invalid_coupon">"Invalid coupon"</string>
    <string name="error_coupon_restriction">"Your coupon cannot be applied to this order."</string>
    <string name="error_load_failed">"Failed to load , please check network settings and try again."</string>
    <string name="error_restaurant_not_found">"No restaurants nearby"</string>
    <string name="error_menu_not_found">"Menu is not ready yet"</string>
    <string name="error_unknown">"Unexpected error, please retry."</string>
    <string name="error_options_not_reach_min">"Please choose at least %1$s item for %2$s"</string>
    <string name="error_restaurant_closed">"This restaurant is closed, please order from a different restaurant."</string>
    <string name="error_cannot_get_location">"RICE cannot get your location, please enter address."</string>
    <string name="error_card_declined">"Your card was declined, please try a different one."</string>
    <string name="error_unavailable">"[%s] is sold out, please remove from cart and try again."</string>
    <string name="error_wechat_notInstalled">"Wechat is not found in your device. Please make sure Wechat is installed."</string>
    <string name="error_applePay_notInstalled">"Apple Pay is not set up. Please set up first."</string>
    <string name="error_card_zip_fail">"Your credit card zip code is incorrect, for security reason, please add card again or select other payment methods."</string>
    <string name="error_address_precheck">"Unable to verify your address, please check address."</string>
    <string name="error_pickup_quote_failed">"Unable to get quote, please tap to retry"</string>
    <string name="error_title_restaurant_not_found">"Please Wait"</string>
    <string name="error_title_menu_not_found">"Please Wait"</string>
    <string name="error_title_load_failed">"Failed"</string>
    <string name="error_title_cannot_get_location">"Failed"</string>
    <string name="hint_cart_will_be_cleared">"Your cart at %s will be cleared，do you wish to continue?"</string>
    <string name="tags_market">"Supermarket"</string>
    <string name="tags_bbq">"BBQ"</string>
    <string name="tags_hotpot">"Hotpot"</string>
    <string name="tags_hotpot_a">"Hotpot"</string>
    <string name="tags_mala_pot">"Mala Pot"</string>
    <string name="tags_mala_soup">"Mala Soup"</string>
    <string name="tags_sichuan">"Szechuan Style"</string>
    <string name="tags_sichuan_a">"Szechuan Style"</string>
    <string name="tags_sichuan_b">"Szechuan Style"</string>
    <string name="tags_hunan">"Hunan Style"</string>
    <string name="tags_bento">"Lunch Box"</string>
    <string name="tags_noodle">"Noodle"</string>
    <string name="tags_shanghai">"Shanghai Style"</string>
    <string name="tags_korean">"Korean"</string>
    <string name="tags_sushi">"Sushi"</string>
    <string name="tags_beijing">"Beijing Style"</string>
    <string name="tags_home">"Home Style"</string>
    <string name="tags_home_a">"Home Style"</string>
    <string name="tags_home_b">"Home Style"</string>
    <string name="tags_cafe">"Milk Tea"</string>
    <string name="tags_dongbei">"Dongbei Style"</string>
    <string name="tags_thai">"Thai"</string>
    <string name="tags_vietnamese">"Vietnamese"</string>
    <string name="tags_crepe">"Chinese Crepe"</string>
    <string name="tags_dim_sum">"Dim Sum"</string>
    <string name="tags_seafood">"Seafood"</string>
    <string name="tags_seafood_a">"Seafood"</string>
    <string name="tags_cantonese">"Cantonese Style"</string>
    <string name="tags_taiwanese">"Taiwan Style"</string>
    <string name="tags_dessert">"Dessert"</string>
    <string name="tags_teppanyaki">"Teppanyaki"</string>
    <string name="tags_casserole_rice">"Casserole Rice"</string>
    <string name="tags_take_food">"Take Food"</string>
    <string name="tags_siu_mei">"Siu Mei"</string>
    <string name="tags_braised_dishes">"Braised Dishes"</string>
    <string name="tags_snack">"Snack"</string>
    <string name="tags_hongkong_cafe">"Cafe"</string>
    <string name="tags_asian_fusion">"Asian Fusion"</string>
    <string name="tags_fujian">"Fujian"</string>
    <string name="tags_wonton">"Wonton"</string>
    <string name="tags_congee">"Congee"</string>
    <string name="tags_malaysia">"Malaysian"</string>
    <string name="tags_ramen">"Ramen"</string>
    <string name="tags_xian">"Xi\ʼan Style"</string>
    <string name="tags_xibei">"Xibei Style"</string>
    <string name="tags_tianjin">"Tianjin Style"</string>
    <string name="tags_jiangzhe">"Jiangzhe Style"</string>
    <string name="tags_yungui">"Yungui Style"</string>
    <string name="tags_vermicelli">"Vermicelli"</string>
    <string name="tags_poke">"Poke"</string>
    <string name="tags_bakery">"Bakery"</string>
    <string name="tags_grill_fish">"Grill Fish"</string>
    <string name="tags_halal">"Halal"</string>
    <string name="tags_chaozhou">"Chaozhou"</string>
    <string name="tags_indian">"Indian"</string>
    <string name="TEST">"test"</string>
    <string name="DEMO">"demo"</string>
    <string name="CLEAR">"clear"</string>
    <string name="LANGUAGE">"English"</string>
    <string name="close">"close"</string>
    <string name="Apple_Pay_Failed">"Apple Pay Failed, please select other payment and try again"</string>
    <string name="wechat_payment_failed">"Wechat Payment failed, please select other payment and try again"</string>
    <string name="alipay_payment_failed">"Alipay Payment failed, please select other payment and
        try again"</string>
    <string name="payment_failed">"Payment Failed, please select other payment and try again"</string>
    <string name="payment_select_failed">"Please select other payment and try again"</string>
    <string name="payment_google_not_support">"Google Pay is not supported, please check the settings."</string>
    <string name="payment_confirm_method">"Confirm to use %s for payment？ "</string>
    <string name="order_pending_warning">"Please complete the payment within %1$s minutes %2$s seconds   "</string>
    <string name="re_order">"Reorder"</string>
    <string name="image_is_helpful">"Image is Helpful"</string>
    <string name="wrong_image">"Wrong Image"</string>
    <string name="other_feedback">"Other Feedback"</string>
    <string name="reason_or_suggestion">"Reason or other suggestion"</string>
    <string name="thank_rating">"Thank you for your rating!"</string>
    <string name="hows_your_delivery">"How was the delivery service?"</string>
    <string name="please_select_reason">"Please select reason"</string>
    <string name="rate_driver">"Rate"</string>
    <string name="like">"Good"</string>
    <string name="quick_efficient">"Quick &amp; Efficient"</string>
    <string name="good_communication">"Good Communication"</string>
    <string name="delivered_with_care">"Delivered with Care"</string>
    <string name="friendly_service">"Friendly Service"</string>
    <string name="dislike">"Bad"</string>
    <string name="slow_inefficient">"Slow &amp; Inefficient"</string>
    <string name="bad_communication">"Bad Communication"</string>
    <string name="delivered_with_careless">"Delivered with Careless"</string>
    <string name="unfriendly_service">"Unfriendly Service"</string>
    <string name="group_order">"Group Order"</string>
    <string name="group_create_group_order_title">"Create Group Order"</string>
    <string name="group_create_group_order_desc">"You can invite your friends to order together"</string>
    <string name="group_create_group_order_placeholder">"Please enter your nickname"</string>
    <string name="group_create_success_title">"Group Order is Created"</string>
    <string name="group_create_success_subtitle">"You can invite your friends to order together"</string>
    <string name="group_create_success_button">"Invite Friends"</string>
    <string name="group_share_title">"Share Your Group"</string>
    <string name="group_share_wechat">"Wechat Friends"</string>
    <string name="group_share_other">"Other"</string>
    <string name="group_close_title">"Exit Group Order"</string>
    <string name="group_close_subtitle_host">"You are the group owner, all members will be removed from group."</string>
    <string name="group_close_subtitle_member">"All food items will be removed"</string>
    <string name="group_join_title">"Join Group Order"</string>
    <string name="group_join_nickname">"Please enter your nickname"</string>
    <string name="group_cancel_join_title">"Cancel Join Group?"</string>
    <string name="group_cancel_join_subtitle">"You will need to click the link again to join group."</string>
    <string name="group_closed_title">"Group is Closed"</string>
    <string name="group_closed_subtitle">"You can create a new group order"</string>
    <string name="group_order_placed">"This group order has been completed."</string>
    <string name="group_please_exit_title">"You are already in a group order"</string>
    <string name="group_please_exit_subtitle">"Please exit %s group order"</string>
    <string name="group_please_exit_button">"Exit Group"</string>
    <string name="group_share_wechat_title">"邀请您加入RICE团体订餐"</string>
    <string name="group_share_wechat_subtitle">"一起来 %s 订餐吧"</string>
    <string name="weekly_menu">"Weekly Menu"</string>
    <string name="leaderboard">"Leaderboard"</string>
    <string name="banner_time">"M/d"</string>
    <string name="subscription_create_success">"Thank you for being RICE VIP."</string>
    <string name="subscription_join">"Subscribe"</string>
    <string name="subscription_vip">"RICE VIP"</string>
    <string name="subscription_check_detail">"View Detail"</string>
    <string name="subscription_current_period">"Next Cycle %s"</string>
    <string name="subscription_valid_until">"Canceled, valid until %s"</string>
    <string name="subscription_cancel">"Cancel Subscription"</string>
    <string name="subscription_canceled_message">"Subscription canceled, thank you being RICE VIP."</string>
    <string name="subscription_confirm_cancel">"Do you wish to cancel your subscription?"</string>
    <string name="subscription_update_payment_success">"Payment method updated"</string>
    <string name="vip_discount">"VIP Discount"</string>
    <string name="vip_discount_without_vip">"Join VIP and Get"</string>
    <string name="reward_title">"Redeem Coin"</string>
    <string name="reward_subtitle0">"1 [coin] for every %s"</string>
    <string name="reward_subtitle1">"You have %s [coin]"</string>
    <string name="balance_alert">"You don't have enough coins."</string>
    <string name="reward_limit_alert">"You can only redeem one item at %s"</string>
    <string name="my_coins">"RICE Coins"</string>
    <string name="n_coins">"You can use %s RICE points for the next order"</string>
    <string name="redeem">"Redeem"</string>
    <string name="restaurant_reward">"Restaurant Reward"</string>
    <string name="n_restaurant_reward">"%s Restaurant Reward"</string>
    <string name="use_ricepo_point">"Redeem Points"</string>
    <string name="use_ricepo_point_unselected">"Redeem Points for %s off"</string>
    <string name="redeem_ricepo_point">"Redeem RICE Points"</string>
    <string name="redeem_current_point">"You have %s RICE Points"</string>
    <string name="redeem_points">"Points"</string>
    <string name="redeem_equal_to">"Equal to %s"</string>
    <string name="redeem_points_over">"Amount over the current balance"</string>
    <string name="ricepo_points_total">"Total RICE Points"</string>
    <string name="ricepo_points_history">"Point History"</string>
    <string name="permission_check_phone">"Please check the phone permissions in the system settings"</string>
    <string name="permission_check_camera">"Please check the camera permissions in the system settings"</string>
    <string name="permission_check_storage">"Please check the storage permissions in the system settings"</string>
    <string name="union_discount">"UnionPay Promo"</string>
    <string name="checkout_window_alert">"Please note this order will be delivered on %s"</string>
    <string name="checkout_continue">"Continue"</string>
    <string name="lucky_counter_title">"Feeling Lucky"</string>
    <string name="lucky_counter_description">"Please select group size, RICE will pick the menu for you"</string>
    <string name="lucky_counter_select_count">"Group Size"</string>
    <string name="lucky_counter_submit">"Feeling Lucky"</string>
    <string name="lucky_counter_confirm">"Add to Cart"</string>
    <string name="alert_food_limit">"[%s] limit %s order for each customer"</string>
    <string name="alert_category_limit">"You can only choose %s item in this category"</string>
    <string name="alert_category_limits">"You can only choose %s items in this category"</string>
    <string name="restaurant_sections_loadmore_title">"Show More"</string>
    <string name="bundle_title">"Choose Bundle Restaurant"</string>
    <string name="bundle_confirm">"Confirm"</string>
    <string name="bundle_change">"Edit Restaurant"</string>
    <string name="bundle_closed">"Restaurant Closed"</string>
    <string name="bundle_remove_alert">"You already selected items in this restaurant, do you want to remove them from cart?"</string>
    <string name="bundle_select_alert">"You have selected multiple restaurants, and you cannot join the group order because of that, please remove the other restaurants and try again."</string>
    <string name="bundle_subtitle">"Order From Multiple Restaurants"</string>
    <string name="feedback_text_placeholder">"Please enter your feedback."</string>
    <string name="feedback_phone_placeholder">"Please enter phone number."</string>
    <string name="feedback_alert_text">"Please enter your feedback."</string>
    <string name="feedback_alert_phone">"Please enter phone number."</string>
    <string name="checkout_precheck_quote_windows">"The delivery window has been updated, please check and try again."</string>
    <string name="order_reward_amount">"You have earned %s [coin]"</string>
    <string name="menu_promotion_remove">"[%s] is removed from the cart because it requires %s subtotal."</string>
    <string name="menu_promotion_add">"You have reached the minimum for the promo item [%s], do you want to add it to cart?"</string>
    <string name="start_immediately">"Start Now"</string>
    <string name="add_cart">"Add to Cart"</string>
    <string name="restaurant_pickup">"Pickup"</string>
    <string name="restaurant_delivery">"Delivery"</string>
    <string name="pickup_unit_miles">"MILES"</string>
    <string name="pickup_map_current_location">"Current Location"</string>
    <string name="pickup_map_custom_location">"Map Area"</string>
    <string name="pickup_map_search">"Search this area"</string>
    <string name="pickup_map_location_failed">"Unable to get current location, please try again or manually enter address."</string>
    <string name="restaurant_pool_expired">"Expired"</string>

    <!-- java.util.UnknownFormatConversionException: Conversion = ''' -->
    <string name="driver_rating_title">"How’s your delivery for %s's order?"</string>
    <string name="driver_rating_incomplete">"Your extra tip has not been paid yet, please go to My RICE and make the payment in the recharge order."</string>
    <string name="chat">"Chat"</string>
    <string name="chat_enter">"Go"</string>
    <string name="ricepo_service_chat">"Chat"</string>
    <string name="label_group_order">"Group Order"</string>
    <string name="driver_rating_tip_change">"Change Tip"</string>
    <string name="driver_rating_tip_current">"Current Tip:"</string>
    <string name="delivery_guarantee">"Delivery Guarantee"</string>
    <string name="delivery_finish_at">"%s ETA"</string>
    <string name="delivery_instant">"Delivery"</string>
    <string name="update_required">"Your current version is outdated, please update your app to continue using RICE service."</string>
    <string name="update_to_install">"RICE updated has been downloaded, please install now."</string>
    <string name="address_confirm">"You are far away from your delivery address, are you sure you want to use this address?"</string>
    <string name="address_update">"Your delivery address has been updated to %s, do you want to set as default address?"</string>
    <string name="delivery_time">"Delivery Time"</string>
    <string name="hint_tip_enter">"Please Enter Tip Amount"</string>
    <string name="restaurant_busy_to_menu">"%s is busy now, it takes longer to prepare food, do you want to continue?"</string>
    <string name="lucky_no_card">"That's it, tap refresh to start over."</string>
    <string name="lucky_refresh">"Start Over"</string>
    <string name="restaurant_prepared_at">"Food Ready %s"</string>
    <string name="checkout_pickup_window_alert">"Please confirm pickup time %s"</string>
    <string name="checkout_options_edit_error">"Food info not available, please try again later."</string>
    <string name="comments_history">"History Comments"</string>
    <string name="order_driver_message">"Driver Message '%s'"</string>
    <string name="quote_food_not_found">"[%s] is not available, we have automatically removed it for you."</string>
    <string name="quote_food_option_not_found">"[%2$s] from [%1$s] is not available, we have automatically removed it for you."</string>
    <string name="quote_food_not_found_restaurant">"[%2$s] from [%1$s] is not available, we have automatically removed it for you."</string>
    <string name="menu_option_select_info">"Select Options"</string>
    <string name="menu_option_food_minimum">"%s Minimum"</string>
    <string name="menu_see_all">"See All"</string>
    <string name="restaurant_search_recommend">"We could not find the result, here's some recommendations"</string>
    <string name="restaurant_search_not_found">"We could not find the result"</string>
    <string name="menu_car_checkout">"Checkout"</string>
    <string name="menu_recommend_like">"Recommend More"</string>
    <string name="refer_earn_reward">"Get Reward"</string>
    <string name="refer_earned">"Earned"</string>
    <string name="refer_people">"People"</string>
    <string name="refer_history">"Refer History"</string>
    <string name="refer_no_record">"No record found"</string>
    <string name="refer_share">"Share Now"</string>
    <string name="refer_get">"Get"</string>
    <string name="refer_title">"Share"</string>
    <string name="refer_branch_prompt">"Using friend refer code %s"</string>
    <string name="refer_start_order">"Start Ordering"</string>
    <string name="menu_jump_title">"Category"</string>
    <string name="diary_image_title">"Save RICE Diary"</string>
    <string name="diary_image_save">"Save your RICE diary into photo album"</string>
    <string name="diary_image_downloaded">"Saved to album, share it to your friends"</string>
    <string name="diary_image_failed">"Unable to download photo, please try again."</string>
    <string name="diary_permission">"Unable to access your album, please turn on the permission in settings page"</string>
    <string name="download">"Download"</string>
    <string name="diary_image_detail">"Check Details"</string>
    <string name="diary_red_link_success">"链接验证成功，您可以在RICE积分页面查看详情，赶紧让好友来点赞吧！"</string>
    <string name="restaurant_sort_title">"Sort"</string>
    <string name="menu_combo_see_more">"See More"</string>
    <string name="five_star">"Very good"</string>
    <string name="four_star">"Not bad"</string>
    <string name="there_star">"Just so so"</string>
    <string name="two_star">"Not so good"</string>
    <string name="one_star">"Very bad"</string>
    <string name="anonymous">"Anonymous"</string>
    <string name="rating_dishes_title">"How was the food ratings?"</string>
    <string name="food_ratings">"Food
Ratings"</string>
    <string name="tags_vegetarian">"Vegetarian"</string>
    <string name="tags_breakfast">"Breakfast"</string>
    <string name="rating_now">"Rate Now"</string>
    <string name="rated">"Rated"</string>
    <string name="canceld">"Canceled"</string>
    <string name="email">"Email"</string>
    <string name="email_verify_codePlaceholder">"Enter verification code"</string>
    <string name="email_verify_receive">"Send verification code"</string>
    <string name="modify">"Change"</string>
    <string name="email_reward">"Get %s Coupon For Adding"</string>
    <string name="mode_switch_popup_title">"Swich Mode"</string>
    <string name="tab_orders">"My Orders"</string>
    <string name="tab_coupon">"Coupons"</string>

    <!-- Fuzzy -->
    <string name="zip">"Postal Code"</string>
    <string name="tittle_postcode_confirm">"Because you changed the postal code, please choose one address from the following addresses:"</string>
    <string name="address_not_found">"The address was not found"</string>
    <string name="enter_postcode">"Please enter your postal code"</string>
    <string name="confirm_payment">"Place Order"</string>
    <string name="app_rating_dialog_title">"Looking forward to your feedback"</string>
    <string name="app_rating_dialog_content">"We welcome any feedback and ratings, \n Your feedback helps us to improve our App"</string>
    <string name="app_rating_dialog_bad">"Dislike"</string>
    <string name="app_rating_dialog_good">"I like it"</string>
    <string name="app_rating_feedback_title">"Feedback"</string>
    <string name="app_rating_feedback_secondtitle">"Details"</string>
    <string name="app_rating_feedback_hint">"Please describe"</string>
    <string name="app_rating_success">"Submitted"</string>
    <string name="account_delete">"Delete Account"</string>
    <string name="account_delete_success">"Your account has been successfully deleted"</string>
    <string name="account_safety">"Security"</string>
    <string name="account_delete_message">"This action completely erases any information of this account. You will not be able to use this account for any RICE related products and services afterwards. "</string>


    <string name="notification_permission">We recommend enabling notifications to avoid missing important order information.</string>

</resources>