<resources>

    <!-- Base application light theme. -->
    <style name="AppThemeLight" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/colorPrimary</item>
    </style>

<!--    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.MaterialComponents.Dark.ActionBar" />-->

<!--    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.MaterialComponents.Light" />-->

    <!-- Base application dark theme. -->
    <style name="AppThemeDark" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimaryByDark</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDarkByDark</item>
        <item name="colorAccent">@color/colorAccentByDark</item>
        <item name="android:windowBackground">@color/colorPrimary</item>
    </style>

    <style name="SplashOut" parent="@android:style/Animation.Activity">
        <item name="android:activityCloseExitAnimation">@anim/splash_out</item>
    </style>

    <style name="SplashFade" parent="@android:style/Animation.Activity">
        <item name="android:windowEnterAnimation">@anim/activity_fade_in</item>
    </style>

    <style name="ActivityBottomIn" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/activity_bottom_in</item>
        <item name="android:activityCloseEnterAnimation">@anim/activity_no</item>
        <item name="android:activityOpenExitAnimation">@anim/activity_no</item>
        <item name="android:activityCloseExitAnimation">@anim/activity_bottom_out</item>
    </style>

    <style name="ButtonPrimaryStyle">
        <item name ="android:backgroundTint">@color/mr</item>
<!--        <item name ="android:background">@color/mr</item>-->
        <item name="android:textColor">@color/w</item>
        <item name="cornerRadius">@dimen/button_radius</item>
        <item name="android:minHeight">40dp</item>
    </style>


    <style name="ButtonSecondaryStyle">
        <item name="android:backgroundTint">@color/card_background</item>
        <item name="android:textColor">@color/mr</item>
        <item name="cornerRadius">@dimen/button_radius</item>
        <!-- android 25 default min height 56 -->
        <item name="android:minHeight">44dp</item>
    </style>

    <style name="ButtonOutlineFiveStyle">
        <item name="android:background">@drawable/button_engraved_outline_five</item>
        <item name="android:textColor">@drawable/button_secondary_color</item>
        <item name="android:minHeight">@dimen/button_normal_height</item>
    </style>

    <style name="ButtonGoldStyle">
        <item name="android:background">@drawable/button_gold_enable</item>
        <item name="android:textColor">@drawable/button_primary_color</item>
        <item name="android:minHeight">40dp</item>
    </style>


    <!-- large title style -->
    <style name="LargeTitleStyle" parent="FontH3">
        <item name="android:textColor">@color/mainText</item>
        <item name="android:alpha">@fraction/full</item>
    </style>

    <!-- title style -->
    <style name="TitleStyle" parent="FontH4">
        <item name="android:textColor">@color/mainText</item>
        <item name="android:alpha">@fraction/full</item>
    </style>

    <!-- subtitle style -->
    <style name="SubTitleStyle" parent="FontH6">
        <item name="android:textColor">@color/subText</item>
    </style>

    <style name="TitleMenuStyle" parent="FontH5">
        <item name="android:textColor">@color/mainText</item>
    </style>

    <!-- submenu style -->
    <style name="SubTitleMenuStyle" parent="FontH6">
        <item name="android:textColor">@color/subTextMenu</item>
    </style>

    <!-- restaurant sub info style -->
    <style name="RestaurantInfoStyle" parent="FontH6extra">
        <item name="android:textColor">@color/subTextMenu</item>
    </style>

    <!-- item font for normal label -->
    <style name="ItemStyle" parent="FontH5">
        <item name="android:textColor">@color/mainText</item>
        <item name="android:alpha">@fraction/full</item>
    </style>

    <!-- item font for deliver label -->
    <style name="SubItemStyle" parent="FontH6">
        <item name="android:textColor">@color/mainText</item>
        <item name="android:alpha">@fraction/full</item>
    </style>

    <!-- alert style -->
    <style name="AssertiveStyle" parent="FontH6">
        <item name="android:textColor">@color/alert</item>
        <item name="android:alpha">@fraction/full</item>
    </style>

    <!-- stable title style -->
    <style name="StableStyle" parent="FontH4">
        <item name="android:textColor">@color/subText</item>
        <item name="android:alpha">@fraction/full</item>
    </style>

    <!-- regular style -->
    <style name="RegularStyle" parent="EditTextStyle">
        <item name="android:textSize">@dimen/font_size_h5</item>
        <item name="android:textColor">@color/textLineText</item>
        <item name="android:tint">@color/textLineText</item>
        <item name="android:background">@drawable/bottom_normal_line</item>
    </style>

    <!-- divider horizontal -->
    <style name="DividerHorizontalStyle">
        <item name="android:layout_height">2dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:background">@drawable/divider_horizontal_in</item>
    </style>

    <!-- restaurant divider horizontal -->
    <style name="RestaurantDividerHorizontalStyle">
        <item name="android:layout_height">2dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:background">@drawable/divider_restaurant_horizontal_in</item>
    </style>

    <!-- divider vertical -->
    <style name="DividerVerticalStyle">
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">2dp</item>
        <item name="android:background">@drawable/divider_vertical_in</item>
    </style>

    <style name="EditTextSupportBorderStyle">
        <item name="android:shadowColor">@color/black</item>
        <item name="android:textColorHighlight">@color/gray6</item>
        <item name="android:textColor">@color/textLineText</item>
        <item name="android:textCursorDrawable">@drawable/text_cursor</item>
        <item name="android:textSize">@dimen/font_size_h5</item>
        <item name="android:background">@drawable/support_edittext_border_bg</item>
    </style>

    <!-- regular editext search style -->
    <style name="SearchStyle" parent="RegularStyle" >
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
        <item name="android:scrollHorizontally">false</item>
        <item name="android:imeOptions">actionSearch</item>
    </style>

    <!-- regular editext done to send style -->
    <style name="SendStyle" parent="RegularStyle" >
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
        <item name="android:scrollHorizontally">false</item>
        <item name="android:imeOptions">actionDone</item>
    </style>

    <style name="TranslucentStyle">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- transparent activity with dialog -->
    <style name="TransparentActivity" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:launchTaskBehindTargetAnimation">@null</item>
        <item name="android:launchTaskBehindSourceAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>

    <style name="DialogTheme" parent="Theme.AppCompat.Light.Dialog">
        <!-- <item name="android:windowBackground">@android:color/transparent</item> -->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
    </style>

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!-- UILabel frame zero style -->
    <style name="LabelItemStyle" parent="ItemStyle">
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="LuckyButtonStyle" parent="FontH6">
        <item name="android:textColor">@color/gray8</item>
    </style>

    <style name="SubLabelStyle" parent="FontH7">
        <item name="android:textColor">@color/gray7</item>
    </style>

    <style name="MenuFoodNameStyle" parent="FontH5" >
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textColor">@color/gray5</item>
    </style>

    <style name="PlaceOrderStyle" parent="FontH3">
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textColor">@color/primary_satisfied_button_text</item>
        <item name="android:shadowColor">@color/place_order_shadow</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">3.0</item>
        <item name="android:shadowRadius">1.0</item>
    </style>

    attribute at index 1: TypedValue{t=0x2/d=0x7f0400ef a=8} -->
    <style name="CustomBottomSheetDialog" parent="@style/ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheet</item>
    </style>
    <style name="CustomBottomSheet" parent="@style/Widget.MaterialComponents.BottomSheet">
        <!-- rounded corners are animated to sharp corners when expand -->
<!--        <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>-->
        <item name="backgroundTint">@android:color/transparent</item>
    </style>
    <style name="CustomShapeAppearanceBottomSheetDialog" >
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">16dp</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
        <item name="cornerSizeBottomLeft">0dp</item>
    </style>

    <style name="TopLeftCornerButton">
        <item name="cornerSizeTopLeft">16dp</item>
    </style>

    <style name="MotdLabelStyle">
        <item name="android:ellipsize">marquee</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="shapeAppearance">@style/TopLeftCornerButton</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">end|bottom</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
    </style>


</resources>
