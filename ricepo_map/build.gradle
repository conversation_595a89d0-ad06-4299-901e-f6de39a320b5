apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'


android {
    namespace 'com.ricepo.map'
    def globalConfiguration = rootProject.extensions.getByName("ext")
    compileSdk globalConfiguration["androidcompileSdk"]
    buildToolsVersion globalConfiguration["androidBuildToolsVersion"]

    defaultConfig {
        minSdkVersion globalConfiguration["androidMinSdkVersion"]
        targetSdkVersion globalConfiguration["androidTargetSdkVersion"]

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    viewBinding {
        enabled = true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility javaVersion
        targetCompatibility javaVersion
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation project(":ricepo_base")
    implementation project(":ricepo_style")

    implementation mapDependencies.googlePlaces
    implementation mapDependencies.rxKotlin

    implementation mapDependencies.roomRuntime
    implementation mapDependencies.roomKtx

    implementation mapDependencies.googleMapUtils

    implementation mapDependencies.constraintLayout

    implementation 'com.google.android.gms:play-services-maps:17.0.0'
    implementation 'androidx.appcompat:appcompat:1.0.2'
    kapt mapDependencies.roomCompiler

    api mapDependencies.mapboxMapSdk
    // The access token parameter is required when using a Mapbox service
    api mapDependencies.mapboxServices
    api mapDependencies.mapboxPluginAnnotation
    implementation mapDependencies.mapboxPluginUtils
}
