package com.ricepo.map.extension

import com.mapbox.mapboxsdk.geometry.LatLng
import com.mapbox.mapboxsdk.geometry.LatLngBounds
import com.ricepo.map.CompassPoint
import net.mastrgamr.mbmapboxutils.SphericalUtil
import java.lang.Math.max
import kotlin.math.ln

//
// Created by <PERSON><PERSON> on 29/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

fun LatLng.distanceMile(target: LatLng?): Double {
  val target = target ?: return -1.0
  val source = this
  val distanceMeters = SphericalUtil.computeDistanceBetween(source, target)

  return distanceMeters.times(0.000621371192)
}

fun LatLng.midPoint(target: LatLng): LatLng {
  // calculate the distance

//    val results = FloatArray(1)
//    Location.distanceBetween(this.latitude, this.longitude, target.latitude,
//        target.longitude, results)
  val distance = SphericalUtil.computeDistanceBetween(this, target)

  // calculate heading
  val heading = SphericalUtil.computeHeading(this, target)

  // mid point position
  return SphericalUtil.computeOffset(this, distance * 0.5, heading)
}

fun LatLng.getCenterAndZoom(target: LatLng, running: LatLng?): Pair<LatLng, Float> {
  val builder = LatLngBounds.Builder()
  builder.include(this)
  builder.include(target)

  if (running != null) {
    builder.include(running)
  }

  val bounds = builder.build()

  val center = bounds.center
  val locations = listOf(this, target, running)
  var maxDistance = 0.0
  var distance = 0.0
  locations?.forEach {
    if (it != null) {
      distance = SphericalUtil.computeDistanceBetween(center, it)
      maxDistance = max(distance, maxDistance)
    }
  }
  val scale = maxDistance / 1000
  var zoom = (16 - (Math.log(scale) / Math.log(2.0))).toFloat()
  if (zoom < 0) {
    zoom = 0f
  }

  return Pair(center, zoom)
}

fun LatLng.getCenterAndZoom(locations: List<LatLng>, zoomLevel: Float = 16f): Pair<LatLng, Float> {
  val builder = LatLngBounds.Builder()
  builder.include(this)

  val center = this
  var maxDistance = 0.0
  var distance = 0.0
  locations?.forEach {
    if (it != null) {
      distance = SphericalUtil.computeDistanceBetween(center, it)
      maxDistance = kotlin.math.max(distance, maxDistance)
    }
  }
  val scale = maxDistance / 1000
  var zoom = if (scale < 1) {
    (zoomLevel - scale).toFloat()
  } else {
    (zoomLevel - kotlin.math.abs(ln(scale) / ln(2.0))).toFloat()
  }
  if (zoom < 2) {
    zoom = 2f
  }

  return Pair(center, zoom)
}

fun LatLng.boundCenterAndPositions(positions: List<LatLng>): LatLngBounds {
  val center = this
  var boundBuilder = LatLngBounds.Builder()
  boundBuilder.include(center)
  positions.forEach {
    boundBuilder.include(it)
  }

  val bound = boundBuilder.build()
  val sw = bound.southWest
  val ne = bound.northEast

  val w1 = center.longitude - sw.longitude
  val w2 = ne.longitude - center.longitude
  val w = kotlin.math.max(w1, w2)

  val h1 = center.latitude - sw.latitude
  val h2 = ne.latitude - center.latitude
  val h = kotlin.math.max(h1, h2)

  val swLongitude = center.longitude - w
  val swLatitude = center.latitude - h
  val neLongitude = center.longitude + w
  val neLatitude = center.latitude + h

//    val swLatLng = LatLng(swLatitude, swLongitude)
//    val neLatLng = LatLng(neLatitude, neLongitude)

  return LatLngBounds.from(neLatitude, neLongitude, swLatitude, swLongitude)
}

fun LatLng.fromCenterAndPositions(positions: List<LatLng>): LatLngBounds? {
  val center = this
  val builder = LatLngBounds.Builder()
  builder.include(center)
  for (position in positions) {
    val other = LatLng(
      2 * center.latitude - position.latitude,
      2 * center.longitude - position.longitude
    )
    builder.include(position)
    builder.include(other)
  }
  return builder.build()
}

fun LatLng.getFullBounds(target: LatLng, running: LatLng?): LatLngBounds {
  val builder = LatLngBounds.Builder()
  builder.include(this)
  builder.include(target)

  if (running != null) {
    builder.include(running)
  }

  return builder.build()
}

/**
 * calculate the relative direction for coordinates
 */
fun LatLng.direction(point: LatLng): CompassPoint {
  // return [-180, 180) computeHeading
  val heading = SphericalUtil.computeHeading(this, point) + 180

  return if (heading == 0.0) {
    CompassPoint.north
  } else if (heading > 0 && heading < 90.0) {
    CompassPoint.northEast
  } else if (heading == 90.0) {
    CompassPoint.east
  } else if (heading > 90.0 && heading < 180.0) {
    CompassPoint.southEast
  } else if (heading == 180.0) {
    CompassPoint.south
  } else if (heading > 180.0 && heading < 270.0) {
    CompassPoint.southWest
  } else if (heading == 270.0) {
    CompassPoint.west
  } else if (heading > 270.0 && heading < 360.0) {
    CompassPoint.northWest
  } else {
    CompassPoint.north
  }
}
