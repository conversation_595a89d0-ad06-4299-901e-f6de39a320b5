package com.ricepo.map.model

import android.location.Location
import androidx.room.Entity
import com.google.android.gms.maps.model.LatLng
import com.google.android.libraries.places.api.model.Place
import com.google.gson.annotations.SerializedName
import com.ricepo.base.data.BaseEntity
import com.ricepo.base.model.AddressObj
import java.util.Locale

//
// Created by <PERSON><PERSON> on 7/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

// MARK: - UserAddress
data class UserAddress(
  val results: List<PlaceDetailResult>,
  val status: String
)

data class GeometryLocation(
  val lat: Double,
  val lng: Double
)

// MARK: - Geometry
data class Geometry(
  val location: GeometryLocation,
  @SerializedName("location_type")
  val locationType: String?,
  val viewport: Object
)

// MARK: - Result
data class PlaceDetailResult(
  @SerializedName("address_components")
  val addressComponents: List<AddressComponent>,
  @SerializedName("formatted_address")
  val formattedAddress: String,
  val geometry: Geometry,
  @SerializedName("place_id")
  val placeId: String?,
  val types: List<String>,
  val name: String?,
  @SerializedName("adr_address")
  val adrAddress: String?,
  val id: String?
)

// MARK: - AddressComponent
data class AddressComponent(
  @SerializedName("long_name")
  val longName: String?,
  @SerializedName("short_name")
  val shortName: String?,
  val types: List<String>
)

// MARK: - Viewport
data class Viewport(val northeast: Location, val southwest: Location)

/**
 * address from place result, and cache entity
 */
@Entity(tableName = "t_address")
data class FormatUserAddress(

    /*
     * Create Equatable
     * will only be trigger when one of below matched
     * 1. address place id changed
     * 2. note changed
     * 3. apt changed
     */
  var name: String? = null,
  var formatted: String? = null,
  var placeId: String? = null,
  var location: FormatLocation? = null,
  var unit: String? = null,
  var note: String? = null,
  var zipcode: String? = null,
  var street: String? = null,
  var number: String? = null,
  var city: String? = null,
  var state: String? = null,
  var country: String? = null,
  // get address from gps
  var source: String? = null
) : BaseEntity() {

  companion object {
    val SOURCE_GPS = "gps"
  }

  fun notUsOrIsNewYork(): Boolean {
    return country != "US" || (city == "New York" || city == "纽约" || city == "紐約")
  }

  fun isUk() = country?.lowercase(Locale.getDefault())?.equals("gb") ?: false

  // ^[A-Za-z0-9_.]+$
  fun isValidUkPostCode(): Boolean =
    zipcode?.replace(" ", "")?.let {
      it.length in 5..7 && it.matches("^[A-Za-z0-9_.]+$".toRegex())
    } ?: false

    /*
     * Initialize with Place input
     */
  constructor(place: Place, placeDescription: String?) : this() {
    placeId = place.id
    formatted = placeDescription ?: place.address
    name = place.name ?: placeDescription?.split(",")?.getOrNull(0)
    val longitude = place.latLng?.longitude
    val latitude = place.latLng?.latitude
    if (longitude != null && latitude != null) {
      location = FormatLocation(
        FormatLocation.TYPE_POINT,
        listOf(longitude, latitude)
      )
    }
    val placeMap = place.addressComponents?.asList()?.mapNotNull {
      it.types.firstOrNull()?.let { type -> type to it }
    }?.toMap()
    number = placeMap?.get("street_number")?.shortName ?: placeMap?.get("street_number")?.name
    street = placeMap?.get("route")?.shortName ?: placeMap?.get("route")?.name
    city = placeMap?.get("locality")?.shortName ?: placeMap?.get("locality")?.name
    state = placeMap?.get("administrative_area_level_1")?.shortName
      ?: placeMap?.get("administrative_area_level_1")?.name
    country = placeMap?.get("country")?.shortName ?: placeMap?.get("country")?.name
    zipcode = placeMap?.get("postal_code")?.shortName ?: placeMap?.get("postal_code")?.name
  }

    /*
     * Initialize with PlaceDetailResult input(Google Http API)
     */
  constructor(result: PlaceDetailResult?) : this() {
    val result = result ?: return
    placeId = result.placeId
    formatted = result.formattedAddress
    name = result.name ?: formatted?.split(",")?.getOrNull(0)
    location = FormatLocation(
      FormatLocation.TYPE_POINT,
      listOf(
        result.geometry.location.lng,
        result.geometry.location.lat
      )
    )
    val placeMap = result.addressComponents?.mapNotNull {
      it.types.firstOrNull()?.let { type -> type to it }
    }?.toMap()
    number = placeMap?.get("street_number")?.shortName ?: placeMap?.get("street_number")?.longName
    street = placeMap?.get("route")?.shortName ?: placeMap?.get("route")?.longName
    city = placeMap?.get("locality")?.shortName ?: placeMap?.get("locality")?.longName
    state = placeMap?.get("administrative_area_level_1")?.shortName
      ?: placeMap?.get("administrative_area_level_1")?.longName
    country = placeMap?.get("country")?.shortName ?: placeMap?.get("country")?.longName
    zipcode = placeMap?.get("postal_code")?.shortName ?: placeMap?.get("postal_code")?.longName
  }

  constructor(addressObj: AddressObj) : this() {
    formatted = addressObj.formatted
    name = formatted?.split(",")?.getOrNull(0)
    addressObj.location?.let {
      location = FormatLocation(it.type, it.coordinates)
    }
    number = addressObj.number
    street = addressObj.street
    city = addressObj.city
    state = addressObj.state
    country = addressObj.country
    zipcode = addressObj.zipcode
  }
}

// MARK: - Location
data class FormatLocation(
  val type: String,
  // [longitude, latitude]
  val coordinates: List<Double>
) {

  val dictionary: Map<String, Any>
    get() = mapOf("type" to type, "coordinates" to coordinates)

  fun loc(): String {
    return coordinates?.joinToString(",") ?: ""
  }

  fun latLng(): LatLng? {
    val lat = coordinates.get(1)
    val lng = coordinates.get(0)
    return if (lat != null && lng != null) {
      LatLng(lat, lng)
    } else {
      null
    }
  }

  companion object {
    const val TYPE_POINT = "Point"
  }
}
