package com.ricepo.map.model

//
// Created by <PERSON><PERSON> on 7/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

data class PlaceAutoCompleteResults(
  val status: String,
  val predictions: List<PlaceAutocompleteResult>
)

data class PlaceAutocompleteResult(
  val description: String,
  val distanceMeters: Int?,
  val placeID: String,
  val id: String,
  val terms: List<PlaceAutocompleteTerm>,
  val types: List<String>?,
  val matchedSubstrings: List<PlaceAutoCompleteMatchedSubString>,
  val structuredFormatting: StructuredFormatting?
) {

  enum class CodingKeys(val rawValue: String) {
    description("description"), placeID("place_id"), distanceMeters("distance_meters"), id("id"), terms("terms"), types("types"), matchedSubstrings("matched_substrings"), structuredFormatting("structuredFormatting");

    companion object {
      operator fun invoke(rawValue: String) = CodingKeys.values().firstOrNull { it.rawValue == rawValue }
    }
  }
}

data class PlaceAutocompleteTerm(
  val offset: Int,
  val value: String
)

data class PlaceAutoCompleteMatchedSubString(
  val length: Int,
  val offset: Int
)

data class StructuredFormatting(
  val mainText: String,
  val mainTextMatchedSubstrings: List<PlaceAutoCompleteMatchedSubString>,
  val secondaryText: String
) {
  enum class CodingKeys(val rawValue: String) {
    mainText("main_text"), mainTextMatchedSubstrings("main_text_matched_substrings"), secondaryText("secondary_text");

    companion object {
      operator fun invoke(rawValue: String) = CodingKeys.values().firstOrNull { it.rawValue == rawValue }
    }
  }
}
