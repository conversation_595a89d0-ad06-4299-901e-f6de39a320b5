package com.ricepo.map.fragment

import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import com.mapbox.mapboxsdk.Mapbox
import com.mapbox.mapboxsdk.maps.MapboxMap
import com.mapbox.mapboxsdk.maps.OnMapReadyCallback
import com.mapbox.mapboxsdk.maps.Style
import com.mapbox.mapboxsdk.maps.SupportMapFragment
import com.ricepo.map.R
import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader
import java.io.Reader
import java.io.StringWriter
import java.io.Writer

//
// Created by <PERSON><PERSON> on 4/6/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
open class MapboxFragment : SupportMapFragment(), OnMapReadyCallback {

  override fun onAttach(context: Context) {
    super.onAttach(context)
    Mapbox.getInstance(context, context.getString(R.string.mapbox_access_token))
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    getMapAsync(this)
  }

  override fun onMapReady(mapboxMap: MapboxMap) {
    try {
//            mapboxMap.setStyle(Style.Builder()
//                .fromUri("mapbox://styles/mapbox/cjf4m44iw0uza2spb3q0a7s41"))

//            val style = Style.Builder().fromUri("asset://")
//            mapboxMap.setStyle(style)

      if (isDarkMode()) {
        mapboxMap.setStyle(Style.DARK)
      } else {
        mapboxMap.setStyle(Style.LIGHT)
      }

      onMapboxReady(mapboxMap)
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  var onMapboxReady: (mapboxMap: MapboxMap) -> Unit = {}

  private fun getStyleJson(): String {
    val inputs: InputStream = resources.openRawResource(R.raw.map_style)
    val writer: Writer = StringWriter()
    val buffer = CharArray(1024)
    try {
      val reader: Reader = BufferedReader(InputStreamReader(inputs, "UTF-8"))
      var n: Int
      while (reader.read(buffer).also { n = it } != -1) {
        writer.write(buffer, 0, n)
      }
    } finally {
      inputs.close()
    }

    return writer.toString()
  }

  fun isDarkMode(): Boolean {
    val mode = resources.configuration.uiMode and
      Configuration.UI_MODE_NIGHT_MASK
    return mode == Configuration.UI_MODE_NIGHT_YES
  }
}
