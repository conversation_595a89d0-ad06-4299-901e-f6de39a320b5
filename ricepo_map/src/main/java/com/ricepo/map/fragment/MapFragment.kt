package com.ricepo.map.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.MapStyleOptions
import com.ricepo.map.R

//
// Created by <PERSON><PERSON> on 28/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

open class MapFragment : SupportMapFragment(), OnMapReadyCallback {

  override fun onCreateView(inflater: LayoutInflater, parent: ViewGroup?, bundle: Bundle?): View? {
    return super.onCreateView(inflater, parent, bundle)
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    getMapAsync(this)
  }

  override fun onMapReady(map: GoogleMap?) {
    try {
      // customise the styling of the base map using a json object
      // defined in a raw resource file
      map?.setMapStyle(
        MapStyleOptions.loadRawResourceStyle(context, R.raw.map_style)
      )
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }
}
