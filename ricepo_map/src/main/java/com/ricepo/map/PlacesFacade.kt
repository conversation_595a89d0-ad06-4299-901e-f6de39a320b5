package com.ricepo.map

import android.Manifest.permission.ACCESS_FINE_LOCATION
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.AutocompleteSessionToken
import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.api.net.FetchPlaceRequest
import com.google.android.libraries.places.api.net.FetchPlaceResponse
import com.google.android.libraries.places.api.net.FindAutocompletePredictionsRequest
import com.google.android.libraries.places.api.net.FindAutocompletePredictionsResponse
import com.google.android.libraries.places.api.net.FindCurrentPlaceRequest
import com.google.android.libraries.places.api.net.FindCurrentPlaceResponse
import com.google.android.libraries.places.api.net.PlacesClient
import com.ricepo.map.model.FormatUserAddress
import com.ricepo.map.model.PlaceAutocompleteResult
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.subjects.PublishSubject
import java.util.Arrays

//
// Created by Thomsen on 7/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class PlacesFacade {

  private var placesClient: PlacesClient

  companion object {
    lateinit var instance: PlacesFacade
    // create single instance by inner class
    fun init(appContext: Context, apiKey: String) {
      instance = SingleHolder.holder(appContext, apiKey)
    }

    fun instance() = instance
  }

  fun fetchPlaceById(placeId: String, placeDescription: String?): Observable<FormatUserAddress> {
    // Specify the fields to return.
    val placeFields = Arrays.asList(
      Place.Field.ADDRESS, Place.Field.ADDRESS_COMPONENTS, Place.Field.ID,
      Place.Field.LAT_LNG, Place.Field.NAME, Place.Field.OPENING_HOURS,
      Place.Field.PHONE_NUMBER, Place.Field.PHOTO_METADATAS, Place.Field.PLUS_CODE,
      Place.Field.PRICE_LEVEL, Place.Field.RATING, Place.Field.TYPES,
      Place.Field.USER_RATINGS_TOTAL, Place.Field.VIEWPORT, Place.Field.UTC_OFFSET,
      Place.Field.WEBSITE_URI
    )

    // Construct a request object, passing the place ID and fields array.
    val request = FetchPlaceRequest.newInstance(placeId, placeFields)

    return Observable.create {
      placesClient.fetchPlace(request)
        .addOnSuccessListener { response: FetchPlaceResponse ->
          val place = response.place
          it.onNext(FormatUserAddress(place, placeDescription))
        }
        .addOnFailureListener { exception: java.lang.Exception ->
          it.onNext(FormatUserAddress())
        }
    }
  }

  /**
   * get place detail by current place id
   */
  fun fetchCurrentPlace(context: Context?): PublishSubject<Any> {

    val subject = PublishSubject.create<Any>()

    // if the context is null and don't found place
    if (context == null) {
      subject.onNext(FormatUserAddress())
      return subject
    }

    // Use fields to define the data types to return place id.
    val placeFields: List<Place.Field> = Arrays.asList(Place.Field.ID)

    // Use the builder to create a FindCurrentPlaceRequest.
    val request = FindCurrentPlaceRequest.newInstance(placeFields)

    // Call findCurrentPlace and handle the response (first check that the user has granted permission).
    if (ContextCompat.checkSelfPermission(
        context,
        ACCESS_FINE_LOCATION
      ) === PackageManager.PERMISSION_GRANTED
    ) {
      val placeResponse = placesClient.findCurrentPlace(request)
      placeResponse.addOnCompleteListener { task: Task<FindCurrentPlaceResponse?> ->
        if (task.isSuccessful) {
          val response = task.result
          val place = response?.placeLikelihoods?.firstOrNull()?.place
          if (place != null && place.id != null) {
            // get place detail by id
            val placeId = place.id ?: ""
            fetchPlaceById(placeId, null)
              .subscribe {
                // set the address from gps location
                it.source = FormatUserAddress.SOURCE_GPS
                subject.onNext(it)
              }
          } else {
            // not place data to emit subscribe
            subject.onNext(FormatUserAddress())
          }
        } else {
          val exception = task.exception
          if (exception is ApiException && exception.statusCode == 7) {
            // network failed
            subject.onNext(false)
          } else {
            subject.onNext(FormatUserAddress())
          }
        }
      }
    } else {
      // A local method to request required permissions;
      // See https://developer.android.com/training/permissions/requesting
      requestLocationPermission(context as Activity, 0)
    }

    return subject
  }

  /**
   * current place not contain ADDRESS_COMPONENTS*
   */
  @Deprecated("please use fetchCurrentPlace method to get current place detail")
  fun fetchCurrentPlaceAll(context: Context?): PublishSubject<FormatUserAddress> {

    val subject = PublishSubject.create<FormatUserAddress>()

    // if the context is null and don't found place
    if (context == null) {
      subject.onNext(FormatUserAddress())
      return subject
    }

    // Use fields to define the data types to return.
    val placeFields: List<Place.Field> = Arrays.asList(
      Place.Field.ADDRESS, Place.Field.ID,
      Place.Field.LAT_LNG, Place.Field.NAME,
      Place.Field.PHOTO_METADATAS, Place.Field.PLUS_CODE,
      Place.Field.PRICE_LEVEL, Place.Field.RATING, Place.Field.TYPES,
      Place.Field.USER_RATINGS_TOTAL, Place.Field.VIEWPORT
    )

    // Use the builder to create a FindCurrentPlaceRequest.
    val request = FindCurrentPlaceRequest.newInstance(placeFields)

    // Call findCurrentPlace and handle the response (first check that the user has granted permission).
    if (ContextCompat.checkSelfPermission(
        context,
        ACCESS_FINE_LOCATION
      ) === PackageManager.PERMISSION_GRANTED
    ) {
      val placeResponse = placesClient.findCurrentPlace(request)
      placeResponse.addOnCompleteListener { task: Task<FindCurrentPlaceResponse?> ->
        if (task.isSuccessful) {
          val response = task.result
          val place = response?.placeLikelihoods?.firstOrNull()?.place
          if (place != null) {
            subject.onNext(FormatUserAddress(place, null))
          } else {
            // not place data to emit subscribe
            subject.onNext(FormatUserAddress())
          }
        } else {
          val exception = task.exception
          if (exception is ApiException) {
          }
          subject.onNext(FormatUserAddress())
        }
      }
    } else {
      // A local method to request required permissions;
      // See https://developer.android.com/training/permissions/requesting
      requestLocationPermission(context as Activity, 0)
    }

    return subject
  }

  /**
   * obtain autocomplete address prediction by google places sdk
   */
  fun findAutocompletePredictions(query: String): Observable<List<PlaceAutocompleteResult>> {

    // Create a new token for the autocomplete session. Pass this to FindAutocompletePredictionsRequest,
    // and once again when the user makes a selection (for example when calling fetchPlace()).
    val token = AutocompleteSessionToken.newInstance()

    // Use the builder to create a FindAutocompletePredictionsRequest.
    // use the geocode filter not return (Long Island, New York, USA)
    val request =
      FindAutocompletePredictionsRequest.builder()
        .setSessionToken(token)
        .setQuery(query)
        .build()

    return Observable.create {
      placesClient.findAutocompletePredictions(request)
        .addOnSuccessListener { response: FindAutocompletePredictionsResponse ->
          // response convert place autocomplete result
          println("riceLogs: got place results")
          it.onNext(
            response.autocompletePredictions.map {
              PlaceAutocompleteResult(
                it.getFullText(null).toString(), it.distanceMeters, it.placeId,
                "", listOf(), listOf(), listOf(), null
              )
            }
          )
        }
        .addOnFailureListener {
        }
    }
  }

  fun requestLocationPermission(thisActivity: Activity, requestCode: Int): Boolean {
    var isGranted = false
    // Here, thisActivity is the current activity
    if (ContextCompat.checkSelfPermission(
        thisActivity,
        ACCESS_FINE_LOCATION
      )
      != PackageManager.PERMISSION_GRANTED
    ) {

      // Permission is not granted
      // Should we show an explanation?
      if (ActivityCompat.shouldShowRequestPermissionRationale(
          thisActivity,
          ACCESS_FINE_LOCATION
        )
      ) {
        // Show an explanation to the user *asynchronously* -- don't block
        // this thread waiting for the user's response! After the user
        // sees the explanation, try again to request the permission.
      } else {
        // No explanation needed, we can request the permission.
        ActivityCompat.requestPermissions(
          thisActivity,
          arrayOf(ACCESS_FINE_LOCATION), requestCode
        )

        // MY_PERMISSIONS_REQUEST_READ_CONTACTS is an
        // app-defined int constant. The callback method gets the
        // result of the request.
      }
    } else {
      // Permission has already been granted
      isGranted = true
    }
    return isGranted
  }

  private constructor(appContext: Context, apiKey: String) {
    // initialize places sdk
    Places.initialize(appContext, apiKey)

    // create a new places client instance
    placesClient = Places.createClient(appContext)
  }

  private object SingleHolder {
    fun holder(appContext: Context, apiKey: String): PlacesFacade {
      return PlacesFacade(appContext, apiKey)
    }
  }
}
