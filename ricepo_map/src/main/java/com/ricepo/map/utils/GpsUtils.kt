package com.ricepo.map.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager
import androidx.core.app.ActivityCompat

//
// Created by <PERSON><PERSON> on 3/15/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
object GpsUtils {

  /**
   * get current location by gps or network
   */
  fun getCurrentLocation(context: Context): Location? {
    val gpsLocation = requestUpdatesFromProvider(context, LocationManager.GPS_PROVIDER)
    val networkLocation = requestUpdatesFromProvider(context, LocationManager.NETWORK_PROVIDER)
    return getBetterLocation(gpsLocation, networkLocation)
  }

  /**
   * get the location by provider
   */
  private fun requestUpdatesFromProvider(context: Context, provider: String): Location? {
    var location: Location? = null
    val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager

    if (locationManager.isProviderEnabled(provider)) {
      if (ActivityCompat.checkSelfPermission(
          context,
          Manifest.permission.ACCESS_FINE_LOCATION
        ) != PackageManager.PERMISSION_GRANTED &&
        ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_COARSE_LOCATION
          ) != PackageManager.PERMISSION_GRANTED
      ) {
        return null
      }
      location = locationManager.getLastKnownLocation(provider)
    }
    return location
  }

  private fun getBetterLocation(
    newLocation: Location?,
    currentBestLocation: Location?
  ): Location? {
    if (currentBestLocation == null) {
      // A new location is always better than no location
      return newLocation
    }
    val newLocation = newLocation ?: return currentBestLocation

    // Check whether the new location fix is newer or older
    val timeDelta = newLocation.time - currentBestLocation.time
    val isSignificantlyNewer: Boolean = timeDelta > TWO_MINUTES
    val isSignificantlyOlder: Boolean = timeDelta < -TWO_MINUTES
    val isNewer = timeDelta > 0

    // If it's been more than two minutes since the current location, use the new location
    // because the user has likely moved.
    if (isSignificantlyNewer) {
      return newLocation
      // If the new location is more than two minutes older, it must be worse
    } else if (isSignificantlyOlder) {
      return currentBestLocation
    }

    // Check whether the new location fix is more or less accurate
    val accuracyDelta = (newLocation.accuracy - currentBestLocation.accuracy).toInt()
    val isLessAccurate = accuracyDelta > 0
    val isMoreAccurate = accuracyDelta < 0
    val isSignificantlyLessAccurate = accuracyDelta > 200

    // Check if the old and new location are from the same provider
    val isFromSameProvider: Boolean = isSameProvider(
      newLocation.provider,
      currentBestLocation.provider
    )

    // Determine location quality using a combination of timeliness and accuracy
    if (isMoreAccurate) {
      return newLocation
    } else if (isNewer && !isLessAccurate) {
      return newLocation
    } else if (isNewer && !isSignificantlyLessAccurate && isFromSameProvider) {
      return newLocation
    }
    return currentBestLocation
  }

  /**
   *  Checks whether two providers are the same
   */
  private fun isSameProvider(provider1: String?, provider2: String?): Boolean {
    return if (provider1 == null) {
      provider2 == null
    } else provider1 == provider2
  }

  private const val TWO_MINUTES = 1000 * 60 * 2
}
