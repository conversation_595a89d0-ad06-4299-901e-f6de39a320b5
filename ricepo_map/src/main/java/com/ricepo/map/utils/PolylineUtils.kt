package com.ricepo.map.utils

import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.PolyUtil

//
// Created by <PERSON><PERSON> on 3/23/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
object PolylineUtils {

  fun decode(encodePath: String?): List<LatLng>? {
    val encodePath = encodePath ?: return null
    return try {
      PolyUtil.decode(encodePath)
    } catch (e: Exception) {
      e.printStackTrace()
      null
    }
  }

  fun decodePoly(encoded: String?): List<LatLng>? {
    val encoded = encoded ?: return null
    val poly: MutableList<LatLng> = ArrayList()
    var index = 0
    val len = encoded.length
    var lat = 0
    var lng = 0
    while (index < len) {
      var b: Int
      var shift = 0
      var result = 0
      do {
        b = encoded[index++].toInt() - 63
        result = result or (b and 0x1f shl shift)
        shift += 5
      } while (b >= 0x20)
      val dlat = if (result and 1 != 0) (result shr 1).inv() else result shr 1
      lat += dlat
      shift = 0
      result = 0
      do {
        b = encoded[index++].toInt() - 63
        result = result or (b and 0x1f shl shift)
        shift += 5
      } while (b >= 0x20)
      val dlng = if (result and 1 != 0) (result shr 1).inv() else result shr 1
      lng += dlng
      // official is 1E5, 1E6 before our tron result
      val p = LatLng(
        lat.toDouble() / 1E6,
        lng.toDouble() / 1E6
      )
      poly.add(p)
    }
    return poly
  }
}
