package com.ricepo.map

import com.ricepo.base.consts.BaseConstant
import com.ricepo.map.model.FormatUserAddress

//
// Created by <PERSON><PERSON> on 1/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class MapFacade {

  companion object {

    /**
     * generate google map static image url by [FormatUserAddress]
     */
    fun generateMapUrl(address: FormatUserAddress, isDark: Boolean): String {
      return generateMapUrl(address?.formatted ?: "", isDark)
    }

    /**
     * generate google map static image url by [address] String
     */
    fun generateMapUrl(address: String, isDark: Boolean): String {
      val staticMapUrl = "https://maps.googleapis.com/maps/api/staticmap"

      val urlParams = UrlBuilder().initParams("key", MapConsts.ANDROID_KEY)
        .appendParams("size", "150x150")
        .appendParams("scale", "2")
        .appendParams("zoom", "15")
        .appendParams("markers", "anchor:32,64|icon:http://${BaseConstant.S_RICE_ROCKS}/v6/pin.png|$address")
      if (isDark) {
        urlParams.appendDarkStyle()
      } else {
        urlParams.appendLightStyle()
      }

      return staticMapUrl + urlParams.build()
    }
  }

  class UrlBuilder() {

    private var builder: StringBuilder = StringBuilder()

    fun initParams(key: String, value: String): UrlBuilder {
      builder.append("?")
      builder.append(key, "=$value")
      return this
    }

    fun appendParams(key: String, value: String): UrlBuilder {
      builder.append("&")
      builder.append(key, "=$value")
      return this
    }

    fun build(): String {
      return builder.toString()
    }

    /**
     * the map light style
     */
    fun appendLightStyle(): UrlBuilder {
      appendParams("style", "element:geometry|color:0xf5f5f5")
      appendParams("style", "element:labels.icon|visibility:off")
      appendParams("style", "element:labels.text.fill|color:0x616161")
      appendParams("style", "element:labels.text.stroke|color:0xf5f5f5")
      appendParams("style", "feature:administrative.land_parcel|element:labels.text.fill|color:0xbdbdbd")
      appendParams("style", "feature:poi|element:geometry|color:0xeeeeee")
      appendParams("style", "feature:poi|element:labels.text.fill|color:0x757575")
      appendParams("style", "feature:poi.park|element:geometry|color:0xe5e5e5")
      appendParams("style", "feature:poi.park|element:labels.text.fill|color:0x9e9e9e")
      appendParams("style", "feature:road|element:geometry|color:0xffffff")
      appendParams("style", "feature:road.arterial|element:labels.text.fill|color:0x757575")
      appendParams("style", "feature:road.highway|element:geometry|color:0xdadada")
      appendParams("style", "feature:road.highway|element:labels.text.fill|color:0x616161")
      appendParams("style", "feature:road.local|element:labels.text.fill|color:0x9e9e9e")
      appendParams("style", "feature:transit.line|element:geometry|color:0xe5e5e5")
      appendParams("style", "feature:transit.station|element:geometry|color:0xeeeeee")
      appendParams("style", "feature:water|element:geometry|color:0xc9c9c9")
      appendParams("style", "feature:water|element:labels.text.fill|color:0x9e9e9e")
      return this
    }

    /**
     * the map dark style
     */
    fun appendDarkStyle(): UrlBuilder {
      appendParams("style", "element:geometry|color:0x212121")
      appendParams("style", "element:labels.icon|visibility:off")
      appendParams("style", "element:labels.text.fill|color:0x757575")
      appendParams("style", "element:labels.text.stroke|color:0x212121")
      appendParams("style", "feature:administrative|element:geometry|color:0x757575")
      appendParams("style", "feature:administrative.country|element:labels.text.fill|color:0x9e9e9e")
      appendParams("style", "feature:administrative.land_parcel|visibility:off")
      appendParams("style", "feature:administrative.locality|element:labels.text.fill|color:0xbdbdbd")
      appendParams("style", "feature:poi|element:labels.text.fill|color:0x757575")
      appendParams("style", "feature:poi.park|element:geometry|color:0x181818")
      appendParams("style", "feature:poi.park|element:labels.text.fill|color:0x616161")
      appendParams("style", "feature:poi.park|element:labels.text.stroke|color:0x1b1b1b")
      appendParams("style", "feature:road|element:geometry.fill|color:0x2c2c2c")
      appendParams("style", "feature:road|element:labels.text.fill|color:0x8a8a8a")
      appendParams("style", "feature:road.arterial|element:geometry|color:0x373737")
      appendParams("style", "feature:road.arterial|element:labels.text.fill|color:0x757575")
      appendParams("style", "feature:road.highway|element:geometry|color:0x3c3c3c")
      appendParams("style", "feature:road.highway|element:labels.text.fill|color:0x616161")
      appendParams("style", "feature:road.highway.controlled_access|element:geometry|color:0x4e4e4e")
      appendParams("style", "feature:road.local|element:labels.text.fill|color:0x616161")
      appendParams("style", "feature:transit|element:labels.text.fill|color:0x757575")
      appendParams("style", "feature:water|element:geometry|color:0x000000")
      appendParams("style", "feature:water|element:labels.text.fill|color:0x3d3d3d")
      return this
    }
  }
}
