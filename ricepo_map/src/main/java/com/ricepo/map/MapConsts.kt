package com.ricepo.map

//
// Created by <PERSON><PERSON> on 1/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object MapConsts {

  const val ANDROID_KEY = "AIzaSyDWiv5vBvWCZTrBT43nFMNFDrsfX7cgrdM"

  // redirect url javascript map api (geocoding)
  const val API_KEY = "AIzaSyDWiv5vBvWCZTrBT43nFMNFDrsfX7cgrdM"
}

enum class CompassPoint(val value: Int) {
  north(1),
  south(-1),
  east(2),
  west(-2),
  northEast(3),
  southWest(-3),
  northWest(4),
  southEast(-4);

  companion object {
    /**
     * init by value default return north
     */
    fun init(value: Int): CompassPoint {
      return values().associateBy { it.value }[value] ?: north
    }
  }

  fun reverse(): Int {
    return -this.value
  }
}
