package com.ricepo.map.overlay

import android.content.Context
import android.graphics.Color
import android.graphics.Point
import android.util.AttributeSet
import android.widget.FrameLayout
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.GoogleMap.OnCameraIdleListener
import com.google.android.gms.maps.GoogleMap.OnCameraMoveListener
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.android.gms.maps.model.Polyline
import com.google.android.gms.maps.model.PolylineOptions
import com.ricepo.map.utils.DpUtils
import java.lang.Exception

open class MapOverlayLayout<V : MarkerView?> @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

  @JvmField
  protected var markersList: MutableList<V> = ArrayList()

  private var currentPolyline: Polyline? = null

  @JvmField
  protected var googleMap: GoogleMap? = null

  @JvmField
  protected var polylines: ArrayList<LatLng>? = null

  override fun onFinishInflate() {
    super.onFinishInflate()
  }

  protected fun addMarker(view: V) {
    markersList.add(view)
    addView(view)
  }

  protected fun removeMarker(view: V) {
    markersList.remove(view)
    removeView(view)
  }

  fun showAllMarkers() {
    for (i in markersList.indices) {
      markersList[i]?.show()
    }
  }

  fun hideAllMarkers() {
    for (i in markersList.indices) {
      markersList[i]?.hide()
    }
  }

  fun removeAllMarker() {
    markersList.clear()
    removeAllViews()
  }

  open fun showMarker(position: Int) {
    markersList[position]?.show()
  }

  private fun refresh(position: Int, point: Point) {
    markersList[position]?.refresh(point)
  }

  fun setupMap(googleMap: GoogleMap?) {
    this.googleMap = googleMap
  }

  fun refresh() {
    val projection = googleMap?.projection ?: return
    for (i in markersList.indices) {
      refresh(i, projection.toScreenLocation(markersList[i]?.latLng()))
    }
  }

  fun getMarker(position: Int): V? {
    return markersList.getOrNull(position)
  }

  fun isMarkerVisible(position: Int): Boolean {
    val projection = googleMap?.projection ?: return false
    val marker = getMarker(position)
    return if (marker != null) {
      projection.visibleRegion.latLngBounds.contains(marker.latLng())
    } else {
      false
    }
  }

  fun isMarkerVisibleByTop(position: Int, y: Int): Boolean {
    val projection = googleMap?.projection ?: return false
    val marker = getMarker(position)
    return if (marker != null) {
//            val ne = projection.visibleRegion.latLngBounds.northeast
//            val sw = projection.visibleRegion.latLngBounds.southwest
      // invalid when rotating map
//            val swPoint = projection.toScreenLocation(sw)
      try {
        val neScreenPoint = Point(
          resources.displayMetrics.widthPixels,
          DpUtils.dpToPx(context, 60).toInt()
        )
        val swScreenPoint = Point(0, y)
        val neLatLng = projection.fromScreenLocation(neScreenPoint)
        val swLatLng = projection.fromScreenLocation(swScreenPoint)
        // southern latitude exceeds northern latitude
//                val latLngBounds = LatLngBounds(swLatLng, neLatLng)
        val builder = LatLngBounds.builder()
        val latLngBounds = builder.include(swLatLng).include(neLatLng).build()
        latLngBounds.contains(marker.latLng())
      } catch (e: Exception) {
        e.printStackTrace()
        false
      }
    } else {
      false
    }
  }

  fun setOnCameraIdleListener(listener: OnCameraIdleListener?) {
    googleMap?.setOnCameraIdleListener(listener)
  }

  fun setOnCameraMoveListener(listener: OnCameraMoveListener?) {
    googleMap?.setOnCameraMoveListener(listener)
  }

  fun moveCamera(latLngBounds: LatLngBounds?) {
    googleMap?.moveCamera(CameraUpdateFactory.newLatLngBounds(latLngBounds, 150))
  }

  fun animateCamera(bounds: LatLngBounds?) {
    val width = width
    val height = height
    val padding = DEFAULT_MAP_PADDING
    googleMap?.animateCamera(
      CameraUpdateFactory.newLatLngBounds(
        bounds,
        width,
        height,
        padding
      )
    )
  }

  val currentLatLng: LatLng?
    get() = googleMap?.let {
      LatLng(
        it.cameraPosition.target.latitude,
        it.cameraPosition.target.longitude
      )
    }

  fun addPolyline(polylines: ArrayList<LatLng>) {
    this.polylines = polylines
    val options = PolylineOptions()
    for (i in 1 until polylines.size) {
      options.add(polylines[i - 1], polylines[i]).width(10f).color(Color.RED).geodesic(true)
    }
    currentPolyline = googleMap?.addPolyline(options)
  }

  fun removeCurrentPolyline() {
    if (currentPolyline != null) currentPolyline?.remove()
  }

  companion object {
    var DEFAULT_MAP_PADDING = 30
  }
}
