package com.ricepo.map.overlay.mapbox

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PointF
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.mapbox.mapboxsdk.geometry.LatLng
import com.ricepo.base.image.ImageLoader
import com.ricepo.map.R
import com.ricepo.map.databinding.MapItemRestaurantBinding
import com.ricepo.map.model.RestaurantMarketModel
import com.ricepo.map.utils.DpUtils
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.view.DrawableAnimationView

class PulseBoxMarkerView(context: Context, latLng: LatLng, point: PointF) :
  BoxMarkerView(context, latLng, point) {

  private var size = 0f
  private var scaleAnimation: Animation? = null
  private var strokeBackgroundPaint: Paint? = null
  private var backgroundPaint: Paint? = null

  private var text: String? = null
  private var textPaint: Paint? = null

  private var showAnimatorSet: AnimatorSet? = null
  private var hideAnimatorSet: AnimatorSet? = null

  private var binding: MapItemRestaurantBinding

  private lateinit var model: RestaurantMarketModel

  private lateinit var click: (RestaurantMarketModel) -> Unit

  constructor(
    context: Context,
    latLng: LatLng,
    point: PointF,
    model: RestaurantMarketModel,
    click: (RestaurantMarketModel) -> Unit
  ) :
    this(context, latLng, point) {
      this.model = model
      this.click = click
      showDefault()
    }

  private fun setupHideAnimatorSet() {
    val animatorScaleX: Animator = ObjectAnimator.ofFloat(this, SCALE_X, 1.0f, 0f)
    val animatorScaleY: Animator = ObjectAnimator.ofFloat(this, SCALE_Y, 1.0f, 0f)
    val animator: Animator = ObjectAnimator.ofFloat(this, ALPHA, 1f, 0f).setDuration(300)
    animator.addListener(object : AnimatorListenerAdapter() {
      override fun onAnimationStart(animation: Animator) {
        super.onAnimationStart(animation)
        visibility = INVISIBLE
        invalidate()
      }
    })
    hideAnimatorSet = AnimatorSet()
    hideAnimatorSet?.playTogether(animator, animatorScaleX, animatorScaleY)
  }

  private fun setupSizes(context: Context) {
    size = DpUtils.dpToPx(context, 32) / 2
  }

  private fun setupShowAnimatorSet() {
    val animatorScaleX: Animator = ObjectAnimator.ofFloat(this, SCALE_X, 1.5f, 1f)
    val animatorScaleY: Animator = ObjectAnimator.ofFloat(this, SCALE_Y, 1.5f, 1f)
    val animator: Animator = ObjectAnimator.ofFloat(this, ALPHA, 0f, 1f).setDuration(300)
    animator.addListener(object : AnimatorListenerAdapter() {
      override fun onAnimationStart(animation: Animator) {
        super.onAnimationStart(animation)
        visibility = VISIBLE
        invalidate()
      }
    })
    showAnimatorSet = AnimatorSet()
    showAnimatorSet?.playTogether(animator, animatorScaleX, animatorScaleY)
  }

  private fun setupScaleAnimation(context: Context) {
    scaleAnimation = AnimationUtils.loadAnimation(context, R.anim.pulse)
    scaleAnimation?.duration = 1000
    scaleAnimation?.interpolator = AccelerateDecelerateInterpolator()
//        scaleAnimation?.repeatCount = Animation.INFINITE
//        scaleAnimation?.repeatMode = Animation.REVERSE
  }

  private fun setupTextPaint(context: Context) {
    textPaint = Paint()
    textPaint?.color = ContextCompat.getColor(context, com.ricepo.style.R.color.white)
    textPaint?.textAlign = Paint.Align.CENTER
    textPaint?.textSize = context.resources.getDimensionPixelSize(
      R.dimen.textsize_medium
    ).toFloat()
  }

  private fun setupStrokeBackgroundPaint(context: Context) {
    strokeBackgroundPaint = Paint()
    strokeBackgroundPaint?.color = ContextCompat.getColor(context, android.R.color.white)
    strokeBackgroundPaint?.style = Paint.Style.STROKE
    strokeBackgroundPaint?.isAntiAlias = true
    strokeBackgroundPaint?.strokeWidth = DpUtils.dpToPx(context, STROKE_DIMEN)
  }

  private fun setupBackgroundPaint(context: Context) {
    backgroundPaint = Paint()
    backgroundPaint?.color =
      ContextCompat.getColor(context, android.R.color.holo_red_dark)
    backgroundPaint?.isAntiAlias = true
  }

  override fun setLayoutParams(params: ViewGroup.LayoutParams) {
    val frameParams = LayoutParams(
      LayoutParams.WRAP_CONTENT,
      LayoutParams.WRAP_CONTENT
    )
    frameParams.width = DpUtils.dpToPx(context, POINT_NAME_WIDTH).toInt()
    frameParams.height = DpUtils.dpToPx(context, POINT_HEIGHT).toInt()
    frameParams.leftMargin = (point.x - frameParams.width / 2).toInt()
    frameParams.topMargin = (point.y - frameParams.height / 3).toInt()
    super.setLayoutParams(frameParams)

//        val nameParams = binding.tvRestaurantName.layoutParams
//        if (nameParams is ConstraintLayout.LayoutParams) {
//            nameParams.width = GuiUtils.dpToPx(context, POINT_NAME_WIDTH).toInt()
//        }
//        binding.tvRestaurantName.layoutParams = nameParams
  }

  fun pulse() {
//        startAnimation(scaleAnimation)
    showCurrent()
//        binding.ivRestaurantImg.startAnimation(scaleAnimation)
    binding.davRestaurantImg.start()
  }

  fun stop() {
//        scaleAnimation?.cancel()
    binding.davRestaurantImg.pause()
    showDefault()
  }

  private fun showCurrent() {
    binding.tvRestaurantName.text = model.name
//        ImageLoader.load(binding.ivRestaurantImg, model.imageUrl)
    binding.ivRestaurantImg.visibility = View.INVISIBLE
    binding.davRestaurantImg.visibility = View.VISIBLE

    binding.davRestaurantImg.clearData()
    binding.davRestaurantImg.setData(getSelectedAnimDatas())
    binding.davRestaurantImg.isRepeat = true
    binding.davRestaurantImg.setGravity(Gravity.FILL_HORIZONTAL)

    binding.davRestaurantImg.alpha = 1f

    val imgParams = binding.davRestaurantImg.layoutParams
    if (imgParams is ConstraintLayout.LayoutParams) {
      imgParams.width = DpUtils.dpToPx(context, POINT_WIDTH).toInt()
    }
    binding.davRestaurantImg.layoutParams = imgParams
  }

  private fun getSelectedAnimDatas(): ArrayList<DrawableAnimationView.AnimData>? {
    return getAnimDatas("selected", 29)
  }

  private fun getAnimDatas(fileName: String, n: Int): ArrayList<DrawableAnimationView.AnimData>? {
    val dataList = ArrayList<DrawableAnimationView.AnimData>()
    var data: DrawableAnimationView.AnimData
    var resId: Int
    var fileName = fileName
    var suffixValue = ""
    for (i in 1..n) {
      if (i < 10) {
        suffixValue += "0"
      }
      try {
        resId = ResourcesUtil.getDrawableId("${fileName}_$suffixValue$i")
        data = DrawableAnimationView.AnimData()
        data.filePath = resId
        dataList.add(data)
        suffixValue = ""
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
    return dataList
  }

  private fun showDefault() {
    binding.tvRestaurantName.text = model.name

    binding.ivRestaurantImg.visibility = View.VISIBLE
    binding.davRestaurantImg.visibility = View.INVISIBLE

    val resId = if (model.isClosed) {
      com.ricepo.style.R.drawable.ic_pickup_restaurant_disabled
    } else {
      com.ricepo.style.R.drawable.ic_pickup_restaurant_enabled
    }
    ImageLoader.load(binding.ivRestaurantImg, resId)
    binding.ivRestaurantImg.alpha = model.imgAlpha

    val imgParams = binding.ivRestaurantImg.layoutParams
    if (imgParams is ConstraintLayout.LayoutParams) {
      imgParams.width = DpUtils.dpToPx(context, POINT_IMAGE_DEFAULT_SIZE).toInt()
    }
    binding.ivRestaurantImg.layoutParams = imgParams
  }

  override fun onDraw(canvas: Canvas) {
//        drawBackground(canvas)
//        drawStrokeBackground(canvas)
//        drawText(canvas)
    super.onDraw(canvas)
  }

  private fun drawText(canvas: Canvas) {
    val text = text ?: return
    val textPaint = textPaint ?: return
    if (!TextUtils.isEmpty(text)) {
      canvas.drawText(
        text, size,
        size - (textPaint.descent() + textPaint.ascent()) / 2,
        textPaint
      )
    }
  }

  private fun drawStrokeBackground(canvas: Canvas) {
    val strokePaint = strokeBackgroundPaint ?: return
    canvas.drawCircle(size, size, DpUtils.dpToPx(context, 28) / 2, strokePaint)
  }

  private fun drawBackground(canvas: Canvas) {
    val paint = backgroundPaint ?: return
    canvas.drawCircle(size, size, size, paint)
  }

  fun setText(text: String?) {
    this.text = text
    invalidate()
  }

  override fun hide() {
    hideAnimatorSet?.start()
  }

  override fun refresh(point: PointF) {
    this.point = point
    updatePulseViewLayoutParams(point)
  }

  override fun show() {
    showAnimatorSet?.start()
  }

  fun showWithDelay(delay: Int) {
    showAnimatorSet?.startDelay = delay.toLong()
    showAnimatorSet?.start()
  }

  fun updatePulseViewLayoutParams(point: PointF?) {
    this.point = point ?: return
    val params = LayoutParams(
      LayoutParams.WRAP_CONTENT,
      LayoutParams.WRAP_CONTENT
    )
    params.width = DpUtils.dpToPx(context, POINT_NAME_WIDTH).toInt()
    params.height = DpUtils.dpToPx(context, POINT_HEIGHT).toInt()
    params.leftMargin = (point.x - params.width / 2).toInt()
    params.topMargin = (point.y - params.height / 3).toInt()
    super.setLayoutParams(params)

//        val nameParams = binding.tvRestaurantName.layoutParams
//        if (nameParams is ConstraintLayout.LayoutParams) {
//            nameParams.width = GuiUtils.dpToPx(context, POINT_NAME_WIDTH).toInt()
//        }
//        binding.tvRestaurantName.layoutParams = nameParams

    invalidate()
  }

  fun setClickTag(model: RestaurantMarketModel) {
    binding.ivRestaurantImg.tag = model
  }

  private fun setupListener() {
    binding.ivRestaurantImg.setOnClickListener { view ->
      val markerModel = view.tag
      if (markerModel is RestaurantMarketModel) {
        click(markerModel)
      }
    }
  }

  private val POINT_IMAGE_DEFAULT_SIZE = 40
  private val POINT_WIDTH = 60
  private val POINT_NAME_WIDTH = 150

  companion object {
    val POINT_HEIGHT = 120
    private const val STROKE_DIMEN = 2
  }

  init {
    visibility = INVISIBLE
    binding = MapItemRestaurantBinding.inflate(
      LayoutInflater.from(context),
      this, true
    )
    setupSizes(context)
    setupScaleAnimation(context)
    setupBackgroundPaint(context)
    setupStrokeBackgroundPaint(context)
    setupTextPaint(context)
    setupShowAnimatorSet()
    setupHideAnimatorSet()
    setupListener()
  }
}
