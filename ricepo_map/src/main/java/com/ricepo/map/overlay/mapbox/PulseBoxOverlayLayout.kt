package com.ricepo.map.overlay.mapbox

import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import com.mapbox.mapboxsdk.geometry.LatLng
import com.mapbox.mapboxsdk.geometry.LatLngBounds
import com.ricepo.map.R
import com.ricepo.map.model.RestaurantMarketModel

class PulseBoxOverlayLayout @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null
) :
  MapBoxOverlayLayout<BoxMarkerView?>(context, attrs) {

  private var startMarker: PulseBoxMarkerView? = null
  private var finishMarker: PulseBoxMarkerView? = null

  private var scaleAnimationDelay = 100

  fun setupMarkers(point: PointF, latLng: LatLng) {
    startMarker = PulseBoxMarkerView(context, latLng, point)
    finishMarker = PulseBoxMarkerView(context, latLng, point)
  }

  fun removeStartMarker() {
    removeMarker(startMarker)
  }

  fun removeFinishMarker() {
    removeMarker(finishMarker)
  }

  fun addStartMarker(latLng: LatLng) {
    startMarker = createPulseMarkerView(latLng)
    startMarker?.updatePulseViewLayoutParams(mMap?.projection?.toScreenLocation(latLng))
    addMarker(startMarker)
    startMarker?.show()
  }

  fun addFinishMarker(latLng: LatLng) {
    finishMarker = createPulseMarkerView(latLng)
    finishMarker?.updatePulseViewLayoutParams(mMap?.projection?.toScreenLocation(latLng))
    addMarker(finishMarker)
    finishMarker?.show()
  }

  private fun createPulseMarkerView(latLng: LatLng): PulseBoxMarkerView? {
    val point = mMap?.projection?.toScreenLocation(latLng) ?: return null
    return PulseBoxMarkerView(context, latLng, point)
  }

  private fun createPulseMarkerView(
    latLng: LatLng,
    point: PointF,
    model: RestaurantMarketModel
  ): PulseBoxMarkerView {
    val pulseMarkerView = PulseBoxMarkerView(context, latLng, point, model) { markerModel ->
      onMarkerClickListener?.onClick(markerModel)
    }
    // marker click tag
    pulseMarkerView.setClickTag(model)
    addMarker(pulseMarkerView)
    return pulseMarkerView
  }

  fun createAndShowMarker(latLng: LatLng, model: RestaurantMarketModel) {
    val point = mMap?.projection?.toScreenLocation(latLng) ?: return
    val marker = createPulseMarkerView(latLng, point, model)
    marker.show()
//        marker.showWithDelay(scaleAnimationDelay)
    scaleAnimationDelay += ANIMATION_DELAY_FACTOR
  }

  override fun showMarker(position: Int) {
    markersList.forEach { view ->
      if (view is PulseBoxMarkerView) {
        view.stop()
      }
    }
    val targetView = markersList.getOrNull(position)
    if (targetView is PulseBoxMarkerView) {
      bringChildToFront(targetView)
      targetView.pulse()
    }
  }

  fun drawStartAndFinishMarker() {
    addStartMarker(polylines!![0])
    addFinishMarker(polylines!![polylines!!.size - 1])
    setOnCameraIdleListener(null)
  }

  fun onBackPressed(latLngBounds: LatLngBounds?) {
    moveCamera(latLngBounds)
    removeStartAndFinishMarkers()
    removeCurrentPolyline()
    showAllMarkers()
    refresh()
  }

  private fun removeStartAndFinishMarkers() {
    removeStartMarker()
    removeFinishMarker()
  }

  private var onMarkerClickListener: OnMarkerClickListener? = null

  fun setOnMarkerClickListener(markerClickListener: (model: RestaurantMarketModel) -> Unit) {
    onMarkerClickListener = object : OnMarkerClickListener {
      override fun onClick(model: RestaurantMarketModel) {
        markerClickListener(model)
      }
    }
  }

  interface OnMarkerClickListener {
    fun onClick(model: RestaurantMarketModel)
  }

  companion object {
    private const val ANIMATION_DELAY_FACTOR = 100
  }

  init {
    inflate(context, R.layout.pulse_wrapper_layout, this)
  }
}
