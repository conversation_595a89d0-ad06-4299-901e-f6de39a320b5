package com.ricepo.map.overlay

import android.content.Context
import android.graphics.Point
import android.util.AttributeSet
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.ricepo.map.R
import com.ricepo.map.model.RestaurantMarketModel

class PulseOverlayLayout @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null
) :
  MapOverlayLayout<MarkerView?>(context, attrs) {

  private var startMarker: PulseMarkerView? = null
  private var finishMarker: PulseMarkerView? = null

  private var scaleAnimationDelay = 100

  fun setupMarkers(point: Point, latLng: LatLng) {
    startMarker = PulseMarkerView(context, latLng, point)
    finishMarker = PulseMarkerView(context, latLng, point)
  }

  fun removeStartMarker() {
    removeMarker(startMarker)
  }

  fun removeFinishMarker() {
    removeMarker(finishMarker)
  }

  fun addStartMarker(latLng: LatLng) {
    startMarker = createPulseMarkerView(latLng)
    startMarker?.updatePulseViewLayoutParams(googleMap?.projection?.toScreenLocation(latLng))
    addMarker(startMarker)
    startMarker?.show()
  }

  fun addFinishMarker(latLng: LatLng) {
    finishMarker = createPulseMarkerView(latLng)
    finishMarker?.updatePulseViewLayoutParams(googleMap?.projection?.toScreenLocation(latLng))
    addMarker(finishMarker)
    finishMarker?.show()
  }

  private fun createPulseMarkerView(latLng: LatLng): PulseMarkerView? {
    val point = googleMap?.projection?.toScreenLocation(latLng) ?: return null
    return PulseMarkerView(context, latLng, point)
  }

  private fun createPulseMarkerView(
    latLng: LatLng,
    point: Point,
    model: RestaurantMarketModel
  ): PulseMarkerView {
    val pulseMarkerView = PulseMarkerView(context, latLng, point, model) { markerModel ->
      onMarkerClickListener?.onClick(markerModel)
    }
    // marker click tag
    pulseMarkerView.setClickTag(model)
    addMarker(pulseMarkerView)
    return pulseMarkerView
  }

  fun createAndShowMarker(latLng: LatLng, model: RestaurantMarketModel) {
    val point = googleMap?.projection?.toScreenLocation(latLng) ?: return
    val marker = createPulseMarkerView(latLng, point, model)
    marker.show()
//        marker.showWithDelay(scaleAnimationDelay)
    scaleAnimationDelay += ANIMATION_DELAY_FACTOR
  }

  override fun showMarker(position: Int) {
    markersList.forEach { view ->
      if (view is PulseMarkerView) {
        view.stop()
      }
    }
    val targetView = markersList.getOrNull(position)
    if (targetView is PulseMarkerView) {
      bringChildToFront(targetView)
      targetView.pulse()
    }
  }

  fun drawStartAndFinishMarker() {
    addStartMarker(polylines!![0])
    addFinishMarker(polylines!![polylines!!.size - 1])
    setOnCameraIdleListener(null)
  }

  fun onBackPressed(latLngBounds: LatLngBounds?) {
    moveCamera(latLngBounds)
    removeStartAndFinishMarkers()
    removeCurrentPolyline()
    showAllMarkers()
    refresh()
  }

  private fun removeStartAndFinishMarkers() {
    removeStartMarker()
    removeFinishMarker()
  }

  private var onMarkerClickListener: OnMarkerClickListener? = null

  fun setOnMarkerClickListener(markerClickListener: (model: RestaurantMarketModel) -> Unit) {
    onMarkerClickListener = object : OnMarkerClickListener {
      override fun onClick(model: RestaurantMarketModel) {
        markerClickListener(model)
      }
    }
  }

  interface OnMarkerClickListener {
    fun onClick(model: RestaurantMarketModel)
  }

  companion object {
    private const val ANIMATION_DELAY_FACTOR = 100
  }

  init {
    inflate(context, R.layout.pulse_wrapper_layout, this)
  }
}
