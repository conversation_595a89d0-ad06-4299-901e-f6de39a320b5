package com.ricepo.map.overlay.mapbox

import android.content.Context
import android.graphics.Color
import android.graphics.PointF
import android.util.AttributeSet
import android.widget.FrameLayout
import com.mapbox.mapboxsdk.annotations.Polyline
import com.mapbox.mapboxsdk.annotations.PolylineOptions
import com.mapbox.mapboxsdk.camera.CameraUpdateFactory
import com.mapbox.mapboxsdk.geometry.LatLng
import com.mapbox.mapboxsdk.geometry.LatLngBounds
import com.mapbox.mapboxsdk.maps.MapboxMap
import com.ricepo.map.utils.DpUtils
import java.lang.Exception

open class MapBoxOverlayLayout<V : BoxMarkerView?> @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

  @JvmField
  protected var markersList: MutableList<V> = ArrayList()

  private var currentPolyline: Polyline? = null

  @JvmField
  protected var mMap: MapboxMap? = null

  @JvmField
  protected var polylines: ArrayList<LatLng>? = null

  override fun onFinishInflate() {
    super.onFinishInflate()
  }

  protected fun addMarker(view: V) {
    markersList.add(view)
    addView(view)
  }

  protected fun removeMarker(view: V) {
    markersList.remove(view)
    removeView(view)
  }

  fun showAllMarkers() {
    for (i in markersList.indices) {
      markersList[i]?.show()
    }
  }

  fun hideAllMarkers() {
    for (i in markersList.indices) {
      markersList[i]?.hide()
    }
  }

  fun removeAllMarker() {
    markersList.clear()
    removeAllViews()
  }

  open fun showMarker(position: Int) {
    markersList[position]?.show()
  }

  private fun refresh(position: Int, point: PointF) {
    markersList[position]?.refresh(point)
  }

  fun setupMap(googleMap: MapboxMap?) {
    this.mMap = googleMap
  }

  fun refresh() {
    val projection = mMap?.projection ?: return
    for (i in markersList.indices) {
      markersList[i]?.latLng()?.let {
        refresh(i, projection.toScreenLocation(it))
      }
    }
  }

  fun getMarker(position: Int): V? {
    return markersList.getOrNull(position)
  }

  fun isMarkerVisible(position: Int): Boolean {
    val projection = mMap?.projection ?: return false
    val marker = getMarker(position)
    return if (marker != null) {
      projection.visibleRegion.latLngBounds.contains(marker.latLng())
    } else {
      false
    }
  }

  fun isMarkerVisibleByTop(position: Int, y: Int): Boolean {
    val projection = mMap?.projection ?: return false
    val marker = getMarker(position)
    return if (marker != null) {
//            val ne = projection.visibleRegion.latLngBounds.northeast
//            val sw = projection.visibleRegion.latLngBounds.southwest
      // invalid when rotating map
//            val swPoint = projection.toScreenLocation(sw)
      try {
        val neScreenPoint = PointF(
          resources.displayMetrics.widthPixels.toFloat(),
          DpUtils.dpToPx(context, 60)
        )
        val swScreenPoint = PointF(0f, y.toFloat())
        val neLatLng = projection.fromScreenLocation(neScreenPoint)
        val swLatLng = projection.fromScreenLocation(swScreenPoint)
        // southern latitude exceeds northern latitude
//                val latLngBounds = LatLngBounds(swLatLng, neLatLng)
        val builder = LatLngBounds.Builder()
        val latLngBounds = builder.include(swLatLng).include(neLatLng).build()
        latLngBounds.contains(marker.latLng())
      } catch (e: Exception) {
        e.printStackTrace()
        false
      }
    } else {
      false
    }
  }

  fun setOnCameraIdleListener(listener: MapboxMap.OnCameraIdleListener?) {
    if (listener != null) {
      mMap?.removeOnCameraIdleListener(listener)
      mMap?.addOnCameraIdleListener(listener)
    }
  }

  fun removeOnCameraIdleListener(listener: MapboxMap.OnCameraIdleListener?) {
    if (listener != null) {
      mMap?.removeOnCameraIdleListener(listener)
    }
  }

  fun setOnCameraMoveListener(listener: MapboxMap.OnCameraMoveListener?) {
    listener?.let {
      mMap?.removeOnCameraMoveListener(it)
      mMap?.addOnCameraMoveListener(it)
    }
  }

  fun moveCamera(latLngBounds: LatLngBounds?) {
    val latLngBounds = latLngBounds ?: return
    mMap?.moveCamera(CameraUpdateFactory.newLatLngBounds(latLngBounds, 150))
  }

  fun animateCamera(bounds: LatLngBounds?) {
    val bounds = bounds ?: return
    val width = width
    val height = height
    val padding = DEFAULT_MAP_PADDING
    mMap?.animateCamera(
      CameraUpdateFactory.newLatLngBounds(
        bounds,
        width.toDouble(),
        height.toDouble(),
        padding
      )
    )
  }

  val currentLatLng: LatLng?
    get() = mMap?.let {
      LatLng(
        it.cameraPosition.target.latitude,
        it.cameraPosition.target.longitude
      )
    }

  fun addPolyline(polylines: ArrayList<LatLng>) {
    this.polylines = polylines
    val options = PolylineOptions()
    for (i in 1 until polylines.size) {
      options.add(polylines[i - 1], polylines[i]).width(10f).color(Color.RED)
    }
    currentPolyline = mMap?.addPolyline(options)
  }

  fun removeCurrentPolyline() {
    if (currentPolyline != null) currentPolyline?.remove()
  }

  companion object {
    var DEFAULT_MAP_PADDING = 30
  }
}
