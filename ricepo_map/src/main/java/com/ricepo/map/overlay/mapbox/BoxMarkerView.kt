package com.ricepo.map.overlay.mapbox

import android.content.Context
import android.graphics.PointF
import android.widget.FrameLayout
import com.mapbox.mapboxsdk.geometry.LatLng

abstract class BoxMarkerView private constructor(context: Context) : FrameLayout(context) {

  protected lateinit var point: PointF

  private lateinit var latLng: LatLng

  constructor(context: Context, latLng: LatLng, point: PointF) : this(context) {
    this.latLng = latLng
    this.point = point
  }

  fun lat(): Double {
    return latLng.latitude
  }

  fun lng(): Double {
    return latLng.longitude
  }

  fun point(): PointF {
    return point
  }

  fun latLng(): LatLng {
    return latLng
  }

  abstract fun show()

  abstract fun hide()

  abstract fun refresh(point: PointF)
}
