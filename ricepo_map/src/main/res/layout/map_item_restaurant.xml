<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:focusableInTouchMode="false"
    android:focusable="false"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_restaurant_img"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_restaurant_name"
        app:layout_constraintVertical_chainStyle="packed"
        android:focusableInTouchMode="false"
        android:focusable="true"
        android:layout_width="60dp"
        android:layout_height="60dp" />

    <com.ricepo.style.view.DrawableAnimationView
        android:id="@+id/dav_restaurant_img"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_restaurant_name"
        android:focusableInTouchMode="false"
        android:focusable="true"
        android:layout_width="60dp"
        android:layout_height="60dp" />

    <com.ricepo.style.view.StrokedTextView
        android:id="@+id/tv_restaurant_name"
        android:maxLines="2"
        android:ellipsize="end"
        android:textColor="@color/pickup_restaurant_name"
        android:textSize="@dimen/sw_13sp"
        android:fontFamily="@font/font_semibold"
        tools:text="@string/test_text_length"
        app:textStrokeColor="@color/stroke_text"
        app:textStrokeWidth="2"
        app:textShadowColor="@color/restaurant_name_shadow"
        app:textShadowDy="@dimen/sw_2dp"
        app:textShadowRadius="@dimen/sw_2dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_restaurant_img"
        app:layout_constraintBottom_toBottomOf="parent"
        android:gravity="center_horizontal"
        android:layout_width="@dimen/sw_91dp"
        android:layout_height="wrap_content" />


</androidx.constraintlayout.widget.ConstraintLayout>
