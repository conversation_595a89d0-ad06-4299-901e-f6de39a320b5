<resources>
    <!--
    TODO: Before you run your application, you need a Google Maps API key.

    To get one, follow this link, follow the directions and press "Create" at the end:

    https://console.developers.google.com/flows/enableapi?apiid=maps_android_backend&keyType=CLIENT_SIDE_ANDROID&r=A8:0A:36:83:2E:36:2A:C3:71:DA:63:8A:1F:B2:B8:24:7C:2A:8C:FA%3Bcom.ricepo.map.fragment

    You can also add your credentials to an existing key, using these values:

    Package name:
    com.ricepo.map.fragment

    SHA-1 certificate fingerprint:
    A8:0A:36:83:2E:36:2A:C3:71:DA:63:8A:1F:B2:B8:24:7C:2A:8C:FA

    Alternatively, follow the directions here:
    https://developers.google.com/maps/documentation/android/start#get-key

    Once you have your key (it starts with "<PERSON>za"), replace the "google_maps_key"
    string in this file.
    -->
    <string name="google_maps_key" templateMergeStrategy="preserve" translatable="false">AIzaSyDWiv5vBvWCZTrBT43nFMNFDrsfX7cgrdM</string>
</resources>
