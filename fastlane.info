> fastlane init
[✔] 🚀
[✔] Looking for iOS and Android projects in current directory...
[11:01:39]: Created new folder './fastlane'.
[11:01:39]: Detected an Android project in the current directory...
[11:01:39]: -----------------------------
[11:01:39]: --- Welcome to fastlane 🚀 ---
[11:01:39]: -----------------------------
[11:01:39]: fastlane can help you with all kinds of automation for your mobile app
[11:01:39]: We recommend automating one task first, and then gradually automating more over time
[11:01:39]:
[11:01:39]: To avoid re-entering your package name and issuer every time you run fastlane, we'll store those in a so-called Appfile.
[11:01:39]: Package Name (com.krausefx.app): rocks.rice.app

To automatically upload builds and metadata to Google Play, fastlane needs a service account json secret file
Follow the Setup Guide on how to get the Json file: https://docs.fastlane.tools/actions/supply/
Feel free to press Enter at any time in order to skip providing pieces of information when asked
[11:01:50]: Path to the json secret file:
[11:02:10]:
[11:02:10]: Do you plan on uploading metadata, screenshots, and builds to Google Play using fastlane?
[11:02:10]: We will now download your existing metadata and screenshots into the `fastlane` folder so fastlane can manage it
[11:02:10]: Download existing metadata and setup metadata management? (y/n)

Please enter "yes" or "no".
[11:02:10]: Download existing metadata and setup metadata management? (y/n)
y
[11:03:16]: Error setting value '' for option 'json_key'
[11:03:16]: Is a directory @ io_fread - /Users/<USER>/Ricepo/ricepo
[11:03:16]: Setting up `supply` (metadata management action) failed, but don't worry, you can try setting it up again using `fastlane supply init` whenever you want.
[11:03:16]: Installing dependencies for you...
[11:03:16]: $ bundle update
No such file or directory - fork failed
[11:03:16]: Something went wrong when running `bundle update` for you
[11:03:16]: Please take a look at your Gemfile at path `Gemfile`
[11:03:16]: and make sure you can run `bundle update` on your machine.
No such file or directory - fork failed
[11:03:16]: Exit status: 127
[11:03:16]: Something went wrong when running `bundle update` for you
[11:03:16]: Please take a look at your Gemfile at path `Gemfile`
[11:03:16]: and make sure you can run `bundle update` on your machine.
[11:03:16]: --------------------------------------------------------
[11:03:16]: --- ✅  Successfully generated fastlane configuration ---
[11:03:16]: --------------------------------------------------------
[11:03:16]: Generated Fastfile at path `./fastlane/Fastfile`
[11:03:16]: Generated Appfile at path `./fastlane/Appfile`
[11:03:16]: Gemfile and Gemfile.lock at path `Gemfile`
[11:03:16]: Please check the newly generated configuration files into git along with your project
[11:03:16]: This way everyone in your team can benefit from your fastlane setup
[11:03:16]: Continue by pressing Enter ⏎
s
[11:03:19]: fastlane will collect the number of errors for each action to detect integration issues
[11:03:19]: No sensitive/private information will be uploaded, more information: https://docs.fastlane.tools/#metrics
[11:03:19]: ----------------------
[11:03:19]: --- fastlane lanes ---
[11:03:19]: ----------------------
[11:03:19]: fastlane uses a `Fastfile` to store the automation configuration
[11:03:19]: Within that, you'll see different lanes.
[11:03:19]: Each is there to automate a different task, like screenshots, code signing, or pushing new releases
[11:03:19]: Continue by pressing Enter ⏎

[11:03:36]: --------------------------------------
[11:03:36]: --- How to customize your Fastfile ---
[11:03:36]: --------------------------------------
[11:03:36]: Use a text editor of your choice to open the newly created Fastfile and take a look
[11:03:36]: You can now edit the available lanes and actions to customize the setup to fit your needs
[11:03:36]: To get a list of all the available actions, open https://docs.fastlane.tools/actions
[11:03:36]: Continue by pressing Enter ⏎

[11:03:37]: ------------------------------
[11:03:37]: --- Where to go from here? ---
[11:03:37]: ------------------------------
[11:03:37]: 📸  Learn more about how to automatically generate localized Google Play screenshots:
[11:03:37]: 		https://docs.fastlane.tools/getting-started/android/screenshots/
[11:03:37]: 👩‍✈️  Learn more about distribution to beta testing services:
[11:03:37]: 		https://docs.fastlane.tools/getting-started/android/beta-deployment/
[11:03:37]: 🚀  Learn more about how to automate the Google Play release process:
[11:03:37]: 		https://docs.fastlane.tools/getting-started/android/release-deployment/
[11:03:37]:
[11:03:37]: To try your new fastlane setup, just enter and run
[11:03:37]: $ fastlane test

#######################################################################
# fastlane 2.145.0 is available. You are on 2.139.0.
# You should use the latest version.
# Please update using `sudo gem install fastlane`.
#######################################################################