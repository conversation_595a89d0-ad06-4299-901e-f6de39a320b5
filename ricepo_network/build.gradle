apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
android {
    namespace 'com.ricepo.network'
    def globalConfiguration = rootProject.extensions.getByName("ext")
    compileSdk globalConfiguration["androidcompileSdk"]
    buildToolsVersion globalConfiguration["androidBuildToolsVersion"]

    defaultConfig {
        minSdkVersion globalConfiguration["androidMinSdkVersion"]
        targetSdkVersion globalConfiguration["androidTargetSdkVersion"]

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility javaVersion
        targetCompatibility javaVersion
    }

    buildFeatures {
        buildConfig true
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(":ricepo_base")
    implementation project(":ricepo_monitor")
    implementation project(":ricepo_style")

    def networkDependencies = rootProject.ext.networkDependencies
    def networkTestDependencies = rootProject.ext.networkTestDependencies

    implementation networkDependencies.kotlin
    implementation networkDependencies.kotlinCoroutines
    implementation networkDependencies.okHttp
    implementation networkDependencies.okHttpLogger
    implementation networkDependencies.retrofit
    implementation networkDependencies.retrofitConverter
    implementation networkDependencies.retrofitRxjava3Adapter
    implementation networkDependencies.rxKotlin
    implementation networkDependencies.rxAndroid
    api networkDependencies.sandwich

    implementation networkDependencies.javaxInject

    debugImplementation networkDependencies.chuckerDebug
    releaseImplementation networkDependencies.chuckerRelease

    testImplementation networkTestDependencies.junit
    testImplementation networkTestDependencies.mockitoKotlin

    androidTestImplementation networkTestDependencies.extJunit
    androidTestImplementation networkTestDependencies.espressoCore

}
